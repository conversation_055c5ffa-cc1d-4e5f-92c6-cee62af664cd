<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=4.0, user-scalable=no">
  <title>在线阅读PDF</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    canvas {
      width: 100%;
      height: 100%;
      border: 1px solid rgb(221, 221, 221);
    }

    /* 加载中的转圈圈样式 */
    .spinner-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 2;
      background-color: rgba(0, 0, 0, 0.3);
    }

    .spinner-box img {
      width: 9.3333vw;
      height: 9.3333vw;
    }

    .spinner-box .title {
      margin: 2.6667vw 0 0;
      font-size: 3.2vw;
      color: #fff;
      text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
      white-space: pre-line;
      text-align: center;
    }

    .spinner-box .spinner {
      width: 9.3333vw;
      height: 9.3333vw;
      animation: rotator 1.4s linear infinite;
    }

    @keyframes rotator {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(270deg);
      }
    }

    .spinner-box .path {
      stroke-dasharray: 187;
      stroke-dashoffset: 0;
      transform-origin: center;
      animation: dash 1.4s ease-in-out infinite, colors 5.6s ease-in-out infinite;
    }

    @keyframes colors {
      0% {
        stroke: #fff;
      }

      25% {
        stroke: #f7e7d7;
      }

      50% {
        stroke: #eecfaf;
      }

      75% {
        stroke: #e5b788;
      }

      100% {
        stroke: #dd9f60;
      }
    }

    @keyframes dash {
      0% {
        stroke-dashoffset: 187;
      }

      50% {
        stroke-dashoffset: 46.75;
        transform: rotate(135deg);
      }

      100% {
        stroke-dashoffset: 187;
        transform: rotate(450deg);
      }
    }
  </style>

</head>
<body>
  <div id="loading" class="spinner-box">
    <!-- 上传中 -->
    <svg class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
      <circle class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33" cy="33" r="30"></circle>
    </svg>
  </div>
  <script src="https://unpkg.com/pdfjs-dist@1.9.426/build/pdf.min.js"></script>
  <script>
    var url = "";
    let pdfDoc = null;

    (function () {
      url = pdf.getPdfParameter();
    })();

    function createPage() {
      let div = document.createElement("canvas");
      document.body.appendChild(div);
      return div;
    }

    function renderPage(num) {
      pdfDoc.getPage(num).then(function (page) {
        let viewport = page.getViewport(2.0);
        let canvas = createPage();
        let ctx = canvas.getContext('2d');

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        page.render({
          canvasContext: ctx,
          viewport: viewport
        });
      });
    }

    let loading = document.getElementById("loading")

    // 显示加载中
    loading.style.display = "flex";

    // 如果是由后端的pdf链接地址，且跨域需要携带cookie验证，getDocument中传{ url: url, withCredentials: true }
    PDFJS.getDocument({ url: url, withCredentials: true }).then(function (pdf) {
      pdfDoc = pdf;

      for (let i = 1; i <= pdfDoc.numPages; i++) {
        renderPage(i)
      }
      // 隐藏加载中
      loading.style.display = "none";
    }).catch(error => {
      alert(error)
    })
  </script>
</body>
</html>
