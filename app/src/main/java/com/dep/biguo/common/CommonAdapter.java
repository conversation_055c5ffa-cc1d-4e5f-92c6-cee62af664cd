package com.dep.biguo.common;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/22
 * @Description: 通用适配器
 */
public abstract class CommonAdapter<T> extends BaseQuickAdapter<T, BaseViewHolder> {

    public CommonAdapter(int layoutResId, @Nullable List<T> data) {
        super(layoutResId, data);
    }

    @Override
    protected abstract void convert(BaseViewHolder holder, T item);
}
