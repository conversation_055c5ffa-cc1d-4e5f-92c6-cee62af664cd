package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.SectionEntity;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/27
 * @Description:
 */
public class WechatBean {

    private int type;
    private String name;
    private List<ItemBean> data;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<ItemBean> getData() {
        return data;
    }

    public void setData(List<ItemBean> data) {
        this.data = data;
    }

    public static class ItemBean extends SectionEntity<ItemBean> {

        /**
         * id : 15
         * name : 张老师
         * phone_number : 18948709420
         * campus : 深圳校区
         * wechat : depeng9420
         * create_time : 2018-04-18 19:38:01
         * img : https://file.biguotk.com/img/qiniu/qx2Z6QuBy2.png
         */

        private int id;
        private String name;
        private String phone_number;
        private String campus;
        private String wechat;
        private String mobile;
        private String create_time;
        private String img;

        public ItemBean(boolean isHeader, String header) {
            super(isHeader, header);
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone_number() {
            return phone_number;
        }

        public void setPhone_number(String phone_number) {
            this.phone_number = phone_number;
        }

        public String getCampus() {
            return campus;
        }

        public void setCampus(String campus) {
            this.campus = campus;
        }

        public String getWechat() {
            return wechat;
        }

        public void setWechat(String wechat) {
            this.wechat = wechat;
        }

        public String getCreate_time() {
            return create_time;
        }

        public void setCreate_time(String create_time) {
            this.create_time = create_time;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }
    }
}
