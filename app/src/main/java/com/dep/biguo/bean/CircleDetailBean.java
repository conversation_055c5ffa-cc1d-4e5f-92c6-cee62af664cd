package com.dep.biguo.bean;

public class CircleDetailBean {
//     "id": 1,
//             "name": "每日学习打卡",
//             "create_time": "2020-09-21 14:58:29",
//             "update_time": null,
//             "is_del": 0,
//             "join_people": 0,
//             "browse_num": 0,
//             "is_hot": 0

    private int id;
    private String name;
    private String create_time;
    private String update_time;
    private int is_del;
    private int join_people;
    private int browse_num;
    private int is_hot;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public int getIs_del() {
        return is_del;
    }

    public void setIs_del(int is_del) {
        this.is_del = is_del;
    }

    public int getJoin_people() {
        return join_people;
    }

    public void setJoin_people(int join_people) {
        this.join_people = join_people;
    }

    public int getBrowse_num() {
        return browse_num;
    }

    public void setBrowse_num(int browse_num) {
        this.browse_num = browse_num;
    }

    public int getIs_hot() {
        return is_hot;
    }

    public void setIs_hot(int is_hot) {
        this.is_hot = is_hot;
    }
}
