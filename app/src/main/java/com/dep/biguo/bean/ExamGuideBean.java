package com.dep.biguo.bean;

import java.util.List;

public class ExamGuideBean extends BaseResponse<ExamGuideBean>{
    private String synopsis;
    private SignUpBean sign_up;
    private ExamSchedulebBean exam_schedule;
    private ScoreQueryBean score_query;
    private AdmissionTicket admission_ticket;
    private SkillCertificate skill_certificate;

    public String getSynopsis() {
        return synopsis;
    }

    public void setSynopsis(String synopsis) {
        this.synopsis = synopsis;
    }

    public SignUpBean getSign_up() {
        return sign_up;
    }

    public void setSign_up(SignUpBean sign_up) {
        this.sign_up = sign_up;
    }

    public ExamSchedulebBean getExam_schedule() {
        return exam_schedule;
    }

    public void setExam_schedule(ExamSchedulebBean exam_schedule) {
        this.exam_schedule = exam_schedule;
    }

    public ScoreQueryBean getScore_query() {
        return score_query;
    }

    public void setScore_query(ScoreQueryBean score_query) {
        this.score_query = score_query;
    }

    public AdmissionTicket getAdmission_ticket() {
        return admission_ticket;
    }

    public void setAdmission_ticket(AdmissionTicket admission_ticket) {
        this.admission_ticket = admission_ticket;
    }

    public SkillCertificate getSkill_certificate() {
        return skill_certificate;
    }

    public void setSkill_certificate(SkillCertificate skill_certificate) {
        this.skill_certificate = skill_certificate;
    }

    public static class  SignUpBean{
       private String registration_date;
       private String registration_name;
       private String registration_url;
       private String registration_condition;

        public String getRegistration_date() {
            return registration_date;
        }

        public void setRegistration_date(String registration_date) {
            this.registration_date = registration_date;
        }

        public String getRegistration_name() {
            return registration_name;
        }

        public void setRegistration_name(String registration_name) {
            this.registration_name = registration_name;
        }

        public String getRegistration_url() {
            return registration_url;
        }

        public void setRegistration_url(String registration_url) {
            this.registration_url = registration_url;
        }

        public String getRegistration_condition() {
            return registration_condition;
        }

        public void setRegistration_condition(String registration_condition) {
            this.registration_condition = registration_condition;
        }
    }

    public static class ExamSchedulebBean{
        private String notes;
        private String exam_type;
        private String exam_kind;
        private List<ExamDateBean> exam_date;

        public String getNotes() {
            return notes;
        }

        public void setNotes(String notes) {
            this.notes = notes;
        }

        public String getExam_type() {
            return exam_type;
        }

        public void setExam_type(String exam_type) {
            this.exam_type = exam_type;
        }

        public String getExam_kind() {
            return exam_kind;
        }

        public void setExam_kind(String exam_kind) {
            this.exam_kind = exam_kind;
        }

        public List<ExamDateBean> getExam_date() {
            return exam_date;
        }

        public void setExam_date(List<ExamDateBean> exam_date) {
            this.exam_date = exam_date;
        }

        public static class ExamDateBean{
            private String date;
            private List<String> data;

            public String getDate() {
                return date;
            }

            public void setDate(String date) {
                this.date = date;
            }

            public List<String> getData() {
                return data;
            }

            public void setData(List<String> data) {
                this.data = data;
            }
        }

    }
    public static class ScoreQueryBean{
        private String results_date;
        private String results_name;
        private String results_url;
        private String pass_mark;
        private String results_tips;

        public String getResults_date() {
            return results_date;
        }

        public void setResults_date(String results_date) {
            this.results_date = results_date;
        }

        public String getResults_name() {
            return results_name;
        }

        public void setResults_name(String results_name) {
            this.results_name = results_name;
        }

        public String getResults_url() {
            return results_url;
        }

        public void setResults_url(String results_url) {
            this.results_url = results_url;
        }

        public String getPass_mark() {
            return pass_mark;
        }

        public void setPass_mark(String pass_mark) {
            this.pass_mark = pass_mark;
        }

        public String getResults_tips() {
            return results_tips;
        }

        public void setResults_tips(String results_tips) {
            this.results_tips = results_tips;
        }
    }

    public static class AdmissionTicket{
        private String printing_date;
        private String printing_name;
        private String printing_url;
        private String printing_flow;

        public String getPrinting_date() {
            return printing_date;
        }

        public void setPrinting_date(String printing_date) {
            this.printing_date = printing_date;
        }

        public String getPrinting_name() {
            return printing_name;
        }

        public void setPrinting_name(String printing_name) {
            this.printing_name = printing_name;
        }

        public String getPrinting_url() {
            return printing_url;
        }

        public void setPrinting_url(String printing_url) {
            this.printing_url = printing_url;
        }

        public String getPrinting_flow() {
            return printing_flow;
        }

        public void setPrinting_flow(String printing_flow) {
            this.printing_flow = printing_flow;
        }
    }

    public static class SkillCertificate{
        private String test_skills;
        private String test_attention;

        public String getTest_skills() {
            return test_skills;
        }

        public void setTest_skills(String test_skills) {
            this.test_skills = test_skills;
        }

        public String getTest_attention() {
            return test_attention;
        }

        public void setTest_attention(String test_attention) {
            this.test_attention = test_attention;
        }
    }
}
