package com.dep.biguo.bean;

import java.util.List;

public class PaySuccessBean {
    public List<Action> action;
    //public List<Course> course;

    public List<Action> getAction() {
        return action;
    }

    public void setAction(List<Action> action) {
        this.action = action;
    }

    /*public List<Course> getCourse() {
        return course;
    }

    public void setCourse(List<Course> course) {
        this.course = course;
    }*/

    public static class Action {
        private String type;//类型type: xcx=微信群， wx_mp= 官方公众号， skill = 职业技能证
        private String name;//名称
        private String intro;//简介说明
        private String img;//图片
        private String tip_img;
        private String target_url;//链接
        private int enable;
        private int need_login;
        private String xcx_path;
        private String button_text;//按钮的文字

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIntro() {
            return intro;
        }

        public void setIntro(String intro) {
            this.intro = intro;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTip_img() {
            return tip_img;
        }

        public void setTip_img(String tip_img) {
            this.tip_img = tip_img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public int getEnable() {
            return enable;
        }

        public void setEnable(int enable) {
            this.enable = enable;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public String getButton_text() {
            return button_text;
        }

        public void setButton_text(String button_text) {
            this.button_text = button_text;
        }
    }

    public static class Course {
        private int courses_id;//课程ID
        private String name;//课程名称
        private String type;//课程类型
        private String code;//课程代码

        public int getCourses_id() {
            return courses_id;
        }

        public void setCourses_id(int courses_id) {
            this.courses_id = courses_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }
}
