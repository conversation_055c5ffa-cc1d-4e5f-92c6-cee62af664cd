package com.dep.biguo.bean;

import java.util.List;

public class InternetStudyGoodsBean {
    private InternetStudyGoodsBean.Master_graph master_graph;//封面图
    private float price; //兑换价格
    private String title; //页面标题
    private String code;//课程代码
    private String subtitle;//副标题
    private int total_buy_count; //已买人数
    private String purchase_notes; //规则
    private int comment_num;//评价数量
    private List<GroupCommentBean> comment;
    private String share_thumb_image;//分享出去的图片
    private String image;//弹窗的缩略图
    private String describe;//商品详情富文本
    private Info info;//个人信息
    private Style styles;//规则中的样式
    private int is_pay;//是否已支付

    public Master_graph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(Master_graph master_graph) {
        this.master_graph = master_graph;
    }

    public float getPrice() {
        return price;
    }

    public void setPrice(float price) {
        this.price = price;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public int getTotal_buy_count() {
        return total_buy_count;
    }

    public void setTotal_buy_count(int total_buy_count) {
        this.total_buy_count = total_buy_count;
    }

    public String getPurchase_notes() {
        return purchase_notes;
    }

    public void setPurchase_notes(String purchase_notes) {
        this.purchase_notes = purchase_notes;
    }

    public int getComment_num() {
        return comment_num;
    }

    public void setComment_num(int comment_num) {
        this.comment_num = comment_num;
    }

    public List<GroupCommentBean> getComment() {
        return comment;
    }

    public void setComment(List<GroupCommentBean> comment) {
        this.comment = comment;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getShare_thumb_image() {
        return share_thumb_image;
    }

    public void setShare_thumb_image(String share_thumb_image) {
        this.share_thumb_image = share_thumb_image;
    }

    public Info getInfo() {
        return info;
    }

    public void setInfo(Info info) {
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public Style getStyles() {
        return styles;
    }

    public void setStyles(Style styles) {
        this.styles = styles;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public static class Master_graph{
        private String graph_type;//url的类型
        private String url;//图片或视频

        public String getGraph_type() {
            return graph_type;
        }

        public void setGraph_type(String graph_type) {
            this.graph_type = graph_type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public static class Course{
        private String code;
        private String name;
        private String price;
        private int professions_id;
        private int is_buy;
        private int is_bundle;
        private String bundle_price;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public int getProfessions_id() {
            return professions_id;
        }

        public void setProfessions_id(int professions_id) {
            this.professions_id = professions_id;
        }

        public int getIs_buy() {
            return is_buy;
        }

        public void setIs_buy(int is_buy) {
            this.is_buy = is_buy;
        }

        public int getIs_bundle() {
            return is_bundle;
        }

        public void setIs_bundle(int is_bundle) {
            this.is_bundle = is_bundle;
        }

        public String getBundle_price() {
            return bundle_price;
        }

        public void setBundle_price(String bundle_price) {
            this.bundle_price = bundle_price;
        }
    }

    public class Info{
        private String key;
        private String name;
        private String IDCard;
        private String number;
        private int is_buy;
        private int is_input;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIDCard() {
            return IDCard;
        }

        public void setIDCard(String IDCard) {
            this.IDCard = IDCard;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public int getIs_buy() {
            return is_buy;
        }

        public void setIs_buy(int is_buy) {
            this.is_buy = is_buy;
        }

        public int getIs_input() {
            return is_input;
        }

        public void setIs_input(int is_input) {
            this.is_input = is_input;
        }
    }

    public static class Style{
        private String[] rule_red;//需要标红的字符串
        private String[] under_line;//需要加下划线的字符串
        private String[] link;//需要加点击事件的字符串

        public String[] getRule_red() {
            return rule_red;
        }

        public void setRule_red(String[] rule_red) {
            this.rule_red = rule_red;
        }

        public String[] getLink() {
            return link;
        }

        public void setLink(String[] link) {
            this.link = link;
        }

        public String[] getUnder_line() {
            return under_line;
        }

        public void setUnder_line(String[] under_line) {
            this.under_line = under_line;
        }
    }
}
