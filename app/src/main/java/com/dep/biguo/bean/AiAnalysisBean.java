package com.dep.biguo.bean;

public class AiAnalysisBean {
    private OutPut output;
    private String question;
    private int free_times;
    private int over_times;
    private int result_code;
    private int status;

    public OutPut getOutput() {
        return output;
    }

    public void setOutput(OutPut output) {
        this.output = output;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFree_times() {
        return free_times;
    }

    public void setFree_times(int free_times) {
        this.free_times = free_times;
    }

    public int getOver_times() {
        return over_times;
    }

    public void setOver_times(int over_times) {
        this.over_times = over_times;
    }

    public int getResult_code() {
        return result_code;
    }

    public void setResult_code(int result_code) {
        this.result_code = result_code;
    }

    public static class OutPut {
        private String session_id;
        private String finish_reason;
        private String text;

        public String getSession_id() {
            return session_id;
        }

        public void setSession_id(String session_id) {
            this.session_id = session_id;
        }

        public String getFinish_reason() {
            return finish_reason;
        }

        public void setFinish_reason(String finish_reason) {
            this.finish_reason = finish_reason;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

    }
}
