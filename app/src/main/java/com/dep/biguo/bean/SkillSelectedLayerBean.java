package com.dep.biguo.bean;

import java.util.List;

public class SkillSelectedLayerBean {
    private int layer_id;//层级id
    private String layer_name;//层级名称
    private List<Professions> professions;

    public int getLayer_id() {
        return layer_id;
    }

    public void setLayer_id(int layer_id) {
        this.layer_id = layer_id;
    }

    public String getLayer_name() {
        return layer_name;
    }

    public void setLayer_name(String layer_name) {
        this.layer_name = layer_name;
    }

    public List<Professions> getProfessions() {
        return professions;
    }

    public void setProfessions(List<Professions> professions) {
        this.professions = professions;
    }

    public class Professions{
        private int professions_id;//专业id
        private String professions_name;//专业名称
        private List<Courses> courses;

        public int getProfessions_id() {
            return professions_id;
        }

        public void setProfessions_id(int professions_id) {
            this.professions_id = professions_id;
        }

        public String getProfessions_name() {
            return professions_name;
        }

        public void setProfessions_name(String professions_name) {
            this.professions_name = professions_name;
        }

        public List<Courses> getCourses() {
            return courses;
        }

        public void setCourses(List<Courses> courses) {
            this.courses = courses;
        }
    }

    public class Courses{
        private int courses_id;//专业id
        private String code;//专业名称
        private String courses_name;//课程名称

        public int getCourses_id() {
            return courses_id;
        }

        public void setCourses_id(int courses_id) {
            this.courses_id = courses_id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getCourses_name() {
            return courses_name;
        }

        public void setCourses_name(String courses_name) {
            this.courses_name = courses_name;
        }
    }
}
