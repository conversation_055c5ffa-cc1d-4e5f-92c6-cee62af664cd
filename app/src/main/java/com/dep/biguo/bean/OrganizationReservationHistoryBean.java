package com.dep.biguo.bean;

public class OrganizationReservationHistoryBean {
    private int reserve_id;//预约id
    private int institutions_id;//机构id
    private String institutions_name;//机构名称
    private int activities_id;//活动id
    private String course_name;//课程名称
    private String reserve_date;//预约日期
    private String reserve_time_period;
    private String course_start_time;
    private int status;
    private String seat_number;
    private String reserve_duration;
    private String reserve_way;

    public int getReserve_id() {
        return reserve_id;
    }

    public void setReserve_id(int reserve_id) {
        this.reserve_id = reserve_id;
    }

    public int getInstitutions_id() {
        return institutions_id;
    }

    public void setInstitutions_id(int institutions_id) {
        this.institutions_id = institutions_id;
    }

    public String getInstitutions_name() {
        return institutions_name;
    }

    public void setInstitutions_name(String institutions_name) {
        this.institutions_name = institutions_name;
    }

    public int getActivities_id() {
        return activities_id;
    }

    public void setActivities_id(int activities_id) {
        this.activities_id = activities_id;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getReserve_date() {
        return reserve_date;
    }

    public void setReserve_date(String reserve_date) {
        this.reserve_date = reserve_date;
    }

    public String getReserve_time_period() {
        return reserve_time_period;
    }

    public void setReserve_time_period(String reserve_time_period) {
        this.reserve_time_period = reserve_time_period;
    }

    public String getCourse_start_time() {
        return course_start_time;
    }

    public void setCourse_start_time(String course_start_time) {
        this.course_start_time = course_start_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSeat_number() {
        return seat_number;
    }

    public void setSeat_number(String seat_number) {
        this.seat_number = seat_number;
    }

    public String getReserve_duration() {
        return reserve_duration;
    }

    public void setReserve_duration(String reserve_duration) {
        this.reserve_duration = reserve_duration;
    }

    public String getReserve_way() {
        return reserve_way;
    }

    public void setReserve_way(String reserve_way) {
        this.reserve_way = reserve_way;
    }
}
