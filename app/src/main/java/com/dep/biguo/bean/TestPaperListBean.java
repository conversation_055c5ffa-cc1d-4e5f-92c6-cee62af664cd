package com.dep.biguo.bean;

public class TestPaperListBean {
    private int id;//试卷id
    private String name;//类型名字
    private String code;//试卷ID
    private String course_code;//课程编码
    private int person_count_answered;//答题人数
    private int total_nums; //总题数
    private int cal_nums;//可答题数
    private int user_answered_num;//用户已做题数
    private int minutes;//考试分钟数
    private int version;//最新版本号
    private int disable_cache;//是否暂不使用数据库缓存
    private long record_expire_time;//有效期

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getPerson_count_answered() {
        return person_count_answered;
    }

    public void setPerson_count_answered(int person_count_answered) {
        this.person_count_answered = person_count_answered;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public int getCal_nums() {
        return cal_nums;
    }

    public void setCal_nums(int cal_nums) {
        this.cal_nums = cal_nums;
    }

    public int getUser_answered_num() {
        return user_answered_num;
    }

    public void setUser_answered_num(int user_answered_num) {
        this.user_answered_num = user_answered_num;
    }

    public int getMinutes() {
        return minutes;
    }

    public void setMinutes(int minutes) {
        this.minutes = minutes;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getDisable_cache() {
        return disable_cache;
    }

    public void setDisable_cache(int disable_cache) {
        this.disable_cache = disable_cache;
    }

    public String getCourse_code() {
        return course_code;
    }

    public void setCourse_code(String course_code) {
        this.course_code = course_code;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }
}
