package com.dep.biguo.bean;

public class IconBean {
    private String type;//类型，根据类型设置跳转页面
    private String name;//文字
    private String img;//图片
    private String xcx_path;//小程序的路径
    private String target_url;//跳转的链接
    private int enable;//是否可用
    private String tip_img;//角标
    private int need_login;//是否需要先登录才能跳转小程序
    private int is_buy;
    private int is_enable;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getXcx_path() {
        return xcx_path;
    }

    public void setXcx_path(String xcx_path) {
        this.xcx_path = xcx_path;
    }

    public String getTarget_url() {
        return target_url;
    }

    public void setTarget_url(String target_url) {
        this.target_url = target_url;
    }

    public int getEnable() {
        return enable;
    }

    public void setEnable(int enable) {
        this.enable = enable;
    }

    public String getTip_img() {
        return tip_img;
    }

    public void setTip_img(String tip_img) {
        this.tip_img = tip_img;
    }

    public int getNeed_login() {
        return need_login;
    }

    public void setNeed_login(int need_login) {
        this.need_login = need_login;
    }

    public int getIs_buy() {
        return is_buy;
    }

    public void setIs_buy(int is_buy) {
        this.is_buy = is_buy;
    }

    public int getIs_enable() {
        return is_enable;
    }

    public void setIs_enable(int is_enable) {
        this.is_enable = is_enable;
    }
}
