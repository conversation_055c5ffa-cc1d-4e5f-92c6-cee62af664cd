package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.dep.biguo.mvp.ui.adapter.VideoChapterAdapter;

import java.util.ArrayList;
import java.util.List;

public class VideoDetailBean {

    private int id;
    private String code;
    private String video_courses_name;
    private String img;
    private String new_img;
    private String price;
    private String member_price;
    private int product_id;
    private int skill_id;
    private int video_type;
    private String type;
    private int is_pay;
    private List<ChapterBean> list;
    private String learn_content;
    private String new_view_id;////最新观看id，章节id_小节id

    public String getNew_img() {
        return new_img;
    }

    public void setNew_img(String new_img) {
        this.new_img = new_img;
    }

    public String getLearn_content() {
        return learn_content;
    }

    public void setLearn_content(String learn_content) {
        this.learn_content = learn_content;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVideo_courses_name() {
        return video_courses_name;
    }

    public void setVideo_courses_name(String video_courses_name) {
        this.video_courses_name = video_courses_name;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public List<ChapterBean> getList() {
        return list;
    }

    public void setList(List<ChapterBean> list) {
        this.list = list;
    }

    public int getVideo_type() {
        return video_type;
    }

    public void setVideo_type(int video_type) {
        this.video_type = video_type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNew_view_id() {
        return new_view_id;
    }

    public void setNew_view_id(String new_view_id) {
        this.new_view_id = new_view_id;
    }

    public static class ChapterBean extends AbstractExpandableItem<ChapterBean.VideoBean> implements MultiItemEntity, Parcelable {

        private String chapter_id;
        private String chapter_name;
        private List<VideoBean> video;

        public String getChapter_id() {
            return chapter_id;
        }

        public void setChapter_id(String chapter_id) {
            this.chapter_id = chapter_id;
        }

        public String getChapter_name() {
            return chapter_name;
        }

        public void setChapter_name(String chapter_name) {
            this.chapter_name = chapter_name;
        }

        public List<VideoBean> getVideo() {
            return video;
        }

        public void setVideo(List<VideoBean> video) {
            this.video = video;
        }



        @Override
        public int getLevel() {
            return 1;
        }

        @Override
        public int getItemType() {
            return VideoChapterAdapter.HEAD;
        }

        public static class VideoBean implements MultiItemEntity, Parcelable {
            /**
             * video_name : 绪论
             * room_id : 5B7B985CE15006F4
             * live_id : 5B7B985CE15006F4
             * playback_id : 41A2A48491EC9CF2
             * chapter_id : 217
             * chapter_name : 绪论
             * video_aly_id : 阿里云视频播放 b90e0e7311a64047b240a47891570444
             * teacherName : 张老师
             */

            private String video_name;
            private String room_id;
            private String live_id;
            private String playback_id;
            private String chapter_id;
            private String chapter_name;
            private String video_id;
            private String video_aly_id;
            private String video_url;
            private String desc;
            private String teacherName;
            private String video_price;

            public String getVideo_price() {
                return video_price;
            }

            public void setVideo_price(String video_price) {
                this.video_price = video_price;
            }

            public String getTeacherName() {
                return teacherName;
            }

            public void setTeacherName(String teacherName) {
                this.teacherName = teacherName;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public String getVideo_id() {
                return video_id;
            }

            public void setVideo_id(String video_id) {
                this.video_id = video_id;
            }

            public String getVideo_aly_id() {
                return video_aly_id;
            }

            public void setVideo_aly_id(String video_aly_id) {
                this.video_aly_id = video_aly_id;
            }

            public String getVideo_url() {
                return video_url;
            }

            public void setVideo_url(String video_url) {
                this.video_url = video_url;
            }

            public String getVideo_name() {
                return video_name;
            }

            public void setVideo_name(String video_name) {
                this.video_name = video_name;
            }

            public String getRoom_id() {
                return room_id;
            }

            public void setRoom_id(String room_id) {
                this.room_id = room_id;
            }

            public String getLive_id() {
                return live_id;
            }

            public void setLive_id(String live_id) {
                this.live_id = live_id;
            }

            public String getPlayback_id() {
                return playback_id;
            }

            public void setPlayback_id(String playback_id) {
                this.playback_id = playback_id;
            }

            public String getChapter_id() {
                return chapter_id;
            }

            public void setChapter_id(String chapter_id) {
                this.chapter_id = chapter_id;
            }

            public String getChapter_name() {
                return chapter_name;
            }

            public void setChapter_name(String chapter_name) {
                this.chapter_name = chapter_name;
            }

            @Override
            public int getItemType() {
                return 0;
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.video_name);
                dest.writeString(this.room_id);
                dest.writeString(this.live_id);
                dest.writeString(this.playback_id);
                dest.writeString(this.chapter_id);
                dest.writeString(this.chapter_name);
                dest.writeString(this.video_id);
                dest.writeString(this.video_aly_id);
                dest.writeString(this.desc);
                dest.writeString(this.teacherName);
                dest.writeString(this.video_price);
            }

            public VideoBean() {
            }

            protected VideoBean(Parcel in) {
                this.video_name = in.readString();
                this.room_id = in.readString();
                this.live_id = in.readString();
                this.playback_id = in.readString();
                this.chapter_id = in.readString();
                this.chapter_name = in.readString();
                this.video_id = in.readString();
                this.video_aly_id = in.readString();
                this.desc = in.readString();
                this.teacherName = in.readString();
                this.video_price = in.readString();
            }

            public static final Creator<VideoBean> CREATOR = new Creator<VideoBean>() {
                @Override
                public VideoBean createFromParcel(Parcel source) {
                    return new VideoBean(source);
                }

                @Override
                public VideoBean[] newArray(int size) {
                    return new VideoBean[size];
                }
            };
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.chapter_name);
            dest.writeList(this.video);
        }

        public ChapterBean() {
        }

        protected ChapterBean(Parcel in) {
            this.chapter_name = in.readString();
            this.video = new ArrayList<VideoBean>();
            in.readList(this.video, VideoBean.class.getClassLoader());
        }

        public static final Creator<ChapterBean> CREATOR = new Creator<ChapterBean>() {
            @Override
            public ChapterBean createFromParcel(Parcel source) {
                return new ChapterBean(source);
            }

            @Override
            public ChapterBean[] newArray(int size) {
                return new ChapterBean[size];
            }
        };
    }

}
