package com.dep.biguo.bean;

public class SecretBean {

    /**
     * code : 00292
     * name : 市政学
     * price : 198.00
     * has_buy : true
     */

    private String code;
    private String name;
    private String price;
    private boolean has_buy;
    private int is_pay;
    private String A;
    private int count;
    private String pay_time;
    private int status;
    private String email;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getPay_time() {
        return pay_time;
    }

    public void setPay_time(String pay_time) {
        this.pay_time = pay_time;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public String getA() {
        return A;
    }

    public void setA(String a) {
        A = a;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public boolean isHas_buy() {
        return has_buy;
    }

    public void setHas_buy(boolean has_buy) {
        this.has_buy = has_buy;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
