package com.dep.biguo.bean;

import java.util.List;

public class GroupJoinBean {
    private List<GroupBean> join_list;//直接拼成列表
    private List<GroupBean> open_list;//发起拼团列表
    private int is_newcomers;//是否是新用户

    public List<GroupBean> getJoin_list() {
        return join_list;
    }

    public void setJoin_list(List<GroupBean> join_list) {
        this.join_list = join_list;
    }

    public List<GroupBean> getOpen_list() {
        return open_list;
    }

    public void setOpen_list(List<GroupBean> open_list) {
        this.open_list = open_list;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public static class GroupBean{
        private long sec;//倒计时秒数
        private int count;//拼团人数
        private String group_id;//拼团id
        private String type;//商品类型
        private String code;//课程编码
        private String name;//课程名称
        private int status;//0去参团1拼团中2已开通
        private int product_id;//必过视频id
        private String price;//原价
        private int is_newcomers;//是否是新人
        private String newcomers_price;//新人活动价
        private String member_price;//原价的折扣价
        private String group_price;//拼团价
        private String member_group_price;//拼团折扣价
        private int source_type;//1时刻套餐2必过套餐
        private int cert_type;
        private int skill_id;//技能证ID
        private String share_text;
        private String share_desc;
        private String group_share_text;
        private String group_share_desc;
        private List<GroupUserBean> users_info;
        private int is_pay_vip;//是否支付了VIP题库，0否，1是
        private GoodsUpgradeBean replenish_goods;

        public int getIs_pay_vip() {
            return is_pay_vip;
        }

        public void setIs_pay_vip(int is_pay_vip) {
            this.is_pay_vip = is_pay_vip;
        }

        public int getSkill_id() {
            return skill_id;
        }

        public void setSkill_id(int skill_id) {
            this.skill_id = skill_id;
        }

        public String getShare_text() {
            return share_text;
        }

        public void setShare_text(String share_text) {
            this.share_text = share_text;
        }

        public String getShare_desc() {
            return share_desc;
        }

        public void setShare_desc(String share_desc) {
            this.share_desc = share_desc;
        }

        public String getGroup_share_desc() {
            return group_share_desc;
        }

        public void setGroup_share_desc(String group_share_desc) {
            this.group_share_desc = group_share_desc;
        }

        public String getGroup_share_text() {
            return group_share_text;
        }

        public void setGroup_share_text(String group_share_text) {
            this.group_share_text = group_share_text;
        }

        public int getCert_type() {
            return cert_type;
        }

        public void setCert_type(int cert_type) {
            this.cert_type = cert_type;
        }

        public long getSec() {
            return sec;
        }

        public void setSec(long sec) {
            this.sec = sec;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public int getIs_newcomers() {
            return is_newcomers;
        }

        public void setIs_newcomers(int is_newcomers) {
            this.is_newcomers = is_newcomers;
        }

        public String getNewcomers_price() {
            return newcomers_price;
        }

        public void setNewcomers_price(String newcomers_price) {
            this.newcomers_price = newcomers_price;
        }

        public String getMember_price() {
            return member_price;
        }

        public void setMember_price(String member_price) {
            this.member_price = member_price;
        }

        public String getGroup_price() {
            return group_price;
        }

        public void setGroup_price(String group_price) {
            this.group_price = group_price;
        }

        public String getMember_group_price() {
            return member_group_price;
        }

        public void setMember_group_price(String member_group_price) {
            this.member_group_price = member_group_price;
        }

        public List<GroupUserBean> getUsers_info() {
            return users_info;
        }

        public void setUsers_info(List<GroupUserBean> users_info) {
            this.users_info = users_info;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public GoodsUpgradeBean getReplenish_goods() {
            return replenish_goods;
        }

        public void setReplenish_goods(GoodsUpgradeBean replenish_goods) {
            this.replenish_goods = replenish_goods;
        }
    }
}
