package com.dep.biguo.bean;

import java.util.List;

public class DayCardV3SignCourseBean {
    public static String FIRST_ENROLL = "join_first";
    public static String JOIN_SUGGEST = "join_suggest";

    private String redirect;//join_first 首次报名join_manage报考管理 join_suggest推荐报考
    private List<SelectCourseBean> course_list;
    private String code; //正常情况下返回课程编码
    private String name;//课程名称

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public List<SelectCourseBean> getCourse_list() {
        return course_list;
    }

    public void setCourse_list(List<SelectCourseBean> course_list) {
        this.course_list = course_list;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static class SelectCourseBean{
        private int courses_id;
        private String code;
        private String name;

        public int getCourses_id() {
            return courses_id;
        }

        public void setCourses_id(int courses_id) {
            this.courses_id = courses_id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
