package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/3
 * @Description:
 */
public class TopicBean extends BaseResponse<List<TopicBean>> {

    private int topic_type;//题型
    private String name;//题库名称
    private int total_nums;//总题数
    private String code;//课程编码
    private long version;//0 //题库版本号
    private int last_id;//最后一次作答的题目的ID
    private int disable_cache;//是否暂不使用数据库缓存
    private long record_expire_time;//有效期

    /**成考、教师资格证 VIP题库 **/
    private int is_open;//是否开通
    private int count;//开通数量
    private int open_count;
    private String vip_price;//该字段已弃用，但后台未去掉，因此暂时保留
    private String vip_share_price;//开通价格

    public int getIs_open() {
        return is_open;
    }

    public void setIs_open(int is_open) {
        this.is_open = is_open;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getOpen_count() {
        return open_count;
    }

    public void setOpen_count(int open_count) {
        this.open_count = open_count;
    }

    public String getVip_price() {
        return vip_price;
    }

    public void setVip_price(String vip_price) {
        this.vip_price = vip_price;
    }

    public String getVip_share_price() {
        return vip_share_price;
    }

    public void setVip_share_price(String vip_share_price) {
        this.vip_share_price = vip_share_price;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public int getDisable_cache() {
        return disable_cache;
    }

    public void setDisable_cache(int disable_cache) {
        this.disable_cache = disable_cache;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }
}
