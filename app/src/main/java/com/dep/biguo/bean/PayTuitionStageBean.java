package com.dep.biguo.bean;

import java.util.List;

public class PayTuitionStageBean {
    private int form_id;//形式id
    private List<PaymentOptions> payment_options;//缴纳选项
    private String name;//名称
    private int is_can_staging;//是否分期  0：否 1：是
    private String pop_protocol_url;//协议或校规
    private String pop_protocol_title;//协议或校规名称


    public int getForm_id() {
        return form_id;
    }

    public void setForm_id(int form_id) {
        this.form_id = form_id;
    }

    public List<PaymentOptions> getPayment_options() {
        return payment_options;
    }

    public void setPayment_options(List<PaymentOptions> payment_options) {
        this.payment_options = payment_options;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIs_can_staging() {
        return is_can_staging;
    }

    public void setIs_can_staging(int is_can_staging) {
        this.is_can_staging = is_can_staging;
    }

    public String getPop_protocol_url() {
        return pop_protocol_url;
    }

    public void setPop_protocol_url(String pop_protocol_url) {
        this.pop_protocol_url = pop_protocol_url;
    }

    public String getPop_protocol_title() {
        return pop_protocol_title;
    }

    public void setPop_protocol_title(String pop_protocol_title) {
        this.pop_protocol_title = pop_protocol_title;
    }

    public static class PaymentOptions{
        private int type;//缴纳类型 1：全款 2：分期缴纳 3：分次缴纳
        private int frequency;//缴纳次数

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getFrequency() {
            return frequency;
        }

        public void setFrequency(int frequency) {
            this.frequency = frequency;
        }
    }
}
