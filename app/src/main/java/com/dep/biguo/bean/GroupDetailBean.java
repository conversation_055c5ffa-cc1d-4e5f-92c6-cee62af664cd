package com.dep.biguo.bean;

import java.util.List;

public class GroupDetailBean {
    private int is_newcomers;//1是新用户，2不是新用户
    private String pay_price;//实际支付的价格
    private String newcomers_price;//新人价格
    private String group_id;//拼团id
    private String code;//课程代码
    private String type;//拼团类型
    private String name;//课程名称
    private String image;//封面图片
    private String price;//原价
    private String group_price;//拼团价
    private String member_group_price;//拼团折扣价
    private int status;//拼团状态 1拼团中 2拼团失败 3拼团成功
    private int sec;//剩余时间
    private int count;//拼团成员数
    private String intro;//拼团介绍
    private List<String> rules;//拼团规则
    private List<GroupUserBean> users_info;//拼团成员信息
    private int is_head;//是否是团长
    private int is_fellow;//该团是否是自己加入的团
    private List<String> succeeded;//同一科目已成团的成员头像
    private String share_text;//商品分享链接的标题
    private String share_desc;//商品分享链接的副标题
    private String group_share_text;//拼团分享链接的标题
    private String group_share_desc;//拼团分享链接的副标题
    private int result_time;//拼团所花的时间
    private int product_id;//必过视频那边的商品ID
    private int source_type;//1时刻套餐2必过套餐
    private int skill_id;//技能证ID
    private int is_pay_vip;//是否支付了VIP题库，0否，1是
    private long version;//题库的版本号
    private long record_expire_time;//有效期
    private GoodsUpgradeBean replenish_goods;

    public int getIs_pay_vip() {
        return is_pay_vip;
    }

    public void setIs_pay_vip(int is_pay_vip) {
        this.is_pay_vip = is_pay_vip;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public String getPay_price() {
        return pay_price;
    }

    public void setPay_price(String pay_price) {
        this.pay_price = pay_price;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSec() {
        return sec;
    }

    public void setSec(int sec) {
        this.sec = sec;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public List<String> getRules() {
        return rules;
    }

    public void setRules(List<String> rules) {
        this.rules = rules;
    }

    public List<GroupUserBean> getUsers_info() {
        return users_info;
    }

    public void setUsers_info(List<GroupUserBean> users_info) {
        this.users_info = users_info;
    }

    public int getIs_head() {
        return is_head;
    }

    public void setIs_head(int is_head) {
        this.is_head = is_head;
    }

    public int getIs_fellow() {
        return is_fellow;
    }

    public void setIs_fellow(int is_fellow) {
        this.is_fellow = is_fellow;
    }

    public List<String> getSucceeded() {
        return succeeded;
    }

    public void setSucceeded(List<String> succeeded) {
        this.succeeded = succeeded;
    }

    public String getShare_text() {
        return share_text;
    }

    public void setShare_text(String share_text) {
        this.share_text = share_text;
    }

    public String getShare_desc() {
        return share_desc;
    }

    public void setShare_desc(String share_desc) {
        this.share_desc = share_desc;
    }

    public String getGroup_share_text() {
        return group_share_text;
    }

    public void setGroup_share_text(String group_share_text) {
        this.group_share_text = group_share_text;
    }

    public String getGroup_share_desc() {
        return group_share_desc;
    }

    public void setGroup_share_desc(String group_share_desc) {
        this.group_share_desc = group_share_desc;
    }

    public int getResult_time() {
        return result_time;
    }

    public void setResult_time(int result_time) {
        this.result_time = result_time;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }

    public GoodsUpgradeBean getReplenish_goods() {
        return replenish_goods;
    }

    public void setReplenish_goods(GoodsUpgradeBean replenish_goods) {
        this.replenish_goods = replenish_goods;
    }
}
