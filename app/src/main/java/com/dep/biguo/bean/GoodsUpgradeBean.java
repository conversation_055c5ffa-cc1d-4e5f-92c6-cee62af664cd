package com.dep.biguo.bean;

import java.util.List;

public class GoodsUpgradeBean {
    private String price_sum;
    private LowPrice min;
    private LowPrice min_group;
    private String min_text;
    private List<GoodsInfos> goods_infos;

    public String getPrice_sum() {
        return price_sum;
    }

    public void setPrice_sum(String price_sum) {
        this.price_sum = price_sum;
    }

    public LowPrice getMin() {
        return min;
    }

    public void setMin(LowPrice min) {
        this.min = min;
    }

    public LowPrice getMin_group() {
        return min_group;
    }

    public void setMin_group(LowPrice min_group) {
        this.min_group = min_group;
    }

    public String getMin_text() {
        return min_text;
    }

    public void setMin_text(String min_text) {
        this.min_text = min_text;
    }

    public List<GoodsInfos> getGoods_infos() {
        return goods_infos;
    }

    public void setGoods_infos(List<GoodsInfos> goods_infos) {
        this.goods_infos = goods_infos;
    }

    public class GoodsInfos{
        private String type;
        private String price;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }
    }

    public class LowPrice{
        private int is_apply;
        private String min_price;
        private String min_text;

        public int getIs_apply() {
            return is_apply;
        }

        public void setIs_apply(int is_apply) {
            this.is_apply = is_apply;
        }

        public String getMin_price() {
            return min_price;
        }

        public void setMin_price(String min_price) {
            this.min_price = min_price;
        }

        public String getMin_text() {
            return min_text;
        }

        public void setMin_text(String min_text) {
            this.min_text = min_text;
        }
    }
}
