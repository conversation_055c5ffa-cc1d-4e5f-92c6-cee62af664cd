package com.dep.biguo.bean;

public class DiscountBean {
    private int id;//领取记录id
    private int users_id; //用户id
    private String coupon_no; //优惠券编号
    private String coupon_name; //优惠券名称
    private String coupon_desc; //优惠券描述
    private int type; //优惠券类型 1抵扣券2满减券
    private int category; //适用品类
    private String products; //适用商品类型
    private String price; //优惠券面额
    private String full_minus_price; //满减价格
    private String receive_time; //领取时间
    private int status; //状态 1未使用 0未生效 2被占用 3已使用 4已回收 5 已过期
    private String valid; //有效期文字
    private String type_name; //类型名称
    private int left_time; //剩余有效时间（秒） 优惠券生效时返回值

    private boolean isOpenDesc;//是否已展开优惠券描述，仅供本地使用

    public DiscountBean(int id, int type) {
        this.id = id;
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getCoupon_no() {
        return coupon_no;
    }

    public void setCoupon_no(String coupon_no) {
        this.coupon_no = coupon_no;
    }

    public String getCoupon_name() {
        return coupon_name;
    }

    public void setCoupon_name(String coupon_name) {
        this.coupon_name = coupon_name;
    }

    public String getCoupon_desc() {
        return coupon_desc;
    }

    public void setCoupon_desc(String coupon_desc) {
        this.coupon_desc = coupon_desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getProducts() {
        return products;
    }

    public void setProducts(String products) {
        this.products = products;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getFull_minus_price() {
        return full_minus_price;
    }

    public void setFull_minus_price(String full_minus_price) {
        this.full_minus_price = full_minus_price;
    }

    public String getReceive_time() {
        return receive_time;
    }

    public void setReceive_time(String receive_time) {
        this.receive_time = receive_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public String getType_name() {
        return type_name;
    }

    public void setType_name(String type_name) {
        this.type_name = type_name;
    }

    public boolean isOpenDesc() {
        return isOpenDesc;
    }

    public void setOpenDesc(boolean openDesc) {
        isOpenDesc = openDesc;
    }

    public int getLeft_time() {
        return left_time;
    }

    public void setLeft_time(int left_time) {
        this.left_time = left_time;
    }
}