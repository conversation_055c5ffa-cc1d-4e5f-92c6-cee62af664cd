package com.dep.biguo.bean;

import java.util.List;

public class PracticeRecordBean {
    private int last_id;
    private List<RecordBean> topics;

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public List<RecordBean> getTopics() {
        return topics;
    }

    public void setTopics(List<RecordBean> topics) {
        this.topics = topics;
    }

    public static class RecordBean {

        private int topic_id;
        private int topic_type;
        private int is_correct;
        private String select_answer;
        private String correct_answer;
        private int isCollection;
        private int mainType;

        public RecordBean(int topic_id, int topic_type, int is_correct, String select_answer, String correct_answer, int mainType) {
            this.topic_id = topic_id;
            this.topic_type = topic_type;
            this.is_correct = is_correct;
            this.select_answer = select_answer;
            this.correct_answer = correct_answer;
            this.mainType = mainType;
        }

        public int getTopic_id() {
            return topic_id;
        }

        public void setTopic_id(int topic_id) {
            this.topic_id = topic_id;
        }

        public int getTopic_type() {
            return topic_type;
        }

        public void setTopic_type(int topic_type) {
            this.topic_type = topic_type;
        }

        public int getIs_correct() {
            return is_correct;
        }

        public void setIs_correct(int is_correct) {
            this.is_correct = is_correct;
        }

        public String getSelect_answer() {
            return select_answer;
        }

        public void setSelect_answer(String select_answer) {
            this.select_answer = select_answer;
        }

        public String getCorrect_answer() {
            return correct_answer;
        }

        public void setCorrect_answer(String correct_answer) {
            this.correct_answer = correct_answer;
        }

        public int getMainType() {
            return mainType;
        }

        public void setMainType(int mainType) {
            this.mainType = mainType;
        }

        public int getIsCollection() {
            return isCollection;
        }

        public void setIsCollection(int isCollection) {
            this.isCollection = isCollection;
        }
    }
}
