package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/30
 * @Description:
 */
public class ProfessionBean extends BaseResponse<List<ProfessionBean>> {

    private int id;
    private String code;
    private int layer;
    private String layer_name;
    private String name;
    private int top;
    private String old_name;
    private int adult_professions_id;//成考的专业所属类别，如经管类、文学类
    private String adult_professions_name;//成考的专业所属类别，如经管类、文学类

    public ProfessionBean() {
    }

    public ProfessionBean(int id, String code, int layer, String name) {
        this.id = id;
        this.code = code;
        this.layer = layer;
        this.name = name;
    }

    public String getOld_name() {
        return old_name;
    }

    public void setOld_name(String old_name) {
        this.old_name = old_name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getLayer() {
        return layer;
    }

    public void setLayer(int layer) {
        this.layer = layer;
    }

    public String getLayer_name() {
        return layer_name;
    }

    public void setLayer_name(String layer_name) {
        this.layer_name = layer_name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTop() {
        return top;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public int getAdult_professions_id() {
        return adult_professions_id;
    }

    public void setAdult_professions_id(int adult_professions_id) {
        this.adult_professions_id = adult_professions_id;
    }

    public String getAdult_professions_name() {
        return adult_professions_name;
    }

    public void setAdult_professions_name(String adult_professions_name) {
        this.adult_professions_name = adult_professions_name;
    }
}
