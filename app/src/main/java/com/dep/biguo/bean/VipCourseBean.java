package com.dep.biguo.bean;

import java.util.List;

/**
 * Created by JX on 2018/6/15.
 */

public class VipCourseBean extends BaseResponse<List<VipCourseBean>> {

    /**
     * id : 4
     * name : aaaa
     * is_recommended : 1
     * price : 333.00
     * start_time : 2018-06-07 14:20:47
     * end_time : 2018-06-07 14:20:47
     * teacher : [{"teacher_id":2,"teacher_name":"陈老师","teacher_img":"http://bg.dengming.pro/teacher/0F0nV1NaAA.png"},{"teacher_id":4,"teacher_name":"王XX","teacher_img":"http://bg.dengming.pro/teacher/9lo8GO5mKj.png"}]
     */

    private int id;
    private String name;
    private int is_recommended;
    private String price;
    private String start_time;
    private String end_time;
    private List<TeacherBean> teacher;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIs_recommended() {
        return is_recommended;
    }

    public void setIs_recommended(int is_recommended) {
        this.is_recommended = is_recommended;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public List<TeacherBean> getTeacher() {
        return teacher;
    }

    public void setTeacher(List<TeacherBean> teacher) {
        this.teacher = teacher;
    }
}
