package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.dep.biguo.mvp.ui.adapter.ChapterAdapter;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/4
 * @Description:
 */
public class ChapterExpandBean extends AbstractExpandableItem<ChapterExpandBean.SectionBean> implements MultiItemEntity {

    private int id;
    private String name;
    private String code;
    private String price;
    private int is_show;
    private int total_nums;
    private List<SectionBean> sections;

    @Override
    public int getLevel() {
        return 1;
    }

    @Override
    public int getItemType() {
        return ChapterAdapter.HEAD;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getIs_show() {
        return is_show;
    }

    public void setIs_show(int is_show) {
        this.is_show = is_show;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public List<SectionBean> getSections() {
        return sections;
    }

    public void setSections(List<SectionBean> sections) {
        this.sections = sections;
    }

    public static class SectionBean implements MultiItemEntity {

        private int section_id;//小节id
        private String name;//章节名称
        private String code;//课程编码
        private long version;//题库版本
        private int total_nums;//总题数
        private int disable_cache;//是否暂不使用数据库缓存
        private long record_expire_time;//有效期

        public int getSection_id() {
            return section_id;
        }

        public void setSection_id(int section_id) {
            this.section_id = section_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public long getVersion() {
            return version;
        }

        public void setVersion(long version) {
            this.version = version;
        }

        public int getTotal_nums() {
            return total_nums;
        }

        public void setTotal_nums(int total_nums) {
            this.total_nums = total_nums;
        }

        public int getDisable_cache() {
            return disable_cache;
        }

        public void setDisable_cache(int disable_cache) {
            this.disable_cache = disable_cache;
        }

        public long getRecord_expire_time() {
            return record_expire_time;
        }

        public void setRecord_expire_time(long record_expire_time) {
            this.record_expire_time = record_expire_time;
        }

        @Override
        public int getItemType() {
            return ChapterAdapter.ITEM;
        }
    }

}
