package com.dep.biguo.bean;

import java.util.List;

/**
 * Created by JX on 2018/6/20.
 */

public class VipCourseClassDetailBean {

    private List<VideoBean> video;
    private List<VideoBean> live;

    public List<VideoBean> getVideo() {
        return video;
    }

    public void setVideo(List<VideoBean> video) {
        this.video = video;
    }

    public List<VideoBean> getLive() {
        return live;
    }

    public void setLive(List<VideoBean> live) {
        this.live = live;
    }

    public static class VideoBean {

        private String date;
        private String time;
        private String name;
        private String teacher_name;
        private String courses_name;
        private String video_url;
        private int live_state;
        private String room_id;
        private String live_id;
        private String playback_id;
        private int id;
        private String video_aly_id;


        public String getVideo_aly_id() {
            return video_aly_id;
        }

        public void setVideo_aly_id(String video_aly_id) {
            this.video_aly_id = video_aly_id;
        }

        public String getPlayback_id() {
            return playback_id;
        }

        public void setPlayback_id(String playback_id) {
            this.playback_id = playback_id;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getLive_id() {
            return live_id;
        }

        public void setLive_id(String live_id) {
            this.live_id = live_id;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTeacher_name() {
            return teacher_name;
        }

        public void setTeacher_name(String teacher_name) {
            this.teacher_name = teacher_name;
        }

        public String getCourses_name() {
            return courses_name;
        }

        public void setCourses_name(String courses_name) {
            this.courses_name = courses_name;
        }

        public String getVideo_url() {
            return video_url;
        }

        public void setVideo_url(String video_url) {
            this.video_url = video_url;
        }

        public int getLive_state() {
            return live_state;
        }

        public void setLive_state(int live_state) {
            this.live_state = live_state;
        }

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }
    }
}
