package com.dep.biguo.bean;

public class ProfessionInfo {
    private Profession server;
    private Profession local;

    public Profession getServer() {
        return server;
    }

    public void setServer(Profession server) {
        this.server = server;
    }

    public Profession getLocal() {
        return local;
    }

    public void setLocal(Profession local) {
        this.local = local;
    }

    public static class Profession{
        private int province_id;
        private String province_name;
        private int city_id;
        private String city_code;
        private String city_name;
        private int school_id;
        private String school_name;
        private int layer_id;
        private String layer_name;
        private int professions_id;
        private String profession_code;
        private String professions_name;
        private String profession_old_name;
        private int profession_has_new;

        public int getProvince_id() {
            return province_id;
        }

        public void setProvince_id(int province_id) {
            this.province_id = province_id;
        }

        public String getProvince_name() {
            return province_name;
        }

        public void setProvince_name(String province_name) {
            this.province_name = province_name;
        }

        public int getCity_id() {
            return city_id;
        }

        public void setCity_id(int city_id) {
            this.city_id = city_id;
        }

        public String getCity_code() {
            return city_code;
        }

        public void setCity_code(String city_code) {
            this.city_code = city_code;
        }

        public String getCity_name() {
            return city_name;
        }

        public void setCity_name(String city_name) {
            this.city_name = city_name;
        }

        public int getSchool_id() {
            return school_id;
        }

        public void setSchool_id(int school_id) {
            this.school_id = school_id;
        }

        public String getSchool_name() {
            return school_name;
        }

        public void setSchool_name(String school_name) {
            this.school_name = school_name;
        }

        public int getLayer_id() {
            return layer_id;
        }

        public void setLayer_id(int layer_id) {
            this.layer_id = layer_id;
        }

        public String getLayer_name() {
            return layer_name;
        }

        public void setLayer_name(String layer_name) {
            this.layer_name = layer_name;
        }

        public int getProfessions_id() {
            return professions_id;
        }

        public void setProfessions_id(int professions_id) {
            this.professions_id = professions_id;
        }

        public String getProfession_code() {
            return profession_code;
        }

        public void setProfession_code(String profession_code) {
            this.profession_code = profession_code;
        }

        public String getProfessions_name() {
            return professions_name;
        }

        public void setProfessions_name(String professions_name) {
            this.professions_name = professions_name;
        }

        public String getProfession_old_name() {
            return profession_old_name;
        }

        public void setProfession_old_name(String profession_old_name) {
            this.profession_old_name = profession_old_name;
        }

        public int getProfession_has_new() {
            return profession_has_new;
        }

        public void setProfession_has_new(int profession_has_new) {
            this.profession_has_new = profession_has_new;
        }
    }
}
