package com.dep.biguo.bean;

import java.util.List;

public class UserFragmentBean {
    private int id;//用户id
    private String nickname;//昵称
    private String avatar;//头像
    private int membership;//是否会员 1是0否
    private String fruit_coin;//永久果币
    private String free_fruit_coin;//赠送果币
    private String integral;//积分
    private int answered_questions;//答题数量
    private int new_coupon;//新优惠券数量
    private String cash_num;//奖金数量
    private String remain_days;//距考试剩余日期
    private int correct;//正确率
    private List<String> exam_times;//考试时间
    private String kefu_seller_username;
    private String kefu_password;
    private int isOfflineStudent;//是否是线下学员

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public int getMembership() {
        return membership;
    }

    public void setMembership(int membership) {
        this.membership = membership;
    }

    public String getFruit_coin() {
        return fruit_coin;
    }

    public void setFruit_coin(String fruit_coin) {
        this.fruit_coin = fruit_coin;
    }

    public String getFree_fruit_coin() {
        return free_fruit_coin;
    }

    public void setFree_fruit_coin(String free_fruit_coin) {
        this.free_fruit_coin = free_fruit_coin;
    }

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public int getAnswered_questions() {
        return answered_questions;
    }

    public void setAnswered_questions(int answered_questions) {
        this.answered_questions = answered_questions;
    }

    public int getNew_coupon() {
        return new_coupon;
    }

    public void setNew_coupon(int new_coupon) {
        this.new_coupon = new_coupon;
    }

    public String getCash_num() {
        return cash_num;
    }

    public void setCash_num(String cash_num) {
        this.cash_num = cash_num;
    }

    public String getRemain_days() {
        return remain_days;
    }

    public void setRemain_days(String remain_days) {
        this.remain_days = remain_days;
    }

    public int getCorrect() {
        return correct;
    }

    public void setCorrect(int correct) {
        this.correct = correct;
    }

    public List<String> getExam_times() {
        return exam_times;
    }

    public void setExam_times(List<String> exam_times) {
        this.exam_times = exam_times;
    }

    public String getKefu_seller_username() {
        return kefu_seller_username;
    }

    public void setKefu_seller_username(String kefu_seller_username) {
        this.kefu_seller_username = kefu_seller_username;
    }

    public String getKefu_password() {
        return kefu_password;
    }

    public void setKefu_password(String kefu_password) {
        this.kefu_password = kefu_password;
    }

    public int getIsOfflineStudent() {
        return isOfflineStudent;
    }

    public void setIsOfflineStudent(int isOfflineStudent) {
        this.isOfflineStudent = isOfflineStudent;
    }
}
