package com.dep.biguo.bean;

import java.util.List;

public class CounsellingZixuanBean {
    private int total_buy_count; //已买人数
    private String image;
    private String title; //页面标题
    private String subtitle; //页面标题
    private String price; //原价
    private String first_pay_price;//首缴金额
    private String describe;//商品详情富文本
    private String purchase_notes; //规则
    private CounsellingZixuanBean.Master_graph master_graph;//封面图
    private CounsellingZixuanBean.Style styles;//规则中的样式
    private int cert_project;
    private List<CounsellingZixuanBean.PaymentOptions> payment_options;//缴纳选项
    private int is_can_staging;//是否分期  0：否 1：是

    public int getTotal_buy_count() {
        return total_buy_count;
    }

    public void setTotal_buy_count(int total_buy_count) {
        this.total_buy_count = total_buy_count;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getFirst_pay_price() {
        return first_pay_price;
    }

    public void setFirst_pay_price(String first_pay_price) {
        this.first_pay_price = first_pay_price;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getPurchase_notes() {
        return purchase_notes;
    }

    public void setPurchase_notes(String purchase_notes) {
        this.purchase_notes = purchase_notes;
    }

    public CounsellingZixuanBean.Master_graph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(CounsellingZixuanBean.Master_graph master_graph) {
        this.master_graph = master_graph;
    }

    public CounsellingZixuanBean.Style getStyles() {
        return styles;
    }

    public void setStyles(CounsellingZixuanBean.Style styles) {
        this.styles = styles;
    }

    public int getCert_project() {
        return cert_project;
    }

    public void setCert_project(int cert_project) {
        this.cert_project = cert_project;
    }

    public List<CounsellingZixuanBean.PaymentOptions> getPayment_options() {
        return payment_options;
    }

    public void setPayment_options(List<CounsellingZixuanBean.PaymentOptions> payment_options) {
        this.payment_options = payment_options;
    }

    public int getIs_can_staging() {
        return is_can_staging;
    }

    public void setIs_can_staging(int is_can_staging) {
        this.is_can_staging = is_can_staging;
    }

    public static class Master_graph{
        private String graph_type;//url的类型
        private String url;//图片或视频

        public String getGraph_type() {
            return graph_type;
        }

        public void setGraph_type(String graph_type) {
            this.graph_type = graph_type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public static class Style{
        private String[] rule_red;//需要标红的字符串
        private String[] under_line;//需要加下划线的字符串
        private String[] link;//需要加点击事件的字符串

        public String[] getRule_red() {
            return rule_red;
        }

        public void setRule_red(String[] rule_red) {
            this.rule_red = rule_red;
        }

        public String[] getLink() {
            return link;
        }

        public void setLink(String[] link) {
            this.link = link;
        }

        public String[] getUnder_line() {
            return under_line;
        }

        public void setUnder_line(String[] under_line) {
            this.under_line = under_line;
        }
    }


    public static class PaymentOptions{
        private int type;//缴纳类型 1：全款 2：分期缴纳 3：分次缴纳
        private int frequency;//缴纳次数

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getFrequency() {
            return frequency;
        }

        public void setFrequency(int frequency) {
            this.frequency = frequency;
        }
    }
}
