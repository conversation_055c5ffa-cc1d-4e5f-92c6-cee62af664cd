package com.dep.biguo.bean;

import java.util.List;

public class ZkNewHomeBean extends BaseResponse<ZkNewHomeBean>{
    private List<Banner> swipers;//顶部banner图
    private List<Banner> top_banner;//中间的banner图
    private List<CourseBean.CourseItemBean> courses;//
    private List<IconBean> icon1;//金刚区图标
    private List<IconBean> icon2;//拼团图标
    private List<IconBean> icon3;//拼团图标
    private List<Recommend> recommend_products;//推荐列表
    private List<ArticleBean> findings;//新闻列表
    private String is_newcomers;
    private String code;
    private int is_join;

    public List<Banner> getSwipers() {
        return swipers;
    }

    public void setSwipers(List<Banner> swipers) {
        this.swipers = swipers;
    }

    public List<Banner> getTop_banner() {
        return top_banner;
    }

    public void setTop_banner(List<Banner> top_banner) {
        this.top_banner = top_banner;
    }

    public List<CourseBean.CourseItemBean> getCourses() {
        return courses;
    }

    public void setCourses(List<CourseBean.CourseItemBean> courses) {
        this.courses = courses;
    }

    public List<IconBean> getIcon1() {
        return icon1;
    }

    public void setIcon1(List<IconBean> icon1) {
        this.icon1 = icon1;
    }


    public List<IconBean> getIcon2() {
        return icon2;
    }

    public void setIcon2(List<IconBean> icon2) {
        this.icon2 = icon2;
    }

    public List<IconBean> getIcon3() {
        return icon3;
    }

    public void setIcon3(List<IconBean> icon3) {
        this.icon3 = icon3;
    }

    public List<Recommend> getRecommend_products() {
        return recommend_products;
    }

    public void setRecommend_products(List<Recommend> recommend_products) {
        this.recommend_products = recommend_products;
    }

    public List<ArticleBean> getFindings() {
        return findings;
    }

    public void setFindings(List<ArticleBean> findings) {
        this.findings = findings;
    }

    public String getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(String is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getIs_join() {
        return is_join;
    }

    public void setIs_join(int is_join) {
        this.is_join = is_join;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static class Banner{
        private int id;//ID
        private String img;//图片
        private String name;//名字
        private String target_url;//跳转链接
        private int type;//类别
        private String xcx_path;//小程序路径
        private int need_login;//是否需要登录
        private int open_wx;//是否需要弹出跳转微信的提示弹窗

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }

        public int getOpen_wx() {
            return open_wx;
        }

        public void setOpen_wx(int open_wx) {
            this.open_wx = open_wx;
        }
    }

    public static class New{
        private int id;//新闻ID
        private String title;//标题
        private String imgone;//图片
        private int pageview;//浏览人数
        private String created_at;//创建时间
        private String target_url;//跳转链接
        private String xcx_path;//微信小程序链接

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getImgone() {
            return imgone;
        }

        public void setImgone(String imgone) {
            this.imgone = imgone;
        }

        public int getPageview() {
            return pageview;
        }

        public void setPageview(int pageview) {
            this.pageview = pageview;
        }

        public String getCreated_at() {
            return created_at;
        }

        public void setCreated_at(String created_at) {
            this.created_at = created_at;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }

    public static class Completion{
        private int done;//已做题目
        private int total;//总题目

        public int getDone() {
            return done;
        }

        public void setDone(int done) {
            this.done = done;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }
    }

    public static class Recommend{
        private int id;
        private String name;
        private String code;
        private String image;
        private String price;
        private String member_price;
        private String group_price;
        private String member_group_price;
        private int total_buy_count;
        private int source_type;
        private int product_id;
        private String type;
        private int is_newcomers;
        private String newcomers_price;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getMember_price() {
            return member_price;
        }

        public void setMember_price(String member_price) {
            this.member_price = member_price;
        }

        public String getMember_group_price() {
            return member_group_price;
        }

        public void setMember_group_price(String member_group_price) {
            this.member_group_price = member_group_price;
        }

        public String getGroup_price() {
            return group_price;
        }

        public void setGroup_price(String group_price) {
            this.group_price = group_price;
        }

        public int getTotal_buy_count() {
            return total_buy_count;
        }

        public void setTotal_buy_count(int total_buy_count) {
            this.total_buy_count = total_buy_count;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getIs_newcomers() {
            return is_newcomers;
        }

        public void setIs_newcomers(int is_newcomers) {
            this.is_newcomers = is_newcomers;
        }

        public String getNewcomers_price() {
            return newcomers_price;
        }

        public void setNewcomers_price(String newcomers_price) {
            this.newcomers_price = newcomers_price;
        }
    }
}
