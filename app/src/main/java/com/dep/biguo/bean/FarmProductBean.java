package com.dep.biguo.bean;

import java.io.Serializable;

/**
 * 农产品实体类
 */
public class FarmProductBean implements Serializable {
    
    private int id;
    private String name;
    private String price;
    private String originalPrice;
    private String imageUrl;
    private int category; // 分类：0全部 1水果 2蔬菜 3农副产品 4干货
    private String description;
    private String origin; // 产地
    private String unit; // 单位
    private int stock; // 库存
    private boolean isOnSale; // 是否在售

    public FarmProductBean() {
    }

    public FarmProductBean(int id, String name, String price, String originalPrice, String imageUrl, int category) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.originalPrice = originalPrice;
        this.imageUrl = imageUrl;
        this.category = category;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }

    public boolean isOnSale() {
        return isOnSale;
    }

    public void setOnSale(boolean onSale) {
        isOnSale = onSale;
    }

    @Override
    public String toString() {
        return "FarmProductBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", price='" + price + '\'' +
                ", originalPrice='" + originalPrice + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", category=" + category +
                ", description='" + description + '\'' +
                ", origin='" + origin + '\'' +
                ", unit='" + unit + '\'' +
                ", stock=" + stock +
                ", isOnSale=" + isOnSale +
                '}';
    }
}