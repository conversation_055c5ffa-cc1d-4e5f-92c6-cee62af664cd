package com.dep.biguo.bean;

import java.util.ArrayList;
import java.util.List;

public class ExpressBean {
    private String port;
    private String transport_number;

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getTransport_number() {
        return transport_number;
    }

    public void setTransport_number(String transport_number) {
        this.transport_number = transport_number;
    }

    /**
     *把ExpressBean集合转变成String集合
     * @param transportInfos
     * @return
     */
    public static List<String> dealExpressBeanToString(List<ExpressBean> transportInfos) {
        List<String> beans = new ArrayList<>();
        for (ExpressBean expressBean : transportInfos) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(expressBean.getPort()).append("-").append(expressBean.getTransport_number());
            beans.add(stringBuilder.toString());
        }
        return beans;
    }

    /**
     * 兼容旧版接口返回的快递数据进行拼接
     * @param transportNumber
     * @param transportType
     * @return
     */
    public static List<String> dealExpressBeanToString(String transportNumber, String transportType) {
        List<String> beans = new ArrayList<>();
        String[] transportTypes = transportType.split(",");
        String[] transportNumbers = transportNumber.split(",");
        for (int i = 0; i < transportTypes.length; i++) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(transportTypes[0]).append("-").append(transportNumbers[0]);
            beans.add(stringBuilder.toString());
        }
        return beans;
    }

}
