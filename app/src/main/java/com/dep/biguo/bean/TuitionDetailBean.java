package com.dep.biguo.bean;

import java.util.List;

public class TuitionDetailBean {
    private String total_installment;//分期总额
    private int total_payments;//扣款周期
    private String remaining_payable;//剩余应还
    private List<PaymentBill> payment_bill;

    public String getTotal_installment() {
        return total_installment;
    }

    public void setTotal_installment(String total_installment) {
        this.total_installment = total_installment;
    }

    public int getTotal_payments() {
        return total_payments;
    }

    public void setTotal_payments(int total_payments) {
        this.total_payments = total_payments;
    }

    public String getRemaining_payable() {
        return remaining_payable;
    }

    public void setRemaining_payable(String remaining_payable) {
        this.remaining_payable = remaining_payable;
    }

    public List<PaymentBill> getPayment_bill() {
        return payment_bill;
    }

    public void setPayment_bill(List<PaymentBill> payment_bill) {
        this.payment_bill = payment_bill;
    }

    public class PaymentBill {
        private int id;
        private int period;//第几期
        private String single_amount;//每次扣款金额
        private String repayment_time;//还款日
        private int status;//状态:1=待归还(还款日),2=已还清,3=还款暂停,4=还款失败,5=还款解除

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getPeriod() {
            return period;
        }

        public void setPeriod(int period) {
            this.period = period;
        }

        public String getSingle_amount() {
            return single_amount;
        }

        public void setSingle_amount(String single_amount) {
            this.single_amount = single_amount;
        }

        public String getRepayment_time() {
            return repayment_time;
        }

        public void setRepayment_time(String repayment_time) {
            this.repayment_time = repayment_time;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }
}
