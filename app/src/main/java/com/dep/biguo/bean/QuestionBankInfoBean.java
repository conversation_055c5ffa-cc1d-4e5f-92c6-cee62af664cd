package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class QuestionBankInfoBean{
    private int id;
    private String code;
    private int total;
    private String name;
    private String create_time;
    private String update_time;
    private int exams_prac_total;//练习题数量
    private int exams_real_total;//真题数量
    private int exams_simu_total;//考试试卷题
    private int exams_vip_total;//VIP题库数量
    private List<Tiku> tikus;

    public static class Tiku{
        private String name;//课程名称
        private int num;//更新数量

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public int getExams_prac_total() {
        return exams_prac_total;
    }

    public void setExams_prac_total(int exams_prac_total) {
        this.exams_prac_total = exams_prac_total;
    }

    public int getExams_real_total() {
        return exams_real_total;
    }

    public void setExams_real_total(int exams_real_total) {
        this.exams_real_total = exams_real_total;
    }

    public int getExams_simu_total() {
        return exams_simu_total;
    }

    public void setExams_simu_total(int exams_simu_total) {
        this.exams_simu_total = exams_simu_total;
    }

    public int getExams_vip_total() {
        return exams_vip_total;
    }

    public void setExams_vip_total(int exams_vip_total) {
        this.exams_vip_total = exams_vip_total;
    }

    public List<Tiku> getTikus() {
        return tikus;
    }

    public void setTikus(List<Tiku> tikus) {
        this.tikus = tikus;
    }
}
