package com.dep.biguo.bean;

import java.util.List;

public class OrderBean {

    /**
     * order_number : 2963431511944705
     * count : 2
     * total_fee : 888
     * name : 121312312321221岁数大所多
     * state : 2
     * img : http://192.168.0.223/goods/jodDeRfpWv.jpg
     * "order_number": "5081951512113969",
     * "transport_number": "617254010117",
     * "transport_type": "shunfeng",
     */

    private int order_id;
    private String order_number;
    private int counts;
    private String total_fee;
    private String name;
    private int state;
    private String img;
    private String type;
    private String transport_number;
    private String transport_type;
    private List<ShopBean> goods_data;
    private String create_time;
    private List<ExpressBean> transport_info;
    private String full_price;
    private int is_group;
    private int is_discuss;//0未评论，1评论
    private int refund_status;//0 未退款，1 退款申请中，2 已拒绝，3 已完成
    private int is_newcomers;//0不是新人活动下的单，1是新人活动下的单
    private int source_type;//笔果:0 必过:1 时刻:2
    private int skill_id;//技能证的ID，0是自考
    private int cert_type;
    private int exam_info_status;//报考信息填写状态
    private int invoice_status;//1 可开票、2 审核中、3 已驳回、4 开票中、5 已开票、6 开票失败
    private String invoice_url;//发票链接
    private String invoice_reason;//发票申请失败原因
    private int is_confirm_receipt;//0=否 1=是

    private List<String> pay_types;//支持的支付方式

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public int getIs_discuss() {
        return is_discuss;
    }

    public void setIs_discuss(int is_discuss) {
        this.is_discuss = is_discuss;
    }

    //private List<ExpressBean> transport_info;

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public List<ShopBean> getGoods_data() {
        return goods_data;
    }

    public void setGoods_data(List<ShopBean> goods_data) {
        this.goods_data = goods_data;
    }

    public String getTransport_number() {
        return transport_number;
    }

    public void setTransport_number(String transport_number) {
        this.transport_number = transport_number;
    }

    public String getTransport_type() {
        return transport_type;
    }

    public void setTransport_type(String transport_type) {
        this.transport_type = transport_type;
    }

    public String getOrder_number() {
        return order_number;
    }

    public void setOrder_number(String order_number) {
        this.order_number = order_number;
    }

    public int getCounts() {
        return counts;
    }

    public void setCounts(int counts) {
        this.counts = counts;
    }

    public String getTotal_fee() {
        return total_fee;
    }

    public void setTotal_fee(String total_fee) {
        this.total_fee = total_fee;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getOrder_id() {
        return order_id;
    }

    public int getIs_group() {
        return is_group;
    }

    public void setIs_group(int is_group) {
        this.is_group = is_group;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public List<ExpressBean> getTransport_info() {
        return transport_info;
    }

    public void setTransport_info(List<ExpressBean> transport_info) {
        this.transport_info = transport_info;
    }

    public String getFull_price() {
        return full_price;
    }

    public void setFull_price(String full_price) {
        this.full_price = full_price;
    }

    public int getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(int refund_status) {
        this.refund_status = refund_status;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public int getExam_info_status() {
        return exam_info_status;
    }

    public void setExam_info_status(int exam_info_status) {
        this.exam_info_status = exam_info_status;
    }

    public int getInvoice_status() {
        return invoice_status;
    }

    public void setInvoice_status(int invoice_status) {
        this.invoice_status = invoice_status;
    }

    public String getInvoice_url() {
        return invoice_url;
    }

    public void setInvoice_url(String invoice_url) {
        this.invoice_url = invoice_url;
    }

    public String getInvoice_reason() {
        return invoice_reason;
    }

    public void setInvoice_reason(String invoice_reason) {
        this.invoice_reason = invoice_reason;
    }

    public List<String> getPay_types() {
        return pay_types;
    }

    public void setPay_types(List<String> pay_types) {
        this.pay_types = pay_types;
    }

    public int getIs_confirm_receipt() {
        return is_confirm_receipt;
    }

    public void setIs_confirm_receipt(int is_confirm_receipt) {
        this.is_confirm_receipt = is_confirm_receipt;
    }
}
