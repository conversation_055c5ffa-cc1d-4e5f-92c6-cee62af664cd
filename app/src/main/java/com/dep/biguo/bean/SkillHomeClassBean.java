package com.dep.biguo.bean;

import java.util.List;

public class SkillHomeClassBean {
    private List<String> classs;
    private List<Banner> swipers;
    private List<Course> list;

    public List<String> getClasss() {
        return classs;
    }

    public void setClasss(List<String> classs) {
        this.classs = classs;
    }

    public List<Banner> getSwipers() {
        return swipers;
    }

    public void setSwipers(List<Banner> swipers) {
        this.swipers = swipers;
    }

    public List<Course> getList() {
        return list;
    }

    public void setList(List<Course> list) {
        this.list = list;
    }

    public static class Banner{
        private String img;//图片
        private String target_url;//跳转链接
        private String xcx_path;//小程序路径

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }


    public static class Course{
        private int is_single;//是否单科1是0否
        private int product_id;//套餐id
        private String name;//套餐名称
        private int class_hour;//课时
        private String price;//价格
        private String group_price;//拼团价
        private String member_price;//单独购买会员价
        private String member_group_price;//拼团购买会员价
        private int valid_type;//有效期类型1固定日期2固定时长
        private int valid_day;//有效期天数
        private String valid_time;//有效期日期
        private List<String> tags;//
        private int is_buy;//是否已购买1是0否
        private int take_time;//已学习时间
        private int buy_count;//已售份数
        private List<Teacher> teachers;//课程老师
        private int source_type;//1时刻套餐2必过套餐
        private String type;//
        private int skill_id;//技能证ID

        public int getIs_single() {
            return is_single;
        }

        public void setIs_single(int is_single) {
            this.is_single = is_single;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getClass_hour() {
            return class_hour;
        }

        public void setClass_hour(int class_hour) {
            this.class_hour = class_hour;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getGroup_price() {
            return group_price;
        }

        public void setGroup_price(String group_price) {
            this.group_price = group_price;
        }

        public String getMember_price() {
            return member_price;
        }

        public void setMember_price(String member_price) {
            this.member_price = member_price;
        }

        public String getMember_group_price() {
            return member_group_price;
        }

        public void setMember_group_price(String member_group_price) {
            this.member_group_price = member_group_price;
        }

        public int getValid_type() {
            return valid_type;
        }

        public void setValid_type(int valid_type) {
            this.valid_type = valid_type;
        }

        public int getValid_day() {
            return valid_day;
        }

        public void setValid_day(int valid_day) {
            this.valid_day = valid_day;
        }

        public String getValid_time() {
            return valid_time;
        }

        public void setValid_time(String valid_time) {
            this.valid_time = valid_time;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }

        public int getIs_buy() {
            return is_buy;
        }

        public void setIs_buy(int is_buy) {
            this.is_buy = is_buy;
        }

        public int getTake_time() {
            return take_time;
        }

        public void setTake_time(int take_time) {
            this.take_time = take_time;
        }

        public int getBuy_count() {
            return buy_count;
        }

        public void setBuy_count(int buy_count) {
            this.buy_count = buy_count;
        }

        public List<Teacher> getTeachers() {
            return teachers;
        }

        public void setTeachers(List<Teacher> teachers) {
            this.teachers = teachers;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getSkill_id() {
            return skill_id;
        }

        public void setSkill_id(int skill_id) {
            this.skill_id = skill_id;
        }
    }
    public static class Teacher{
        private int id;//老师id
        private String name;//老师名字
        private String image;//老师头像（长方形）
        private String avatar;//老师头像（正方形）
        private int classroom_id;//班型ID

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public int getClassroom_id() {
            return classroom_id;
        }

        public void setClassroom_id(int classroom_id) {
            this.classroom_id = classroom_id;
        }
    }
}
