package com.dep.biguo.bean;

import java.util.List;

public class InviteTaskDetails {
    private int is_exchange;//兑换状态 0=任务未完成，不可兑换，待解锁， 1=已解锁， (待)去兑换，2=任务完成， 已兑换，不可重复兑换
    private int task_limit;//任务需要完成人数, 成功邀请几位好友可得
    private int invite_people;//已成功邀请人数
    private List<String> invite_people_list;
    private int status;//任务状态 0=激活任务、1=进行中, 2=去兑换, 3=已兑换, 4= 活动未开始、5=活动结束
    private int is_other_activate;//是否有其它激活任务 0=否，1=是,2=关闭
    private int record_id;//激活记录ID
    private int task_set_id;//任务ID
    private String title;//任务标题
    private String subtitle;//任务副标题
    private String task_date;//任务起止时间
    private String task_img;//任务图片
    private String type;//奖励类型
    private int is_course;//是否是课程类型的奖励
    private int can_exchange_invite_number;//当前任务已邀请人数

    public int getIs_exchange() {
        return is_exchange;
    }

    public void setIs_exchange(int is_exchange) {
        this.is_exchange = is_exchange;
    }

    public int getTask_limit() {
        return task_limit;
    }

    public void setTask_limit(int task_limit) {
        this.task_limit = task_limit;
    }

    public int getInvite_people() {
        return invite_people;
    }

    public void setInvite_people(int invite_people) {
        this.invite_people = invite_people;
    }

    public List<String> getInvite_people_list() {
        return invite_people_list;
    }

    public void setInvite_people_list(List<String> invite_people_list) {
        this.invite_people_list = invite_people_list;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIs_other_activate() {
        return is_other_activate;
    }

    public void setIs_other_activate(int is_other_activate) {
        this.is_other_activate = is_other_activate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getRecord_id() {
        return record_id;
    }

    public void setRecord_id(int record_id) {
        this.record_id = record_id;
    }

    public int getTask_set_id() {
        return task_set_id;
    }

    public void setTask_set_id(int task_set_id) {
        this.task_set_id = task_set_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTask_date() {
        return task_date;
    }

    public void setTask_date(String task_date) {
        this.task_date = task_date;
    }

    public String getTask_img() {
        return task_img;
    }

    public void setTask_img(String task_img) {
        this.task_img = task_img;
    }

    public int getIs_course() {
        return is_course;
    }

    public void setIs_course(int is_course) {
        this.is_course = is_course;
    }

    public int getCan_exchange_invite_number() {
        return can_exchange_invite_number;
    }

    public void setCan_exchange_invite_number(int can_exchange_invite_number) {
        this.can_exchange_invite_number = can_exchange_invite_number;
    }
}