package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/29
 * @Description:
 */
public class CircleReplayBean {

    private String replay_name;
    private int type;//0点赞动态,1评论动态,2转发动态,3点赞评论,4回复评论
    private String created_at;
    private String replay_content;
    private String replay_avatar;
    private ReplayToBean replay_to;
    private CircleBean.Moment posts;

    public String getReplay_name() {
        return replay_name;
    }

    public void setReplay_name(String replay_name) {
        this.replay_name = replay_name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getReplay_content() {
        return replay_content;
    }

    public void setReplay_content(String replay_content) {
        this.replay_content = replay_content;
    }

    public String getReplay_avatar() {
        return replay_avatar;
    }

    public void setReplay_avatar(String replay_avatar) {
        this.replay_avatar = replay_avatar;
    }

    public ReplayToBean getReplay_to() {
        return replay_to;
    }

    public void setReplay_to(ReplayToBean replay_to) {
        this.replay_to = replay_to;
    }

    public CircleBean.Moment getPosts() {
        return posts;
    }

    public void setPosts(CircleBean.Moment posts) {
        this.posts = posts;
    }

    public static class ReplayToBean {
        private String comment_content;
        private String comment_name;

        public String getComment_content() {
            return comment_content;
        }

        public void setComment_content(String comment_content) {
            this.comment_content = comment_content;
        }

        public String getComment_name() {
            return comment_name;
        }

        public void setComment_name(String comment_name) {
            this.comment_name = comment_name;
        }
    }
}
