package com.dep.biguo.bean;

import java.util.List;

public class StudyBean {
    private int is_super_vip; //是否已购买超V
    private int is_yami; //是否已购买押密
    private int is_vip; //是否已购买VIP题库
    private int is_high;//是否已购买高频考点
    private int has_yami; //是否上架押密, -1预定押密未上架，0押密未上架，1押密已上架
    private int has_video; //是否上架视频
    private int has_chapter; //是否上架章节训练
    private int has_real; //是否上架历年真题
    private int question_cal_nums; //总可答题数量
    private int answered_nums; //总已答题数
    private int time_remains; //距考试剩余时长
    private int take_time; //总答题时间
    private int today_answered;//今日答题数量
    private String correct_rate;//正确率
    private String completion_rate;//完成率
    private int error_topic_nums;//错题数量
    private int collection_topic_nums;//收藏数量
    private List<Icon> icons;
    private List<Banner> banner;
    private List<QuestionBank> tikus;

    public int getIs_super_vip() {
        return is_super_vip;
    }

    public void setIs_super_vip(int is_super_vip) {
        this.is_super_vip = is_super_vip;
    }

    public int getIs_yami() {
        return is_yami;
    }

    public void setIs_yami(int is_yami) {
        this.is_yami = is_yami;
    }

    public int getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(int is_vip) {
        this.is_vip = is_vip;
    }

    public int getIs_high() {
        return is_high;
    }

    public void setIs_high(int is_high) {
        this.is_high = is_high;
    }

    public int getHas_yami() {
        return has_yami;
    }

    public void setHas_yami(int has_yami) {
        this.has_yami = has_yami;
    }

    public int getHas_video() {
        return has_video;
    }

    public void setHas_video(int has_video) {
        this.has_video = has_video;
    }

    public int getHas_chapter() {
        return has_chapter;
    }

    public void setHas_chapter(int has_chapter) {
        this.has_chapter = has_chapter;
    }

    public int getHas_real() {
        return has_real;
    }

    public void setHas_real(int has_real) {
        this.has_real = has_real;
    }

    public int getQuestion_cal_nums() {
        return question_cal_nums;
    }

    public void setQuestion_cal_nums(int question_cal_nums) {
        this.question_cal_nums = question_cal_nums;
    }

    public int getAnswered_nums() {
        return answered_nums;
    }

    public void setAnswered_nums(int answered_nums) {
        this.answered_nums = answered_nums;
    }

    public int getTime_remains() {
        return time_remains;
    }

    public void setTime_remains(int time_remains) {
        this.time_remains = time_remains;
    }

    public int getTake_time() {
        return take_time;
    }

    public void setTake_time(int take_time) {
        this.take_time = take_time;
    }

    public int getToday_answered() {
        return today_answered;
    }

    public void setToday_answered(int today_answered) {
        this.today_answered = today_answered;
    }

    public List<Icon> getIcons() {
        return icons;
    }

    public void setIcons(List<Icon> icons) {
        this.icons = icons;
    }

    public List<Banner> getBanner() {
        return banner;
    }

    public void setBanner(List<Banner> banner) {
        this.banner = banner;
    }

    public List<QuestionBank> getTikus() {
        return tikus;
    }

    public void setTikus(List<QuestionBank> tikus) {
        this.tikus = tikus;
    }

    public String getCorrect_rate() {
        return correct_rate;
    }

    public void setCorrect_rate(String correct_rate) {
        this.correct_rate = correct_rate;
    }

    public String getCompletion_rate() {
        return completion_rate;
    }

    public void setCompletion_rate(String completion_rate) {
        this.completion_rate = completion_rate;
    }

    public int getError_topic_nums() {
        return error_topic_nums;
    }

    public void setError_topic_nums(int error_topic_nums) {
        this.error_topic_nums = error_topic_nums;
    }

    public int getCollection_topic_nums() {
        return collection_topic_nums;
    }

    public void setCollection_topic_nums(int collection_topic_nums) {
        this.collection_topic_nums = collection_topic_nums;
    }

    public static class QuestionBank{
        private int type;//题库类型
        private String name;//题库名称
        private int total;//题库总数
        private int cal_nums;//可答题数
        private int answered;//已答题数
        private int has;//是否已上架
        private String desc;//广告文字
        private String intro;//网络助学需要用到
        private long version;//题库版本号
        private long record_expire_time;//有效时间

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getCal_nums() {
            return cal_nums;
        }

        public void setCal_nums(int cal_nums) {
            this.cal_nums = cal_nums;
        }

        public int getAnswered() {
            return answered;
        }

        public void setAnswered(int answered) {
            this.answered = answered;
        }

        public int getHas() {
            return has;
        }

        public void setHas(int has) {
            this.has = has;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getIntro() {
            return intro;
        }

        public void setIntro(String intro) {
            this.intro = intro;
        }

        public long getVersion() {
            return version;
        }

        public void setVersion(long version) {
            this.version = version;
        }

        public long getRecord_expire_time() {
            return record_expire_time;
        }

        public void setRecord_expire_time(long record_expire_time) {
            this.record_expire_time = record_expire_time;
        }
    }

    public static class Banner{
        private int id;
        private int type;//图标类型 考前押密-yami 视频课程-video 我的错题 errors 我的收藏-collections
        private String img;//图标地址
    }

    public static class Icon{
        private String img;//服务器的图片
        private String type;//类型
        private String title;//本地属性名称

        private int icon;//本地属性，当服务器的图片为空时调用本地图片资源

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getIcon() {
            return icon;
        }

        public void setIcon(int icon) {
            this.icon = icon;
        }

    }
}
