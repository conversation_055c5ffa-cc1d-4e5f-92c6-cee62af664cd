package com.dep.biguo.bean;

import java.util.List;

public class PrizeBean {
    private Prize exchange;//奖品列表为空时返回兑换选项
    private List<Prize> list;//奖品列表

    public Prize getExchange() {
        return exchange;
    }

    public void setExchange(Prize exchange) {
        this.exchange = exchange;
    }

    public List<Prize> getList() {
        return list;
    }

    public void setList(List<Prize> list) {
        this.list = list;
    }

    public static class Prize{
        private int prize_type;//奖品类型 5：vip题 6：视频 7：押密,8积分，9果币 10精讲视频 11串讲视频 12优惠券13会员
        private String value; //课程编码或积分果币值
        private String name; //选项显示名称
        private int prize_id; //奖品id

        public int getPrize_type() {
            return prize_type;
        }

        public void setPrize_type(int prize_type) {
            this.prize_type = prize_type;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getPrize_id() {
            return prize_id;
        }

        public void setPrize_id(int prize_id) {
            this.prize_id = prize_id;
        }
    }

}
