package com.dep.biguo.bean;

public class UploadEnrollInfoImageBean {
    private String name;//类型
    private String url;//文件路径

    private String encryptUrl;//加密后的文件路径
    private int defaultImage;//未上传图片时的默认图片

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getEncryptUrl() {
        return encryptUrl;
    }

    public void setEncryptUrl(String encryptUrl) {
        this.encryptUrl = encryptUrl;
    }

    public int getDefaultImage() {
        return defaultImage;
    }

    public void setDefaultImage(int defaultImage) {
        this.defaultImage = defaultImage;
    }
}
