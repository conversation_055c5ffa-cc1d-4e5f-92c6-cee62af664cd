package com.dep.biguo.bean;

import java.util.List;

public class InvitorList<PERSON>ean extends BaseResponse<List<InvitorBean>> {
    private int total;
    private int current_page;
    private List<InvitorBean> list;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getCurrent_page() {
        return current_page;
    }

    public void setCurrent_page(int current_page) {
        this.current_page = current_page;
    }

    public List<InvitorBean> getList() {
        return list;
    }

    public void setList(List<InvitorBean> list) {
        this.list = list;
    }
}
