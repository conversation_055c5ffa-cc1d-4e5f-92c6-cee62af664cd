package com.dep.biguo.bean;

public class MySecretBean {
    private String code; //课程编码
    private String name;//课程名称
    private int num;//题目数量
    private String expire;//有效期
    private String get_time;//获取时间
    private int open_status;//0 电子档未开放 1电子档开放下载
    private int passed;//课程是否及格，排序依据
    private Scholarship scholarship;//奖学金排行榜状态

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getExpire() {
        return expire;
    }

    public void setExpire(String expire) {
        this.expire = expire;
    }

    public String getGet_time() {
        return get_time;
    }

    public void setGet_time(String get_time) {
        this.get_time = get_time;
    }

    public int getOpen_status() {
        return open_status;
    }

    public void setOpen_status(int open_status) {
        this.open_status = open_status;
    }

    public int getPassed() {
        return passed;
    }

    public void setPassed(int passed) {
        this.passed = passed;
    }

    public Scholarship getScholarship() {
        return scholarship;
    }

    public void setScholarship(Scholarship scholarship) {
        this.scholarship = scholarship;
    }

    public static class Scholarship{
        private int status;//奖学金 0=未开放，1=生效中，2=开放成绩申报，3=开放排行
        private String sales_restriction;//开放课程代码生效销量
        private String maximum_bonus;//最高奖金额度

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getSales_restriction() {
            return sales_restriction;
        }

        public void setSales_restriction(String sales_restriction) {
            this.sales_restriction = sales_restriction;
        }

        public String getMaximum_bonus() {
            return maximum_bonus;
        }

        public void setMaximum_bonus(String maximum_bonus) {
            this.maximum_bonus = maximum_bonus;
        }
    }
}
