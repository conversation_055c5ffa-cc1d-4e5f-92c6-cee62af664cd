package com.dep.biguo.bean;

public class MyCouponBean {
//    "shelf_life": "保质期 没有说明过期了",
//            "create_time": "领取时间",
//            "end_time": "到期时间",
//            "type": "1 vip课堂优惠券",
//            "price": "优惠券面额",
//            "state": "1未使用  2 过期 3 已使用",
//            "condition": "描述",
//            "title": "标题"
    private int id;
    private String shelf_life;
    private int users_id;
    private String price;
    private int type;//1 vip课堂优惠券
    private String create_time;
    private String end_time;
    private String use_time;
    private String condition;
    private String title;
    private int state;//1未使用 2 过期 3 已使用

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getShelf_life() {
        return shelf_life;
    }

    public void setShelf_life(String shelf_life) {
        this.shelf_life = shelf_life;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getUse_time() {
        return use_time;
    }

    public void setUse_time(String use_time) {
        this.use_time = use_time;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
