package com.dep.biguo.bean;

import java.util.List;

public class MyGroupBean {
    private String group_id;//拼团id
    private String type;//拼团类型
    private int video_type;//视频类型，精讲1，串讲2
    private int status;//1拼团中，2拼团失败，3拼团成功，4拼团取消
    private String code;//课程代码
    private String name;//课程名称
    private String image;//风险图片
    private String price;//单独购买价
    private String group_price;//拼团价 旧版接口为实付价
    private String member_group_price;//拼团折扣价
    private String pay_price;//实付的拼团价
    private String group_share_text;//拼团分享链接的标题
    private String group_share_desc;//拼团分享链接的副标题
    private List<GroupUserBean> users_info;//拼团用户信息
    private int sec;//剩余秒数
    private int count;//已拼数量
    private int is_refund;//是否退款
    private int product_id;//必过视频的ID
    private int is_open;//1开通0未开通或过期
    private String newcomers_price;//新人价格
    private int is_newcomers;//1是新人，0不是新人
    private int source_type;//1时刻套餐2必过套餐
    private int skill_id;//技能证ID
    private int is_pay_vip;//是否支付了VIP题库，0否，1是
    private long version;//题库版本号
    private GoodsUpgradeBean replenish_goods;

    public int getIs_pay_vip() {
        return is_pay_vip;
    }

    public void setIs_pay_vip(int is_pay_vip) {
        this.is_pay_vip = is_pay_vip;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getVideo_type() {
        return video_type;
    }

    public void setVideo_type(int video_type) {
        this.video_type = video_type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<GroupUserBean> getUsers_info() {
        return users_info;
    }

    public void setUsers_info(List<GroupUserBean> users_info) {
        this.users_info = users_info;
    }

    public int getSec() {
        return sec;
    }

    public void setSec(int sec) {
        this.sec = sec;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getIs_refund() {
        return is_refund;
    }

    public void setIs_refund(int is_refund) {
        this.is_refund = is_refund;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public String getPay_price() {
        return pay_price;
    }

    public void setPay_price(String pay_price) {
        this.pay_price = pay_price;
    }

    public String getGroup_share_text() {
        return group_share_text;
    }

    public void setGroup_share_text(String group_share_text) {
        this.group_share_text = group_share_text;
    }

    public String getGroup_share_desc() {
        return group_share_desc;
    }

    public void setGroup_share_desc(String group_share_desc) {
        this.group_share_desc = group_share_desc;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getIs_open() {
        return is_open;
    }

    public void setIs_open(int is_open) {
        this.is_open = is_open;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public GoodsUpgradeBean getReplenish_goods() {
        return replenish_goods;
    }

    public void setReplenish_goods(GoodsUpgradeBean replenish_goods) {
        this.replenish_goods = replenish_goods;
    }
}
