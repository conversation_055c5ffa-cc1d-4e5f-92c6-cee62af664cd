package com.dep.biguo.bean;

import java.util.List;

public class BiguoVipBean {
    private String nickname;//昵称
    private String mobile;//手机号码
    private String price;//价格
    private String discount; //折扣
    private int open_effective_year; //有效年份
    private int expiration_days;//到期天数
    private int is_open;//是否开通 1=是 0=否
    private int show_renew;//是否显示续费 1=是 0=否
    private String expire_date;
    private List<Equity> equity;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public int getOpen_effective_year() {
        return open_effective_year;
    }

    public void setOpen_effective_year(int open_effective_year) {
        this.open_effective_year = open_effective_year;
    }

    public int getExpiration_days() {
        return expiration_days;
    }

    public void setExpiration_days(int expiration_days) {
        this.expiration_days = expiration_days;
    }

    public int getIs_open() {
        return is_open;
    }

    public void setIs_open(int is_open) {
        this.is_open = is_open;
    }

    public int getShow_renew() {
        return show_renew;
    }

    public void setShow_renew(int show_renew) {
        this.show_renew = show_renew;
    }

    public String getExpire_date() {
        return expire_date;
    }

    public void setExpire_date(String expire_date) {
        this.expire_date = expire_date;
    }

    public List<Equity> getEquity() {
        return equity;
    }

    public void setEquity(List<Equity> equity) {
        this.equity = equity;
    }

    public static class Equity{
        private String icon;
        private String title;
        private String desc;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
