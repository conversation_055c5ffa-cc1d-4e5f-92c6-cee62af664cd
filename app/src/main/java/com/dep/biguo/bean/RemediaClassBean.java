package com.dep.biguo.bean;

import java.util.List;

public class Remedia<PERSON>lass<PERSON>ean extends BaseResponse<RemediaClassBean> {
    private List<ClassRoomBean> classroom_list;
    private List<VideoLiveBean> live;
    private List<HomeBookBean> shopGoods;
    private List<AdvertisingBean> advertising;
    private List<AdvertisingBean> tutorial_banner;
    private List<VideoCourseBean> courses_list;
    private List<HomeBookBean> goodsList;
    private List<HomeBookBean> bookList;

    public List<ClassRoomBean> getClassroom_list() {
        return classroom_list;
    }

    public void setClassroom_list(List<ClassRoomBean> classroom_list) {
        this.classroom_list = classroom_list;
    }

    public List<HomeBookBean> getShopGoods() {
        return shopGoods;
    }

    public void setShopGoods(List<HomeBookBean> shopGoods) {
        this.shopGoods = shopGoods;
    }

    public List<AdvertisingBean> getAdvertising() {
        return advertising;
    }

    public void setAdvertising(List<AdvertisingBean> advertising) {
        this.advertising = advertising;
    }

    public List<VideoCourseBean> getCourses_list() {
        return courses_list;
    }

    public void setCourses_list(List<VideoCourseBean> courses_list) {
        this.courses_list = courses_list;
    }

    public List<HomeBookBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<HomeBookBean> goodsList) {
        this.goodsList = goodsList;
    }

    public List<HomeBookBean> getBookList() {
        return bookList;
    }

    public void setBookList(List<HomeBookBean> bookList) {
        this.bookList = bookList;
    }

    public List<VideoLiveBean> getLive() {
        return live;
    }

    public void setLive(List<VideoLiveBean> live) {
        this.live = live;
    }

    public List<AdvertisingBean> getTutorial_banner() {
        return tutorial_banner;
    }

    public void setTutorial_banner(List<AdvertisingBean> tutorial_banner) {
        this.tutorial_banner = tutorial_banner;
    }

    public static class AdvertisingBean {
        private int id;
        private String url;
        private String img;
        private int type;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

}
