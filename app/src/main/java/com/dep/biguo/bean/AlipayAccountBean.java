package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class AlipayAccountBean implements Parcelable {
//     "id": 1,
//             "zfb": ***********,
//             "name": "大叔",
//             "users_id": 155557,
//             "create_time": "2020-11-27 15:10:12",
//             "update_time": null

    private String zfb;
    private String name;
    private int users_id;
    private String create_time;
    private String update_time;

    protected AlipayAccountBean(Parcel in) {
        zfb = in.readString();
        name = in.readString();
        users_id = in.readInt();
        create_time = in.readString();
        update_time = in.readString();
    }

    public static final Creator<AlipayAccountBean> CREATOR = new Creator<AlipayAccountBean>() {
        @Override
        public AlipayAccountBean createFromParcel(Parcel in) {
            return new AlipayAccountBean(in);
        }

        @Override
        public AlipayAccountBean[] newArray(int size) {
            return new AlipayAccountBean[size];
        }
    };

    public String getZfb() {
        return zfb;
    }

    public void setZfb(String zfb) {
        this.zfb = zfb;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(zfb);
        dest.writeString(name);
        dest.writeInt(users_id);
        dest.writeString(create_time);
        dest.writeString(update_time);
    }
}
