package com.dep.biguo.bean;

public class AllVipBean {
    private String code;//课程代码
    private String name;//课程名称
    private String price;//原价
    private String type;
    private int total_nums;//题目数量
    private int last_id;//最后做的题目的ID
    private long version;//题库版本
    private int record_expire_time;//有效期
    private String group_price;//拼团价
    private String member_price;//笔果折扣卡原价
    private String member_group_price;//笔果折扣卡拼团价
    private String newcomers_price;//新人价格
    private int is_newcomers;//1是新人，0不是新人
    private int status;//开通状态,0未开通，1拼团中，2已开通
    private int courses_id;//课程ID
    private int total_buy_count;//已购人数

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(int record_expire_time) {
        this.record_expire_time = record_expire_time;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCourses_id() {
        return courses_id;
    }

    public void setCourses_id(int courses_id) {
        this.courses_id = courses_id;
    }

    public int getTotal_buy_count() {
        return total_buy_count;
    }

    public void setTotal_buy_count(int total_buy_count) {
        this.total_buy_count = total_buy_count;
    }
}
