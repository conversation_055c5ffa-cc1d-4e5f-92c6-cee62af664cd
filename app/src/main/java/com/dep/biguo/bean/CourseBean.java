package com.dep.biguo.bean;

import java.util.List;

public class CourseBean extends BaseResponse<CourseBean> {
    private double pass_score;//及格分数
    private String icon_url;//考试计划图片
    private long start_application_time;//预计报考的时间
    private long end_application_time;//截止报考的时间
    private WeChatBean wx_group;//微信群
    private List<CourseItemBean> courses_joined;//报名课程
    private List<CourseItemBean> courses_not_joined;//未报名课程
    private List<CourseItemBean> courses_passed;//已通过课程
    private String tutorial_class_banner;//辅导班入口
    private String bottom_price_text;//新人最低价
    private int is_formulate_study_plan;//是否有制定学习计划
    private int is_plan_in_profession;//当有计划的时候，该计划是否属于当前专业
    private int is_click_study_plan;//是否点击学习计划推荐0否1是
    private String tutoring_class_title;//自考辅导班文案
    private String study_plan_title;//学习计划文案
    private String compare_picture;//课改对照表
    private String exam_plan_banner_url;//新版APP用到的的考试计划图片

    public String getIcon_url() {
        return icon_url;
    }

    public void setIcon_url(String icon_url) {
        this.icon_url = icon_url;
    }

    public double getPass_score() {
        return pass_score;
    }

    public void setPass_score(double pass_score) {
        this.pass_score = pass_score;
    }

    public long getStart_application_time() {
        return start_application_time;
    }

    public void setStart_application_time(long start_application_time) {
        this.start_application_time = start_application_time;
    }

    public long getEnd_application_time() {
        return end_application_time;
    }

    public void setEnd_application_time(long end_application_time) {
        this.end_application_time = end_application_time;
    }

    public WeChatBean getWx_group() {
        return wx_group;
    }

    public void setWx_group(WeChatBean wx_group) {
        this.wx_group = wx_group;
    }

    public List<CourseItemBean> getCourses_joined() {
        return courses_joined;
    }

    public void setCourses_joined(List<CourseItemBean> courses_joined) {
        this.courses_joined = courses_joined;
    }

    public List<CourseItemBean> getCourses_not_joined() {
        return courses_not_joined;
    }

    public void setCourses_not_joined(List<CourseItemBean> courses_not_joined) {
        this.courses_not_joined = courses_not_joined;
    }

    public List<CourseItemBean> getCourses_passed() {
        return courses_passed;
    }

    public void setCourses_passed(List<CourseItemBean> courses_passed) {
        this.courses_passed = courses_passed;
    }

    public String getTutorial_class_banner() {
        return tutorial_class_banner;
    }

    public void setTutorial_class_banner(String tutorial_class_banner) {
        this.tutorial_class_banner = tutorial_class_banner;
    }

    public String getBottom_price_text() {
        return bottom_price_text;
    }

    public void setBottom_price_text(String bottom_price_text) {
        this.bottom_price_text = bottom_price_text;
    }

    public int getIs_formulate_study_plan() {
        return is_formulate_study_plan;
    }

    public void setIs_formulate_study_plan(int is_formulate_study_plan) {
        this.is_formulate_study_plan = is_formulate_study_plan;
    }

    public int getIs_plan_in_profession() {
        return is_plan_in_profession;
    }

    public void setIs_plan_in_profession(int is_plan_in_profession) {
        this.is_plan_in_profession = is_plan_in_profession;
    }

    public int getIs_click_study_plan() {
        return is_click_study_plan;
    }

    public void setIs_click_study_plan(int is_click_study_plan) {
        this.is_click_study_plan = is_click_study_plan;
    }

    public String getTutoring_class_title() {
        return tutoring_class_title;
    }

    public void setTutoring_class_title(String tutoring_class_title) {
        this.tutoring_class_title = tutoring_class_title;
    }

    public String getStudy_plan_title() {
        return study_plan_title;
    }

    public void setStudy_plan_title(String study_plan_title) {
        this.study_plan_title = study_plan_title;
    }

    public String getCompare_picture() {
        return compare_picture;
    }

    public void setCompare_picture(String compare_picture) {
        this.compare_picture = compare_picture;
    }

    public String getExam_plan_banner_url() {
        return exam_plan_banner_url;
    }

    public void setExam_plan_banner_url(String exam_plan_banner_url) {
        this.exam_plan_banner_url = exam_plan_banner_url;
    }

    public static class WeChatBean{
        private String xcx_path;
        private String target_url;
        private int need_login;

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }
    }

    public static class CourseItemBean{
        private int courses_id;//课程id
        private int id;
        private int joined;//0未报名，1已报名
        private String code;//课程编码
        private String name;//课程名称
        private String exam_time;//考试时间 没有返回null
        private float score;//分数
        private int is_online_assistance;//0没有开放，1已开放
        private String credits;//学分
        private int type;//0未设置，1必考，2选考，3加考

        public int getCourses_id() {
            return courses_id;
        }

        public void setCourses_id(int courses_id) {
            this.courses_id = courses_id;
        }

        public int getJoined() {
            return joined;
        }

        public void setJoined(int joined) {
            this.joined = joined;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getExam_time() {
            return exam_time;
        }

        public void setExam_time(String exam_time) {
            this.exam_time = exam_time;
        }

        public double getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getIs_online_assistance() {
            return is_online_assistance;
        }

        public void setIs_online_assistance(int is_online_assistance) {
            this.is_online_assistance = is_online_assistance;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCredits() {
            return credits;
        }

        public void setCredits(String credits) {
            this.credits = credits;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }
}
