package com.dep.biguo.bean;

import java.util.List;

public class OrganizationCourseDetailBean {
    private int activities_id;
    private String course_cover;//课程封面
    private List<String> banners;
    private String course_name;//课程名称
    private String start_course_time;//开课时间
    private String teacher;//主讲老师
    private String teacher_introduction;//老师介绍
    private String course_introduction;//课程介绍
    private String longitude;
    private String latitude;
    private String address;//地址
    private String contact_number;//机构联系电话
    private boolean is_reserved;//是否已预约
    private int surplus_count;//剩余座位

    public int getActivities_id() {
        return activities_id;
    }

    public void setActivities_id(int activities_id) {
        this.activities_id = activities_id;
    }

    public String getCourse_cover() {
        return course_cover;
    }

    public void setCourse_cover(String course_cover) {
        this.course_cover = course_cover;
    }

    public List<String> getBanners() {
        return banners;
    }

    public void setBanners(List<String> banners) {
        this.banners = banners;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getStart_course_time() {
        return start_course_time;
    }

    public void setStart_course_time(String start_course_time) {
        this.start_course_time = start_course_time;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public String getTeacher_introduction() {
        return teacher_introduction;
    }

    public void setTeacher_introduction(String teacher_introduction) {
        this.teacher_introduction = teacher_introduction;
    }

    public String getCourse_introduction() {
        return course_introduction;
    }

    public void setCourse_introduction(String course_introduction) {
        this.course_introduction = course_introduction;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContact_number() {
        return contact_number;
    }

    public void setContact_number(String contact_number) {
        this.contact_number = contact_number;
    }

    public boolean isIs_reserved() {
        return is_reserved;
    }

    public void setIs_reserved(boolean is_reserved) {
        this.is_reserved = is_reserved;
    }

    public int getSurplus_count() {
        return surplus_count;
    }

    public void setSurplus_count(int surplus_count) {
        this.surplus_count = surplus_count;
    }

    public static class Banner{
        private int id;//ID
        private String name;//名字
        private String img;//图片
        private String target_url;//跳转链接
        private String xcx_path;//小程序路径

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }
}
