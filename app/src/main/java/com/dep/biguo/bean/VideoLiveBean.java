package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.SectionEntity;

import java.util.List;

public class VideoLiveBean extends SectionEntity<VideoLiveBean> {

    private List<VideoLiveBean> list;
    private String day;

    private int id;
    private String code;
    private String course_name;
    private String teacher_id;
    private String room_id;
    private String live_Id;
    private String playback_id;
    private String start_time;
    private String end_time;
    private int state;
    private int count;
    private int is_buy;
    private String teacher_name;
    private String teacher_img;
    private String live_date;

    public VideoLiveBean(boolean isHeader, String header) {
        super(isHeader, header);
    }

    public String getLive_Id() {
        return live_Id;
    }

    public void setLive_Id(String live_Id) {
        this.live_Id = live_Id;
    }

    public String getPlayback_id() {
        return playback_id;
    }

    public void setPlayback_id(String playback_id) {
        this.playback_id = playback_id;
    }

    public List<VideoLiveBean> getList() {
        return list;
    }

    public void setList(List<VideoLiveBean> list) {
        this.list = list;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getTeacher_id() {
        return teacher_id;
    }

    public void setTeacher_id(String teacher_id) {
        this.teacher_id = teacher_id;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getIs_buy() {
        return is_buy;
    }

    public void setIs_buy(int is_buy) {
        this.is_buy = is_buy;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }

    public String getTeacher_img() {
        return teacher_img;
    }

    public void setTeacher_img(String teacher_img) {
        this.teacher_img = teacher_img;
    }

    public String getLive_date() {
        return live_date;
    }

    public void setLive_date(String live_date) {
        this.live_date = live_date;
    }
}
