package com.dep.biguo.bean;

public class ArticleBean {
    private int id;//新闻ID
    private String title;//标题
    private String imgone;//图片
    private int pageview;//浏览人数
    private String created_at;//创建时间
    private String target_url;//跳转链接
    private String xcx_path;//微信小程序链接

    private String content;//内容，老版本的字段
    private String create_time;//创建时间，老版本的字段

    public int getPageview() {
        return pageview;
    }

    public void setPageview(int pageview) {
        this.pageview = pageview;
    }

    public String getXcx_path() {
        return xcx_path;
    }

    public void setXcx_path(String xcx_path) {
        this.xcx_path = xcx_path;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTarget_url() {
        return target_url;
    }

    public void setTarget_url(String target_url) {
        this.target_url = target_url;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getImgone() {
        return imgone;
    }

    public void setImgone(String imgone) {
        this.imgone = imgone;
    }
}
