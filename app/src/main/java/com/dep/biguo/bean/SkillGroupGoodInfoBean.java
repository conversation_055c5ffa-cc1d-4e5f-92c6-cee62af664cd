package com.dep.biguo.bean;

import java.util.List;

public class SkillGroupGoodInfoBean {
    private int is_pay;//是否拥有题库
    private int total_buy_count;//已购数目
    private List<String> label;//商品标签
    private String group_id;
    private String name;//课程名称
    private int comment_num;//商品评论数
    private List<GroupCommentBean> comment;//商品评论
    private int is_comment;//是否已评论
    private String describe;//商品描述详情
    private int source_type;//来源类型：笔果:0，必过:1，时刻:2
    private String code;//课程代码,成考必传
    private List<GroupGoodInfoBean.CommonQuestion> qa;//常见问题
    private String share_text;//商品分享链接的标题
    private String share_desc;//商品分享链接的副标题
    private String group_share_text;//拼团分享链接的标题
    private String group_share_desc;//拼团分享链接的副标题
    private String price; //原价
    private String member_price;//折扣价
    private String group_price;//拼团价
    private String member_group_price;//拼团折扣价
    private List<String> rules;//拼团规则
    private int group_num;//正在拼团数
    private int order_id;//当已购买后，order_id不为0
    private List<GroupBean> conduct_group;//正在拼团的列表
    private List<OrderPayDetailBean.Bposter> redeem;
    private String remind_buy;//展示对应类型的推荐商品
    private GroupGoodInfoBean.MasterGraph master_graph;//封面播放链接
    private int product_id;//套餐/商品id
    private int cert_type;//
    private String share_thumb_image;//分享链接的图片

    public String getShare_thumb_image() {
        return share_thumb_image;
    }

    public void setShare_thumb_image(String share_thumb_image) {
        this.share_thumb_image = share_thumb_image;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public int getTotal_buy_count() {
        return total_buy_count;
    }

    public void setTotal_buy_count(int total_buy_count) {
        this.total_buy_count = total_buy_count;
    }

    public List<String> getLabel() {
        return label;
    }

    public void setLabel(List<String> label) {
        this.label = label;
    }


    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getComment_num() {
        return comment_num;
    }

    public void setComment_num(int comment_num) {
        this.comment_num = comment_num;
    }

    public List<GroupCommentBean> getComment() {
        return comment;
    }

    public void setComment(List<GroupCommentBean> comment) {
        this.comment = comment;
    }

    public int getIs_comment() {
        return is_comment;
    }

    public void setIs_comment(int is_comment) {
        this.is_comment = is_comment;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<GroupGoodInfoBean.CommonQuestion> getQa() {
        return qa;
    }

    public void setQa(List<GroupGoodInfoBean.CommonQuestion> qa) {
        this.qa = qa;
    }

    public String getShare_text() {
        return share_text;
    }

    public void setShare_text(String share_text) {
        this.share_text = share_text;
    }

    public String getShare_desc() {
        return share_desc;
    }

    public void setShare_desc(String share_desc) {
        this.share_desc = share_desc;
    }

    public String getGroup_share_text() {
        return group_share_text;
    }

    public void setGroup_share_text(String group_share_text) {
        this.group_share_text = group_share_text;
    }

    public String getGroup_share_desc() {
        return group_share_desc;
    }

    public void setGroup_share_desc(String group_share_desc) {
        this.group_share_desc = group_share_desc;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public List<String> getRules() {
        return rules;
    }

    public void setRules(List<String> rules) {
        this.rules = rules;
    }

    public int getGroup_num() {
        return group_num;
    }

    public void setGroup_num(int group_num) {
        this.group_num = group_num;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public List<GroupBean> getConduct_group() {
        return conduct_group;
    }

    public List<OrderPayDetailBean.Bposter> getRedeem() {
        return redeem;
    }

    public void setRedeem(List<OrderPayDetailBean.Bposter> redeem) {
        this.redeem = redeem;
    }

    public void setConduct_group(List<GroupBean> conduct_group) {
        this.conduct_group = conduct_group;
    }

    public String getRemind_buy() {
        return remind_buy;
    }

    public void setRemind_buy(String remind_buy) {
        this.remind_buy = remind_buy;
    }

    public GroupGoodInfoBean.MasterGraph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(GroupGoodInfoBean.MasterGraph master_graph) {
        this.master_graph = master_graph;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }
}
