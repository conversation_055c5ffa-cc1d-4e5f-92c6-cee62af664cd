package com.dep.biguo.bean;

import java.util.List;

public class OrderDetailBean {
    private int id;
    private int order_id;
    private String goods_id;
    private int users_id;
    private String order_number;
    private String total_fee;
    private int count;
    private int pay_state;
    private int state;
    private String name;
    private String phone_number;
    private String address;
    private String create_time;
    private String update_time;
    private String pay_time;
    private String pay_type;
    private String delivery_time;//发货时间
    private String receiving_time;//收货时间
    private String transport_type;
    private String transport_number;
    private String goods_name;
    private String img;
    private String transport_name;
    private String transport_time;
    private String transport_ftime;
    private String transport_context;
    private String transport_location;
    private double Unit_price;
    private List<ShopBean> goods_data;
    private int counts;
    private double transport_price;
    private String content;
    private int remain_time;
    private String preferential_price;
    private List<ExpressBean> transport_info;
    private AddressInfo address_info;
    private String type;
    private int is_group;//是否是拼团
    private int coupons_id;//优惠券ID
    private String coupon_price;//优惠券价格
    private int refund_status;//是否是退款
    private int is_discuss;//是否已评价
    private String full_price;//总计
    private String active_discount;//活动优惠
    private String member_discount;//会员优惠
    private int is_newcomers;//0不是新人活动下的单，1是新人活动下的单
    private int source_type;//笔果:0 必过:1 时刻:2
    private int skill_id;//技能证ID
    private int exam_info_status;//报考信息填写状态
    private String group_expect_tips;//取消拼团时的提示，用于挽留用户
    private int allow_cancel;//是否显示取消拼团的按钮
    private int invoice_status;//1 可开票、2 审核中、3 已驳回、4 开票中、5 已开票、6 开票失败
    private String invoice_url;//发票链接
    private String invoice_reason;//发票申请失败原因
    private List<String> pay_types;//支持的支付方式
    private int is_confirm_receipt;//0=否 1=是

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public String getFull_price() {
        return full_price;
    }

    public void setFull_price(String full_price) {
        this.full_price = full_price;
    }

    public String getActive_discount() {
        return active_discount;
    }

    public void setActive_discount(String active_discount) {
        this.active_discount = active_discount;
    }

    public String getMember_discount() {
        return member_discount;
    }

    public void setMember_discount(String member_discount) {
        this.member_discount = member_discount;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getCounts() {
        return counts;
    }

    public void setCounts(int counts) {
        this.counts = counts;
    }

    public double getTransport_price() {
        return transport_price;
    }

    public void setTransport_price(double transport_price) {
        this.transport_price = transport_price;
    }

    public List<ShopBean> getGoods_data() {
        return goods_data;
    }

    public void setGoods_data(List<ShopBean> goods_data) {
        this.goods_data = goods_data;
    }

    public int getState() {
        return state;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGoods_id() {
        return goods_id;
    }

    public void setGoods_id(String goods_id) {
        this.goods_id = goods_id;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getOrder_number() {
        return order_number;
    }

    public void setOrder_number(String order_number) {
        this.order_number = order_number;
    }

    public String getTotal_fee() {
        return total_fee;
    }

    public void setTotal_fee(String total_fee) {
        this.total_fee = total_fee;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getPay_state() {
        return pay_state;
    }

    public void setPay_state(int pay_state) {
        this.pay_state = pay_state;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone_number() {
        return phone_number;
    }

    public void setPhone_number(String phone_number) {
        this.phone_number = phone_number;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getPay_time() {
        return pay_time;
    }

    public void setPay_time(String pay_time) {
        this.pay_time = pay_time;
    }

    public String getDelivery_time() {
        return delivery_time;
    }

    public void setDelivery_time(String delivery_time) {
        this.delivery_time = delivery_time;
    }

    public String getReceiving_time() {
        return receiving_time;
    }

    public void setReceiving_time(String receiving_time) {
        this.receiving_time = receiving_time;
    }

    public String getPay_type() {
        return pay_type;
    }

    public void setPay_type(String pay_type) {
        this.pay_type = pay_type;
    }

    public String getTransport_type() {
        return transport_type;
    }

    public void setTransport_type(String transport_type) {
        this.transport_type = transport_type;
    }

    public String getTransport_number() {
        return transport_number;
    }

    public void setTransport_number(String transport_number) {
        this.transport_number = transport_number;
    }

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getTransport_name() {
        return transport_name;
    }

    public void setTransport_name(String transport_name) {
        this.transport_name = transport_name;
    }

    public String getTransport_time() {
        return transport_time;
    }

    public void setTransport_time(String transport_time) {
        this.transport_time = transport_time;
    }

    public String getTransport_ftime() {
        return transport_ftime;
    }

    public void setTransport_ftime(String transport_ftime) {
        this.transport_ftime = transport_ftime;
    }

    public String getTransport_context() {
        return transport_context;
    }

    public void setTransport_context(String transport_context) {
        this.transport_context = transport_context;
    }

    public String getTransport_location() {
        return transport_location;
    }

    public void setTransport_location(String transport_location) {
        this.transport_location = transport_location;
    }

    public double getUnit_price() {
        return Unit_price;
    }

    public void setUnit_price(double Unit_price) {
        this.Unit_price = Unit_price;
    }

    public List<ExpressBean> getTransport_info() {
        return transport_info;
    }

    public void setTransport_info(List<ExpressBean> transport_info) {
        this.transport_info = transport_info;
    }

    public int getRemain_time() {
        return remain_time;
    }

    public void setRemain_time(int remain_time) {
        this.remain_time = remain_time;
    }

    public String getPreferential_price() {
        return preferential_price;
    }

    public void setPreferential_price(String preferential_price) {
        this.preferential_price = preferential_price;
    }

    public AddressInfo getAddress_info() {
        return address_info;
    }

    public void setAddress_info(AddressInfo address_info) {
        this.address_info = address_info;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIs_group() {
        return is_group;
    }

    public void setIs_group(int is_group) {
        this.is_group = is_group;
    }

    public int getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(int refund_status) {
        this.refund_status = refund_status;
    }

    public int getIs_discuss() {
        return is_discuss;
    }

    public void setIs_discuss(int is_discuss) {
        this.is_discuss = is_discuss;
    }

    public int getCoupons_id() {
        return coupons_id;
    }

    public void setCoupons_id(int coupons_id) {
        this.coupons_id = coupons_id;
    }

    public String getCoupon_price() {
        return coupon_price;
    }

    public void setCoupon_price(String coupon_price) {
        this.coupon_price = coupon_price;
    }

    public int getExam_info_status() {
        return exam_info_status;
    }

    public void setExam_info_status(int exam_info_status) {
        this.exam_info_status = exam_info_status;
    }

    public String getGroup_expect_tips() {
        return group_expect_tips;
    }

    public void setGroup_expect_tips(String group_expect_tips) {
        this.group_expect_tips = group_expect_tips;
    }

    public int getAllow_cancel() {
        return allow_cancel;
    }

    public void setAllow_cancel(int allow_cancel) {
        this.allow_cancel = allow_cancel;
    }

    public int getInvoice_status() {
        return invoice_status;
    }

    public void setInvoice_status(int invoice_status) {
        this.invoice_status = invoice_status;
    }

    public String getInvoice_url() {
        return invoice_url;
    }

    public void setInvoice_url(String invoice_url) {
        this.invoice_url = invoice_url;
    }

    public String getInvoice_reason() {
        return invoice_reason;
    }

    public void setInvoice_reason(String invoice_reason) {
        this.invoice_reason = invoice_reason;
    }

    public List<String> getPay_types() {
        return pay_types;
    }

    public void setPay_types(List<String> pay_types) {
        this.pay_types = pay_types;
    }

    public int getIs_confirm_receipt() {
        return is_confirm_receipt;
    }

    public void setIs_confirm_receipt(int is_confirm_receipt) {
        this.is_confirm_receipt = is_confirm_receipt;
    }

    public static class AddressInfo{
        private String name;
        private String phone;
        private String address;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }
}
