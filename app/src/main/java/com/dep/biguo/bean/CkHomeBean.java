package com.dep.biguo.bean;

import java.util.List;

public class CkHomeBean extends BaseResponse<CkHomeBean>{
    private List<Banner> swipers;//顶部banner图
    private List<IconBean> icons_1;//金刚区图标
    private List<IconBean> icons_2;//拼团图标
    private List<AllVideoBean> video_course;//视频课程
    private List<ArticleBean> findings;//新闻列表

    public List<Banner> getSwipers() {
        return swipers;
    }

    public void setSwipers(List<Banner> swipers) {
        this.swipers = swipers;
    }

    public List<IconBean> getIcons_1() {
        return icons_1;
    }

    public void setIcons_1(List<IconBean> icons_1) {
        this.icons_1 = icons_1;
    }

    public List<IconBean> getIcons_2() {
        return icons_2;
    }

    public void setIcons_2(List<IconBean> icons_2) {
        this.icons_2 = icons_2;
    }

    public List<AllVideoBean> getVideo_course() {
        return video_course;
    }

    public void setVideo_course(List<AllVideoBean> video_course) {
        this.video_course = video_course;
    }

    public List<ArticleBean> getFindings() {
        return findings;
    }

    public void setFindings(List<ArticleBean> findings) {
        this.findings = findings;
    }

    public static class Banner{
        private int id;//ID
        private String img;//图片
        private String name;//名字
        private String target_url;//跳转链接
        private int type;//类别
        private String xcx_path;//小程序路径
        private int need_login;//是否需要登录

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }
    }

    public static class New{
        private int id;//新闻ID
        private String title;//标题
        private String imgone;//图片
        private int pageview;//浏览人数
        private String created_at;//创建时间
        private String target_url;//跳转链接
        private String xcx_path;//微信小程序链接

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getImgone() {
            return imgone;
        }

        public void setImgone(String imgone) {
            this.imgone = imgone;
        }

        public int getPageview() {
            return pageview;
        }

        public void setPageview(int pageview) {
            this.pageview = pageview;
        }

        public String getCreated_at() {
            return created_at;
        }

        public void setCreated_at(String created_at) {
            this.created_at = created_at;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }

    public static class Completion{
        private int done;//已做题目
        private int total;//总题目

        public int getDone() {
            return done;
        }

        public void setDone(int done) {
            this.done = done;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }
    }
}
