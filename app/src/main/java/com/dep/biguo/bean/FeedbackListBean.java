package com.dep.biguo.bean;

import java.util.List;

public class FeedbackListBean {
    private String id;
    private int type;//1安卓，2苹果，3web端
    private String img_1;//截图1
    private String img_2;//截图2
    private String img_3;//截图3
    private String content;//举报内容
    private String contact;//联系人
    private String created_at;//创建时间
    private String nickname;//昵称
    private String avatar;//头像
    private List<Reply> feedback_reply;//回复列表


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getImg_1() {
        return img_1;
    }

    public void setImg_1(String img_1) {
        this.img_1 = img_1;
    }

    public String getImg_2() {
        return img_2;
    }

    public void setImg_2(String img_2) {
        this.img_2 = img_2;
    }

    public String getImg_3() {
        return img_3;
    }

    public void setImg_3(String img_3) {
        this.img_3 = img_3;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<Reply> getFeedback_reply() {
        return feedback_reply;
    }

    public void setFeedback_reply(List<Reply> feedback_reply) {
        this.feedback_reply = feedback_reply;
    }

    public static class Reply{
        private int id;
        private String feedback_id;//被回复的记录的ID
        private String content;//回复的内容
        private int is_del;//是否已删除
        private int users_id;//0表示管理员回复，非0表示反馈者回复
        private String created_at;//
        private String nickname;//昵称
        private String avatar;//头像


        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getFeedback_id() {
            return feedback_id;
        }

        public void setFeedback_id(String feedback_id) {
            this.feedback_id = feedback_id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getIs_del() {
            return is_del;
        }

        public void setIs_del(int is_del) {
            this.is_del = is_del;
        }

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public String getCreated_at() {
            return created_at;
        }

        public void setCreated_at(String created_at) {
            this.created_at = created_at;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }
    }
}
