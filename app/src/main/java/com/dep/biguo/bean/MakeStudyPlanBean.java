package com.dep.biguo.bean;

import java.util.List;

public class MakeStudyPlanBean {
    private int plan_day;//当前天数
    private int max_day;//总天数
    private int min_day;//最小天数
    private List<PlanBean> list;

    public int getPlan_day() {
        return plan_day;
    }

    public void setPlan_day(int plan_day) {
        this.plan_day = plan_day;
    }

    public int getMax_day() {
        return max_day;
    }

    public void setMax_day(int max_day) {
        this.max_day = max_day;
    }

    public int getMin_day() {
        return min_day;
    }

    public void setMin_day(int min_day) {
        this.min_day = min_day;
    }

    public List<PlanBean> getList() {
        return list;
    }

    public void setList(List<PlanBean> list) {
        this.list = list;
    }

    public static class PlanBean {
        private String code;
        private String name;
        private String exam_time;
        private int is_formulate;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getExam_time() {
            return exam_time;
        }

        public void setExam_time(String exam_time) {
            this.exam_time = exam_time;
        }

        public int getIs_formulate() {
            return is_formulate;
        }

        public void setIs_formulate(int is_formulate) {
            this.is_formulate = is_formulate;
        }
    }
}
