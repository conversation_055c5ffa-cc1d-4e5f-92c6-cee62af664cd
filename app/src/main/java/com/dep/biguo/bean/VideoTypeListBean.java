package com.dep.biguo.bean;

public class VideoTypeListBean {
    private int buy_count;//购买份数
    private int is_buy;//是否购买
    private String price;//价格
    private String group_price;//拼团价
    private String member_group_price;//笔果折扣卡拼团价
    private String type_name;//课程名称
    private String code;//课程编码
    private int product_id;
    private String type;//视频类型
    private String video_title;
    private String img;//图片banner
    private int courses_id;//课程id
    private int take_time;//已学时间 （秒）
    private String newcomers_price;//新人价格
    private int is_newcomers;//1是新人，0不是新人
    private int source_type;//1时刻套餐2必过套餐


    public int getBuy_count() {
        return buy_count;
    }

    public void setBuy_count(int buy_count) {
        this.buy_count = buy_count;
    }

    public int getIs_buy() {
        return is_buy;
    }

    public void setIs_buy(int is_buy) {
        this.is_buy = is_buy;
    }

    public int getCourses_id() {
        return courses_id;
    }

    public void setCourses_id(int courses_id) {
        this.courses_id = courses_id;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public String getType_name() {
        return type_name;
    }

    public void setType_name(String type_name) {
        this.type_name = type_name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVideo_title() {
        return video_title;
    }

    public void setVideo_title(String video_title) {
        this.video_title = video_title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getTake_time() {
        return take_time;
    }

    public void setTake_time(int take_time) {
        this.take_time = take_time;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }
}
