package com.dep.biguo.bean;

public class PracticeCommentOperateBean {
    private int icon;
    private String text;
    private String type;

    public PracticeCommentOperateBean(int icon, String text, String type) {
        this.icon = icon;
        this.text = text;
        this.type = type;
    }

    public int getIcon() {
        return icon;
    }

    public void setIcon(int icon) {
        this.icon = icon;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
