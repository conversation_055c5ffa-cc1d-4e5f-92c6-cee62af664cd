package com.dep.biguo.bean;

public class ClassRoomBean {

    /**
     * id : 13
     * name : 行政管理学（广东）
     * price : 4980.00
     * img : http://img.biguotk.com/avatars/2orQrRjWWlW1kbELFKTT9Ey9oqM0TIJl4bcd4yzo.jpeg
     * count : 2
     */

    private int id;
    private String name;
    private String price;
    private String img;
    private int count;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
