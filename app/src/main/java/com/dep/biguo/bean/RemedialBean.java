package com.dep.biguo.bean;

import java.util.List;

public class RemedialBean extends BaseResponse<List<RemedialBean>> {
    //     "id": 52,
//             "name": "广开辅导班",
//             "url": null,
//             "img": "https://file.biguotk.com/img/20201209135530033b2ecbd3ee83a6ae8b223c59fa716e.png",
//             "is_show": 1,
//             "create_time": "2020-12-09 13:55:41",
//             "update_time": "2020-12-09 13:57:26",
//             "type": 9,
//             "skill_type": 0
    private int id;
    private String name;
    private String url;
    private String img;
    private int is_show;
    private String create_time;
    private String update_time;
    private int type;
    private int skill_type;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getIs_show() {
        return is_show;
    }

    public void setIs_show(int is_show) {
        this.is_show = is_show;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSkill_type() {
        return skill_type;
    }

    public void setSkill_type(int skill_type) {
        this.skill_type = skill_type;
    }
}
