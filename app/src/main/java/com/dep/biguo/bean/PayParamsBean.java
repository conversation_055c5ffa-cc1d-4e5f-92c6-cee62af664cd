package com.dep.biguo.bean;

import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.HashMap;
import java.util.Map;

/**crtl+左键 {@link PayUtils} 查看所有支付类型
 *
 */
public class PayParamsBean {
    //公共参数
    public static final String TYPE = "type";//商品类型
    public static final String PAY_TYPE = "pay_type";//支付方式
    public static final String COUPON_ID = "coupon_id";//优惠券ID
    public static final String LAYER_ID = "layer_id";//层次，1自考本科、2自考专科、3技能证书、6教师资格证、8会计证、71专科、76本科、96高升本、111人力资源管理师、114建造师、118全国英语等级考试，自考可以不传
    public static final String ADULT_PROFESSIONS_ID = "adult_professions_id";//成考专业ID

    /**VIP题库，押密题库，预订押密，视频课程
     * {@link PayUtils#VIP,PayUtils#YAMI,PayUtils#YAMI_RESERVE,PayUtils#VIDEO}
     */
    public static final String CODE = "code";
    //GROUP_ID和R_ID在拼团时需要传递（单独购买结算时传递过去不影响）
    public static final String IS_GROUP = "is_group";//0单独购买, 1发起拼团
    public static final String GROUP_ID = "group_id";//发起拼团传0，加入拼团传group_id
    public static final String R_ID = "r_id";//单独购买或发起拼团传0，加入拼团传团长的ID
    public static final String NEWCOMERS = "newcomers";//是否是新用户
    public static final String IS_REPLENISH = "is_replenish";//是否需要产品升级（后台支持用户选择产品升级的功能，前端暂不支持）
    //必过视频需要（结算时传递过去不影响）
    public static final String PRODUCT_ID = "product_id";
    public static final String VIDEO_TYPE = "video_type";//老视频（前端视为精讲视频，后端需要区分）为0，必过精讲视频为1，必过串讲视频为2
    //技能证必传的参数
    public static final String SKILL_ID = "skill_id";

    /**历年真题
     * {@link PayUtils#REAL_PAPER}
     */
    public static final String EXAMS_REAL_PAPER_ID = "exams_real_paper_id";

    /**超级会员
     * {@link PayUtils#SUPER_VIP}
     */
    public static final String PROFESSIONS_ID = "professions_id";//专业ID
    public static final String LAYER = "layer";//专业所属本科还是专科

    /**笔果折扣卡
     * {@link PayUtils#MEMBERSHIP}
     */
    public static final String EXTEND = "extend";//0：购买，1：续费

    /**自习室抵扣卡
     * {@link PayUtils#MEMBERSHIP}
     */
    public static final String CARD_ID = "card_id";//抵扣卡类型， 1=小时卡 2=月卡 3=年卡

    /**VIP课堂
     * {@link PayUtils#CLASS_ROOM}
     */
    public static final String CLASSROOM_ID = "classroom_id";
    public static final String SOURCE_TYPE = "source_type";

    /**充值果币
     * {@link PayUtils#FRUIT_COIN}
     */
    public static final String PRICE = "price";

    /**缴纳学费
     * {@link PayUtils#TUITION}
     */
    public static final String REAL_NAME = "real_name";//真实姓名
    public static final String MOBILE = "mobile";//手机号
    public static final String MARK = "mark";//备注
    public static final String COST = "cost";//支付金额
    public static final String TOTAL_FEE = "total_fee";//学费总金额
    public static final String FORM_ID = "form_id";//报读形式id
    public static final String OPTION_TYPE = "option_type";//缴纳选项类型 1：全款 2：分期缴纳 3：分次缴纳
    public static final String OPTION_FREQUENCY = "option_frequency";//缴纳选项类型（分期/分次）

    /**购买图书
     * {@link PayUtils#BOOK}
     */
    public static final String GOODS_INFO = "goods_info";
    public static final String GOODS_IDS = "goods_id";
    public static final String COUNT = "count";
    public static final String CONTENT = "content";

    /**章节训练
     * {@link PayUtils#CHAPTER}
     */
    public static final String CHAPTER_LIST = "chapter_list";//章节id，支持多选，以"_"连接

    /**首页轮播图的英语二(首页轮播图的英语二与视频的英语二不是同一个商品，视频的英语二属于视频课程)
     *{@link PayUtils#HOME_ENGLISH_TWO}
     */
    public static final String BUY_TYPE = "buy_type";//1=普通班2=零基础3=零基础至尊班

    /**网络助学
     *
     */
    public static final String IS_BUNDLE = "is_bundle";//是否选中了押题包，购买传1，不买传0


    private Map<String,Object> paramsMap;

    private void addPublicParams(){
        if(Constant.ZK.equals(UserCache.getAppType())) {
            paramsMap.put(LAYER_ID, UserCache.getProfession().getLayer());
            paramsMap.put(PROFESSIONS_ID, UserCache.getProfession().getId());

        }else if(Constant.CK.equals(UserCache.getAppType())) {
            paramsMap.put(LAYER_ID, UserCache.getProfession().getLayer());
            paramsMap.put(PROFESSIONS_ID, UserCache.getProfession().getId());
            paramsMap.put(ADULT_PROFESSIONS_ID, UserCache.getProfession().getAdult_professions_id());

        }else if(Constant.JSZ.equals(UserCache.getAppType())) {
            paramsMap.put(LAYER_ID, 6);
            //paramsMap.put(PROFESSIONS_ID, UserHelper.getJSZGrade().getId());
        }
    }

    public static PayParamsBean init(String json){
        PayParamsBean paramsBean = new PayParamsBean();
        paramsBean.paramsMap = new Gson().fromJson(json,new TypeToken<Map<String,Object>>(){}.getType());
        paramsBean.addPublicParams();
        return paramsBean;
    }

    public static PayParamsBean init(){
        PayParamsBean paramsBean = new PayParamsBean();
        paramsBean.paramsMap = new HashMap<>();
        paramsBean.addPublicParams();
        return paramsBean;
    }

    public void put(String key, Object value){
        paramsMap.put(key,value);
    }

    public <T> T getParams(String key){
        return (T) paramsMap.get(key);
    }

    public Map<String,Object> getParamsMap(){
        return paramsMap;
    }

    public void destroy(){
        paramsMap.clear();
        paramsMap = null;
    }
}
