package com.dep.biguo.bean;

public class PracticeContinueBean {

    //is_continue为0，没有历史记录 is_continue为1，有历史记录，APP提示用户是否延续上次答题记录

    private int page;
    private int end_id;
    private int is_continue;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getEnd_id() {
        return end_id;
    }

    public void setEnd_id(int end_id) {
        this.end_id = end_id;
    }

    public int getIs_continue() {
        return is_continue;
    }

    public void setIs_continue(int is_continue) {
        this.is_continue = is_continue;
    }
}
