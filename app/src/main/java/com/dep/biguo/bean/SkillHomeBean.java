package com.dep.biguo.bean;

import java.util.List;

public class SkillHomeBean {
    private List<String> classs;
    private List<Banner> swipers;

    public List<String> getClasss() {
        return classs;
    }

    public void setClasss(List<String> classs) {
        this.classs = classs;
    }

    public List<Banner> getSwipers() {
        return swipers;
    }

    public void setSwipers(List<Banner> swipers) {
        this.swipers = swipers;
    }

    public static class Banner{
        private String img;//图片
        private String target_url;//跳转链接
        private String xcx_path;//小程序路径

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }
}
