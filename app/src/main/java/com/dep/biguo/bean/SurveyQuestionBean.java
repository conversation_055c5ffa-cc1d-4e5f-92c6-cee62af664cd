package com.dep.biguo.bean;

import java.util.ArrayList;
import java.util.List;

public class SurveyQuestionBean {
    private List<SurveyBean> list;
    private String xcx_path;
    private String target_url;
    private String img;
    private ExclusiveTeacher exclusive_teacher;

    public List<SurveyBean> getList() {
        return list;
    }

    public void setList(List<SurveyBean> list) {
        this.list = list;
    }

    public String getXcx_path() {
        return xcx_path;
    }

    public void setXcx_path(String xcx_path) {
        this.xcx_path = xcx_path;
    }

    public String getTarget_url() {
        return target_url;
    }

    public void setTarget_url(String target_url) {
        this.target_url = target_url;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public ExclusiveTeacher getExclusive_teacher() {
        return exclusive_teacher;
    }

    public void setExclusive_teacher(ExclusiveTeacher exclusive_teacher) {
        this.exclusive_teacher = exclusive_teacher;
    }

    public class ExclusiveTeacher{
        private String text;
        private String avatar;
        private String nickname;
        private String affiliation_text;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAffiliation_text() {
            return affiliation_text;
        }

        public void setAffiliation_text(String affiliation_text) {
            this.affiliation_text = affiliation_text;
        }
    }
}
