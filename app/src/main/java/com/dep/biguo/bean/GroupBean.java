package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.util.List;

public class GroupBean implements MultiItemEntity {

    private long sec;
    private int count;
    private String group_id;
    private String type;
    private String code;
    private String name;
    private List<GroupUserBean> users_info;

    /**
     * 头部
     **/
    private int itemType;
    private String headName;
    private boolean isEmpty;

    public GroupBean(int itemType, String headName, boolean isEmpty) {
        this.itemType = itemType;
        this.headName = headName;
        this.isEmpty = isEmpty;
    }

    public boolean isEmpty() {
        return isEmpty;
    }

    public void setEmpty(boolean empty) {
        isEmpty = empty;
    }

    public List<GroupUserBean> getUsers_info() {
        return users_info;
    }

    public void setUsers_info(List<GroupUserBean> users_info) {
        this.users_info = users_info;
    }

    public long getSec() {
        return sec;
    }

    public void setSec(long sec) {
        this.sec = sec;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    public String getHeadName() {
        return headName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
