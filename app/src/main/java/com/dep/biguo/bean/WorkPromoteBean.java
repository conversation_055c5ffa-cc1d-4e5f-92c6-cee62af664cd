package com.dep.biguo.bean;

import java.util.List;

public class WorkPromoteBean {
    private int skill_id;//技能证ID
    private int product_id;
    private String name;//名称
    private int cert_type;//0自考1成考2技能证9职场提升
    private int class_hour;//课时
    private String image;//封面图
    private String price;//单独购买价格
    private String group_price;//拼团价
    private String member_price;//单独购买会员价
    private String member_group_price;//拼团购买会员价
    private int valid_type;//有效期类型 1固定日期0固定天数
    private int valid_day;//有效期天数
    private String valid_time;//有效期截止日期
    private int buy_count;//购买总数
    private int is_relearn;//是否可重学，1是0否
    private String type;//自考video，成考adult_video，技能证skill_video，职场提升vocation_video
    private int take_time;//学习时间
    private int is_buy;//是否购买，0未购买，1已购买
    private int source_type;//视频类型，1必过，2时刻
    private List<String> tags;//标签

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }

    public int getClass_hour() {
        return class_hour;
    }

    public void setClass_hour(int class_hour) {
        this.class_hour = class_hour;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public int getValid_type() {
        return valid_type;
    }

    public void setValid_type(int valid_type) {
        this.valid_type = valid_type;
    }

    public int getValid_day() {
        return valid_day;
    }

    public void setValid_day(int valid_day) {
        this.valid_day = valid_day;
    }

    public String getValid_time() {
        return valid_time;
    }

    public void setValid_time(String valid_time) {
        this.valid_time = valid_time;
    }

    public int getBuy_count() {
        return buy_count;
    }

    public void setBuy_count(int buy_count) {
        this.buy_count = buy_count;
    }

    public int getIs_relearn() {
        return is_relearn;
    }

    public void setIs_relearn(int is_relearn) {
        this.is_relearn = is_relearn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getTake_time() {
        return take_time;
    }

    public void setTake_time(int take_time) {
        this.take_time = take_time;
    }

    public int getIs_buy() {
        return is_buy;
    }

    public void setIs_buy(int is_buy) {
        this.is_buy = is_buy;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }
}
