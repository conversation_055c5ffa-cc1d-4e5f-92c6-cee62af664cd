package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.MultiItemEntity;

public class VideoGoupCourseBean implements MultiItemEntity {
// "id": 5,
//         "video_courses_name": "现代人员测评",
//         "img": "https:\/\/file.biguotk.com\/img\/
//         "code": "00463",
//         "new_img": "https:\/\/file.biguotk.com\/i
//         "is_vip": 0,
//         "group_id": 0,
//         "is_pay": 1,
//         "is_pay_group": 0,
//         "group_price": "108"

    private int id;
    private String video_courses_name;
    private String img;
    private String code;
    private String new_img;
    private int is_vip;
    private int group_id;
    private int product_id;
    private int video_type;
    private int is_pay;
    private int is_pay_group;
    private String group_price;
    private String member_group_price;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getVideo_courses_name() {
        return video_courses_name;
    }

    public void setVideo_courses_name(String video_courses_name) {
        this.video_courses_name = video_courses_name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNew_img() {
        return new_img;
    }

    public void setNew_img(String new_img) {
        this.new_img = new_img;
    }

    public int getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(int is_vip) {
        this.is_vip = is_vip;
    }

    public int getGroup_id() {
        return group_id;
    }

    public void setGroup_id(int group_id) {
        this.group_id = group_id;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getVideo_type() {
        return video_type;
    }

    public void setVideo_type(int video_type) {
        this.video_type = video_type;
    }

    public int getIs_pay_group() {
        return is_pay_group;
    }

    public void setIs_pay_group(int is_pay_group) {
        this.is_pay_group = is_pay_group;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    @Override
    public int getItemType() {
        return 3;
    }
}
