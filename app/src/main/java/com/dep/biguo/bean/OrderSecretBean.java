package com.dep.biguo.bean;

import java.util.List;

public class OrderSecretBean {
    private String price;//价格
    private String member_price;//折扣价
    private int count;//已参与人数
    private int success_num;//成团人数
    private int is_pay;//是否已支付
    private String cover;//封面
    private int reserve_status;//押密预定状态， 0=待预订（未支付），1=已预订（未成团），2=制作中（已成团），3=已开通
    private List<Rule> rules;

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getSuccess_num() {
        return success_num;
    }

    public void setSuccess_num(int success_num) {
        this.success_num = success_num;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public int getReserve_status() {
        return reserve_status;
    }

    public void setReserve_status(int reserve_status) {
        this.reserve_status = reserve_status;
    }

    public List<Rule> getRules() {
        return rules;
    }

    public void setRules(List<Rule> rules) {
        this.rules = rules;
    }

    public static class Rule{
        private String title; //标题
        private String contents;//内容
        private String[] rule_red;//加红
        private String[] under_line;//下划线

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContents() {
            return contents;
        }

        public void setContents(String contents) {
            this.contents = contents;
        }

        public String[] getRule_red() {
            return rule_red;
        }

        public void setRule_red(String[] rule_red) {
            this.rule_red = rule_red;
        }

        public String[] getUnder_line() {
            return under_line;
        }

        public void setUnder_line(String[] under_line) {
            this.under_line = under_line;
        }
    }
}
