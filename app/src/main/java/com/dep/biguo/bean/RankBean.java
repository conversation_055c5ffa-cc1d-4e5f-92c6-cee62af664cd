package com.dep.biguo.bean;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/11
 * @Description:
 */
public class RankBean {

    /**
     * sort : 1
     * users_id : 26584
     * avatar : https://www.biguotk.com/avatars/<EMAIL>
     * nickname : 186****9786
     * count : 489
     */

    private int sort;
    private int users_id;
    private String avatar;
    private String nickname;
    private int count;

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
