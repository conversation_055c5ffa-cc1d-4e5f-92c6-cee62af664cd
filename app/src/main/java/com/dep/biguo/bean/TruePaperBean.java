package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/4
 * @Description:
 */
public class TruePaperBean extends BaseResponse<List<TruePaperBean>> {

    private int id;
    private String name;
    private String price;
    private int share_or_pay;
    private int count;
    private int answer_count;
    private int current_position;
    private int is_show;
    private int is_new;
    private int version;
    private long record_expire_time;//有效期

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getIs_show() {
        return is_show;
    }

    public void setIs_show(int is_show) {
        this.is_show = is_show;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }

    public int getAnswer_count() {
        return answer_count;
    }

    public void setAnswer_count(int answer_count) {
        this.answer_count = answer_count;
    }

    public int getCurrent_position() {
        return current_position;
    }

    public void setCurrent_position(int current_position) {
        this.current_position = current_position;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getShare_or_pay() {
        return share_or_pay;
    }

    public void setShare_or_pay(int share_or_pay) {
        this.share_or_pay = share_or_pay;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }
}
