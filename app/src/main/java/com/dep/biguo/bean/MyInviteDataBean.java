package com.dep.biguo.bean;

import java.util.List;

public class MyInviteDataBean extends BaseResponse<List<InvitorBean>> {
    private float total_earnings_amount;
    private int people;
    private List<InvitorBean> invite_list;
    private List<IncomeBean> earnings_list;
    private List<Rank> rank_list;
    private InviteTaskDetails invite_task_details;


    public float getTotal_earnings_amount() {
        return total_earnings_amount;
    }

    public void setTotal_earnings_amount(float total_earnings_amount) {
        this.total_earnings_amount = total_earnings_amount;
    }

    public int getPeople() {
        return people;
    }

    public void setPeople(int people) {
        this.people = people;
    }

    public List<InvitorBean> getInvite_list() {
        return invite_list;
    }

    public void setInvite_list(List<InvitorBean> invite_list) {
        this.invite_list = invite_list;
    }

    public List<IncomeBean> getEarnings_list() {
        return earnings_list;
    }

    public void setEarnings_list(List<IncomeBean> earnings_list) {
        this.earnings_list = earnings_list;
    }

    public List<Rank> getRank_list() {
        return rank_list;
    }

    public void setRank_list(List<Rank> rank_list) {
        this.rank_list = rank_list;
    }

    public InviteTaskDetails getInvite_task_details() {
        return invite_task_details;
    }

    public void setInvite_task_details(InviteTaskDetails invite_task_details) {
        this.invite_task_details = invite_task_details;
    }

    public static class Rank{
        private int users_id;
        private String avatar;
        private String username;
        private String withdrawal_amount;

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getWithdrawal_amount() {
            return withdrawal_amount;
        }

        public void setWithdrawal_amount(String withdrawal_amount) {
            this.withdrawal_amount = withdrawal_amount;
        }
    }
}
