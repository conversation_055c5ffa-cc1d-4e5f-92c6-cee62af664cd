package com.dep.biguo.bean;

public class GroupCommentBean {
//                    "id": 4,
//                            "img": "https:\/\/thirdwx.qlogo.cn\/mmopen\/vi_32\/j1LmYTicy5iam0ygE4Lsw52WvwVJWtqKiaU4DA9nzQ2
//                            7alVTvsUJ4Nzwv60qoITm3n3v0hypM2IgfE1spLXMubuXg\/132",
//            "name": "迷途路上的迷途的迷途",
//            "code": "00159",
//            "type": 1,
//            "content": "这个VIP题库好棒啊",
//            "is_del": 0,
//            "is_audit": 1,
//            "create_time": null,
//            "update_time": null

    private int id;
    private int users_id;
    private String avatar;
    private String name;
    private String professions_name;
    private String created_at;
    private String comment;
    private int recommend;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProfessions_name() {
        return professions_name;
    }

    public void setProfessions_name(String professions_name) {
        this.professions_name = professions_name;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public int getRecommend() {
        return recommend;
    }

    public void setRecommend(int recommend) {
        this.recommend = recommend;
    }
}
