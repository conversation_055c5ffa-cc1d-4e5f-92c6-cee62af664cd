package com.dep.biguo.bean;

import java.util.List;

public class MyVideoBean {
    private List<Classroom> classroom_list;
    private List<Video> video_list;

    public List<Classroom> getClassroom_list() {
        return classroom_list;
    }

    public void setClassroom_list(List<Classroom> classroom_list) {
        this.classroom_list = classroom_list;
    }

    public List<Video> getVideo_list() {
        return video_list;
    }

    public void setVideo_list(List<Video> video_list) {
        this.video_list = video_list;
    }

    public class Classroom{
        private int classroom_id;//0不是VIP课堂，大于0是VIP课堂
        private String name;//课程名称
        private String img;
        private String get_time;//获取时间
        private String codes; //课程编码
        private String expire;//有效期

        public int getClassroom_id() {
            return classroom_id;
        }

        public void setClassroom_id(int classroom_id) {
            this.classroom_id = classroom_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getGet_time() {
            return get_time;
        }

        public void setGet_time(String get_time) {
            this.get_time = get_time;
        }

        public String getCodes() {
            return codes;
        }

        public void setCodes(String codes) {
            this.codes = codes;
        }

        public String getExpire() {
            return expire;
        }

        public void setExpire(String expire) {
            this.expire = expire;
        }
    }

    public class Video {
        private String img;
        private String code; //课程编码
        private String name;//课程名称
        private int num;//题目数量
        private String expire;//有效期
        private String get_time;//获取时间
        private int passed;//课程是否及格，排序依据
        private int video_type;//视频类型
        private String video_type_name;//视频类型
        private int product_id;//必过id
        private int classroom_id;//0不是VIP课堂，大于0是VIP课堂
        private int source_type;//0笔果视频1时刻套餐2必过套餐
        private int cert_type;//0自考，1成考，2技能证，9学历提升
        private int skill_id;//技能证ID
        private String type;//商品类型：技能证:skill_video，职场提升:vocation_video

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public String getExpire() {
            return expire;
        }

        public void setExpire(String expire) {
            this.expire = expire;
        }

        public String getGet_time() {
            return get_time;
        }

        public void setGet_time(String get_time) {
            this.get_time = get_time;
        }

        public int getPassed() {
            return passed;
        }

        public void setPassed(int passed) {
            this.passed = passed;
        }

        public int getVideo_type() {
            return video_type;
        }

        public void setVideo_type(int video_type) {
            this.video_type = video_type;
        }

        public String getVideo_type_name() {
            return video_type_name;
        }

        public void setVideo_type_name(String video_type_name) {
            this.video_type_name = video_type_name;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public int getClassroom_id() {
            return classroom_id;
        }

        public void setClassroom_id(int classroom_id) {
            this.classroom_id = classroom_id;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getCert_type() {
            return cert_type;
        }

        public void setCert_type(int cert_type) {
            this.cert_type = cert_type;
        }

        public int getSkill_id() {
            return skill_id;
        }

        public void setSkill_id(int skill_id) {
            this.skill_id = skill_id;
        }
    }
}
