package com.dep.biguo.bean;

import java.util.List;

public class ClassTeacherEvaluateBean {
    private int teacher_id;//老师ID
    private String teacher_name;///老师昵称
    private String teacher_img;//老师头像
    private String teacher_mobile;//微信/手机号
    private int product_id;//产品ID
    private int hasClassTeacher;//是否已评价0否1是
    private String content;//评价内容
    private List<String> file_urls;//图片
    private int is_replace;//是否更换老师申请0否1是
    private int score_grade;//评分等级0差评1中评（默认）2好评

    public int getTeacher_id() {
        return teacher_id;
    }

    public void setTeacher_id(int teacher_id) {
        this.teacher_id = teacher_id;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }

    public String getTeacher_img() {
        return teacher_img;
    }

    public void setTeacher_img(String teacher_img) {
        this.teacher_img = teacher_img;
    }

    public String getTeacher_mobile() {
        return teacher_mobile;
    }

    public void setTeacher_mobile(String teacher_mobile) {
        this.teacher_mobile = teacher_mobile;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getHasClassTeacher() {
        return hasClassTeacher;
    }

    public void setHasClassTeacher(int hasClassTeacher) {
        this.hasClassTeacher = hasClassTeacher;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getFile_urls() {
        return file_urls;
    }

    public void setFile_urls(List<String> file_urls) {
        this.file_urls = file_urls;
    }

    public int getIs_replace() {
        return is_replace;
    }

    public void setIs_replace(int is_replace) {
        this.is_replace = is_replace;
    }

    public int getScore_grade() {
        return score_grade;
    }

    public void setScore_grade(int score_grade) {
        this.score_grade = score_grade;
    }
}
