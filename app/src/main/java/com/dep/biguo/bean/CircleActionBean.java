package com.dep.biguo.bean;

/**触发圈子的交互时，使用该类进行通知。
 * <p color=#fff>因为包含的层级较多，EventBus传递一个对象不够用，需要这样一个封装类进行传递</p>
 *
 */
public class CircleActionBean {
    public static final int MOMENT_GOOD = 0;//动态点赞操作
    public static final int COMMENT_GOOD = 1;//评论点赞操作
    public static final int SHARE = 2;//分享/转发操作
    public static final int COMMENT = 3;//评论操作
    public static final int MOMENT_DELETE = 4;//删除操作
    public static final int COMMENT_DELETE = 5;//删除操作

    private CircleBean.Moment moment;//动态
    private CircleCommentBean comment;//评论
    private CircleCommentBean childComment;//子评论
    private int action;//本次操作

    public CircleActionBean(CircleBean.Moment moment, CircleCommentBean comment, CircleCommentBean childComment, int action) {
        this.moment = moment;
        this.comment = comment;
        this.childComment = childComment;
        this.action = action;
    }

    public CircleBean.Moment getMoment() {
        return moment;
    }

    public void setMoment(CircleBean.Moment moment) {
        this.moment = moment;
    }

    public CircleCommentBean getComment() {
        return comment;
    }

    public void setComment(CircleCommentBean comment) {
        this.comment = comment;
    }

    public CircleCommentBean getChildComment() {
        return childComment;
    }

    public void setChildComment(CircleCommentBean childComment) {
        this.childComment = childComment;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }
}
