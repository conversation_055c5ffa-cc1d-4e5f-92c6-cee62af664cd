package com.dep.biguo.bean;

public class InviteTaskRewardBean {
    private String title; //页面标题
    private String subtitle;//副标题
    private String rules; //规则
    private IntegralExchangeBean.Style styles;//规则中的样式
    private int opend_all; //是否当前专业已全部永久开通 0否 1是
    private String image;//弹窗的缩略图
    private IntegralExchangeBean.Master_graph master_graph;//封面图
    private String describe;//商品详情富文本
    private int is_exchange;//兑换状态 0=进行中， 1=已解锁， (待)去兑换，2=任务完成
    private int is_activity;//是否在活动期内 0=否，1=是
    private String type;//商品类型

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public IntegralExchangeBean.Style getStyles() {
        return styles;
    }

    public void setStyles(IntegralExchangeBean.Style styles) {
        this.styles = styles;
    }

    public int getOpend_all() {
        return opend_all;
    }

    public void setOpend_all(int opend_all) {
        this.opend_all = opend_all;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public IntegralExchangeBean.Master_graph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(IntegralExchangeBean.Master_graph master_graph) {
        this.master_graph = master_graph;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public int getIs_exchange() {
        return is_exchange;
    }

    public void setIs_exchange(int is_exchange) {
        this.is_exchange = is_exchange;
    }

    public int getIs_activity() {
        return is_activity;
    }

    public void setIs_activity(int is_activity) {
        this.is_activity = is_activity;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
