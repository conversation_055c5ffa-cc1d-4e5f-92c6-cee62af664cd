package com.dep.biguo.bean;

import java.util.List;

public class CommentBean {
    private int count;//回复的总数量
    private int have_more;//还可以加载的数量
    private int offset;//已加载多少条
    private int ceiling_id;//所属评论的ID
    private List<Parses> parses;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getHave_more() {
        return have_more;
    }

    public void setHave_more(int have_more) {
        this.have_more = have_more;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<Parses> getParses() {
        return parses;
    }

    public void setParses(List<Parses> parses) {
        this.parses = parses;
    }

    public int getCeiling_id() {
        return ceiling_id;
    }

    public void setCeiling_id(int ceiling_id) {
        this.ceiling_id = ceiling_id;
    }


    public static class Parses {
        private int id;//评论ID
        private int exams_id;//题目ID
        private String code;//科目代码
        private String mainType;//题目类型，参考PracticeHelper类题目类型
        private int users_id;//用户ID
        private String name;//用户昵称
        private String avatar;//用户头像
        private String content;//评论内容
        private int parent_id;//评论的id
        private int liked;//是否被点赞
        private int like_;//点赞数量
        private int delable;//评论是否可以删除，0不可删除，1可删除
        private String parent_name;//被回复的人的名字
        private int ceiling_id;//为0，则是回复楼主
        private String province;//评论发布时的所在省份
        private String create_time;//评论发布日期
        private CommentBean tree;

        private boolean isLocalAdd;//是否是插入时本地生成的记录，本地生成的记录需要设置为true

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getExams_id() {
            return exams_id;
        }

        public void setExams_id(int exams_id) {
            this.exams_id = exams_id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMainType() {
            return mainType;
        }

        public void setMainType(String mainType) {
            this.mainType = mainType;
        }

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getParent_id() {
            return parent_id;
        }

        public void setParent_id(int parent_id) {
            this.parent_id = parent_id;
        }

        public int getLiked() {
            return liked;
        }

        public void setLiked(int liked) {
            this.liked = liked;
        }

        public int getLike_() {
            return like_;
        }

        public void setLike_(int like_) {
            this.like_ = like_;
        }

        public String getParent_name() {
            return parent_name;
        }

        public int getDelable() {
            return delable;
        }

        public void setDelable(int delable) {
            this.delable = delable;
        }

        public void setParent_name(String parent_name) {
            this.parent_name = parent_name;
        }

        public int getCeiling_id() {
            return ceiling_id;
        }

        public void setCeiling_id(int ceiling_id) {
            this.ceiling_id = ceiling_id;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCreate_time() {
            return create_time;
        }

        public void setCreate_time(String create_time) {
            this.create_time = create_time;
        }

        public CommentBean getTree() {
            return tree;
        }

        public void setTree(CommentBean tree) {
            this.tree = tree;
        }

        public boolean isLocalAdd() {
            return isLocalAdd;
        }

        public void setLocalAdd(boolean localAdd) {
            isLocalAdd = localAdd;
        }
    }
}
