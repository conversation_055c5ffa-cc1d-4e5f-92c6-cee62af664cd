package com.dep.biguo.bean;

public class PushBean {
    public static final String ORDER_PAGE = "order";//订单类型的通知消息
    public static final String INFO_CENTER = "info_center";//消息中心的通知消息
    public static final String COURSE = "course";//课程相关的通知消息
    public static final String FRUITCOIN = "fruitcoin";//果币的通知消息
    public static final String INTEGRAL = "integral";//积分的通知消息
    public static final String COUPONS = "coupons";//优惠券的通知消息
    public static final String URL = "url";//H5的通知消息
    public static final String FEED_BACK = "feedback";//意见反馈通知消息
    public static final String TUITION = "tuition";//收到电子协议签署通知消息

    public String page;//取值：ORDER_PAGE
    public String type;//公共属性，可能为int类型，也可能为String类型
    /*订单类型独有属性*/
    public int order_id;//订单ID
    /*H5通知独有属性*/
    public String url;//订单所属类型，1

}
