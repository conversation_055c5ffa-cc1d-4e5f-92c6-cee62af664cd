package com.dep.biguo.bean;

public class LiveClandestineBean {
    private String name;//名称
    private String type;//类型（押密yami，视频video2，高频考点hf， 模拟试卷simu）
    private int status;//考前押密的开通状态
    private int count;//视频章节数量、高频考点的总提题数、模拟试卷的总套数
    private int cal_nums;//高频考点的可答题数量
    private int answered;//高频考点的已答题数量
    private int product_id;//视频需要这个参数
    private int source_type;//1时刻套餐2必过套餐,视频需要这个参数
    private int simu_paper_done;//模拟试卷已做套数
    private long version;//题库版本号
    private long record_expire_time;//有效期


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCal_nums() {
        return cal_nums;
    }

    public void setCal_nums(int cal_nums) {
        this.cal_nums = cal_nums;
    }

    public int getAnswered() {
        return answered;
    }

    public void setAnswered(int answered) {
        this.answered = answered;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getSimu_paper_done() {
        return simu_paper_done;
    }

    public void setSimu_paper_done(int simu_paper_done) {
        this.simu_paper_done = simu_paper_done;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }
}
