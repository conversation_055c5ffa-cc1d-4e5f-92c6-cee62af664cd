package com.dep.biguo.bean;

import java.util.List;

public class SchoolBean extends BaseResponse<List<SchoolBean>>{
//   "id": 102,
//           "name": "深圳大学",
//           "created_at": "2020-04-08 15:06:57",
//           "updated_at": null,
//           "is_del": 0,
//           "province_id": 19,
//           "type": 0

    private int id;
    private String name;
    private String created_at;
    private String updated_at;
    private int is_del;
    private int province_id;
    private int type;

    public SchoolBean() {
    }

    public SchoolBean(int id, String name, int province_id) {
        this.id = id;
        this.name = name;
        this.province_id = province_id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(String updated_at) {
        this.updated_at = updated_at;
    }

    public int getIs_del() {
        return is_del;
    }

    public void setIs_del(int is_del) {
        this.is_del = is_del;
    }

    public int getProvince_id() {
        return province_id;
    }

    public void setProvince_id(int province_id) {
        this.province_id = province_id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
