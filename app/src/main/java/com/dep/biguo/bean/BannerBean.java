package com.dep.biguo.bean;

import java.io.Serializable;

/**
 * Banner 横幅数据模型
 * 用于商城首页轮播图数据
 */
public class BannerBean implements Serializable {

    private int id;
    private String title;           // 横幅标题
    private String imageUrl;        // 图片URL
    private String linkUrl;         // 点击跳转链接
    private int type;              // 横幅类型：1-商品详情，2-活动页面，3-外部链接
    private int sort;              // 排序
    private boolean isActive;      // 是否启用

    public BannerBean() {
    }

    public BannerBean(int id, String title, String imageUrl, String linkUrl, int type) {
        this.id = id;
        this.title = title;
        this.imageUrl = imageUrl;
        this.linkUrl = linkUrl;
        this.type = type;
        this.isActive = true;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    @Override
    public String toString() {
        return "BannerBean{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", linkUrl='" + linkUrl + '\'' +
                ", type=" + type +
                ", sort=" + sort +
                ", isActive=" + isActive +
                '}';
    }
}
