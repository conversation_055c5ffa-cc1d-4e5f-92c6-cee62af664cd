package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.MultiItemEntity;

public class MessageListBean implements MultiItemEntity {
    public static final int SYSTEM_MESSAGE_TYPE = 1;//系统消息渠道
    public static final int PUSH_MESSAGE_TYPE = 2;//推送消息渠道
    public static final int ACCOUNT_MESSAGE_TYPE = 3;//账号通知渠道
    public static final int SERVICE_MESSAGE_TYPE = 4;//服务通知渠道

    /*本地属性*/
    private int channel_type;//消息渠道,1系统消息，2推送消息，3账号通知，4服务通知。在获取到后台数据时自行添加数值，用来实现多布局

    /*公共属性*/
    private int id;//记录ID
    private String channel;//消息渠道
    private String title;//标题
    private String created_at;//消息日期

    /*系统通知、推送消息*/
    private String sub_title;//副标题
    private String abstract_content;//摘要
    private String url;//详情链接
    private String cover;//封面链接
    private String product_type;//要跳转到拼团特惠的商品类型

    /*账号通知、服务通知*/
    private String msg_type;//账号通知或服务通知的消息分支类型，取值有：ACCOUNT_MONEY_TYPE，ACCOUNT_ACCOUNT_TYPE，SERVICE_ORDER_TYPE，ACCOUNT_SERVICE_TYPE
    private String content;//账号通知的内容
    private String from_nick;//署名
    private String from_avatar;//署名头像
    /*服务通知*/
    private String type;//服务通知的订单类型
    private String state;//服务通知的的订单状态

    public int getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(int channel_type) {
        this.channel_type = channel_type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getSub_title() {
        return sub_title;
    }

    public void setSub_title(String sub_title) {
        this.sub_title = sub_title;
    }

    public String getAbstract_content() {
        return abstract_content;
    }

    public void setAbstract_content(String abstract_content) {
        this.abstract_content = abstract_content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getProduct_type() {
        return product_type;
    }

    public void setProduct_type(String product_type) {
        this.product_type = product_type;
    }

    public String getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(String msg_type) {
        this.msg_type = msg_type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFrom_nick() {
        return from_nick;
    }

    public void setFrom_nick(String from_nick) {
        this.from_nick = from_nick;
    }

    public String getFrom_avatar() {
        return from_avatar;
    }

    public void setFrom_avatar(String from_avatar) {
        this.from_avatar = from_avatar;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public int getItemType() {
        return channel_type;
    }

}
