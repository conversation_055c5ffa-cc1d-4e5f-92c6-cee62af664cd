package com.dep.biguo.bean;

import java.util.List;

public class DayCardV3Bean {
    private String avatar;//头像
    private String desc;//打卡介绍
    private String integral;//积分
    private int sign_add_up_num;//累计打卡天数
    private int round_index;//本轮打卡完成第几天
    private int today_signed;//今天是否打卡
    private int sign_num;//连续打卡天数
    private String sign_rank;//连续打卡超过的用户比例
    private List<Prize> prize_list;
    private List<Exchange> exchange;
    private String code;//打卡课程的课程代码
    private String name;//打卡课程的课程名称

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public int getSign_add_up_num() {
        return sign_add_up_num;
    }

    public void setSign_add_up_num(int sign_add_up_num) {
        this.sign_add_up_num = sign_add_up_num;
    }

    public int getRound_index() {
        return round_index;
    }

    public void setRound_index(int round_index) {
        this.round_index = round_index;
    }

    public int getToday_signed() {
        return today_signed;
    }

    public void setToday_signed(int today_signed) {
        this.today_signed = today_signed;
    }

    public int getSign_num() {
        return sign_num;
    }

    public void setSign_num(int sign_num) {
        this.sign_num = sign_num;
    }

    public String getSign_rank() {
        return sign_rank;
    }

    public void setSign_rank(String sign_rank) {
        this.sign_rank = sign_rank;
    }

    public List<Prize> getPrize_list() {
        return prize_list;
    }

    public void setPrize_list(List<Prize> prize_list) {
        this.prize_list = prize_list;
    }

    public List<Exchange> getExchange() {
        return exchange;
    }

    public void setExchange(List<Exchange> exchange) {
        this.exchange = exchange;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static class Prize{
        private int prize_id;
        private int index;//顺序
        private String prize_type;//奖品类型
        private String icon; //图标
        private String prize_value; //积分果币数量 或优惠券id 或题库编码
        private int valid_lenth; //有效期天数
        private int is_receive;//-1 奖品未获取 0未领取 1已领取
        private int is_expire;//0未过期，1已过期
        private String prize_desc;//优惠券的描述，不是优惠券则为空字符串
        private String valid_date;//格式化的优惠券到期日期，不是优惠券则为空字符串
        private String coupon_category;//优惠券的类型，参考PayUtils类中的订单类型，以及增加一个all类表示通用优惠券

        public int getPrize_id() {
            return prize_id;
        }

        public void setPrize_id(int prize_id) {
            this.prize_id = prize_id;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getPrize_type() {
            return prize_type;
        }

        public void setPrize_type(String prize_type) {
            this.prize_type = prize_type;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getPrize_value() {
            return prize_value;
        }

        public void setPrize_value(String prize_value) {
            this.prize_value = prize_value;
        }

        public int getValid_lenth() {
            return valid_lenth;
        }

        public void setValid_lenth(int valid_lenth) {
            this.valid_lenth = valid_lenth;
        }

        public int getIs_receive() {
            return is_receive;
        }

        public void setIs_receive(int is_receive) {
            this.is_receive = is_receive;
        }

        public int getIs_expire() {
            return is_expire;
        }

        public void setIs_expire(int is_expire) {
            this.is_expire = is_expire;
        }

        public String getPrize_desc() {
            return prize_desc;
        }

        public void setPrize_desc(String prize_desc) {
            this.prize_desc = prize_desc;
        }

        public String getValid_date() {
            return valid_date;
        }

        public void setValid_date(String valid_date) {
            this.valid_date = valid_date;
        }

        public String getCoupon_category() {
            return coupon_category;
        }

        public void setCoupon_category(String coupon_category) {
            this.coupon_category = coupon_category;
        }
    }

    public static class Exchange {
        private String type;
        private String num;
        private String image;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getNum() {
            return num;
        }

        public void setNum(String num) {
            this.num = num;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }
    }
}
