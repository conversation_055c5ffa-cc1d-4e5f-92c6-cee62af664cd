package com.dep.biguo.bean.jsz;

import com.chad.library.adapter.base.entity.MultiItemEntity;

public class JSZProvinceBean implements MultiItemEntity {
    public static final int TYPE_A = 1;
    public static final int TYPE_B = 2;
    private int mItemType;
    private int id;
    private String name;
    private int adult_professions_id;
    private String adult_professions_name;

    //层次接口返回
    private int form_id;
    private int layer_id;
    private int professions_id;
    private String form_name;
    private String layer_name;
    private String professions_name;

    public int getAdult_professions_id() {
        return adult_professions_id;
    }

    public void setAdult_professions_id(int adult_professions_id) {
        this.adult_professions_id = adult_professions_id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getForm_id() {
        return form_id;
    }

    public void setForm_id(int form_id) {
        this.form_id = form_id;
    }

    public int getLayer_id() {
        return layer_id;
    }

    public void setLayer_id(int layer_id) {
        this.layer_id = layer_id;
    }

    public int getProfessions_id() {
        return professions_id;
    }

    public void setProfessions_id(int professions_id) {
        this.professions_id = professions_id;
    }

    public String getForm_name() {
        return form_name;
    }

    public void setForm_name(String form_name) {
        this.form_name = form_name;
    }

    public String getLayer_name() {
        return layer_name;
    }

    public void setLayer_name(String layer_name) {
        this.layer_name = layer_name;
    }

    public String getProfessions_name() {
        return professions_name;
    }

    public void setProfessions_name(String professions_name) {
        this.professions_name = professions_name;
    }

    public String getAdult_professions_name() {
        return adult_professions_name;
    }

    public void setAdult_professions_name(String adult_professions_name) {
        this.adult_professions_name = adult_professions_name;
    }

    public void setItemType(int mItemType) {
        this.mItemType = mItemType;
    }

    @Override
    public int getItemType() {
        return mItemType;
    }
}
