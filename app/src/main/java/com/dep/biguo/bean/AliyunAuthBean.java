package com.dep.biguo.bean;

public class AliyunAuthBean {
    private String PlayAuth;
    private String region;
    private VideoMetaBean VideoMeta;

    public String getPlayAuth() {
        return PlayAuth;
    }

    public void setPlayAuth(String playAuth) {
        PlayAuth = playAuth;
    }

    public VideoMetaBean getVideoMeta() {
        return VideoMeta;
    }

    public void setVideoMeta(VideoMetaBean videoMeta) {
        VideoMeta = videoMeta;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public static class VideoMetaBean {
        private String Status;
        private String VideoId;
        private String Title;
        private String CoverURL;
        private float Duration;

        public String getStatus() {
            return Status;
        }

        public void setStatus(String status) {
            Status = status;
        }

        public String getVideoId() {
            return VideoId;
        }

        public void setVideoId(String videoId) {
            VideoId = videoId;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String title) {
            Title = title;
        }

        public String getCoverURL() {
            return CoverURL;
        }

        public void setCoverURL(String coverURL) {
            CoverURL = coverURL;
        }

        public float getDuration() {
            return Duration;
        }

        public void setDuration(float duration) {
            Duration = duration;
        }
    }
}
