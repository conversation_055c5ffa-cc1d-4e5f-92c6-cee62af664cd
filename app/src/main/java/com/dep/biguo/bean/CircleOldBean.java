package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/22
 * @Description: 圈子实体类
 */
public class CircleOldBean implements Parcelable {

    /**
     * id : 4371
     * users_id : 10037
     * content :
     * top : 1
     * is_del : 0
     * likes_count : 2
     * address : 广东省
     * user_is_certification : 1
     * comments_count : 0
     * avatar :
     * nickname :
     * imgone :
     * imgtwo :
     * imgthree :
     * time : 2天前
     * isLike : 0
     */

    private int id;
    private int users_id;
    private String content;
    private int top;
    private String is_del;
    private int likes_count;
    private String address;
    private int comments_count;
    private String avatar;
    private String nickname;
    private String imgone;
    private String imgtwo;
    private String imgthree;
    private String time;
    private int isLike;
    private int post_table_id;
    private String post_table_str;

    public CircleOldBean(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getTop() {
        return top;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public String getIs_del() {
        return is_del;
    }

    public void setIs_del(String is_del) {
        this.is_del = is_del;
    }

    public int getLikes_count() {
        return likes_count;
    }

    public void setLikes_count(int likes_count) {
        this.likes_count = likes_count;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getComments_count() {
        return comments_count;
    }

    public void setComments_count(int comments_count) {
        this.comments_count = comments_count;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getImgone() {
        return imgone;
    }

    public void setImgone(String imgone) {
        this.imgone = imgone;
    }

    public String getImgtwo() {
        return imgtwo;
    }

    public void setImgtwo(String imgtwo) {
        this.imgtwo = imgtwo;
    }

    public String getImgthree() {
        return imgthree;
    }

    public void setImgthree(String imgthree) {
        this.imgthree = imgthree;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public int getIsLike() {
        return isLike;
    }

    public void setIsLike(int isLike) {
        this.isLike = isLike;
    }

    public int getPost_table_id() {
        return post_table_id;
    }

    public void setPost_table_id(int post_table_id) {
        this.post_table_id = post_table_id;
    }

    public String getPost_table_str() {
        return post_table_str;
    }

    public void setPost_table_str(String post_table_str) {
        this.post_table_str = post_table_str;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeInt(this.users_id);
        dest.writeString(this.content);
        dest.writeInt(this.top);
        dest.writeString(this.is_del);
        dest.writeInt(this.likes_count);
        dest.writeString(this.address);
        dest.writeInt(this.comments_count);
        dest.writeString(this.avatar);
        dest.writeString(this.nickname);
        dest.writeString(this.imgone);
        dest.writeString(this.imgtwo);
        dest.writeString(this.imgthree);
        dest.writeString(this.time);
        dest.writeInt(this.isLike);
        dest.writeInt(this.post_table_id);
        dest.writeString(this.post_table_str);
    }

    public CircleOldBean() {
    }

    protected CircleOldBean(Parcel in) {
        this.id = in.readInt();
        this.users_id = in.readInt();
        this.content = in.readString();
        this.top = in.readInt();
        this.is_del = in.readString();
        this.likes_count = in.readInt();
        this.address = in.readString();
        this.comments_count = in.readInt();
        this.avatar = in.readString();
        this.nickname = in.readString();
        this.imgone = in.readString();
        this.imgtwo = in.readString();
        this.imgthree = in.readString();
        this.time = in.readString();
        this.isLike = in.readInt();
        this.post_table_id = in.readInt();
        this.post_table_str = in.readString();
    }

    public static final Creator<CircleOldBean> CREATOR = new Creator<CircleOldBean>() {
        @Override
        public CircleOldBean createFromParcel(Parcel source) {
            return new CircleOldBean(source);
        }

        @Override
        public CircleOldBean[] newArray(int size) {
            return new CircleOldBean[size];
        }
    };
}
