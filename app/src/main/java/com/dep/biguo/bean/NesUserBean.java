package com.dep.biguo.bean;


public class NesUserBean {
    private String img;//封面
    private String type;//拼团类型
    private String name;//名称
    private String intro;//特点
    private String price;//原价
    private String newcomers_price;//活动价
    private int whether_newcomers;//是否处于拼团中或已购买

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public int getWhether_newcomers() {
        return whether_newcomers;
    }

    public void setWhether_newcomers(int whether_newcomers) {
        this.whether_newcomers = whether_newcomers;
    }
}
