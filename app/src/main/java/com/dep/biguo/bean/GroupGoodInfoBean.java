package com.dep.biguo.bean;

import java.util.List;

public class GroupGoodInfoBean {
    private int is_pay;//是否拥有题库
    private int total_buy_count;//已购数目
    private List<String> label;//商品标签
    private int product_id;
    private int video_type;//视频类型
    private String group_id;
    private String name;//课程名称
    private String code;//课程编码
    private int num;//总题数
    private List<QuestionStructure> num_detail;//VIP题目结构列表
    private int comment_num;//商品评论数
    private List<GroupCommentBean> comment;//商品评论
    private int is_comment;//是否已评论
    private String desc;//商品描述详情
    private List<CommonQuestion> qa;//常见问题
    private String share_text;//商品分享链接的标题
    private String share_desc;//商品分享链接的副标题
    private String group_share_text;//拼团分享链接的标题
    private String group_share_desc;//拼团分享链接的副标题
    private String price; //原价
    private int is_newcomers;//1是新用户，2不是新用户
    private String newcomers_price;//新人活动价
    private String member_price;//折扣价
    private String group_price;//拼团价
    private String member_group_price;//拼团折扣价
    private List<String> rules;//拼团规则
    private int group_num;//正在拼团数
    private String short_content;//押密内容预览
    private int order_id;//当已购买后，order_id不为0
    private List<GroupBean> conduct_group;//正在拼团的列表
    private List<OrderPayDetailBean.Bposter> redeem;//权益，返回弹窗用到
    private String remind_buy;//展示对应类型的推荐商品
    private MasterGraph master_graph;//封面播放链接
    private int source_type;//1时刻套餐2必过套餐
    private int cert_type;//
    private String describe;//商品详情富文本
    private int is_pay_vip;//是否支付了VIP题库，0否，1是
    private Scholarship scholarship;
    private GoodsUpgradeBean replenish_goods;//产品升级
    private List<GuessHistory> guess_history;//押密押中列表

    public int getIs_pay_vip() {
        return is_pay_vip;
    }

    public void setIs_pay_vip(int is_pay_vip) {
        this.is_pay_vip = is_pay_vip;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public int getTotal_buy_count() {
        return total_buy_count;
    }

    public void setTotal_buy_count(int total_buy_count) {
        this.total_buy_count = total_buy_count;
    }

    public List<String> getLabel() {
        return label;
    }

    public void setLabel(List<String> label) {
        this.label = label;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getVideo_type() {
        return video_type;
    }

    public void setVideo_type(int video_type) {
        this.video_type = video_type;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public List<QuestionStructure> getNum_detail() {
        return num_detail;
    }

    public void setNum_detail(List<QuestionStructure> num_detail) {
        this.num_detail = num_detail;
    }

    public int getComment_num() {
        return comment_num;
    }

    public void setComment_num(int comment_num) {
        this.comment_num = comment_num;
    }

    public List<GroupCommentBean> getComment() {
        return comment;
    }

    public void setComment(List<GroupCommentBean> comment) {
        this.comment = comment;
    }

    public int getIs_comment() {
        return is_comment;
    }

    public void setIs_comment(int is_comment) {
        this.is_comment = is_comment;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<CommonQuestion> getQa() {
        return qa;
    }

    public void setQa(List<CommonQuestion> qa) {
        this.qa = qa;
    }

    public String getShare_text() {
        return share_text;
    }

    public void setShare_text(String share_text) {
        this.share_text = share_text;
    }

    public String getShare_desc() {
        return share_desc;
    }

    public void setShare_desc(String share_desc) {
        this.share_desc = share_desc;
    }

    public String getGroup_share_text() {
        return group_share_text;
    }

    public void setGroup_share_text(String group_share_text) {
        this.group_share_text = group_share_text;
    }

    public String getGroup_share_desc() {
        return group_share_desc;
    }

    public void setGroup_share_desc(String group_share_desc) {
        this.group_share_desc = group_share_desc;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getIs_newcomers() {
        return is_newcomers;
    }

    public void setIs_newcomers(int is_newcomers) {
        this.is_newcomers = is_newcomers;
    }

    public String getNewcomers_price() {
        return newcomers_price;
    }

    public void setNewcomers_price(String newcomers_price) {
        this.newcomers_price = newcomers_price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public List<String> getRules() {
        return rules;
    }

    public void setRules(List<String> rules) {
        this.rules = rules;
    }

    public int getGroup_num() {
        return group_num;
    }

    public void setGroup_num(int group_num) {
        this.group_num = group_num;
    }

    public String getShort_content() {
        return short_content;
    }

    public void setShort_content(String short_content) {
        this.short_content = short_content;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public List<GroupBean> getConduct_group() {
        return conduct_group;
    }

    public List<OrderPayDetailBean.Bposter> getRedeem() {
        return redeem;
    }

    public void setRedeem(List<OrderPayDetailBean.Bposter> redeem) {
        this.redeem = redeem;
    }

    public void setConduct_group(List<GroupBean> conduct_group) {
        this.conduct_group = conduct_group;
    }

    public String getRemind_buy() {
        return remind_buy;
    }

    public void setRemind_buy(String remind_buy) {
        this.remind_buy = remind_buy;
    }

    public MasterGraph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(MasterGraph master_graph) {
        this.master_graph = master_graph;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public Scholarship getScholarship() {
        return scholarship;
    }

    public void setScholarship(Scholarship scholarship) {
        this.scholarship = scholarship;
    }

    public GoodsUpgradeBean getReplenish_goods() {
        return replenish_goods;
    }

    public void setReplenish_goods(GoodsUpgradeBean replenish_goods) {
        this.replenish_goods = replenish_goods;
    }

    public List<GuessHistory> getGuess_history() {
        return guess_history;
    }

    public void setGuess_history(List<GuessHistory> guess_history) {
        this.guess_history = guess_history;
    }

    public static class QuestionStructure{
        private int type;
        private String name;
        private int total_nums;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getTotal_nums() {
            return total_nums;
        }

        public void setTotal_nums(int total_nums) {
            this.total_nums = total_nums;
        }
    }

    public static class CommonQuestion{
        private String question;//问题
        private String answer;//回答

        public String getQuestion() {
            return question;
        }

        public void setQuestion(String question) {
            this.question = question;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }
    }

    public static class MasterGraph{
        private String graph_type;
        private String url;
        private String image_url;

        public String getGraph_type() {
            return graph_type;
        }

        public void setGraph_type(String graph_type) {
            this.graph_type = graph_type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getImage_url() {
            return image_url;
        }

        public void setImage_url(String image_url) {
            this.image_url = image_url;
        }
    }


    public static class Scholarship{
        private int status;//奖学金 0=未开放，1=生效中，2=开放成绩申报，3=开放排行
        private String sales_restriction;//开放课程代码生效销量
        private String maximum_bonus;//最高奖金额度

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getSales_restriction() {
            return sales_restriction;
        }

        public void setSales_restriction(String sales_restriction) {
            this.sales_restriction = sales_restriction;
        }

        public String getMaximum_bonus() {
            return maximum_bonus;
        }

        public void setMaximum_bonus(String maximum_bonus) {
            this.maximum_bonus = maximum_bonus;
        }
    }

    public static class GuessHistory{
        private String title;
        private List<GuessHistoryItem> list;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<GuessHistoryItem> getList() {
            return list;
        }

        public void setList(List<GuessHistoryItem> list) {
            this.list = list;
        }
    }

    public static class GuessHistoryItem{
        private String code;
        private String name;
        private String url;
        private String guess;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getGuess() {
            return guess;
        }

        public void setGuess(String guess) {
            this.guess = guess;
        }
    }
}
