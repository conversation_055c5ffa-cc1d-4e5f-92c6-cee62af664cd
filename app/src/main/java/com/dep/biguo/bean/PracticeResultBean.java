package com.dep.biguo.bean;

import java.util.List;

public class PracticeResultBean {

    private int topic_count;
    private int answer_count;
    private int undone;
    private String probability;
    private List<QuestionBean> topic_list;
    private int correct_count;

    public int getTopic_count() {
        return topic_count;
    }

    public void setTopic_count(int topic_count) {
        this.topic_count = topic_count;
    }

    public int getAnswer_count() {
        return answer_count;
    }

    public void setAnswer_count(int answer_count) {
        this.answer_count = answer_count;
    }

    public int getUndone() {
        return undone;
    }

    public void setUndone(int undone) {
        this.undone = undone;
    }

    public String getProbability() {
        return probability;
    }

    public void setProbability(String probability) {
        this.probability = probability;
    }

    public List<QuestionBean> getTopic_list() {
        return topic_list;
    }

    public void setTopic_list(List<QuestionBean> topic_list) {
        this.topic_list = topic_list;
    }

    public int getCorrect_count() {
        return correct_count;
    }

    public void setCorrect_count(int correct_count) {
        this.correct_count = correct_count;
    }
}
