package com.dep.biguo.bean;

import java.util.List;

public class ExchangeDetailBean extends BaseResponse<List<ExchangeDetailBean.ExchangeBean>> {
//     "total": 11,
//             "current_page": 1,
//             "list": ,

    private int total;
    private int current_page;
    private List<ExchangeBean> list;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getCurrent_page() {
        return current_page;
    }

    public void setCurrent_page(int current_page) {
        this.current_page = current_page;
    }

    public List<ExchangeBean> getList() {
        return list;
    }

    public void setList(List<ExchangeBean> list) {
        this.list = list;
    }

    public static class ExchangeBean {
        //     "id": 28,
//             "users_id": 155557,
//             "total_fee": "1.00果币",
//             "status": 2,
//             "type": 2,
//             "name": null,
//             "zfb": null,
//             "is_del": 0,
//             "create_time": "2020-1
//             "update_time": null,
//             "cash_time": null,
//             "desc": "兑换果币"
        private int id;
        private int users_id;
        private String total_fee;
        private int status;
        private int type;
        private String name;
        private String zfb;
        private int is_del;
        private String create_time;
        private String update_time;
        private String cash_time;
        private String desc;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public String getTotal_fee() {
            return total_fee;
        }

        public void setTotal_fee(String total_fee) {
            this.total_fee = total_fee;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getZfb() {
            return zfb;
        }

        public void setZfb(String zfb) {
            this.zfb = zfb;
        }

        public int getIs_del() {
            return is_del;
        }

        public void setIs_del(int is_del) {
            this.is_del = is_del;
        }

        public String getCreate_time() {
            return create_time;
        }

        public void setCreate_time(String create_time) {
            this.create_time = create_time;
        }

        public String getUpdate_time() {
            return update_time;
        }

        public void setUpdate_time(String update_time) {
            this.update_time = update_time;
        }

        public String getCash_time() {
            return cash_time;
        }

        public void setCash_time(String cash_time) {
            this.cash_time = cash_time;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
