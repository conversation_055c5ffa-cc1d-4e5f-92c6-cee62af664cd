package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class AddressBean implements Parcelable {

    /**
     * id : 2
     * users_id : 10043
     * name : 黄大仙
     * phone : 13377665432
     * provinces : 广东省
     * city : 深圳市
     * area : 龙岗区
     * detail : 你找不到我的你找不到我的你找不到我的你找不不到我的
     * update_time : 2017-11-29 13:53:02
     */

    private int id;
    private int users_id;
    private String name;
    private String phone;
    private String provinces;
    private String city;
    private String area;
    private String detail;
    private String update_time;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvinces() {
        return provinces;
    }

    public void setProvinces(String provinces) {
        this.provinces = provinces;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeInt(this.users_id);
        dest.writeString(this.name);
        dest.writeString(this.phone);
        dest.writeString(this.provinces);
        dest.writeString(this.city);
        dest.writeString(this.area);
        dest.writeString(this.detail);
        dest.writeString(this.update_time);
    }

    public AddressBean() {
    }

    protected AddressBean(Parcel in) {
        this.id = in.readInt();
        this.users_id = in.readInt();
        this.name = in.readString();
        this.phone = in.readString();
        this.provinces = in.readString();
        this.city = in.readString();
        this.area = in.readString();
        this.detail = in.readString();
        this.update_time = in.readString();
    }

    public static final Creator<AddressBean> CREATOR = new Creator<AddressBean>() {
        @Override
        public AddressBean createFromParcel(Parcel source) {
            return new AddressBean(source);
        }

        @Override
        public AddressBean[] newArray(int size) {
            return new AddressBean[size];
        }
    };
}

