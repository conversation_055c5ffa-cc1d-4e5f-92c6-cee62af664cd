package com.dep.biguo.bean;

public class VipBean {

    /**
     * code : 00164
     * name : 【VIP题库】劳动经济学
     * count : 817
     * vip_price : 46.00
     * vip_share_price : 36.00
     * remark : 817道题
     * locked : true
     * group_price : 15
     */

    private String code;
    private String name;
    private int count;
    private String vip_price;
    private String vip_share_price;
    private String remark;
    private boolean locked;
    private String group_price;
    private String pay_time;

    public String getPay_time() {
        return pay_time;
    }

    public void setPay_time(String pay_time) {
        this.pay_time = pay_time;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getVip_price() {
        return vip_price;
    }

    public void setVip_price(String vip_price) {
        this.vip_price = vip_price;
    }

    public String getVip_share_price() {
        return vip_share_price;
    }

    public void setVip_share_price(String vip_share_price) {
        this.vip_share_price = vip_share_price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }
}
