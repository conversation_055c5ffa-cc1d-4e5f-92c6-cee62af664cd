package com.dep.biguo.bean;

import java.util.List;

public class SelectCourseBean {
    private String plan_img;
    private List<Course> courses_joined;
    private List<Course> courses_not_joined;
    private List<Course> courses_passed;

    public String getPlan_img() {
        return plan_img;
    }

    public void setPlan_img(String plan_img) {
        this.plan_img = plan_img;
    }

    public List<Course> getCourses_joined() {
        return courses_joined;
    }

    public void setCourses_joined(List<Course> courses_joined) {
        this.courses_joined = courses_joined;
    }

    public List<Course> getCourses_not_joined() {
        return courses_not_joined;
    }

    public void setCourses_not_joined(List<Course> courses_not_joined) {
        this.courses_not_joined = courses_not_joined;
    }

    public List<Course> getCourses_passed() {
        return courses_passed;
    }

    public void setCourses_passed(List<Course> courses_passed) {
        this.courses_passed = courses_passed;
    }

    public class Course{
        private int courses_id;//课程id
        private int joined;//0未报名，1已报名
        private String code;//课程编码
        private String name;//课程名称
        private String exam_time;//考试时间 没有返回null
        private float score;//分数
        private int is_online_assistance;//0没有开放，1已开放

        public int getCourses_id() {
            return courses_id;
        }

        public void setCourses_id(int courses_id) {
            this.courses_id = courses_id;
        }

        public int getJoined() {
            return joined;
        }

        public void setJoined(int joined) {
            this.joined = joined;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getExam_time() {
            return exam_time;
        }

        public void setExam_time(String exam_time) {
            this.exam_time = exam_time;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getIs_online_assistance() {
            return is_online_assistance;
        }

        public void setIs_online_assistance(int is_online_assistance) {
            this.is_online_assistance = is_online_assistance;
        }
    }
}
