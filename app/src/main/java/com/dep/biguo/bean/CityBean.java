package com.dep.biguo.bean;

import java.util.List;

public class CityBean{
    private String title;
    private List<City> list;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<City> getList() {
        return list;
    }

    public void setList(List<City> list) {
        this.list = list;
    }

    public static class City{
        private int city_id;//城市id
        private String city_code;//城市编码
        private String city_name;//城市名称
        private String province_name;//省份名称
        private int province_id;//省份ID
        private String initial;//城市首字母

        public City() {
        }

        public City(int id, String city_name, String city_code, int province_id, String province_name) {
            this.city_id = id;
            this.city_name = city_name;
            this.city_code = city_code;
            this.province_id = province_id;
            this.province_name = province_name;
        }

        public int getCity_id() {
            return city_id;
        }

        public void setCity_id(int city_id) {
            this.city_id = city_id;
        }

        public String getCity_code() {
            return city_code;
        }

        public void setCity_code(String city_code) {
            this.city_code = city_code;
        }

        public String getCity_name() {
            return city_name;
        }

        public void setCity_name(String city_name) {
            this.city_name = city_name;
        }

        public String getProvince_name() {
            return province_name;
        }

        public void setProvince_name(String province_name) {
            this.province_name = province_name;
        }

        public int getProvince_id() {
            return province_id;
        }

        public void setProvince_id(int province_id) {
            this.province_id = province_id;
        }

        public String getInitial() {
            return initial;
        }

        public void setInitial(String initial) {
            this.initial = initial;
        }
    }

}
