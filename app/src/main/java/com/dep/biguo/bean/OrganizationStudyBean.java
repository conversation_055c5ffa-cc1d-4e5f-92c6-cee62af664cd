package com.dep.biguo.bean;

import java.util.List;

public class OrganizationStudyBean {
    private String project;
    private int form_id;
    private List<OrganizationItem> products;


    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public int getForm_id() {
        return form_id;
    }

    public void setForm_id(int form_id) {
        this.form_id = form_id;
    }

    public List<OrganizationItem> getProducts() {
        return products;
    }

    public void setProducts(List<OrganizationItem> products) {
        this.products = products;
    }

    public static class OrganizationItem{
        private int product_id;
        private String picture;
        private String title;
        private String price;
        private String original_price;
        private String subtitle;
        private String profession_code;

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public String getPicture() {
            return picture;
        }

        public void setPicture(String picture) {
            this.picture = picture;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getOriginal_price() {
            return original_price;
        }

        public void setOriginal_price(String original_price) {
            this.original_price = original_price;
        }

        public String getProfession_code() {
            return profession_code;
        }

        public void setProfession_code(String profession_code) {
            this.profession_code = profession_code;
        }
    }
}
