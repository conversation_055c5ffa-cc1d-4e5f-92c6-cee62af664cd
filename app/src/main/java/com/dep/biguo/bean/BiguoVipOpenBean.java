package com.dep.biguo.bean;

import java.util.List;

public class BiguoVipOpenBean {
    private String nickname;
    private String mobile;
    private String price;
    private String group_price;
    private String discount;
    private int open_effective_year;
    private int expiration_days;
    private int is_open;
    private String highest_reduce_price;
    private List<DiscountsBean> enjoy_discounts;
    private List<DiscountsBean> not_enjoy_discounts;
    private List<EquityBean> equity;
    private int total_people_num;
    private GroupInfo group_info;//0未邀请，1邀请成功，2被邀请成功
    private int group_id;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public int getOpen_effective_year() {
        return open_effective_year;
    }

    public void setOpen_effective_year(int open_effective_year) {
        this.open_effective_year = open_effective_year;
    }

    public int getExpiration_days() {
        return expiration_days;
    }

    public void setExpiration_days(int expiration_days) {
        this.expiration_days = expiration_days;
    }

    public int getIs_open() {
        return is_open;
    }

    public void setIs_open(int is_open) {
        this.is_open = is_open;
    }

    public String getHighest_reduce_price() {
        return highest_reduce_price;
    }

    public void setHighest_reduce_price(String highest_reduce_price) {
        this.highest_reduce_price = highest_reduce_price;
    }

    public List<DiscountsBean> getEnjoy_discounts() {
        return enjoy_discounts;
    }

    public void setEnjoy_discounts(List<DiscountsBean> enjoy_discounts) {
        this.enjoy_discounts = enjoy_discounts;
    }

    public List<DiscountsBean> getNot_enjoy_discounts() {
        return not_enjoy_discounts;
    }

    public void setNot_enjoy_discounts(List<DiscountsBean> not_enjoy_discounts) {
        this.not_enjoy_discounts = not_enjoy_discounts;
    }

    public List<EquityBean> getEquity() {
        return equity;
    }

    public void setEquity(List<EquityBean> equity) {
        this.equity = equity;
    }

    public int getTotal_people_num() {
        return total_people_num;
    }

    public void setTotal_people_num(int total_people_num) {
        this.total_people_num = total_people_num;
    }

    public GroupInfo getGroup_info() {
        return group_info;
    }

    public void setGroup_info(GroupInfo group_info) {
        this.group_info = group_info;
    }

    public int getGroup_id() {
        return group_id;
    }

    public void setGroup_id(int group_id) {
        this.group_id = group_id;
    }

    public static class DiscountsBean {
        private String icon;
        private String title;
        private String reduce_price;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getReduce_price() {
            return reduce_price;
        }

        public void setReduce_price(String reduce_price) {
            this.reduce_price = reduce_price;
        }
    }

    public static class EquityBean {
        private String icon;
        private String title;
        private String desc;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static class GroupInfo{
        private long sec;
        private int users_id;
        private int is_head;
        private String group_id;
        private int status;
        private String share_url;

        public long getSec() {
            return sec;
        }

        public void setSec(long sec) {
            this.sec = sec;
        }

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public int getIs_head() {
            return is_head;
        }

        public void setIs_head(int is_head) {
            this.is_head = is_head;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getShare_url() {
            return share_url;
        }

        public void setShare_url(String share_url) {
            this.share_url = share_url;
        }
    }
}
