package com.dep.biguo.bean;

import com.bigkoo.pickerview.model.IPickerViewData;

import java.util.List;

public class PickerJsonBean implements IPickerViewData {

    /**
     * name : 省份
     * city : [{"name":"北京市","area":["东城区","西城区","崇文区","宣武区","朝阳区"]}]
     */

    private String code;
    private String name;
    private List<PickerJsonBean> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<PickerJsonBean> getChildren() {
        return children;
    }

    public void setChildren(List<PickerJsonBean> children) {
        this.children = children;
    }

    // 实现 IPickerViewData 接口，
    // 这个用来显示在PickerView上面的字符串，
    // PickerView会通过IPickerViewData获取getPickerViewText方法显示出来。
    @Override
    public String getPickerViewText() {
        return this.name;
    }
}

