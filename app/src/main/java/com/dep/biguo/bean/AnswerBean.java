package com.dep.biguo.bean;

import java.util.List;

public class AnswerBean {
    private int last_id;
    private List<History> list;

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public List<History> getList() {
        return list;
    }

    public void setList(List<History> list) {
        this.list = list;
    }

    private static class History{
        private int id;//题目ID
        private int topic_type;//题型
        private int mainType;//题库类型
        private String select_answer;//所选答案

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getTopic_type() {
            return topic_type;
        }

        public void setTopic_type(int topic_type) {
            this.topic_type = topic_type;
        }

        public int getMainType() {
            return mainType;
        }

        public void setMainType(int mainType) {
            this.mainType = mainType;
        }

        public String getSelect_answer() {
            return select_answer;
        }

        public void setSelect_answer(String select_answer) {
            this.select_answer = select_answer;
        }
    }
}
