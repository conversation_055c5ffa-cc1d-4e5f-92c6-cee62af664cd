package com.dep.biguo.bean;

import java.util.List;

public class OrganizationStudyGoodsBean {
    private int product_id;//产品ID
    private String title;//标题
    private String subtitle;//副标题
    private String price;//价格
    private Master_graph master_graph;//封面
    private String original_price;//原价
    private String describe;//详情介绍内容
    private String purchase_notes;//购买须知
    private int is_pay;//状态 1=未报名 2=已报名
    private List<String> pay_types;//支付类型
    private int institution_id;//机构ID
    private String institution_name;//机构名称
    private String logo;//机构logo
    private String profession_name;//专业名称
    private String profession_code;//专业代码

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Master_graph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(Master_graph master_graph) {
        this.master_graph = master_graph;
    }

    public String getOriginal_price() {
        return original_price;
    }

    public void setOriginal_price(String original_price) {
        this.original_price = original_price;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getPurchase_notes() {
        return purchase_notes;
    }

    public void setPurchase_notes(String purchase_notes) {
        this.purchase_notes = purchase_notes;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public List<String> getPay_types() {
        return pay_types;
    }

    public void setPay_types(List<String> pay_types) {
        this.pay_types = pay_types;
    }

    public int getInstitution_id() {
        return institution_id;
    }

    public void setInstitution_id(int institution_id) {
        this.institution_id = institution_id;
    }

    public String getInstitution_name() {
        return institution_name;
    }

    public void setInstitution_name(String institution_name) {
        this.institution_name = institution_name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getProfession_name() {
        return profession_name;
    }

    public void setProfession_name(String profession_name) {
        this.profession_name = profession_name;
    }

    public String getProfession_code() {
        return profession_code;
    }

    public void setProfession_code(String profession_code) {
        this.profession_code = profession_code;
    }

    public static class Master_graph{
        private String graph_type;//url的类型
        private String url;//图片或视频

        public String getGraph_type() {
            return graph_type;
        }

        public void setGraph_type(String graph_type) {
            this.graph_type = graph_type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
