package com.dep.biguo.bean;

import java.util.List;

public class OrganizationDeductionCardBean {
    private int card_id;//抵扣卡id
    private String name;//名称
    private int card_type;//卡类型 1=小时卡 2=月卡 3=年卡
    private List<String> text;//文案
    private String expiration_text;//有效期文本
    private int status; //状态 1=立即开卡 2=去续费 3=已开通
    private String price;//价格
    private String original_price;//原价
    private String updated_at;//更新时间
    private String created_a;//创建时间
    private List<String> pay_types;//支持的支付平台

    public int getCard_id() {
        return card_id;
    }

    public void setCard_id(int card_id) {
        this.card_id = card_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCard_type() {
        return card_type;
    }

    public void setCard_type(int card_type) {
        this.card_type = card_type;
    }

    public List<String> getText() {
        return text;
    }

    public void setText(List<String> text) {
        this.text = text;
    }

    public String getExpiration_text() {
        return expiration_text;
    }

    public void setExpiration_text(String expiration_text) {
        this.expiration_text = expiration_text;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getOriginal_price() {
        return original_price;
    }

    public void setOriginal_price(String original_price) {
        this.original_price = original_price;
    }

    public String getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(String updated_at) {
        this.updated_at = updated_at;
    }

    public String getCreated_a() {
        return created_a;
    }

    public void setCreated_a(String created_a) {
        this.created_a = created_a;
    }

    public List<String> getPay_types() {
        return pay_types;
    }

    public void setPay_types(List<String> pay_types) {
        this.pay_types = pay_types;
    }
}
