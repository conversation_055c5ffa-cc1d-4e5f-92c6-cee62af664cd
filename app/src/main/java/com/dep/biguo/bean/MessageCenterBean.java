package com.dep.biguo.bean;

import java.util.List;

public class MessageCenterBean {
    private int comments_unread;//回复我的 的未读数量
    private List<Data> channel_list;

    public int getComments_unread() {
        return comments_unread;
    }

    public void setComments_unread(int comments_unread) {
        this.comments_unread = comments_unread;
    }

    public List<Data> getChannel_list() {
        return channel_list;
    }

    public void setChannel_list(List<Data> channel_list) {
        this.channel_list = channel_list;
    }

    public static class Data{
        private String title;//标题
        private String time;//接受到消息时的日期
        private int last_id;//最新一条消息的ID
        private String channel;//消息类型名称
        private int type;//消息类型
        private int unread;//消息未读数

        public int getUnread() {
            return unread;
        }

        public void setUnread(int unread) {
            this.unread = unread;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public int getLast_id() {
            return last_id;
        }

        public void setLast_id(int last_id) {
            this.last_id = last_id;
        }

        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }
}
