package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/11
 * @Description:
 */
public class RankGroupBean {

    private String users_id;
    private String nickname;
    private String avatar;
    private String count;
    private int sort;
    private int sort_status;//-1下降，0不显示，1上升

    private List<RankBean> list;

    public String getUsers_id() {
        return users_id;
    }

    public void setUsers_id(String users_id) {
        this.users_id = users_id;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public List<RankBean> getList() {
        return list;
    }

    public void setList(List<RankBean> list) {
        this.list = list;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getSort_status() {
        return sort_status;
    }

    public void setSort_status(int sort_status) {
        this.sort_status = sort_status;
    }
}
