package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.util.List;


public class CardBean {
    private int last_id;//上次答题位置 题目id
    private int is_complete;//是否全部答完,1是，0否
    private int user_newest;//本地题库里最后一道题目的ID，若题目的ID大于它，则认为时新增的题目
    private int version;//错题或收藏的版本号
    private List<Topic> topics;//答题记录

    public static class TopicHead extends Topic{
        private int count;
        private int topic_type;
        private String topic_type_name;


        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public int getTopic_type() {
            return topic_type;
        }

        public void setTopic_type(int topic_type) {
            this.topic_type = topic_type;
        }

        public String getTopic_type_name() {
            return topic_type_name;
        }

        public void setTopic_type_name(String topic_type_name) {
            this.topic_type_name = topic_type_name;
        }

    }

    public static class Topic{
        private String code;//课程代码
        private int mainType;//题库类型，vip、真题、章节训练、免费、打卡
        private int id;//题目id
        private int topic_type;//题目类型1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
        private String topic_type_name;//题目类型名称1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
        private String correctOption;//正确答案
        private String select_answer;//用户选项 为空即代表未答
        private String value;////真题ID/章节ID/试卷ID/code
        private int is_correct;//是否已检验对错，0选中（蓝），1正确（绿），2错误（红），3题目有误

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getMainType() {
            return mainType;
        }

        public void setMainType(int mainType) {
            this.mainType = mainType;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getTopic_type() {
            return topic_type;
        }

        public void setTopic_type(int topic_type) {
            this.topic_type = topic_type;
        }

        public String getTopic_type_name() {
            return topic_type_name;
        }

        public void setTopic_type_name(String topic_type_name) {
            this.topic_type_name = topic_type_name;
        }

        public String getCorrectOption() {
            return correctOption;
        }

        public void setCorrectOption(String correctOption) {
            this.correctOption = correctOption;
        }

        public String getSelect_answer() {
            return select_answer;
        }

        public void setSelect_answer(String select_answer) {
            this.select_answer = select_answer;
        }

        public int getIs_correct() {
            return is_correct;
        }

        public void setIs_correct(int is_correct) {
            this.is_correct = is_correct;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public int getIs_complete() {
        return is_complete;
    }

    public void setIs_complete(int is_complete) {
        this.is_complete = is_complete;
    }

    public int getUser_newest() {
        return user_newest;
    }

    public void setUser_newest(int user_newest) {
        this.user_newest = user_newest;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<Topic> getTopics() {
        return topics;
    }

    public void setTopics(List<Topic> topics) {
        this.topics = topics;
    }
}
