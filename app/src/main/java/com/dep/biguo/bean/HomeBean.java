package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/27
 * @Description:
 */
public class HomeBean extends BaseResponse<HomeBean>{

    private List<BannerBean> swiper;
    private List<AdvertisingBean> advertising;
    private List<AdvertisingBean> tutorial_banner;
    private List<ArticleBean> findings;
    private List<MenuBean> icon;
    private List<ClassRoomBean> classroom;
    private List<HomeBookBean> shopGoods;
    private List<SchoolDataBean> school_list;
    private List<ArticleBean> school_news;
    private int shopGoodsId;
    private String is_vip;
    private String is_yami;
    private String is_video;
    public List<MenuBean> getIcon() {
        return icon;
    }

    public void setIcon(List<MenuBean> icon) {
        this.icon = icon;
    }

    public List<ArticleBean> getFindings() {
        return findings;
    }

    public void setFindings(List<ArticleBean> findings) {
        this.findings = findings;
    }

    public List<AdvertisingBean> getAdvertising() {
        return advertising;
    }

    public void setAdvertising(List<AdvertisingBean> advertising) {
        this.advertising = advertising;
    }

    public List<BannerBean> getSwiper() {
        return swiper;
    }

    public void setSwiper(List<BannerBean> swiper) {
        this.swiper = swiper;
    }

    public List<ClassRoomBean> getClassroom() {
        return classroom;
    }

    public void setClassroom(List<ClassRoomBean> classroom) {
        this.classroom = classroom;
    }

    public List<HomeBookBean> getShopGoods() {
        return shopGoods;
    }

    public void setShopGoods(List<HomeBookBean> shopGoods) {
        this.shopGoods = shopGoods;
    }

    public int getShopGoodsId() {
        return shopGoodsId;
    }

    public void setShopGoodsId(int shopGoodsId) {
        this.shopGoodsId = shopGoodsId;
    }

    public List<SchoolDataBean> getSchool_list() {
        return school_list;
    }

    public void setSchool_list(List<SchoolDataBean> school_list) {
        this.school_list = school_list;
    }

    public List<AdvertisingBean> getTutorial_banner() {
        return tutorial_banner;
    }

    public void setTutorial_banner(List<AdvertisingBean> tutorial_banner) {
        this.tutorial_banner = tutorial_banner;
    }

    public List<ArticleBean> getSchool_news() {
        return school_news;
    }

    public void setSchool_news(List<ArticleBean> school_news) {
        this.school_news = school_news;
    }

    public String getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(String is_vip) {
        this.is_vip = is_vip;
    }

    public String getIs_yami() {
        return is_yami;
    }

    public void setIs_yami(String is_yami) {
        this.is_yami = is_yami;
    }

    public String getIs_video() {
        return is_video;
    }

    public void setIs_video(String is_video) {
        this.is_video = is_video;
    }

    public static class MenuBean {
        private int type;
        private String title;
        private String img;
        private String tip_img;
        private int status;
        private String target_url;

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTip_img() {
            return tip_img;
        }

        public void setTip_img(String tip_img) {
            this.tip_img = tip_img;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }

    public static class AdvertisingBean {
        private int id;
        private String url;
        private String img;
        private int type;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class BannerBean {
        private int id;
        private String img_url;
        private String target_url;
        private String xcx_path;//小程序路径

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getImg_url() {
            return img_url;
        }

        public void setImg_url(String img_url) {
            this.img_url = img_url;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }

    //vip 押密 视频的权限
    public static class QuestionRightBean{
        private int is_vip;
        private int is_yami;
        private int is_video;

        public int getIs_vip() {
            return is_vip;
        }

        public void setIs_vip(int is_vip) {
            this.is_vip = is_vip;
        }

        public int getIs_yami() {
            return is_yami;
        }

        public void setIs_yami(int is_yami) {
            this.is_yami = is_yami;
        }

        public int getIs_video() {
            return is_video;
        }

        public void setIs_video(int is_video) {
            this.is_video = is_video;
        }
    }

}
