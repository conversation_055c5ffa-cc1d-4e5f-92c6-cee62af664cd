package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/28
 * @Description:
 */
public class CircleCommentBean {
    private int users_id;
    private int comment_id;
    private String author_avatar;//头像
    private String author_name;//昵称
    private String created_at;//发布时间
    private String ip_location;//省份呢
    private String comment_content;//评论内容
    private String replay_name;//回复的谁
    private int replay_num;//未显示的评论条数
    private int is_like;//是否已点赞
    private int like_count;//点赞数量
    private List<CircleCommentBean> replay_to;//回复列表

    public int getUsers_id() {
        return users_id;
    }

    public void setUsers_id(int users_id) {
        this.users_id = users_id;
    }

    public int getComment_id() {
        return comment_id;
    }

    public void setComment_id(int comment_id) {
        this.comment_id = comment_id;
    }

    public String getAuthor_avatar() {
        return author_avatar;
    }

    public void setAuthor_avatar(String author_avatar) {
        this.author_avatar = author_avatar;
    }

    public String getAuthor_name() {
        return author_name;
    }

    public void setAuthor_name(String author_name) {
        this.author_name = author_name;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getIp_location() {
        return ip_location;
    }

    public void setIp_location(String ip_location) {
        this.ip_location = ip_location;
    }

    public String getComment_content() {
        return comment_content;
    }

    public void setComment_content(String comment_content) {
        this.comment_content = comment_content;
    }

    public String getReplay_name() {
        return replay_name;
    }

    public void setReplay_name(String replay_name) {
        this.replay_name = replay_name;
    }

    public int getReplay_num() {
        return replay_num;
    }

    public void setReplay_num(int replay_num) {
        this.replay_num = replay_num;
    }

    public int getIs_like() {
        return is_like;
    }

    public void setIs_like(int is_like) {
        this.is_like = is_like;
    }

    public int getLike_count() {
        return like_count;
    }

    public void setLike_count(int like_count) {
        this.like_count = like_count;
    }

    public List<CircleCommentBean> getReplay_to() {
        return replay_to;
    }

    public void setReplay_to(List<CircleCommentBean> replay_to) {
        this.replay_to = replay_to;
    }
}
