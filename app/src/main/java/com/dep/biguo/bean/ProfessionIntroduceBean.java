package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/3
 * @Description:
 */
public class ProfessionIntroduceBean extends BaseResponse<ProfessionIntroduceBean> implements Parcelable {

    private String title;
    private String introduce;
    private List<String> school;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public List<String> getSchool() {
        return school;
    }

    public void setSchool(List<String> school) {
        this.school = school;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(this.introduce);
        dest.writeStringList(this.school);
    }

    public ProfessionIntroduceBean() {
    }

    protected ProfessionIntroduceBean(Parcel in) {
        this.title = in.readString();
        this.introduce = in.readString();
        this.school = in.createStringArrayList();
    }

    public static final Creator<ProfessionIntroduceBean> CREATOR = new Creator<ProfessionIntroduceBean>() {
        @Override
        public ProfessionIntroduceBean createFromParcel(Parcel source) {
            return new ProfessionIntroduceBean(source);
        }

        @Override
        public ProfessionIntroduceBean[] newArray(int size) {
            return new ProfessionIntroduceBean[size];
        }
    };
}
