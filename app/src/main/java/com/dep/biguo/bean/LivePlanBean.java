package com.dep.biguo.bean;

public class LivePlanBean {
    private int appointment_count;//预约人数
    private int channelId;//直播频道ID
    private int id;//直播课程ID
    private String course_name;//直播课程名称
    private String course_code;//课程代码
    private String teacher_name;//老师名称
    private String teacher_img;//老师头像
    private String start_time;
    private String end_time;
    private String live_time;//直播时间
    private int is_appointment;//是否预约 0=否，1=是
    private int onlineNum;//在线人数
    private String watchStatusText;//观看页状态描述
    private int pageView;//页面累计观看数
    private String watchStatus;//观看页状态,live：直播中，end：直播结束，playback：回放中，waiting：等待中，unStart：未开始，banpush：已禁播，banpush：计划中
    private String splashImg;//频道引导图url
    private String video_aly_id;//阿里的视频ID
    private String course_img;//直播课的封面
    private String desc;//课程简介
    private int course_type;//直播课类型 1公共课，2专业课
    private int is_top;//0=未置顶， 1=置顶

    public int getAppointment_count() {
        return appointment_count;
    }

    public void setAppointment_count(int appointment_count) {
        this.appointment_count = appointment_count;
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getCourse_code() {
        return course_code;
    }

    public void setCourse_code(String course_code) {
        this.course_code = course_code;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }

    public String getTeacher_img() {
        return teacher_img;
    }

    public void setTeacher_img(String teacher_img) {
        this.teacher_img = teacher_img;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getLive_time() {
        return live_time;
    }

    public void setLive_time(String live_time) {
        this.live_time = live_time;
    }

    public int getIs_appointment() {
        return is_appointment;
    }

    public void setIs_appointment(int is_appointment) {
        this.is_appointment = is_appointment;
    }

    public int getOnlineNum() {
        return onlineNum;
    }

    public void setOnlineNum(int onlineNum) {
        this.onlineNum = onlineNum;
    }

    public String getWatchStatusText() {
        return watchStatusText;
    }

    public void setWatchStatusText(String watchStatusText) {
        this.watchStatusText = watchStatusText;
    }

    public int getPageView() {
        return pageView;
    }

    public void setPageView(int pageView) {
        this.pageView = pageView;
    }

    public String getWatchStatus() {
        return watchStatus;
    }

    public void setWatchStatus(String watchStatus) {
        this.watchStatus = watchStatus;
    }

    public String getSplashImg() {
        return splashImg;
    }

    public void setSplashImg(String splashImg) {
        this.splashImg = splashImg;
    }

    public String getVideo_aly_id() {
        return video_aly_id;
    }

    public void setVideo_aly_id(String video_aly_id) {
        this.video_aly_id = video_aly_id;
    }

    public String getCourse_img() {
        return course_img;
    }

    public void setCourse_img(String course_img) {
        this.course_img = course_img;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCourse_type() {
        return course_type;
    }

    public void setCourse_type(int course_type) {
        this.course_type = course_type;
    }

    public int getIs_top() {
        return is_top;
    }

    public void setIs_top(int is_top) {
        this.is_top = is_top;
    }

    public static class AppointmentLiveBean{
        private int is_appointment;//是否预约 0=否，1=是

        public int getIs_appointment() {
            return is_appointment;
        }

        public void setIs_appointment(int is_appointment) {
            this.is_appointment = is_appointment;
        }
    }
}
