package com.dep.biguo.bean;


import java.util.List;

public class TruePaperNewBean {
    private String ai_score;
    private String ai_title;
    private String ai_message;
    private String ai_operate;
    private String ai_tip_text;
    private String ai_jump_type;
    private List<String> ai_score_rankings;
    private String completion_rate;
    private int today_answer_num;
    private int total_answer_num;
    private String correct_rate;
    private int take_time;
    private int time_remains;
    private int total;
    private long record_expire_time;
    private long version;//0 //题库版本号
    private int is_formulate_study_plan;
    private int is_plan_in_profession;
    private String study_plan_title;
    private int product_id;
    private int is_online_assistance;
    private List<TruePaperNewItemBean> real_paper_list;

    public String getAi_score() {
        return ai_score;
    }

    public void setAi_score(String ai_score) {
        this.ai_score = ai_score;
    }

    public String getAi_title() {
        return ai_title;
    }

    public void setAi_title(String ai_title) {
        this.ai_title = ai_title;
    }

    public String getAi_message() {
        return ai_message;
    }

    public void setAi_message(String ai_message) {
        this.ai_message = ai_message;
    }

    public String getAi_operate() {
        return ai_operate;
    }

    public void setAi_operate(String ai_operate) {
        this.ai_operate = ai_operate;
    }

    public String getAi_tip_text() {
        return ai_tip_text;
    }

    public void setAi_tip_text(String ai_tip_text) {
        this.ai_tip_text = ai_tip_text;
    }

    public String getAi_jump_type() {
        return ai_jump_type;
    }

    public void setAi_jump_type(String ai_jump_type) {
        this.ai_jump_type = ai_jump_type;
    }

    public List<String> getAi_score_rankings() {
        return ai_score_rankings;
    }

    public void setAi_score_rankings(List<String> ai_score_rankings) {
        this.ai_score_rankings = ai_score_rankings;
    }

    public String getCompletion_rate() {
        return completion_rate;
    }

    public void setCompletion_rate(String completion_rate) {
        this.completion_rate = completion_rate;
    }

    public int getToday_answer_num() {
        return today_answer_num;
    }

    public void setToday_answer_num(int today_answer_num) {
        this.today_answer_num = today_answer_num;
    }

    public int getTotal_answer_num() {
        return total_answer_num;
    }

    public void setTotal_answer_num(int total_answer_num) {
        this.total_answer_num = total_answer_num;
    }

    public String getCorrect_rate() {
        return correct_rate;
    }

    public void setCorrect_rate(String correct_rate) {
        this.correct_rate = correct_rate;
    }

    public int getTake_time() {
        return take_time;
    }

    public void setTake_time(int take_time) {
        this.take_time = take_time;
    }

    public int getTime_remains() {
        return time_remains;
    }

    public void setTime_remains(int time_remains) {
        this.time_remains = time_remains;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getIs_formulate_study_plan() {
        return is_formulate_study_plan;
    }

    public void setIs_formulate_study_plan(int is_formulate_study_plan) {
        this.is_formulate_study_plan = is_formulate_study_plan;
    }

    public int getIs_plan_in_profession() {
        return is_plan_in_profession;
    }

    public void setIs_plan_in_profession(int is_plan_in_profession) {
        this.is_plan_in_profession = is_plan_in_profession;
    }

    public String getStudy_plan_title() {
        return study_plan_title;
    }

    public void setStudy_plan_title(String study_plan_title) {
        this.study_plan_title = study_plan_title;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getIs_online_assistance() {
        return is_online_assistance;
    }

    public void setIs_online_assistance(int is_online_assistance) {
        this.is_online_assistance = is_online_assistance;
    }

    public List<TruePaperNewItemBean> getReal_paper_list() {
        return real_paper_list;
    }

    public void setReal_paper_list(List<TruePaperNewItemBean> real_paper_list) {
        this.real_paper_list = real_paper_list;
    }
}
