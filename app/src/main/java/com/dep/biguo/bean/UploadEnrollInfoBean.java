package com.dep.biguo.bean;

import java.util.Map;

public class UploadEnrollInfoBean {
    private String key;//后台返回的RSA密文
    private String name; //姓名
    private String identity_card_number; //身份证号码
    private String portrait;//头像
    private String identity_card_front;//身份证正面
    private String identity_card_reverse;//身份证反面
    private String academic_certificate; //学历证书
    private String academic_certificate_proof; //注册备案表/验证报告
    private String social_security_card_front;//社保卡正面
    private String social_security_card_reverse;//社保卡反面
    private String residence_permit_front;//居住证正面
    private String residence_permit_reverse;//居住证反面
    private String social_security_pay_proof;//社保缴纳证明
    private String others;//其他证件

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentity_card_number() {
        return identity_card_number;
    }

    public void setIdentity_card_number(String identity_card_number) {
        this.identity_card_number = identity_card_number;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getIdentity_card_front() {
        return identity_card_front;
    }

    public void setIdentity_card_front(String identity_card_front) {
        this.identity_card_front = identity_card_front;
    }

    public String getIdentity_card_reverse() {
        return identity_card_reverse;
    }

    public void setIdentity_card_reverse(String identity_card_reverse) {
        this.identity_card_reverse = identity_card_reverse;
    }

    public String getAcademic_certificate() {
        return academic_certificate;
    }

    public void setAcademic_certificate(String academic_certificate) {
        this.academic_certificate = academic_certificate;
    }

    public String getAcademic_certificate_proof() {
        return academic_certificate_proof;
    }

    public void setAcademic_certificate_proof(String academic_certificate_proof) {
        this.academic_certificate_proof = academic_certificate_proof;
    }

    public String getSocial_security_card_front() {
        return social_security_card_front;
    }

    public void setSocial_security_card_front(String social_security_card_front) {
        this.social_security_card_front = social_security_card_front;
    }

    public String getSocial_security_card_reverse() {
        return social_security_card_reverse;
    }

    public void setSocial_security_card_reverse(String social_security_card_reverse) {
        this.social_security_card_reverse = social_security_card_reverse;
    }

    public String getResidence_permit_front() {
        return residence_permit_front;
    }

    public void setResidence_permit_front(String residence_permit_front) {
        this.residence_permit_front = residence_permit_front;
    }

    public String getResidence_permit_reverse() {
        return residence_permit_reverse;
    }

    public void setResidence_permit_reverse(String residence_permit_reverse) {
        this.residence_permit_reverse = residence_permit_reverse;
    }

    public String getSocial_security_pay_proof() {
        return social_security_pay_proof;
    }

    public void setSocial_security_pay_proof(String social_security_pay_proof) {
        this.social_security_pay_proof = social_security_pay_proof;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }
}
