package com.dep.biguo.bean;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/26
 * @Description:
 */
public class AppVersionBean {
    private String name;//APP名称
    private String type;//Android-笔果自考题库:1，iOS-笔果自考题库:2
    private String version;//版本号
    private int institutions_id;//机构id
    private int is_examine;//审核状态：1开发中 2审核中，3已发布
    private int is_update;//是否有更新0否，1是,2强制更新
    private String content;//版本内容
    private String protocol_version;//隐私政策版本号
    private String app_download_link;//APP的下载地址

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getInstitutions_id() {
        return institutions_id;
    }

    public void setInstitutions_id(int institutions_id) {
        this.institutions_id = institutions_id;
    }

    public int getIs_examine() {
        return is_examine;
    }

    public void setIs_examine(int is_examine) {
        this.is_examine = is_examine;
    }

    public int getIs_update() {
        return is_update;
    }

    public void setIs_update(int is_update) {
        this.is_update = is_update;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getProtocol_version() {
        return protocol_version;
    }

    public void setProtocol_version(String protocol_version) {
        this.protocol_version = protocol_version;
    }

    public String getApp_download_link() {
        return app_download_link;
    }

    public void setApp_download_link(String app_download_link) {
        this.app_download_link = app_download_link;
    }
}
