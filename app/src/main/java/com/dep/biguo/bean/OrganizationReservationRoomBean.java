package com.dep.biguo.bean;

import java.util.List;

public class OrganizationReservationRoomBean {
    private int activities_id;//活动id
    private List<String> carousel;//分布图
    private String institutions_name;//机构名称
    private String open_time;//开放时间
    private int open_time_id;//开放时间段id
    private List<String> reserve_dates;
    private List<SelectionList> selection_list;

    public int getActivities_id() {
        return activities_id;
    }

    public void setActivities_id(int activities_id) {
        this.activities_id = activities_id;
    }

    public List<String> getCarousel() {
        return carousel;
    }

    public void setCarousel(List<String> carousel) {
        this.carousel = carousel;
    }

    public String getInstitutions_name() {
        return institutions_name;
    }

    public void setInstitutions_name(String institutions_name) {
        this.institutions_name = institutions_name;
    }

    public String getOpen_time() {
        return open_time;
    }

    public void setOpen_time(String open_time) {
        this.open_time = open_time;
    }

    public int getOpen_time_id() {
        return open_time_id;
    }

    public void setOpen_time_id(int open_time_id) {
        this.open_time_id = open_time_id;
    }

    public List<String> getReserve_dates() {
        return reserve_dates;
    }

    public void setReserve_dates(List<String> reserve_dates) {
        this.reserve_dates = reserve_dates;
    }

    public List<SelectionList> getSelection_list() {
        return selection_list;
    }

    public void setSelection_list(List<SelectionList> selection_list) {
        this.selection_list = selection_list;
    }

    public static class SelectionList {
        private String region_name;//区域名称
        private String region;//区域号
        private List<Seat> seat;

        public String getRegion_name() {
            return region_name;
        }

        public void setRegion_name(String region_name) {
            this.region_name = region_name;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public List<Seat> getSeat() {
            return seat;
        }

        public void setSeat(List<Seat> seat) {
            this.seat = seat;
        }
    }

    public static class Seat {
        private int reserve_status;//预约状态  0=可预约 1=已预约 2=已约满 3=该时间段已有预约
        private String number;//座位号
        private String surplus_time_period;//剩余可预约时段数量

        public int getReserve_status() {
            return reserve_status;
        }

        public void setReserve_status(int reserve_status) {
            this.reserve_status = reserve_status;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getSurplus_time_period() {
            return surplus_time_period;
        }

        public void setSurplus_time_period(String surplus_time_period) {
            this.surplus_time_period = surplus_time_period;
        }
    }
}
