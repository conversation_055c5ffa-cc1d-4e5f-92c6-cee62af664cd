package com.dep.biguo.bean;

import java.util.List;

public class SkillVideosBean {
    private String title;//标题
    private List<VideoBean> videos;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<VideoBean> getVideos() {
        return videos;
    }

    public void setVideos(List<VideoBean> videos) {
        this.videos = videos;
    }

    public static class VideoBean{
        private String name;//课程名称
        private String image;//套餐封面
        private String teacher_name;//课程老师
        private String social_title;//老师头衔
        private int source_type;//来源类型：笔果:0，必过:1，时刻:2
        private int buy_count;//购买份数
        private int product_id;//商品id
        private String course_id;//课程id
        private int chapter_id;//章节id
        private int item_id;//视频id
        private int is_free;//0付费，1免费
        private int skill_id;//
        private int cert_type;//考试类型：0自考，1成考，2技能证，9职场提升
        private String type;//商品类型
        //必过视频独有字段
        private int cst_id;//必过班型id

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getTeacher_name() {
            return teacher_name;
        }

        public void setTeacher_name(String teacher_name) {
            this.teacher_name = teacher_name;
        }

        public String getSocial_title() {
            return social_title;
        }

        public void setSocial_title(String social_title) {
            this.social_title = social_title;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getBuy_count() {
            return buy_count;
        }

        public void setBuy_count(int buy_count) {
            this.buy_count = buy_count;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public String getCourse_id() {
            return course_id;
        }

        public void setCourse_id(String course_id) {
            this.course_id = course_id;
        }

        public int getChapter_id() {
            return chapter_id;
        }

        public void setChapter_id(int chapter_id) {
            this.chapter_id = chapter_id;
        }

        public int getItem_id() {
            return item_id;
        }

        public void setItem_id(int item_id) {
            this.item_id = item_id;
        }

        public int getIs_free() {
            return is_free;
        }

        public void setIs_free(int is_free) {
            this.is_free = is_free;
        }

        public int getSkill_id() {
            return skill_id;
        }

        public void setSkill_id(int skill_id) {
            this.skill_id = skill_id;
        }

        public int getCert_type() {
            return cert_type;
        }

        public void setCert_type(int cert_type) {
            this.cert_type = cert_type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getCst_id() {
            return cst_id;
        }

        public void setCst_id(int cst_id) {
            this.cst_id = cst_id;
        }
    }
}
