package com.dep.biguo.bean;

import java.util.List;

public class UpdateQuestionBean {
    private int version;
    private String key;
    private String source;
    private List<DeleteTopics> delete_topics;
    private List<QuestionBean> update_topics;
    private List<String> fileUrls;//文件地址

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<DeleteTopics> getDelete_topics() {
        return delete_topics;
    }

    public void setDelete_topics(List<DeleteTopics> delete_topics) {
        this.delete_topics = delete_topics;
    }

    public List<QuestionBean> getUpdate_topics() {
        return update_topics;
    }

    public void setUpdate_topics(List<QuestionBean> update_topics) {
        this.update_topics = update_topics;
    }

    public List<String> getFileUrls() {
        return fileUrls;
    }

    public void setFileUrls(List<String> fileUrls) {
        this.fileUrls = fileUrls;
    }

    public static class DeleteTopics{
        private int mainType;
        private int id;
        private String value;

        public int getMainType() {
            return mainType;
        }

        public void setMainType(int mainType) {
            this.mainType = mainType;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
