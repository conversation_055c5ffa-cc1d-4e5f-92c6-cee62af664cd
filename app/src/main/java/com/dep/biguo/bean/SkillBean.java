package com.dep.biguo.bean;

import java.util.List;

public class SkillBean {
    private List<Banner> swipers;
    private List<ScrollBean> certs;
    private List<Icon> icons;

    public List<Banner> getSwipers() {
        return swipers;
    }

    public void setSwipers(List<Banner> swipers) {
        this.swipers = swipers;
    }

    public List<ScrollBean> getCerts() {
        return certs;
    }

    public void setCerts(List<ScrollBean> certs) {
        this.certs = certs;
    }

    public List<Icon> getIcons() {
        return icons;
    }

    public void setIcons(List<Icon> icons) {
        this.icons = icons;
    }

    public static class ScrollBean{
        private int skill_id;///科目id
        private String name;//技能证名称
        private int source_type;//来源类型：笔果:0，必过:1，时刻:2

        public int getSkill_id() {
            return skill_id;
        }

        public void setSkill_id(int skill_id) {
            this.skill_id = skill_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }
    }

    public static class Banner{
        private int id;//ID
        private String name;//名字
        private String img;//图片
        private String target_url;//跳转链接
        private String xcx_path;//小程序路径

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }

    public static class Icon{
        private String type;//类型，根据类型设置跳转页面
        private String name;//文字
        private String img;//图片
        private String xcx_path;//小程序路径
        private String sub_type;//h5或xcx，用于判断target_url是h5还是小程序
        private String target_url;//跳转的链接
        private String tip_img;//角标
        private int need_login;//是否需要先登录才能跳转小程序

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public String getSub_type() {
            return sub_type;
        }

        public void setSub_type(String sub_type) {
            this.sub_type = sub_type;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getTip_img() {
            return tip_img;
        }

        public void setTip_img(String tip_img) {
            this.tip_img = tip_img;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }
    }
}
