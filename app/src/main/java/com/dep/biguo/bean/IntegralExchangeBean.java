package com.dep.biguo.bean;

public class IntegralExchangeBean {
    private String title; //页面标题
    private String subtitle;//副标题
    private String exchange_price; //兑换价格
    private int stock; //已兑换人数
    private String rules; //规则
    private Style styles;//规则中的样式
    private String integral; //账户积分
    private int need_sign_days;//还需多少天连续打卡可获取兑换资格 0就是可以兑换
    private int opend_all; //是否当前专业已全部永久开通 0否 1是
    private String image;//弹窗的缩略图
    private Master_graph master_graph;//封面图
    private String describe;//商品详情富文本

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getExchange_price() {
        return exchange_price;
    }

    public void setExchange_price(String exchange_price) {
        this.exchange_price = exchange_price;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public Style getStyles() {
        return styles;
    }

    public void setStyles(Style styles) {
        this.styles = styles;
    }

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public int getNeed_sign_days() {
        return need_sign_days;
    }

    public void setNeed_sign_days(int need_sign_days) {
        this.need_sign_days = need_sign_days;
    }

    public int getOpend_all() {
        return opend_all;
    }

    public void setOpend_all(int opend_all) {
        this.opend_all = opend_all;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Master_graph getMaster_graph() {
        return master_graph;
    }

    public void setMaster_graph(Master_graph master_graph) {
        this.master_graph = master_graph;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public static class Style{
        private String[] rule_red;//需要标红的字符串
        private String[] under_line;//需要加下划线的字符串
        private String[] link;//需要加点击事件的字符串

        public String[] getRule_red() {
            return rule_red;
        }

        public void setRule_red(String[] rule_red) {
            this.rule_red = rule_red;
        }

        public String[] getLink() {
            return link;
        }

        public void setLink(String[] link) {
            this.link = link;
        }

        public String[] getUnder_line() {
            return under_line;
        }

        public void setUnder_line(String[] under_line) {
            this.under_line = under_line;
        }
    }

    public static class Master_graph{
        private String graph_type;//url的类型
        private String url;//图片或视频

        public String getGraph_type() {
            return graph_type;
        }

        public void setGraph_type(String graph_type) {
            this.graph_type = graph_type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
