package com.dep.biguo.bean;

import com.dep.biguo.bean.ck.CKProvinceBean;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/24
 * @Description: 用户
 */
public class UserBean {
    private String token;
    private int user_id;//用户id
    private int province_id;//省份id
    private String province_name; //省份名称
    private int city_id;//城市id
    private String city_code;//城市编码
    private String city_name; //城市名称
    private int school_id; //学校id
    private String school_name;//学校名称
    private int professions_id;//专业id
    private String professions_name;//专业名称
    private String professions_code;//专业代码
    private int membership;//是否会员 1是0否
    private String membership_expire;//会员有效期
    private String member_discount;//笔果折扣卡的折扣数，与开通时设置的折扣有关
    private int pass_set;//是否设置密码
    private int is_extension;//是否分销
    private String fruit_coin;//永久果币
    private String free_fruit_coin; //免费果币数量
    private String freecoin_expire_time;//免费果币到期时间
    private String coin_near_expire; //马上要到期的免费果币（30天内）
    private String integral;//积分
    private String nickname;//昵称
    private String email;//email
    private String mobile;//手机号
    private String avatar;//头像
    private String realname;//真实姓名
    private String _idcard;//身份证
    private String created_at; //注册时间
    private String institutions_name;//机构名称
    private int is_label;//是否被打过标签
    private int is_new_users;//是否是新用户
    private long new_user_end_time;//新用户状态结束倒计时
    private int layer;//1本科，2专科
    private String is_super_vip;//是否是超级VIP 1是，0否
    private String kefu_password;//环信的登录密码
    private String kefu_token;//环信的登录token
    private String kefu_seller_username;//客服的IM账号，用于环信聊天
    private String rsa_mobile;//加密后的手机号
    private String seller_mobile;//客服的手机号
    private CkInfoBean adult_professions;//成考专业信息
    private int is_register;//0登录，1注册
    private int is_course_modify;//是否有课改课程：1是0否

    private String unionid;//微信绑定时的id
    private int is_create;//登录时返回，使用该类进行解析

    private String openid;//QQ绑定时的id

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getUser_id() {
        return user_id;
    }

    public void setUser_id(int user_id) {
        this.user_id = user_id;
    }

    public int getProvince_id() {
        return province_id;
    }

    public void setProvince_id(int province_id) {
        this.province_id = province_id;
    }

    public String getProvince_name() {
        return province_name;
    }

    public void setProvince_name(String province_name) {
        this.province_name = province_name;
    }

    public int getCity_id() {
        return city_id;
    }

    public void setCity_id(int city_id) {
        this.city_id = city_id;
    }

    public String getCity_code() {
        return city_code;
    }

    public void setCity_code(String city_code) {
        this.city_code = city_code;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public int getSchool_id() {
        return school_id;
    }

    public void setSchool_id(int school_id) {
        this.school_id = school_id;
    }

    public String getSchool_name() {
        return school_name;
    }

    public void setSchool_name(String school_name) {
        this.school_name = school_name;
    }

    public int getProfessions_id() {
        return professions_id;
    }

    public void setProfessions_id(int professions_id) {
        this.professions_id = professions_id;
    }

    public String getProfessions_name() {
        return professions_name;
    }

    public void setProfessions_name(String professions_name) {
        this.professions_name = professions_name;
    }

    public String getProfessions_code() {
        return professions_code;
    }

    public void setProfessions_code(String professions_code) {
        this.professions_code = professions_code;
    }

    public int getMembership() {
        return membership;
    }

    public void setMembership(int membership) {
        this.membership = membership;
    }

    public String getMembership_expire() {
        return membership_expire;
    }

    public void setMembership_expire(String membership_expire) {
        this.membership_expire = membership_expire;
    }

    public String getMember_discount() {
        return member_discount;
    }

    public void setMember_discount(String member_discount) {
        this.member_discount = member_discount;
    }

    public int getPass_set() {
        return pass_set;
    }

    public void setPass_set(int pass_set) {
        this.pass_set = pass_set;
    }

    public int getIs_extension() {
        return is_extension;
    }

    public void setIs_extension(int is_extension) {
        this.is_extension = is_extension;
    }

    public String getFruit_coin() {
        return fruit_coin;
    }

    public void setFruit_coin(String fruit_coin) {
        this.fruit_coin = fruit_coin;
    }

    public String getFree_fruit_coin() {
        return free_fruit_coin;
    }

    public void setFree_fruit_coin(String free_fruit_coin) {
        this.free_fruit_coin = free_fruit_coin;
    }

    public String getFreecoin_expire_time() {
        return freecoin_expire_time;
    }

    public void setFreecoin_expire_time(String freecoin_expire_time) {
        this.freecoin_expire_time = freecoin_expire_time;
    }

    public String getCoin_near_expire() {
        return coin_near_expire;
    }

    public void setCoin_near_expire(String coin_near_expire) {
        this.coin_near_expire = coin_near_expire;
    }

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String get_idcard() {
        return _idcard;
    }

    public void set_idcard(String _idcard) {
        this._idcard = _idcard;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getInstitutions_name() {
        return institutions_name;
    }

    public void setInstitutions_name(String institutions_name) {
        this.institutions_name = institutions_name;
    }

    public int getIs_label() {
        return is_label;
    }

    public void setIs_label(int is_label) {
        this.is_label = is_label;
    }

    public int getIs_new_users() {
        return is_new_users;
    }

    public void setIs_new_users(int is_new_users) {
        this.is_new_users = is_new_users;
    }

    public long getNew_user_end_time() {
        return new_user_end_time;
    }

    public void setNew_user_end_time(long new_user_end_time) {
        this.new_user_end_time = new_user_end_time;
    }

    public int getLayer() {
        return layer;
    }

    public void setLayer(int layer) {
        this.layer = layer;
    }

    public String getIs_super_vip() {
        return is_super_vip;
    }

    public void setIs_super_vip(String is_super_vip) {
        this.is_super_vip = is_super_vip;
    }

    public String getKefu_password() {
        return kefu_password;
    }

    public void setKefu_password(String kefu_password) {
        this.kefu_password = kefu_password;
    }

    public String getKefu_token() {
        return kefu_token;
    }

    public void setKefu_token(String kefu_token) {
        this.kefu_token = kefu_token;
    }

    public String getKefu_seller_username() {
        return kefu_seller_username;
    }

    public void setKefu_seller_username(String kefu_seller_username) {
        this.kefu_seller_username = kefu_seller_username;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public int getIs_create() {
        return is_create;
    }

    public void setIs_create(int is_create) {
        this.is_create = is_create;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getRsa_mobile() {
        return rsa_mobile;
    }

    public void setRsa_mobile(String rsa_mobile) {
        this.rsa_mobile = rsa_mobile;
    }

    public String getSeller_mobile() {
        return seller_mobile;
    }

    public void setSeller_mobile(String seller_mobile) {
        this.seller_mobile = seller_mobile;
    }

    public CkInfoBean getAdult_professions() {
        return adult_professions;
    }

    public void setAdult_professions(CkInfoBean adult_professions) {
        this.adult_professions = adult_professions;
    }

    public int getIs_register() {
        return is_register;
    }

    public void setIs_register(int is_register) {
        this.is_register = is_register;
    }

    public int getIs_course_modify() {
        return is_course_modify;
    }

    public void setIs_course_modify(int is_course_modify) {
        this.is_course_modify = is_course_modify;
    }

    public static class CkInfoBean{
        private int province_id;
        private String province_name;
        private int school_id;
        private String school_name;
        private int id;
        private int adult_professions_id;
        private String adult_professions_name;
        private String city_name;
        private int city_id;

        //层次接口返回
        private int form_id;
        private int layer_id;
        private int professions_id;
        private String form_name;
        private String layer_name;
        private String professions_name;

        public int getProvince_id() {
            return province_id;
        }

        public void setProvince_id(int province_id) {
            this.province_id = province_id;
        }

        public String getProvince_name() {
            return province_name;
        }

        public void setProvince_name(String province_name) {
            this.province_name = province_name;
        }

        public int getSchool_id() {
            return school_id;
        }

        public void setSchool_id(int school_id) {
            this.school_id = school_id;
        }

        public String getSchool_name() {
            return school_name;
        }

        public void setSchool_name(String school_name) {
            this.school_name = school_name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getAdult_professions_id() {
            return adult_professions_id;
        }

        public void setAdult_professions_id(int adult_professions_id) {
            this.adult_professions_id = adult_professions_id;
        }

        public String getAdult_professions_name() {
            return adult_professions_name;
        }

        public void setAdult_professions_name(String adult_professions_name) {
            this.adult_professions_name = adult_professions_name;
        }

        public int getForm_id() {
            return form_id;
        }

        public void setForm_id(int form_id) {
            this.form_id = form_id;
        }

        public int getLayer_id() {
            return layer_id;
        }

        public void setLayer_id(int layer_id) {
            this.layer_id = layer_id;
        }

        public int getProfessions_id() {
            return professions_id;
        }

        public void setProfessions_id(int professions_id) {
            this.professions_id = professions_id;
        }

        public String getCity_name() {
            return city_name;
        }

        public void setCity_name(String city_name) {
            this.city_name = city_name;
        }

        public int getCity_id() {
            return city_id;
        }

        public void setCity_id(int city_id) {
            this.city_id = city_id;
        }

        public String getForm_name() {
            return form_name;
        }

        public void setForm_name(String form_name) {
            this.form_name = form_name;
        }

        public String getLayer_name() {
            return layer_name;
        }

        public void setLayer_name(String layer_name) {
            this.layer_name = layer_name;
        }

        public String getProfessions_name() {
            return professions_name;
        }

        public void setProfessions_name(String professions_name) {
            this.professions_name = professions_name;
        }


    }
}
