package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.dep.biguo.mvp.ui.adapter.CommentReportAdapter;

import java.util.List;

public class CommentReportBean extends AbstractExpandableItem<CommentReportBean> implements MultiItemEntity {
    private String text;
    private String value;
    private int type;
    private List<CommentReportBean> options;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<CommentReportBean> getOptions() {
        return options;
    }

    public void setOptions(List<CommentReportBean> options) {
        this.options = options;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public int getItemType() {
        return type;
    }

    @Override
    public int getLevel() {
        return type;
    }
}
