package com.dep.biguo.bean;

import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.DisplayHelper;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.widget.IconView;

import java.util.List;

public class CkHomeIconAdapter extends CommonAdapter<IconBean> {
    private int itemWidth;

    public void setItemWidth(int itemWidth) {
        this.itemWidth = itemWidth;
    }

    public CkHomeIconAdapter(@Nullable List<IconBean> data) {
        super(0, data);
    }

    @Override
    protected BaseViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
        IconView iconView = new IconView(mContext);
        int dp42 = DisplayHelper.dp2px(mContext, 42);
        iconView.setIconWidth(dp42);
        iconView.setIconHeight(dp42);
        iconView.setLayoutParams(new RecyclerView.LayoutParams(itemWidth, RelativeLayout.LayoutParams.WRAP_CONTENT));
        return new BaseViewHolder(iconView);
    }

    @Override
    protected void convert(BaseViewHolder holder, IconBean item) {
        IconView iconView = (IconView) holder.itemView;
        iconView.setData(item.getImg(), item.getName());
        iconView.setXcx_path(item.getXcx_path());
        iconView.setTarget_url(item.getTarget_url());
        iconView.setShowTipImage(item.getTip_img());

        RecyclerView.LayoutParams iconViewLayoutParams = (RecyclerView.LayoutParams) iconView.getLayoutParams();
        if(holder.getAbsoluteAdapterPosition() < 4){
            iconViewLayoutParams.topMargin = DisplayHelper.dp2px(mContext, 0);
        }else {
            iconViewLayoutParams.topMargin = DisplayHelper.dp2px(mContext, 18);
        }
    }
}
