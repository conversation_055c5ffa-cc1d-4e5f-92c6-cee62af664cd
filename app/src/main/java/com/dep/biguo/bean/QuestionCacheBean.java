package com.dep.biguo.bean;

import com.dep.biguo.utils.database.ChapterSectionsRealBean;

import java.util.List;

import io.realm.RealmList;

public class QuestionCacheBean {
    private String code;//课程代码
    private int mainType;//题库
    private String name;//课程名称
    private String mainTypeName;//题库名称
    private String real_id;//真题ID
    private String chapter_sections_id;//章节ID
    private String chapter_id;//章节(小节)ID
    private String paper_id;//模拟试卷ID
    private String tpoic_name;//题库名称
    private String chapter_name;//小节名称
    private String real_name;//真题名称
    private List<QuestionCacheBean> sections;
    private boolean isTopic;//是否是题库

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMainTypeName() {
        return mainTypeName;
    }

    public void setMainTypeName(String mainTypeName) {
        this.mainTypeName = mainTypeName;
    }

    public String getReal_id() {
        return real_id;
    }

    public void setReal_id(String real_id) {
        this.real_id = real_id;
    }

    public String getChapter_sections_id() {
        return chapter_sections_id;
    }

    public void setChapter_sections_id(String chapter_sections_id) {
        this.chapter_sections_id = chapter_sections_id;
    }

    public String getChapter_id() {
        return chapter_id;
    }

    public void setChapter_id(String chapter_id) {
        this.chapter_id = chapter_id;
    }

    public String getPaper_id() {
        return paper_id;
    }

    public void setPaper_id(String paper_id) {
        this.paper_id = paper_id;
    }

    public String getTpoic_name() {
        return tpoic_name;
    }

    public void setTpoic_name(String tpoic_name) {
        this.tpoic_name = tpoic_name;
    }

    public String getChapter_name() {
        return chapter_name;
    }

    public void setChapter_name(String chapter_name) {
        this.chapter_name = chapter_name;
    }

    public String getReal_name() {
        return real_name;
    }

    public void setReal_name(String real_name) {
        this.real_name = real_name;
    }

    public List<QuestionCacheBean> getSections() {
        return sections;
    }

    public void setSections(List<QuestionCacheBean> sections) {
        this.sections = sections;
    }

    public boolean isTopic() {
        return isTopic;
    }

    public void setTopic(boolean topic) {
        isTopic = topic;
    }
}
