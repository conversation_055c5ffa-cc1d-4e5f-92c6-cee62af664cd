package com.dep.biguo.bean;

public class LiveBeegoBean {
    private int liveScene;
    private String teacherName;//老师名称
    private String productId;//商品id
    private String endDate;//直播结束时间
    private String cstId;//班型id
    private String roomImg;
    private String startTime;//直播开始时间
    private String title; //直播名称
    private String courseId;//课程id（课程代码）
    private int channelId;//直播id
    private int liveStatus;//直播状态(0-未开始；1-直播中；2-已结束/回放)
    private String playUrl;//直播地址

    public int getLiveScene() {
        return liveScene;
    }

    public void setLiveScene(int liveScene) {
        this.liveScene = liveScene;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getCstId() {
        return cstId;
    }

    public void setCstId(String cstId) {
        this.cstId = cstId;
    }

    public String getRoomImg() {
        return roomImg;
    }

    public void setRoomImg(String roomImg) {
        this.roomImg = roomImg;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(int liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }
}
