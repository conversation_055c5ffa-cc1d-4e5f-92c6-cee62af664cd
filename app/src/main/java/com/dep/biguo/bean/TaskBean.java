package com.dep.biguo.bean;

import com.dep.biguo.R;

import java.util.List;

public class TaskBean {
    public static String ADD_WX = "add_wx";//添加客服微信图片
    public static String FIRST_SIGN = "first_sign";//完成第一次打卡
    public static String YAMI_INFO = "yami_info";//完善押密信息
    public static String WX_GROUP = "wx_group";//添加备考交流群
    public static String QUESTIONNAIRE = "questionnaire";//调查问卷
    public static String DAY_SIGN = "day_sign";//签到打卡
    public static String DAY_ANSWER = "day_answer";//做题
    public static String DAY_POST = "day_post";//圈子发帖
    public static String DAY_SHARE = "day_share";//分享笔果App
    public static String PAY = "pay";//支付福利
    public static String PRODUCT_COMMENT = "product_comment";//评价一次xxxx（商品）
    public static String PROMOTER = "promoter";//成为笔果推广员
    public static String MEMBER = "member";//开通笔果折扣卡
    public static String YAMI = "yami";//购买一次押密
    public static String WX_PUBLIC = "wx_gzh";//关注官方公众号
    public static String DOUYIN = "douyin";//关注官方抖音号
    public static String APP_COMMENT = "give_good_review";//APP评论/吐槽

    private String integral; //当前积分
    private int seller_id; //专属客服id
    private String seller_wx; //专属客服微信
    private String valid_integral;//即将过期的积分
    private int order_id;//订单id
    private int order_type;//订单类型（消费订单、服务订单、图书订单）
    private List<Bean> new_user_tasks;//新手任务
    private List<Bean> daily_tasks;//每日任务
    private List<Bean> long_term_tasks;//长期任务

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public int getSeller_id() {
        return seller_id;
    }

    public void setSeller_id(int seller_id) {
        this.seller_id = seller_id;
    }

    public String getSeller_wx() {
        return seller_wx;
    }

    public void setSeller_wx(String seller_wx) {
        this.seller_wx = seller_wx;
    }

    public String getValid_integral() {
        return valid_integral;
    }

    public void setValid_integral(String valid_integral) {
        this.valid_integral = valid_integral;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public int getOrder_type() {
        return order_type;
    }

    public void setOrder_type(int order_type) {
        this.order_type = order_type;
    }

    public List<Bean> getNew_user_tasks() {
        return new_user_tasks;
    }

    public void setNew_user_tasks(List<Bean> new_user_tasks) {
        this.new_user_tasks = new_user_tasks;
    }

    public List<Bean> getDaily_tasks() {
        return daily_tasks;
    }

    public void setDaily_tasks(List<Bean> daily_tasks) {
        this.daily_tasks = daily_tasks;
    }

    public List<Bean> getLong_term_tasks() {
        return long_term_tasks;
    }

    public void setLong_term_tasks(List<Bean> long_term_tasks) {
        this.long_term_tasks = long_term_tasks;
    }

    public static class Bean{
        private String name;//任务名称
        private String desc;//任务描述
        private String type; //任务类型
        private int limit; //限制次数
        private int prize; //任务奖励
        private int done;//已做次数
        private int status; //1去完成2去领取3已完成 以文案为准
        private String task_icon;//任务图标
        private int[] prize_ids;//奖品列表

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public int getPrize() {
            return prize;
        }

        public void setPrize(int prize) {
            this.prize = prize;
        }

        public int getDone() {
            return done;
        }

        public void setDone(int done) {
            this.done = done;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getTask_icon() {
            return task_icon;
        }

        public void setTask_icon(String task_icon) {
            this.task_icon = task_icon;
        }

        public int[] getPrize_ids() {
            return prize_ids;
        }

        public void setPrize_ids(int[] prize_ids) {
            this.prize_ids = prize_ids;
        }
    }
}
