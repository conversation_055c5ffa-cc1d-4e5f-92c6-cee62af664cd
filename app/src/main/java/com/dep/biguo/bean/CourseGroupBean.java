package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/30
 * @Description:
 */
public class CourseGroupBean extends BaseResponse<List<CourseGroupBean>> {

    private String group;
    private List<CourseBean> courses;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public List<CourseBean> getCourses() {
        return courses;
    }

    public void setCourses(List<CourseBean> courses) {
        this.courses = courses;
    }

    public static class CourseBean implements MultiItemEntity, Serializable {

        private int id;
        private String code;
        private String name;
        private int joined;
        private int type;
        private int top;
        private String score;

        /**
         * 头部
         **/
        private int itemType;
        private String headName;
        private boolean isEmpty;

        public CourseBean(int itemType, String headName, boolean isEmpty) {
            this.itemType = itemType;
            this.headName = headName;
            this.isEmpty = isEmpty;
        }

        public CourseBean() {

        }

        public boolean isEmpty() {
            return isEmpty;
        }

        public String getScore() {
            return score;
        }

        public void setScore(String score) {
            this.score = score;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getJoined() {
            return joined;
        }

        public void setJoined(int joined) {
            this.joined = joined;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getTop() {
            return top;
        }

        public void setTop(int top) {
            this.top = top;
        }

        public void setItemType(int itemType) {
            this.itemType = itemType;
        }

        public void setHeadName(String headName) {
            this.headName = headName;
        }

        public void setEmpty(boolean empty) {
            isEmpty = empty;
        }

        @Override
        public int getItemType() {
            return itemType;
        }

        public String getHeadName() {
            return headName;
        }
    }

}
