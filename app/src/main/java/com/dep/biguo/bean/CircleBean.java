package com.dep.biguo.bean;

import java.util.List;

public class CircleBean {
    private List<Topic> hotList;
    private List<Moment> publishList;

    public List<Topic> getHotList() {
        return hotList;
    }

    public void setHotList(List<Topic> hotList) {
        this.hotList = hotList;
    }

    public List<Moment> getPublishList() {
        return publishList;
    }

    public void setPublishList(List<Moment> publishList) {
        this.publishList = publishList;
    }

    public static class Topic {
        private int posts_table_id;
        private String name;//话题名称
        private int browse_num;//浏览数
        private int join_people;//参与人数
        private String desc;//描述
        private List<String> color;//背景颜色，十六进制值，第一个值是渐变色的起始颜色，第二个是终止颜色

        public int getPosts_table_id() {
            return posts_table_id;
        }

        public void setPosts_table_id(int posts_table_id) {
            this.posts_table_id = posts_table_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getBrowse_num() {
            return browse_num;
        }

        public void setBrowse_num(int browse_num) {
            this.browse_num = browse_num;
        }

        public int getJoin_people() {
            return join_people;
        }

        public void setJoin_people(int join_people) {
            this.join_people = join_people;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public List<String> getColor() {
            return color;
        }

        public void setColor(List<String> color) {
            this.color = color;
        }
    }

    public static class Moment {
        private int users_id;//发布人ID
        private int posts_id;//动态ID
        private String avatar;//头像
        private String nickname;//昵称
        private String created_at;//创建时间
        private String ip_location;//发布动态时的所处省份
        private List<Topic> hot_topic;//话题
        private String content;//动态文本内容
        private List<Media> files;//图片或视频
        private int relay_count;//分享数量
        private int comments_count;//评论数量
        private int likes_count;//点赞数量
        private int is_like;//是否点赞，0否，1是
        private int audit_status;//审核状态，0审核中，1审核通过
        private int top;//是否是置顶动态
        private int recommend;//0不推荐，1推荐
        private int is_official;//0不是，1是
        private AdBean ad;//广告

        public int getUsers_id() {
            return users_id;
        }

        public void setUsers_id(int users_id) {
            this.users_id = users_id;
        }

        public int getPosts_id() {
            return posts_id;
        }

        public void setPosts_id(int posts_id) {
            this.posts_id = posts_id;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getCreated_at() {
            return created_at;
        }

        public void setCreated_at(String created_at) {
            this.created_at = created_at;
        }

        public String getIp_location() {
            return ip_location;
        }

        public void setIp_location(String ip_location) {
            this.ip_location = ip_location;
        }

        public List<Topic> getHot_topic() {
            return hot_topic;
        }

        public void setHot_topic(List<Topic> hot_topic) {
            this.hot_topic = hot_topic;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public List<Media> getFiles() {
            return files;
        }

        public void setFiles(List<Media> files) {
            this.files = files;
        }

        public int getRelay_count() {
            return relay_count;
        }

        public void setRelay_count(int relay_count) {
            this.relay_count = relay_count;
        }

        public int getComments_count() {
            return comments_count;
        }

        public void setComments_count(int comments_count) {
            this.comments_count = comments_count;
        }

        public int getLikes_count() {
            return likes_count;
        }

        public void setLikes_count(int likes_count) {
            this.likes_count = likes_count;
        }

        public int getIs_like() {
            return is_like;
        }

        public void setIs_like(int is_like) {
            this.is_like = is_like;
        }

        public int getAudit_status() {
            return audit_status;
        }

        public void setAudit_status(int audit_status) {
            this.audit_status = audit_status;
        }

        public int getTop() {
            return top;
        }

        public void setTop(int top) {
            this.top = top;
        }

        public int getRecommend() {
            return recommend;
        }

        public void setRecommend(int recommend) {
            this.recommend = recommend;
        }

        public int getIs_official() {
            return is_official;
        }

        public void setIs_official(int is_official) {
            this.is_official = is_official;
        }

        public AdBean getAd() {
            return ad;
        }

        public void setAd(AdBean ad) {
            this.ad = ad;
        }
    }

    public static class Media{
        private String video_url;
        private String cover_url;
        private String type;
        private int width;
        private int height;

        public String getVideo_url() {
            return video_url;
        }

        public void setVideo_url(String video_url) {
            this.video_url = video_url;
        }

        public String getCover_url() {
            return cover_url;
        }

        public void setCover_url(String cover_url) {
            this.cover_url = cover_url;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }
    }
}
