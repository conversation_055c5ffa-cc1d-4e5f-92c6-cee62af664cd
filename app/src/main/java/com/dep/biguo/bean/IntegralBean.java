package com.dep.biguo.bean;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/27
 * @Description:
 */
public class IntegralBean {

    private String integral;
    private int sign_in_num;
    private int sign_in_integral;
    private int is_sign_in;
    private int share_num;
    private int share_integral;
    private int is_posts;
    private int is_exercise;
    private String img_url;
    private String details_url;

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public int getSign_in_num() {
        return sign_in_num;
    }

    public void setSign_in_num(int sign_in_num) {
        this.sign_in_num = sign_in_num;
    }

    public int getSign_in_integral() {
        return sign_in_integral;
    }

    public void setSign_in_integral(int sign_in_integral) {
        this.sign_in_integral = sign_in_integral;
    }

    public int getIs_sign_in() {
        return is_sign_in;
    }

    public void setIs_sign_in(int is_sign_in) {
        this.is_sign_in = is_sign_in;
    }

    public int getShare_num() {
        return share_num;
    }

    public void setShare_num(int share_num) {
        this.share_num = share_num;
    }

    public int getShare_integral() {
        return share_integral;
    }

    public void setShare_integral(int share_integral) {
        this.share_integral = share_integral;
    }

    public int getIs_posts() {
        return is_posts;
    }

    public void setIs_posts(int is_posts) {
        this.is_posts = is_posts;
    }

    public int getIs_exercise() {
        return is_exercise;
    }

    public void setIs_exercise(int is_exercise) {
        this.is_exercise = is_exercise;
    }

    public String getImg_url() {
        return img_url;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    public String getDetails_url() {
        return details_url;
    }

    public void setDetails_url(String details_url) {
        this.details_url = details_url;
    }
}
