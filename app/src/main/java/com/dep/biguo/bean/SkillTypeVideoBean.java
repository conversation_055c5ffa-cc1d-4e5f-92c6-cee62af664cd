package com.dep.biguo.bean;

public class SkillTypeVideoBean {
    private int product_id;//商品id
    private int skill_id;
    private int cert_type;
    private String course_id;//课程id
    private int chapter_id;//课程章id
    private int item_id;//课程节id
    private int cst_id;
    private int is_free;//是否免费：1是，0否
    private String name;//课程名
    private String image;//图片
    private String teacher_name;//老师名称
    private String social_title;//头衔
    private int buy_count;//购买人数
    private int source_type;//来源类型：笔果:0，必过:1，时刻:2
    private String type;//商品类型

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }

    public String getCourse_id() {
        return course_id;
    }

    public void setCourse_id(String course_id) {
        this.course_id = course_id;
    }

    public int getChapter_id() {
        return chapter_id;
    }

    public void setChapter_id(int chapter_id) {
        this.chapter_id = chapter_id;
    }

    public int getItem_id() {
        return item_id;
    }

    public void setItem_id(int item_id) {
        this.item_id = item_id;
    }

    public int getCst_id() {
        return cst_id;
    }

    public void setCst_id(int cst_id) {
        this.cst_id = cst_id;
    }

    public int getIs_free() {
        return is_free;
    }

    public void setIs_free(int is_free) {
        this.is_free = is_free;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }

    public String getSocial_title() {
        return social_title;
    }

    public void setSocial_title(String social_title) {
        this.social_title = social_title;
    }

    public int getBuy_count() {
        return buy_count;
    }

    public void setBuy_count(int buy_count) {
        this.buy_count = buy_count;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
