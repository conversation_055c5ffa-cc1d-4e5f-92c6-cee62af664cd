package com.dep.biguo.bean;

import java.util.List;

public class StudyPlanBean {
    private int plan_id;//计划ID
    private int status;//计划的状态：1计划中 2计划已完成 0计划取消
    private int plan_day;//计划总天数
    private int study_day;//已学天数
    private int remaining_day;//剩余学习天数
    private int earned_integral;//已获得积分
    private int answer_nums;//答题数量
    private int watch_time;//学习时长
    private String start_time;//计划开始时间
    private String end_time;//计划结束时间
    private List<TargetCourse> today_study_targets;//今日学习目标
    private List<PlanCourse> plan_study_courses;//计划学习课程

    public int getPlan_id() {
        return plan_id;
    }

    public void setPlan_id(int plan_id) {
        this.plan_id = plan_id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPlan_day() {
        return plan_day;
    }

    public void setPlan_day(int plan_day) {
        this.plan_day = plan_day;
    }

    public int getStudy_day() {
        return study_day;
    }

    public void setStudy_day(int study_day) {
        this.study_day = study_day;
    }

    public int getRemaining_day() {
        return remaining_day;
    }

    public void setRemaining_day(int remaining_day) {
        this.remaining_day = remaining_day;
    }

    public int getEarned_integral() {
        return earned_integral;
    }

    public void setEarned_integral(int earned_integral) {
        this.earned_integral = earned_integral;
    }

    public int getAnswer_nums() {
        return answer_nums;
    }

    public void setAnswer_nums(int answer_nums) {
        this.answer_nums = answer_nums;
    }

    public int getWatch_time() {
        return watch_time;
    }

    public void setWatch_time(int watch_time) {
        this.watch_time = watch_time;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public List<TargetCourse> getToday_study_targets() {
        return today_study_targets;
    }

    public void setToday_study_targets(List<TargetCourse> today_study_targets) {
        this.today_study_targets = today_study_targets;
    }

    public List<PlanCourse> getPlan_study_courses() {
        return plan_study_courses;
    }

    public void setPlan_study_courses(List<PlanCourse> plan_study_courses) {
        this.plan_study_courses = plan_study_courses;
    }

    public static class TargetCourse{
        private String code;//课程代码
        private String name;//学习目标名称
        private String title;//任务名称
        private int is_open;//是否已购买
        private int daily_target;//当日目标答题数
        private int daily_completed;//当日完成答题数
        private int daily_target_time;//当日目标观看时长（分钟）
        private int daily_completed_time;//当日完成观看时长（分钟）
        private int daily_reward_claimed;//每日学习目标奖励是否已领取（0未领取，1已领取）
        private int daily_integral_reward;//任务奖励
        private int source_type;//视频来源类型：笔果:0，必过:1，时刻:2，迈读:3
        private int product_id;//商品id
        private int video_type;//视频类型，0、1、2这样的值
        private String type;//视频类型，video1、video2这样的值
        private int main_type;//题库类型
        private long version;//题库版本号
        private long record_expire_time;//有效时间

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getIs_open() {
            return is_open;
        }

        public void setIs_open(int is_open) {
            this.is_open = is_open;
        }

        public int getDaily_target() {
            return daily_target;
        }

        public void setDaily_target(int daily_target) {
            this.daily_target = daily_target;
        }

        public int getDaily_completed() {
            return daily_completed;
        }

        public void setDaily_completed(int daily_completed) {
            this.daily_completed = daily_completed;
        }

        public int getDaily_target_time() {
            return daily_target_time;
        }

        public void setDaily_target_time(int daily_target_time) {
            this.daily_target_time = daily_target_time;
        }

        public int getDaily_completed_time() {
            return daily_completed_time;
        }

        public void setDaily_completed_time(int daily_completed_time) {
            this.daily_completed_time = daily_completed_time;
        }

        public int getDaily_reward_claimed() {
            return daily_reward_claimed;
        }

        public void setDaily_reward_claimed(int daily_reward_claimed) {
            this.daily_reward_claimed = daily_reward_claimed;
        }

        public int getDaily_integral_reward() {
            return daily_integral_reward;
        }

        public void setDaily_integral_reward(int daily_integral_reward) {
            this.daily_integral_reward = daily_integral_reward;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public int getVideo_type() {
            return video_type;
        }

        public void setVideo_type(int video_type) {
            this.video_type = video_type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getMain_type() {
            return main_type;
        }

        public void setMain_type(int main_type) {
            this.main_type = main_type;
        }

        public long getVersion() {
            return version;
        }

        public void setVersion(long version) {
            this.version = version;
        }

        public long getRecord_expire_time() {
            return record_expire_time;
        }

        public void setRecord_expire_time(long record_expire_time) {
            this.record_expire_time = record_expire_time;
        }
    }

    public static class PlanCourse{
        private String code;
        private String name;
        private List<PlanCourseTopic> list;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<PlanCourseTopic> getList() {
            return list;
        }

        public void setList(List<PlanCourseTopic> list) {
            this.list = list;
        }
    }

    public static class PlanCourseTopic{
        private String code;//课程代码
        private String name;//题库名称
        private String title;
        private int is_open;//是否开通：0否1是
        private int total_required_answers;//完成整套题库所需总答题数
        private int accumulated_answers;//累计完成答题数
        private int total_required_time;//完成整套题库所需总观看时长（分钟）
        private int accumulated_time;//累计完成观看时长（分钟）
        private int total_reward_claimed;//题库完成奖励是否已领取（0未领取，1已领取）
        private int total_integral_reward;//奖励
        private int source_type;//视频来源类型：笔果:0，必过:1，时刻:2，迈读:3
        private int product_id;//商品id
        private int video_type;//视频类型，0、1、2这样的值
        private String type;//视频类型，video1、video2这样的值
        private int main_type;//题库类型
        private long version;//题库版本号
        private long record_expire_time;//有效时间

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getIs_open() {
            return is_open;
        }

        public void setIs_open(int is_open) {
            this.is_open = is_open;
        }

        public int getTotal_required_answers() {
            return total_required_answers;
        }

        public void setTotal_required_answers(int total_required_answers) {
            this.total_required_answers = total_required_answers;
        }

        public int getAccumulated_answers() {
            return accumulated_answers;
        }

        public void setAccumulated_answers(int accumulated_answers) {
            this.accumulated_answers = accumulated_answers;
        }

        public int getTotal_required_time() {
            return total_required_time;
        }

        public void setTotal_required_time(int total_required_time) {
            this.total_required_time = total_required_time;
        }

        public int getAccumulated_time() {
            return accumulated_time;
        }

        public void setAccumulated_time(int accumulated_time) {
            this.accumulated_time = accumulated_time;
        }

        public int getTotal_reward_claimed() {
            return total_reward_claimed;
        }

        public void setTotal_reward_claimed(int total_reward_claimed) {
            this.total_reward_claimed = total_reward_claimed;
        }

        public int getTotal_integral_reward() {
            return total_integral_reward;
        }

        public void setTotal_integral_reward(int total_integral_reward) {
            this.total_integral_reward = total_integral_reward;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int product_id) {
            this.product_id = product_id;
        }

        public int getVideo_type() {
            return video_type;
        }

        public void setVideo_type(int video_type) {
            this.video_type = video_type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getMain_type() {
            return main_type;
        }

        public void setMain_type(int main_type) {
            this.main_type = main_type;
        }

        public long getVersion() {
            return version;
        }

        public void setVersion(long version) {
            this.version = version;
        }

        public long getRecord_expire_time() {
            return record_expire_time;
        }

        public void setRecord_expire_time(long record_expire_time) {
            this.record_expire_time = record_expire_time;
        }
    }
}
