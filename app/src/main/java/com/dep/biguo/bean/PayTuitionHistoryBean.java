package com.dep.biguo.bean;

public class PayTuitionHistoryBean {
    private int pay_record_id;//缴纳记录id
    private String title;//缴纳记录标题
    private String amount;//缴纳金额
    private String remark;//备注
    private int status;//缴纳状态   -1：取消支付 1：未支付 2：已支付   3：周期还款中 4：已结清 5：已解约 6: 还款中断
    private String status_text;//缴纳状态文本
    private String pay_cost_text;//缴纳文本
    private int type; //缴纳类型 1：全款 2：分期缴纳 3：分次缴纳
    private Protocol protocol;//协议信息
    private String create_time;
    private String order_id;

    private boolean isOpenDesc;//是否已展开备注描述，仅供本地使用

    public int getPay_record_id() {
        return pay_record_id;
    }

    public void setPay_record_id(int pay_record_id) {
        this.pay_record_id = pay_record_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStatus_text() {
        return status_text;
    }

    public void setStatus_text(String status_text) {
        this.status_text = status_text;
    }

    public String getPay_cost_text() {
        return pay_cost_text;
    }

    public void setPay_cost_text(String pay_cost_text) {
        this.pay_cost_text = pay_cost_text;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Protocol getProtocol() {
        return protocol;
    }

    public void setProtocol(Protocol protocol) {
        this.protocol = protocol;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public boolean isOpenDesc() {
        return isOpenDesc;
    }

    public void setOpenDesc(boolean openDesc) {
        isOpenDesc = openDesc;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public static class Protocol{
        private int protocol_id;//协议id
        private int status;//协议状态 -1：无协议  0：协议生成中 1：协议待签署 2：协议已签署 3：协议已撤销
        private String status_text;//协议状态文本

        public int getProtocol_id() {
            return protocol_id;
        }

        public void setProtocol_id(int protocol_id) {
            this.protocol_id = protocol_id;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getStatus_text() {
            return status_text;
        }

        public void setStatus_text(String status_text) {
            this.status_text = status_text;
        }
    }
}
