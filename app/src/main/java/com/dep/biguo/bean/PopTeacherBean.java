package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class PopTeacherBean implements Parcelable {
//     "name": "机器人",
//             "mobile": null,
//             "wx_number": null,
//             "campus": null

    private String name;
    private String mobile;
    private String wx_number;
    private String campus;

    protected PopTeacherBean(Parcel in) {
        name = in.readString();
        mobile = in.readString();
        wx_number = in.readString();
        campus = in.readString();
    }

    public static final Creator<PopTeacherBean> CREATOR = new Creator<PopTeacherBean>() {
        @Override
        public PopTeacherBean createFromParcel(Parcel in) {
            return new PopTeacherBean(in);
        }

        @Override
        public PopTeacherBean[] newArray(int size) {
            return new PopTeacherBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(name);
        parcel.writeString(mobile);
        parcel.writeString(wx_number);
        parcel.writeString(campus);
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWx_number() {
        return wx_number;
    }

    public void setWx_number(String wx_number) {
        this.wx_number = wx_number;
    }

    public String getCampus() {
        return campus;
    }

    public void setCampus(String campus) {
        this.campus = campus;
    }
}
