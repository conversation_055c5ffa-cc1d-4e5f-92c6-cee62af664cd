package com.dep.biguo.bean;

import com.dep.biguo.mvp.model.api.Api;

import java.io.Serializable;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/23
 * @Description: Api请求回调参数
 */
public class BaseResponse<T> implements Serializable {
    private T data;
    private String result_code;
    private String result_info;

    @Override
    public String toString() {
        return "BaseResponse{" +
                "result_code='" + result_code + '\'' +
                ", result_info='" + result_info + '\'' +
                '}';
    }

    public T getData() {
        return data;
    }

    public String getResult_code() {
        return result_code;
    }

    public String getResult_info() {
        return result_info;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void setResult_code(String result_code) {
        this.result_code = result_code;
    }

    public void setResult_info(String result_info) {
        this.result_info = result_info;
    }

    /**
     * 请求是否成功
     */
    public boolean isSuccess() {
        return Api.REQUEST_SUCCESS.equals(result_code);
    }

    /**
     * 是否需要绑定微信
     */
    public boolean isNeedBindPhone() {
        return Api.NEED_BIND_PHONE.equals(result_code);
    }

    /**
     * 请求是否失败
     */
    public boolean isFail() {
        return Api.REQUEST_FAIL.equals(result_code);
    }

    /**
     * 请求是否登录信息失效
     */
    public boolean isTimeout() {
        return Api.LOGIN_TIMEOUT.equals(result_code);
    }

    /**
     * 请求是否版本过低
     */
    public boolean isVersionLow() {
        return Api.VERSION_LOW.equals(result_code);
    }

    /**
     * AI解析次数消耗完毕
     */
    public boolean isAICountToZero() {
        return Api.AI_COUNT_TO_ZERO.equals(result_code);
    }

    /**
     * 请求是否被拉黑
     */
    public boolean isInBlackList() {
        return Api.BLACK_LIST.equals(result_code);
    }

}
