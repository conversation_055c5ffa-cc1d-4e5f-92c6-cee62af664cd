package com.dep.biguo.bean;

import java.util.List;

public class OrganizationBean {
    private int id;//机构ID
    private String name;//机构名称
    private String logo;//机构logo
    private String address;//机构的地址
    private int km;//距离当前用户的距离
    private String intro;//机构的简介
    private String longitude;//精度
    private String latitude;//纬度
    private String province_name;//机构所在省份名称
    private String city_code;//机构所在城市代码
    private String city_name;//机构所在城市名称
    private String mobile;//机构的联系电话
    private String email;//机构的邮箱
    private int is_apply;//是否显示我要咨询按钮
    private List<Banner> top_carousel_list;//首页banner图
    private List<String> label;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getIs_apply() {
        return is_apply;
    }

    public void setIs_apply(int is_apply) {
        this.is_apply = is_apply;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getKm() {
        return km;
    }

    public void setKm(int km) {
        this.km = km;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getProvince_name() {
        return province_name;
    }

    public void setProvince_name(String province_name) {
        this.province_name = province_name;
    }

    public String getCity_code() {
        return city_code;
    }

    public void setCity_code(String city_code) {
        this.city_code = city_code;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public List<String> getLabel() {
        return label;
    }

    public void setLabel(List<String> label) {
        this.label = label;
    }

    public List<Banner> getTop_carousel_list() {
        return top_carousel_list;
    }

    public void setTop_carousel_list(List<Banner> top_carousel_list) {
        this.top_carousel_list = top_carousel_list;
    }

    public static class Banner{
        private String cover_url;//名字
        private String video_url;//图片
        private String type;//跳转链接

        public String getCover_url() {
            return cover_url;
        }

        public void setCover_url(String cover_url) {
            this.cover_url = cover_url;
        }

        public String getVideo_url() {
            return video_url;
        }

        public void setVideo_url(String video_url) {
            this.video_url = video_url;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
