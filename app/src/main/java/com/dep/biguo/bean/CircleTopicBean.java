package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class CircleTopicBean implements Parcelable {
//    "id": 1,
//            "name": "每日学习打卡",
//            "create_time": "2020-09-21 14:58:29",
//            "update_time": null,
//            "is_del": 0,
//            "join_people": 0,
//            "browse_num": 0,
//            "is_hot": 0

    private int id;
    private String name;
    private String create_time;
    private String update_time;
    private int is_del;
    private int join_people;
    private int browse_num;
    private int is_hot;

    protected CircleTopicBean(Parcel in) {
        id = in.readInt();
        name = in.readString();
        create_time = in.readString();
        update_time = in.readString();
        is_del = in.readInt();
        join_people = in.readInt();
        browse_num = in.readInt();
        is_hot = in.readInt();
    }

    public static final Creator<CircleTopicBean> CREATOR = new Creator<CircleTopicBean>() {
        @Override
        public CircleTopicBean createFromParcel(Parcel in) {
            return new CircleTopicBean(in);
        }

        @Override
        public CircleTopicBean[] newArray(int size) {
            return new CircleTopicBean[size];
        }
    };

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public int getIs_del() {
        return is_del;
    }

    public void setIs_del(int is_del) {
        this.is_del = is_del;
    }

    public int getJoin_people() {
        return join_people;
    }

    public void setJoin_people(int join_people) {
        this.join_people = join_people;
    }

    public int getBrowse_num() {
        return browse_num;
    }

    public void setBrowse_num(int browse_num) {
        this.browse_num = browse_num;
    }

    public int getIs_hot() {
        return is_hot;
    }

    public void setIs_hot(int is_hot) {
        this.is_hot = is_hot;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(id);
        parcel.writeString(name);
        parcel.writeString(create_time);
        parcel.writeString(update_time);
        parcel.writeInt(is_del);
        parcel.writeInt(join_people);
        parcel.writeInt(browse_num);
        parcel.writeInt(is_hot);
    }

}
