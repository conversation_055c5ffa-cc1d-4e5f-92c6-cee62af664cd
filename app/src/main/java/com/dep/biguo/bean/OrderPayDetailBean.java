package com.dep.biguo.bean;

import java.util.List;

public class OrderPayDetailBean {
    private String name;
    private String preferential_price;
    private String transport_price;
    private String total_fee;
    private int counts;
    private String yj_price;
    private List<Bposter> bposter;

    private AddressBean address_info;
    private List<ShopBean> goods_data;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPreferential_price() {
        return preferential_price;
    }

    public void setPreferential_price(String preferential_price) {
        this.preferential_price = preferential_price;
    }

    public String getTransport_price() {
        return transport_price;
    }

    public void setTransport_price(String transport_price) {
        this.transport_price = transport_price;
    }

    public String getTotal_fee() {
        return total_fee;
    }

    public void setTotal_fee(String total_fee) {
        this.total_fee = total_fee;
    }

    public int getCounts() {
        return counts;
    }

    public void setCounts(int counts) {
        this.counts = counts;
    }

    public String getYj_price() {
        return yj_price;
    }

    public void setYj_price(String yj_price) {
        this.yj_price = yj_price;
    }

    public AddressBean getAddress_info() {
        return address_info;
    }

    public void setAddress_info(AddressBean address_info) {
        this.address_info = address_info;
    }

    public List<ShopBean> getGoods_data() {
        return goods_data;
    }

    public void setGoods_data(List<ShopBean> goods_data) {
        this.goods_data = goods_data;
    }

    public List<Bposter> getBposter() {
        return bposter;
    }

    public void setBposter(List<Bposter> bposter) {
        this.bposter = bposter;
    }

    public static class Bposter{
        private String title;
        private String img;
        private String title_two;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTitle_two() {
            return title_two;
        }

        public void setTitle_two(String title_two) {
            this.title_two = title_two;
        }
    }
}
