package com.dep.biguo.bean;

import com.chad.library.adapter.base.entity.MultiItemEntity;

public class YaMiBean implements MultiItemEntity {
    private String code;
    private String name;
    private String count;
    private int is_pay_group;
    private int is_pay;
    private String group_price;
    private String member_group_price;
    private int group_id;
    private int is_vip;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIs_pay_group() {
        return is_pay_group;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public void setIs_pay_group(int is_pay_group) {
        this.is_pay_group = is_pay_group;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public int getGroup_id() {
        return group_id;
    }

    public void setGroup_id(int group_id) {
        this.group_id = group_id;
    }

    @Override
    public int getItemType() {
        return 2;
    }

    public int getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(int is_vip) {
        this.is_vip = is_vip;
    }
}
