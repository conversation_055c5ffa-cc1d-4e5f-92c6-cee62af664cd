package com.dep.biguo.bean;

import java.util.List;

public class AllQuestionBean {
    private String key;
    private String source;//source:json直接解析；file需要访问返回的链接，获取到数据再解析
    private List<QuestionBean> topics;
    private List<String> fileUrls;//文件地址

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<QuestionBean> getTopics() {
        return topics;
    }

    public void setTopics(List<QuestionBean> topics) {
        this.topics = topics;
    }

    public List<String> getFileUrls() {
        return fileUrls;
    }

    public void setFileUrls(List<String> fileUrls) {
        this.fileUrls = fileUrls;
    }
}
