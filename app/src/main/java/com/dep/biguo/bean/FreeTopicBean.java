package com.dep.biguo.bean;

public class FreeTopicBean {
    private int courses_id;
    private int topic_type;//题型
    private String name;//题库名称
    private int total_num;//总题数
    private String code;//课程编码
    private long version;//0 //题库版本号
    private int last_id;//最后一次作答的题目的ID
    private int disable_cache;//是否暂不使用数据库缓存
    private long record_expire_time;//有效期

    public int getCourses_id() {
        return courses_id;
    }

    public void setCourses_id(int courses_id) {
        this.courses_id = courses_id;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTotal_num() {
        return total_num;
    }

    public void setTotal_num(int total_num) {
        this.total_num = total_num;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public int getDisable_cache() {
        return disable_cache;
    }

    public void setDisable_cache(int disable_cache) {
        this.disable_cache = disable_cache;
    }

    public long getRecord_expire_time() {
        return record_expire_time;
    }

    public void setRecord_expire_time(long record_expire_time) {
        this.record_expire_time = record_expire_time;
    }
}
