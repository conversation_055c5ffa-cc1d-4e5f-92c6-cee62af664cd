package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class ShopBean implements Parcelable {

    /**
     * id : 1
     * name : ddddd
     * price : 1111
     * group_id : 1
     * preferential_price : 111
     * img : http://192.168.0.223/goods/NVxbY05Mjt.jpg

     */

    private int id;
    private String name;
    private String price;
    private String total_fee;
    private String preferential_price;

    private int group_id;
    private String img;

    private String postage;

    private String introduce;
    private String introduce_img;
    private String describe;//书籍详情7.0.1之后用
    private boolean is_like;

    private String code;
    private int count; //商品数量
    private int quantity; //库存
    private boolean is_select; //是否选中
    private int video_type;//老视频（前端视为精讲视频，后端需要区分）为0，必过精讲视频为1，必过串讲视频为2
    private int product_id;//商品ID
    private int refund_status;//退款状态 1= 未退款，2=退款中 3 = 已退款
    private String project;
    private Scholarship scholarship;//奖学金
    private int true_paper_id;//真题ID
    private long version;//题库的版本号
    private long expire_time;//题库的有效期
    private String courses_name;
    private int courses_id;
    private String courses_code;
    private int is_expire;//0=否 1=是

    private int is_pay_vip;//是否支付了VIP题库，0否，1是

    // 二手教材相关字段
    private String publisher; // 出版社
    private String condition; // 成色
    private String location; // 位置
    private String publishTime; // 发布时间
    private boolean urgent; // 是否急售
    private String category; // 分类

    private String tag; // 商品标签
    private int sales_volume; // 销量

    // getter和setter方法
    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public boolean isUrgent() {
        return urgent;
    }

    public void setUrgent(boolean urgent) {
        this.urgent = urgent;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getSales_volume() {
        return sales_volume;
    }

    public void setSales_volume(int sales_volume) {
        this.sales_volume = sales_volume;
    }

    public int getIs_pay_vip() {
        return is_pay_vip;
    }

    public void setIs_pay_vip(int is_pay_vip) {
        this.is_pay_vip = is_pay_vip;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getTotal_fee() {
        return total_fee;
    }

    public void setTotal_fee(String total_fee) {
        this.total_fee = total_fee;
    }

    public String getPreferential_price() {
        return preferential_price;
    }

    public void setPreferential_price(String preferential_price) {
        this.preferential_price = preferential_price;
    }

    public int getGroup_id() {
        return group_id;
    }

    public void setGroup_id(int group_id) {
        this.group_id = group_id;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getPostage() {
        return postage;
    }

    public void setPostage(String postage) {
        this.postage = postage;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getIntroduce_img() {
        return introduce_img;
    }

    public void setIntroduce_img(String introduce_img) {
        this.introduce_img = introduce_img;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public boolean isIs_like() {
        return is_like;
    }

    public void setIs_like(boolean is_like) {
        this.is_like = is_like;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public boolean getIs_select() {
        return is_select;
    }

    public void setIs_select(boolean is_select) {
        this.is_select = is_select;
    }

    public static Creator<ShopBean> getCREATOR() {
        return CREATOR;
    }

    public int getVideo_type() {
        return video_type;
    }

    public void setVideo_type(int video_type) {
        this.video_type = video_type;
    }

    public int getTrue_paper_id() {
        return true_paper_id;
    }

    public void setTrue_paper_id(int true_paper_id) {
        this.true_paper_id = true_paper_id;
    }

    public int getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(int refund_status) {
        this.refund_status = refund_status;
    }

    public boolean isIs_select() {
        return is_select;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public Scholarship getScholarship() {
        return scholarship;
    }

    public void setScholarship(Scholarship scholarship) {
        this.scholarship = scholarship;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public long getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(long expire_time) {
        this.expire_time = expire_time;
    }

    public String getCourses_name() {
        return courses_name;
    }

    public void setCourses_name(String courses_name) {
        this.courses_name = courses_name;
    }

    public int getCourses_id() {
        return courses_id;
    }

    public void setCourses_id(int courses_id) {
        this.courses_id = courses_id;
    }

    public String getCourses_code() {
        return courses_code;
    }

    public void setCourses_code(String courses_code) {
        this.courses_code = courses_code;
    }

    public int getIs_expire() {
        return is_expire;
    }

    public void setIs_expire(int is_expire) {
        this.is_expire = is_expire;
    }

    public static class Scholarship implements Parcelable{
        private int status;
        private String sales_restriction;
        private String maximum_bonus;
        private String code;

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getSales_restriction() {
            return sales_restriction;
        }

        public void setSales_restriction(String sales_restriction) {
            this.sales_restriction = sales_restriction;
        }

        public String getMaximum_bonus() {
            return maximum_bonus;
        }

        public void setMaximum_bonus(String maximum_bonus) {
            this.maximum_bonus = maximum_bonus;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        protected Scholarship(Parcel in) {
            status = in.readInt();
            sales_restriction = in.readString();
            maximum_bonus = in.readString();
            code = in.readString();
        }

        public static final Creator<Scholarship> CREATOR = new Creator<Scholarship>() {
            @Override
            public Scholarship createFromParcel(Parcel in) {
                return new Scholarship(in);
            }

            @Override
            public Scholarship[] newArray(int size) {
                return new Scholarship[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(status);
            dest.writeString(sales_restriction);
            dest.writeString(maximum_bonus);
            dest.writeString(code);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeString(this.name);
        dest.writeString(this.price);
        dest.writeString(this.preferential_price);
        dest.writeInt(this.group_id);
        dest.writeString(this.img);
        dest.writeString(this.postage);
        dest.writeString(this.introduce);
        dest.writeString(this.introduce_img);
        dest.writeByte(this.is_like ? (byte) 1 : (byte) 0);
        dest.writeInt(this.count);
        dest.writeString(this.code);
        dest.writeInt(this.quantity);
        dest.writeByte(this.is_select ? (byte) 1 : (byte) 0);
        dest.writeInt(this.video_type);
        dest.writeParcelable(this.scholarship, flags);
        dest.writeString(this.tag);
        dest.writeInt(this.sales_volume);
    }

    public ShopBean() {
    }

    protected ShopBean(Parcel in) {
        this.id = in.readInt();
        this.name = in.readString();
        this.price = in.readString();
        this.preferential_price = in.readString();
        this.group_id = in.readInt();
        this.img = in.readString();
        this.postage = in.readString();
        this.introduce = in.readString();
        this.introduce_img = in.readString();
        this.is_like = in.readByte() != 0;
        this.count = in.readInt();
        this.code = in.readString();
        this.quantity = in.readInt();
        this.is_select = in.readByte() != 0;
        this.video_type = in.readInt();
        this.scholarship = in.readParcelable(Scholarship.class.getClassLoader());
        this.tag = in.readString();
        this.sales_volume = in.readInt();
    }

    public static final Creator<ShopBean> CREATOR = new Creator<ShopBean>() {
        @Override
        public ShopBean createFromParcel(Parcel source) {
            return new ShopBean(source);
        }

        @Override
        public ShopBean[] newArray(int size) {
            return new ShopBean[size];
        }
    };
}
