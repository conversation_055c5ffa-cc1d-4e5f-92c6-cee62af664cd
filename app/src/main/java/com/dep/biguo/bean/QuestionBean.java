package com.dep.biguo.bean;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/5
 * @Description:
 */
public class QuestionBean extends BaseResponse<List<QuestionBean>> {
    //AI解析的获取状态
    public static final int AI_FAIL = -1;//加载失败
    public static final int AI_CAN = 0;//可以继续加载
    public static final int AI_LOADING = 1;//正在加载
    public static final int AI_SUCCESS = 2;//加载成功

    //加载评论的几种状态
    public static final int LOAD_FAIL = -1;//加载失败
    public static final int LOAD_CAN = 0;//可以继续加载
    public static final int LOAD_LOADING = 1;//正在加载
    public static final int LOAD_NO_MORE = 2;//已加载完所有评论

    private int id;
    private String code;//题型代码
    private int mainType;//免费、VIP、真题、章节
    private int topic_type;//题库类型（单选、多选、判断、问答、填空、完形填空、阅读理解、名词解析）
    private String topic_type_name;//题库类型名称
    private String questionAsk;//问题
    private String A;//选项A
    private String B;//选项B
    private String C;//选项C
    private String D;//选项D
    private String E;//选项E
    private String F;//选项F
    private String G;//选项G
    private String correctOption;//参考答案
    private String explanation;//题目详解
    private int isCollection;//是否已收藏
    private String value;//真题ID/章节ID/试卷ID/code
    private int mark;//分数
    private String video_parse_cover;//视频解析的封面

    private String audio_url;//音频
    private String video_parse_url;//视频解析的地址

    private String aiExplanationHint;//AI解析的说明
    private String aiExplanation;//AI解析
    private int aiStatus;//请求AI解析的状态
    private boolean isShowAIAnalyze;//是否展开AI解析

    private int is_high_frequency;//是否是高频考点
    private List<Object> commentList;//所有评论、子评论、优量汇信息流广告、评论间的分割线

    private int offset;//评论已加载多少条
    private int loadStatus;//评论的加载状态
    private boolean isLoadAdvertisement;//是否加载过广告，广告存在commentList中

    public List<Object> getCommentList() {
        return commentList;
    }

    public void setCommentList(List<Object> commentList) {
        this.commentList = commentList;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getA() {
        return A;
    }

    public void setA(String a) {
        A = a;
    }

    public String getB() {
        return B;
    }

    public void setB(String b) {
        B = b;
    }

    public String getC() {
        return C;
    }

    public void setC(String c) {
        C = c;
    }

    public String getD() {
        return D;
    }

    public void setD(String d) {
        D = d;
    }

    public String getE() {
        return E;
    }

    public void setE(String e) {
        E = e;
    }

    public String getF() {
        return F;
    }

    public void setF(String f) {
        F = f;
    }

    public String getG() {
        return G;
    }

    public void setG(String g) {
        G = g;
    }

    public String getCorrectOption() {
        return correctOption;
    }

    public void setCorrectOption(String correctOption) {
        this.correctOption = correctOption;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public int getIsCollection() {
        return isCollection;
    }

    public void setIsCollection(int isCollection) {
        this.isCollection = isCollection;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getMark() {
        return mark;
    }

    public void setMark(int mark) {
        this.mark = mark;
    }

    public String getAudio_url() {
        return audio_url;
    }

    public void setAudio_url(String audio_url) {
        this.audio_url = audio_url;
    }

    public String getVideo_parse_cover() {
        return video_parse_cover;
    }

    public void setVideo_parse_cover(String video_parse_cover) {
        this.video_parse_cover = video_parse_cover;
    }

    public String getVideo_parse_url() {
        return video_parse_url;
    }

    public void setVideo_url(String video_url) {
        this.video_parse_url = video_url;
    }

    public String getQuestionAsk() {
        return questionAsk;
    }

    public void setQuestionAsk(String questionAsk) {
        this.questionAsk = questionAsk;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getTopic_type_name() {
        return topic_type_name;
    }

    public void setTopic_type_name(String topic_type_name) {
        this.topic_type_name = topic_type_name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getIs_high_frequency() {
        return is_high_frequency;
    }

    public void setIs_high_frequency(int is_high_frequency) {
        this.is_high_frequency = is_high_frequency;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getLoadStatus() {
        return loadStatus;
    }

    public void setLoadStatus(int loadStatus) {
        this.loadStatus = loadStatus;
    }

    public boolean isLoadAdvertisement() {
        return isLoadAdvertisement;
    }

    public void setLoadAdvertisement(boolean loadAdvertisement) {
        isLoadAdvertisement = loadAdvertisement;
    }

    public String getAiExplanationHint() {
        return aiExplanationHint;
    }

    public void setAiExplanationHint(String aiExplanationHint) {
        this.aiExplanationHint = aiExplanationHint;
    }

    public String getAiExplanation() {
        return aiExplanation;
    }

    public void setAiExplanation(String aiExplanation) {
        this.aiExplanation = aiExplanation;
    }

    public int getAiStatus() {
        return aiStatus;
    }

    public void setAiStatus(int aiStatus) {
        this.aiStatus = aiStatus;
    }

    public boolean isShowAIAnalyze() {
        return isShowAIAnalyze;
    }

    public void setShowAIAnalyze(boolean showAIAnalyze) {
        isShowAIAnalyze = showAIAnalyze;
    }
}
