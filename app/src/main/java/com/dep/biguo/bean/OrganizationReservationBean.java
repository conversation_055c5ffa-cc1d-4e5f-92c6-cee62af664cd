package com.dep.biguo.bean;

import java.util.List;

public class OrganizationReservationBean {
    private String image;
    private List<Reservation> study_rooms;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public List<Reservation> getStudy_rooms() {
        return study_rooms;
    }

    public void setStudy_rooms(List<Reservation> study_rooms) {
        this.study_rooms = study_rooms;
    }

    public static class Reservation{
        private int activities_id;//活动id
        private String name;//名称
        private String cover_url;//封面url
        private String open_time; //开放时间
        private int surplus_count;//剩余座位
        private String address;//自习室所在地址

        public int getActivities_id() {
            return activities_id;
        }

        public void setActivities_id(int activities_id) {
            this.activities_id = activities_id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCover_url() {
            return cover_url;
        }

        public void setCover_url(String cover_url) {
            this.cover_url = cover_url;
        }

        public String getOpen_time() {
            return open_time;
        }

        public void setOpen_time(String open_time) {
            this.open_time = open_time;
        }

        public int getSurplus_count() {
            return surplus_count;
        }

        public void setSurplus_count(int surplus_count) {
            this.surplus_count = surplus_count;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }
}
