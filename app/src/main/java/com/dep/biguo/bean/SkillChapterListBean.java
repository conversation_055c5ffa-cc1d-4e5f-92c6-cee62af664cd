package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.dep.biguo.mvp.ui.adapter.VideoListAdapter;

import java.util.ArrayList;
import java.util.List;

public class SkillChapterListBean {
    private String new_view_id;////最新观看id，章节id_小节id
    private int is_pay;//是否购买：0否1是
    private int source_type;//来源类型：笔果:0，必过:1，时刻:2
    private int product_id;//商品id
    private int skill_id;//技能证id
    private String product_name;//商品名称
    private int chapter_num;//章节数
    private int class_hour;//课时
    private int cert_type;//商品类型：自考:0,成考:1,技能证:2,网络教育:3,统招专升本:4,研究生考试:5,职场提升:9,高职高考:10
    private String price;//价格
    private String member_price;//会员价格
    private String group_price;//拼团价格
    private String member_group_price;//会员拼团价格
    private List<Chapter> list;

    public String getNew_view_id() {
        return new_view_id;
    }

    public void setNew_view_id(String new_view_id) {
        this.new_view_id = new_view_id;
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void setSkill_id(int skill_id) {
        this.skill_id = skill_id;
    }

    public int getIs_pay() {
        return is_pay;
    }

    public void setIs_pay(int is_pay) {
        this.is_pay = is_pay;
    }

    public int getSource_type() {
        return source_type;
    }

    public void setSource_type(int source_type) {
        this.source_type = source_type;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getProduct_name() {
        return product_name;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public int getChapter_num() {
        return chapter_num;
    }

    public void setChapter_num(int chapter_num) {
        this.chapter_num = chapter_num;
    }

    public int getClass_hour() {
        return class_hour;
    }

    public void setClass_hour(int class_hour) {
        this.class_hour = class_hour;
    }

    public int getCert_type() {
        return cert_type;
    }

    public void setCert_type(int cert_type) {
        this.cert_type = cert_type;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getMember_price() {
        return member_price;
    }

    public void setMember_price(String member_price) {
        this.member_price = member_price;
    }

    public String getGroup_price() {
        return group_price;
    }

    public void setGroup_price(String group_price) {
        this.group_price = group_price;
    }

    public String getMember_group_price() {
        return member_group_price;
    }

    public void setMember_group_price(String member_group_price) {
        this.member_group_price = member_group_price;
    }

    public List<Chapter> getList() {
        return list;
    }

    public void setList(List<Chapter> list) {
        this.list = list;
    }

    public static class Chapter extends AbstractExpandableItem<Video> implements MultiItemEntity, Parcelable {
        private String course_id;//课程id
        private int cst_id;//小班id
        private int chapter_id;//章节id
        private String chapter_name;//章节名称
        private List<Video> video;

        public String getCourse_id() {
            return course_id;
        }

        public void setCourse_id(String course_id) {
            this.course_id = course_id;
        }

        public int getCst_id() {
            return cst_id;
        }

        public void setCst_id(int cst_id) {
            this.cst_id = cst_id;
        }

        public int getChapter_id() {
            return chapter_id;
        }

        public void setChapter_id(int chapter_id) {
            this.chapter_id = chapter_id;
        }

        public String getChapter_name() {
            return chapter_name;
        }

        public void setChapter_name(String chapter_name) {
            this.chapter_name = chapter_name;
        }

        public List<Video> getVideo() {
            return video;
        }

        public void setVideo(List<Video> video) {
            this.video = video;
        }

        @Override
        public int getLevel() {
            return 1;
        }

        @Override
        public int getItemType() {
            return VideoListAdapter.HEAD;
        }
        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.course_id);
            dest.writeInt(this.cst_id);
            dest.writeInt(this.chapter_id);
            dest.writeString(this.chapter_name);
            dest.writeList(this.video);
        }

        public Chapter() {

        }

        protected Chapter(Parcel in) {
            this.course_id = in.readString();
            this.cst_id = in.readInt();
            this.chapter_id = in.readInt();
            this.chapter_name = in.readString();
            this.video = new ArrayList<>();
            in.readList(this.video, VideoDetailBean.ChapterBean.VideoBean.class.getClassLoader());
        }

        public static final Creator<Chapter> CREATOR = new Creator<Chapter>() {
            @Override
            public Chapter createFromParcel(Parcel source) {
                return new Chapter(source);
            }

            @Override
            public Chapter[] newArray(int size) {
                return new Chapter[size];
            }
        };
    }

    public static class Video implements MultiItemEntity, Parcelable{
        private int product_id;//套餐id
        private String course_id;//课程id
        private int chapter_id;//章节id
        private int cst_id;//小班id
        private int source_type;//来源类型：笔果:0，必过:1，时刻:2
        private int cert_type;

        private int item_id;//小节id
        private String item_name;//小节名称
        private int is_free;//1免费0不免费
        private String video_aly_id;
        private String room_id;
        private String live_id;
        private String playback_id;
        private int sort;
        private String video_url;

        public int getProduct_id() {
            return product_id;
        }

        public void setProduct_id(int classroom_id) {
            this.product_id = classroom_id;
        }

        public String getCourse_id() {
            return course_id;
        }

        public void setCourse_id(String course_id) {
            this.course_id = course_id;
        }

        public int getChapter_id() {
            return chapter_id;
        }

        public void setChapter_id(int chapter_id) {
            this.chapter_id = chapter_id;
        }

        public int getCst_id() {
            return cst_id;
        }

        public void setCst_id(int cst_id) {
            this.cst_id = cst_id;
        }

        public int getSource_type() {
            return source_type;
        }

        public void setSource_type(int source_type) {
            this.source_type = source_type;
        }

        public int getCert_type() {
            return cert_type;
        }

        public void setCert_type(int cert_type) {
            this.cert_type = cert_type;
        }

        public int getItem_id() {
            return item_id;
        }

        public void setItem_id(int item_id) {
            this.item_id = item_id;
        }

        public String getItem_name() {
            return item_name;
        }

        public void setItem_name(String item_name) {
            this.item_name = item_name;
        }

        public int getIs_free() {
            return is_free;
        }

        public void setIs_free(int is_free) {
            this.is_free = is_free;
        }

        public String getVideo_aly_id() {
            return video_aly_id;
        }

        public void setVideo_aly_id(String video_aly_id) {
            this.video_aly_id = video_aly_id;
        }

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public String getLive_id() {
            return live_id;
        }

        public void setLive_id(String live_id) {
            this.live_id = live_id;
        }

        public String getPlayback_id() {
            return playback_id;
        }

        public void setPlayback_id(String playback_id) {
            this.playback_id = playback_id;
        }

        public int getSort() {
            return sort;
        }

        public void setSort(int sort) {
            this.sort = sort;
        }

        public String getVideo_url() {
            return video_url;
        }

        public void setVideo_url(String video_url) {
            this.video_url = video_url;
        }

        @Override
        public int getItemType() {
            return 0;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.item_id);
            dest.writeString(this.item_name);
            dest.writeInt(this.is_free);
            dest.writeString(this.video_aly_id);
            dest.writeString(this.room_id);
            dest.writeString(this.live_id);
            dest.writeString(this.live_id);
            dest.writeString(this.playback_id);
            dest.writeInt(this.sort);
            dest.writeString(this.video_url);
        }

        public Video() {

        }

        protected Video(Parcel in) {
            this.item_id = in.readInt();
            this.item_name = in.readString();
            this.is_free = in.readInt();
            this.video_aly_id = in.readString();
            this.room_id = in.readString();
            this.live_id = in.readString();
            this.playback_id = in.readString();
            this.sort = in.readInt();
            this.video_url = in.readString();
        }

        public static final Creator<Video> CREATOR = new Creator<Video>() {
            @Override
            public Video createFromParcel(Parcel source) {
                return new Video(source);
            }

            @Override
            public Video[] newArray(int size) {
                return new Video[size];
            }
        };
    }
}
