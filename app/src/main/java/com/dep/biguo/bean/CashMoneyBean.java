package com.dep.biguo.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class CashMoneyBean implements Parcelable {
    //    cashMoney  -users_id
//    total -总收益
//    total_yami 压密总收益
//    *yami_money 压密待结算收益
//    *total_money 历史结算收益
//    *money 可提现余额
//    *zfb 支付宝账号信息
    private float total_yami;
    private float total_money;
    private float min_money;
    private int fruit_coin;

    //提现页面的字段
    private float total_earnings_amount;
    private float not_earnings_amount;
    private float available_amount;
    private float cash_record_amount;
    private float cash_min_money;

    private int audit_status;//成为推广员申请状态，0未申请，1申请中，2申请完成，3被拒绝

    private AlipayAccountBean alipay;

    protected CashMoneyBean(Parcel in) {
        total_earnings_amount = in.readFloat();
        total_yami = in.readFloat();
        not_earnings_amount = in.readFloat();
        total_money = in.readFloat();
        available_amount = in.readFloat();
        cash_record_amount = in.readFloat();
        min_money = in.readFloat();
        fruit_coin = in.readInt();
        alipay = in.readParcelable(AlipayAccountBean.class.getClassLoader());
    }

    public static final Creator<CashMoneyBean> CREATOR = new Creator<CashMoneyBean>() {
        @Override
        public CashMoneyBean createFromParcel(Parcel in) {
            return new CashMoneyBean(in);
        }

        @Override
        public CashMoneyBean[] newArray(int size) {
            return new CashMoneyBean[size];
        }
    };

    public float getTotal_yami() {
        return total_yami;
    }

    public void setTotal_yami(float total_yami) {
        this.total_yami = total_yami;
    }

    public float getNot_earnings_amount() {
        return not_earnings_amount;
    }

    public void setNot_earnings_amount(float not_earnings_amount) {
        this.not_earnings_amount = not_earnings_amount;
    }

    public float getTotal_money() {
        return total_money;
    }

    public void setTotal_money(float total_money) {
        this.total_money = total_money;
    }

    public float getAvailable_amount() {
        return available_amount;
    }

    public void setAvailable_amount(float available_amount) {
        this.available_amount = available_amount;
    }

    public AlipayAccountBean getAlipay() {
        return alipay;
    }

    public void setAlipay(AlipayAccountBean alipay) {
        this.alipay = alipay;
    }

    public float getTotal_earnings_amount() {
        return total_earnings_amount;
    }

    public void setTotal_earnings_amount(float total_earnings_amount) {
        this.total_earnings_amount = total_earnings_amount;
    }

    public float getCash_record_amount() {
        return cash_record_amount;
    }

    public void setCash_record_amount(float cash_record_amount) {
        this.cash_record_amount = cash_record_amount;
    }

    public float getCash_min_money() {
        return cash_min_money;
    }

    public void setCash_min_money(float cash_min_money) {
        this.cash_min_money = cash_min_money;
    }

    public float getMin_money() {
        return min_money;
    }

    public void setMin_money(float min_money) {
        this.min_money = min_money;
    }

    public int getFruit_coin() {
        return fruit_coin;
    }

    public void setFruit_coin(int fruit_coin) {
        this.fruit_coin = fruit_coin;
    }

    public int getAudit_status() {
        return audit_status;
    }

    public void setAudit_status(int audit_status) {
        this.audit_status = audit_status;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeFloat(total_earnings_amount);
        dest.writeFloat(total_yami);
        dest.writeFloat(not_earnings_amount);
        dest.writeFloat(total_money);
        dest.writeFloat(available_amount);
        dest.writeFloat(cash_record_amount);
        dest.writeFloat(min_money);
        dest.writeInt(fruit_coin);
        dest.writeParcelable(alipay, flags);
    }
}
