package com.dep.biguo.widget;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.ArticleBean;
import com.dep.biguo.bean.IconBean;
import com.dep.biguo.bean.ZkHomeBean;
import com.dep.biguo.databinding.HomeViewBinding;
import com.dep.biguo.mvp.presenter.HomePresenter;
import com.dep.biguo.mvp.ui.activity.AllVideoActivity;
import com.dep.biguo.mvp.ui.activity.ArticleActivity;
import com.dep.biguo.mvp.ui.activity.BiguoVipActivity;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.mvp.ui.activity.ChapterActivity;
import com.dep.biguo.mvp.ui.activity.CourseActivity;
import com.dep.biguo.mvp.ui.activity.DayCardV3Activity;
import com.dep.biguo.mvp.ui.activity.ErrorCollActivity;
import com.dep.biguo.mvp.ui.activity.GroupActivity;
import com.dep.biguo.mvp.ui.activity.GroupGoodsActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.InternetStudyGoodsActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.MyActivity;
import com.dep.biguo.mvp.ui.activity.NewUserActivity;
import com.dep.biguo.mvp.ui.activity.SchoolRecommendActivity;
import com.dep.biguo.mvp.ui.activity.SearchUrlActivity;
import com.dep.biguo.mvp.ui.activity.SelectCourseActivity;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.SimuPaperActivity;
import com.dep.biguo.mvp.ui.activity.TextBooksActivity;
import com.dep.biguo.mvp.ui.activity.TopicActivity;
import com.dep.biguo.mvp.ui.activity.TruePaperAllNewActivity;
import com.dep.biguo.mvp.ui.adapter.HomeIconAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeNewAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeRecommendAdapter;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.mmkv.CacheInterfaceData;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.wxapi.WxMinApplication;
import com.hjq.toast.ToastUtils;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

public class HomeHeadView extends ConstraintLayout {
    public HomeHeadView(@NonNull Context context) {
        super(context);
    }
    /*public HomeViewBinding homeViewBinding;
    private HomePresenter mPresenter;

    public HomeIconAdapter mIconAdapter;//icon图标
    public HomeRecommendAdapter recommendAdapter;//推荐列表
    public HomeNewAdapter mNewsAdapter;//新闻

    public HomeHeadView(@NonNull Context context, HomePresenter presenter) {
        super(context);
        this.mPresenter = presenter;
        init();
    }

    public HomeHeadView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HomeHeadView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void init(){
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        setLayoutParams(layoutParams);

        homeViewBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.home_view, this, true);
        homeViewBinding.setOnClickListener(this);

        mIconAdapter = new HomeIconAdapter(new ArrayList<>());
        recommendAdapter = new HomeRecommendAdapter();
        mNewsAdapter = new HomeNewAdapter(new ArrayList<>());

        //初始化顶部banner图
        initBanner(homeViewBinding.headBannerView);
        //金刚区适配器
        initIconRecyclerView();
        //初始化顶部banner图
        initBanner(homeViewBinding.bottomBannerView);
        //绑定视频课程适配器
        initRecommendAdapter();
        //绑定新闻适配器
        initNewsAdapter();
    }


    private void initIconRecyclerView(){
        homeViewBinding.iconRecyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));
        int iconWidth = 80;
        //这个iconRecyclerView.getLayoutParams().width的算法我也说不清，反正就是能用
        int startStudyViewWidth = homeViewBinding.startStudyView.getLayoutParams().width;
        int iconSpace = (DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), iconWidth*2) - startStudyViewWidth) / 2;
        int icon1ViewEndMargin = ((ConstraintLayout.LayoutParams)homeViewBinding.icon1View.getLayoutParams()).rightMargin / 2;
        int icon2ViewStartMargin = ((ConstraintLayout.LayoutParams)homeViewBinding.icon2View.getLayoutParams()).leftMargin / 2;
        homeViewBinding.iconRecyclerView.getLayoutParams().width = iconSpace + DisplayHelper.dp2px(getContext(), iconWidth*2) + startStudyViewWidth + icon1ViewEndMargin + icon2ViewStartMargin;

        mIconAdapter.setItemWidth(DisplayHelper.dp2px(getContext(), iconWidth));
        mIconAdapter.bindToRecyclerView(homeViewBinding.iconRecyclerView);
        mIconAdapter.setOnItemClickListener((adapter, view, position) -> {
            IconBean icon = mIconAdapter.getItem(position);
            //埋点每个icon的点击次数
            new UmengEventUtils(getContext())
                    .addParams("icon_type", icon.getName())
                    .pushEvent(UmengEventUtils.CLICK_ICON);

            if(icon.getEnable() == StartFinal.NO){
                ToastUtils.show("当前省份未开放该功能");
                return;
            }
            iconStartActivity(icon.getType(), icon.getXcx_path(), icon.getTarget_url(), icon.getNeed_login() == StartFinal.YES);
        });
    }

    private void initRecommendAdapter(){
        //添加流布局管理器
        MaxCountItemManager layoutManager = new MaxCountItemManager(getContext());
        layoutManager.setSpaceHorizontal(DisplayHelper.dp2px(getContext(), 10));
        layoutManager.setSpaceVertical(DisplayHelper.dp2px(getContext(), 10));
        homeViewBinding.recommendRecyclerView.setLayoutManager(layoutManager);
        homeViewBinding.recommendRecyclerView.setAdapter(recommendAdapter);
        recommendAdapter.setOnItemClickListener((adapter, view, position) -> {
            ZkHomeBean.Recommend recommend = recommendAdapter.getItem(position);
            if(StartFinal.BOOK.equals(recommend.getType())){
                Intent intent = new Intent(getContext(), ShopDetailActivity.class);
                intent.putExtra(ShopDetailActivity.GOODSID, recommend.getProduct_id());
                getContext().startActivity(intent);
            }else {
                GroupGoodsActivity.start(getActivity(), recommend.getCode(), recommend.getType(), recommend.getSource_type(), recommend.getProduct_id(), 0);
            }
        });
    }

    private void initNewsAdapter(){
        homeViewBinding.newsRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));
        mNewsAdapter.bindToRecyclerView(homeViewBinding.newsRecyclerView);
        mNewsAdapter.setOnItemClickListener((adapter, view, position) -> {
            ArticleBean news = mNewsAdapter.getItem(position);
            if(!TextUtils.isEmpty(news.getXcx_path()) && AppUtil.isInstallWechat(getContext())) {
                WxMinApplication.StartWechat(getContext(), news.getXcx_path(), news.getTarget_url());
                mPresenter.redNews(news);

            }else {
                HtmlActivity.start(getContext(), news.getTarget_url(), HtmlActivity.ARTICLE_TYPE, news.getId());
            }
        });

    }

    *//**初始化banner图
     * <p color=#fff>注：Banner.setImages()的数组与Banner.update()的数组不能是同一个，否则不会触发刷新</p>
     * @param banner
     *//*
    private void initBanner(Banner banner) {
        //去掉左右边距，根据比例计算banner图的高度
        float scale = banner == homeViewBinding.headBannerView ? (140f / 345f) : (60f / 335f);
        banner.getLayoutParams().height = (int) ((DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), 20)) * scale);
        banner.setImages(new ArrayList<>());
        banner.setImageLoader(new BannerRoundImageLoader());
        banner.setBannerStyle(BannerConfig.NOT_INDICATOR);
        banner.start();
        banner.setOnBannerListener(position -> {
            ZkHomeBean.Banner bannerBean = mPresenter.getBannerBean(banner == homeViewBinding.headBannerView ? 1 : 2, position);
            if(bannerBean == null) return;

            //埋点统计轮播图的点击次数
            new UmengEventUtils(getContext())
                    .addParams("banner_id", bannerBean.getId()+"")
                    .addParams("banner_title", TextUtils.isEmpty(bannerBean.getName()) ? "" : bannerBean.getName())
                    .pushEvent(UmengEventUtils.CLICK_BANNER);

            if(bannerBean.getNeed_login() == 1 && !MainAppUtils.checkLogin(getContext())) return;

            if(bannerBean.getType() == 123){//学梦计划
                HtmlActivity.start(getContext(), bannerBean.getTarget_url());

            }else if(bannerBean.getType() == 14 || bannerBean.getType() == 15) {//14未购买笔果折扣卡，15已购买笔果折扣卡
                if(!MainAppUtils.checkLogin(getContext())) return;
                if(UserCache.isMemberShip()){
                    ArmsUtils.startActivity(BiguoVipActivity.class);
                }else {
                    ArmsUtils.startActivity(BiguoVipOpenActivity.class);
                }
            }else if(bannerBean.getType() == 16) {//名校推荐
                ArmsUtils.startActivity(SchoolRecommendActivity.class);

            }else if(bannerBean.getType() == 18) {//网络助学
                InternetStudyGoodsActivity.start(getContext());

            }else if(bannerBean.getType() == 23) {//精讲视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO1);

            }else if(bannerBean.getType() == 24) {//串讲视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO2);

            }else if(bannerBean.getType() == 25) {//特训班视频
                AllVideoActivity.start(getContext(), StartFinal.VIDEO3);

            }else if(!TextUtils.isEmpty(bannerBean.getXcx_path())) {//跳转小程序
                WxMinApplication.StartEncoderUrlToMinAppOrApp(getContext(), bannerBean.getXcx_path(), bannerBean.getTarget_url());

            }else if(!TextUtils.isEmpty(bannerBean.getTarget_url())){//跳转H5页面
                HtmlActivity.start(getActivity(), bannerBean.getTarget_url());
            }

        });
    }

    public void onClick(View view) {
        if(view == homeViewBinding.newUserActivityView){//新人活动
            if(!MainAppUtils.checkLogin(getContext())) return;
            NewUserActivity.start(getContext());

        }else if(view == homeViewBinding.startStudyView){//开始刷题
            //埋点开始刷题按钮的点击次数
            new UmengEventUtils(getContext())
                    .addParams("icon_type", "开始刷题")
                    .pushEvent(UmengEventUtils.CLICK_ICON);

            CourseActivity.start(getContext(), mPresenter.getCourseShowMode());

        }else if(view == homeViewBinding.icon1View ||view == homeViewBinding.icon2View || view == homeViewBinding.icon3View || view == homeViewBinding.icon4View){//“开始刷题”周围的四个按钮
            IconView iconView = (IconView) view;
            ZkHomeBean zkHomeBean = CacheInterfaceData.getZkHomeBean();
            if(zkHomeBean != null && !AppUtil.isEmpty(zkHomeBean.getIcons_1()) && zkHomeBean.getIcons_1().size() >= iconView.getPosition()) {
                IconBean iconBean = zkHomeBean.getIcons_1().get(iconView.getPosition());
                boolean needLogin = iconBean.getNeed_login() == StartFinal.YES;
                iconStartActivity(iconView.getType(), iconView.getXcx_path(), iconView.getTarget_url(), needLogin);

                //埋点开始刷题四周按钮的点击次数
                new UmengEventUtils(getContext())
                        .addParams("icon_type", iconBean.getName())
                        .pushEvent(UmengEventUtils.CLICK_ICON);
            }

        }else if(view == homeViewBinding.vipGroupView){//VIP题库
            //埋点考试神器VIP题库的点击次数
            new UmengEventUtils(getContext())
                    .addParams("click_vip_or_yami", "vip")
                    .pushEvent(UmengEventUtils.CLICK_VIP_OR_YAMI);

            GroupActivity.Start(getContext(), StartFinal.VIP, 0);

        }else if(view == homeViewBinding.secretGroupView){//考前押密
            //埋点考试神器押密题库的点击次数
            new UmengEventUtils(getContext())
                    .addParams("click_vip_or_yami", "yami")
                    .pushEvent(UmengEventUtils.CLICK_VIP_OR_YAMI);

            GroupActivity.Start(getContext(), StartFinal.YAMI, 0);

        }else if(view == homeViewBinding.examNewsView){//考试资讯
            moveIndicator(homeViewBinding.examNewsView, homeViewBinding.schoolNewsView, homeViewBinding.newsIndicatorView);
            homeViewBinding.newsIndicatorView.setTag(ArticleActivity.HISTORY_NEWS);
            mPresenter.getNewList(HomePresenter.EXAM_NEW);

        }else if(view == homeViewBinding.schoolNewsView){//学校新闻
            moveIndicator(homeViewBinding.schoolNewsView, homeViewBinding.examNewsView, homeViewBinding.newsIndicatorView);
            homeViewBinding.newsIndicatorView.setTag(ArticleActivity.SCHOOL_NEWS);
            mPresenter.getNewList(HomePresenter.SCHOOL_NEW);

        }else if(view == homeViewBinding.moreNewsView){//更多新闻
            ArticleActivity.start(getActivity(), homeViewBinding.newsIndicatorView.getTag() == null ? ArticleActivity.HISTORY_NEWS : (Integer) homeViewBinding.newsIndicatorView.getTag());

        }else if(view == homeViewBinding.moreCircleView){//更多动态
            Activity activity = getActivity();
            if(activity instanceof MainActivity){
                MainActivity mainActivity = (MainActivity) activity;
                mainActivity.scrollViewPager(3);
            }
        }
    }

    public void moveIndicator(TextView targetView, TextView otherView, View indicatorView){
        //指示器更换对齐目标
        ConstraintLayout.LayoutParams indicatorLP = (ConstraintLayout.LayoutParams)indicatorView.getLayoutParams();
        indicatorLP.startToStart = targetView.getId();
        indicatorLP.topToBottom = targetView.getId();
        indicatorLP.endToEnd = targetView.getId();
        indicatorView.setLayoutParams(indicatorLP);
        //改变字体大小和颜色
        targetView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
        targetView.setTextColor(getResources().getColor(R.color.tblack));
        otherView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        otherView.setTextColor(getResources().getColor(R.color.tblack3));
    }

    private void iconStartActivity(String type, String path, String targetUrl, boolean needLogin){
        //设置需要检测的type值集合
        if(needLogin && !MainAppUtils.checkLogin(getContext())) return;

        if(type.contains("video")) {//视频
            //AllVideoActivity.start(getContext(), type);
            TextBooksActivity.start(getContext(), 2);
            //ErrorCollActivity.Start(getContext());

        }else if(type.equals("free")) {//免费题库
            TopicActivity.start(getActivity(), PracticeHelper.PRACTICE_COURSE);

        }else if(type.equals("real_paper")) {//历年真题
            TruePaperAllNewActivity.start(getContext());

        }else if(type.equals("chapter")) {//章节训练
            ArmsUtils.startActivity(ChapterActivity.class);

        }else if(type.equals("simu")) {//模拟试卷
            ArmsUtils.startActivity(SimuPaperActivity.class);

        }else if(type.equals("error_topic")) {//我的错题
            MyActivity.start(getActivity(), 3);

        }else if(type.equals("collection")) {//我的收藏
            MyActivity.start(getActivity(), 4);

        }else if(type.equals("zhcx")) {//综合查询
            ArmsUtils.startActivity(SearchUrlActivity.class);

        }else if(type.equals("pintuan")) {//我要拼团
            GroupActivity.Start(getContext(), "", 0);

        }else if(type.equals("zixun")) {//咨询老师
            WxMinApplication.StartWechat(getContext());

        }else if(type.equals("sign")) {//每日打卡
            ArmsUtils.startActivity(DayCardV3Activity.class);

        }else if(type.equals("member")) {//笔果折扣卡
            if(UserCache.isMemberShip()){
                ArmsUtils.startActivity(BiguoVipActivity.class);
            }else {
                ArmsUtils.startActivity(BiguoVipOpenActivity.class);
            }

        }else if(type.equals("douyin")) {//抖音直播
            Activity activity = AppManager.getAppManager().getTopActivity();
            if(activity instanceof MainActivity) {
                new MessageDialog.Builder(((MainActivity)activity).getSupportFragmentManager())
                        .setTitle("温馨提醒")
                        .setContent("即将跳转至抖音，是否继续？")
                        .setNegativeText("取消")
                        .setPositiveText("继续")
                        .setPositiveClickListener((View.OnClickListener) v -> {
                            Uri uri = Uri.parse(targetUrl);
                            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            getContext().startActivity(intent);
                        }).builder()
                        .show();
            }

        }else if(type.equals("elite_school")) {//名校推荐
            ArmsUtils.startActivity(SchoolRecommendActivity.class);

        }else if(type.equals("network_aid")) {//网络助学
            InternetStudyGoodsActivity.start(getContext());

        }else if(!TextUtils.isEmpty(path)) {//跳转小程序
            if(needLogin && !MainAppUtils.checkLogin(getContext())) return;
            WxMinApplication.StartWechat(getContext(), path, targetUrl);

        }else if(!TextUtils.isEmpty(targetUrl)){//跳转H5页面, 我要报名、积分入户、学梦计划
            HtmlActivity.start(getActivity(), targetUrl);

        }
    }

    public void setHomeData(ZkHomeBean zkHomeBean) {
        //初始化顶部banner图
        homeViewBinding.headBannerView.update(zkHomeBean);
        //新人活动
        homeViewBinding.newUserActivityView.setVisibility(TextUtils.isEmpty(zkHomeBean.getIs_newcomers()) ? View.GONE : View.VISIBLE);
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.newUserActivityView, zkHomeBean.getIs_newcomers());

        //设置学习进度
        try {
            float progress = Float.parseFloat(zkHomeBean.getCompletion()) * 100;
            progress = Math.max(0, Math.min(progress, 100f));
            homeViewBinding.startStudyView.setProgress(progress);
            homeViewBinding.numberProgressView.setText(String.format(Locale.CHINA, "开始刷题\n%.1f%%", progress));
        }catch (Exception e){
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }

        //获取金刚区的icon
        List<IconBean> iconList = zkHomeBean.getIcons_1();
        if(AppUtil.isEmpty(iconList))return;

        //加载围绕开始刷题按钮的四个小按钮, iconList第一个是开始刷题按钮的图标，所以循环要从1开始
        IconView[] iconViews = {homeViewBinding.icon1View, homeViewBinding.icon2View, homeViewBinding.icon3View, homeViewBinding.icon4View};
        for(int i=1; i<iconList.size() && i < iconViews.length+1; i++){
            IconBean icon = iconList.get(i);

            iconViews[i-1].setData(icon.getImg(), icon.getName());
            iconViews[i-1].setType(icon.getType());
            iconViews[i-1].setXcx_path(icon.getXcx_path());
            iconViews[i-1].setTarget_url(icon.getTarget_url());
            iconViews[i-1].setPosition(i);
        }

        //开始刷题按钮底部的一排按钮
        List<IconBean> iconBottomList = new ArrayList<>();
        for(int i=5; i<iconList.size(); i++){
            iconBottomList.add(iconList.get(i));
        }
        homeViewBinding.line0View.setVisibility(iconBottomList.size() > 0 ? View.VISIBLE : View.GONE);
        homeViewBinding.iconRecyclerView.setVisibility(iconBottomList.size() > 0 ? View.VISIBLE : View.GONE);
        mIconAdapter.setNewData(iconBottomList);

        //初始化中间banner图
        List<String> middleBannerList = mPresenter.getBannerImgList(2);
        if(!AppUtil.isEmpty(middleBannerList)) {
            homeViewBinding.bottomBannerView.setVisibility(View.VISIBLE);
            homeViewBinding.bottomBannerView.update(middleBannerList);
        }else {
            homeViewBinding.bottomBannerView.setVisibility(View.GONE);
        }

        //获取考试神器的icon
        List<IconBean> groupList = zkHomeBean.getIcons_2();
        String secretGroupImg = null;
        String vipGroupImg = null;
        //加载考试神器的图片
        for(IconBean icon : groupList){
            if(icon.getType().equals(StartFinal.YAMI)){
                secretGroupImg = icon.getImg();
            }else if(icon.getType().equals(StartFinal.VIP)){
                vipGroupImg = icon.getImg();
            }
        }
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.secretGroupView, secretGroupImg);
        ImageLoader.loadImageNoPlaceholder(homeViewBinding.vipGroupView, vipGroupImg);

        //考试必备
        recommendAdapter.setNewData(zkHomeBean.getRecommend_products());
        //考试资讯/学校新闻
        mNewsAdapter.setNewData(zkHomeBean.getFindings());
    }

    public void setNews(List<ArticleBean> list){
        mNewsAdapter.setNewData(list);
    }

    public void notifyNews(){
        mNewsAdapter.notifyDataSetChanged();
    }

    public void shoExamNews(){
        onClick(homeViewBinding.examNewsView);
    }

    public void setShowCircleTitleView(boolean isShow){
        int visibility = isShow ? View.VISIBLE : View.GONE;
        homeViewBinding.line6View.setVisibility(visibility);
        homeViewBinding.circleTitleView.setVisibility(visibility);
        homeViewBinding.moreCircleView.setVisibility(visibility);
    }

    public Activity getActivity(){
        return AppManager.getAppManager().getTopActivity();
    }*/
}
