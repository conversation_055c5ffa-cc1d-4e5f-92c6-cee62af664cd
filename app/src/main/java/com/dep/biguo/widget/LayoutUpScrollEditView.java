package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.biguo.utils.util.KeyboardUtils;

/**当输入框被打开时，调用传入的回调对象，主要是用于布局往上滚动，展示完整的输入框，并且不能被滚动布局影响输入框的滚动事件
 *
 */
public class LayoutUpScrollEditView extends androidx.appcompat.widget.AppCompatEditText {
    private OnTouchClickListener onTouchClickListener;

    public LayoutUpScrollEditView(@NonNull Context context) {
        super(context);
    }

    public LayoutUpScrollEditView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public LayoutUpScrollEditView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        //触摸的位置不在输入框的范围内，不执行回调，且不获取焦点，不弹出软键盘
        if(event.getX() >= getX() && event.getX() <= getX() + getWidth()
                && event.getY() >= getY() && event.getY() <= getY() + getHeight()) {
            if(event.getAction()==MotionEvent.ACTION_UP){
                //通知父控件不要干扰
                getParent().requestDisallowInterceptTouchEvent(false);
                //不能输入，则不回调
                if (onTouchClickListener != null && isEnabled()) {
                    onTouchClickListener.onTouch();
                }
            }
        }else {
            clearFocus();
            KeyboardUtils.hideKeyboard(this);
        }
        return super.onTouchEvent(event);
    }


    public interface OnTouchClickListener{
        void onTouch();
    }

    public void setOnTouchClickListener(OnTouchClickListener onTouchClickListener) {
        this.onTouchClickListener = onTouchClickListener;
    }
}
