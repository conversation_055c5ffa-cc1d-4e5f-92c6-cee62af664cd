package com.dep.biguo.widget;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dep.biguo.R;
import com.duma.ld.mylibrary.ColorEvaluator;

public class SwitchView  extends View implements View.OnClickListener {
    private int bgColor;
    private String leftColor;
    private String rightColor;
    private String textLeftColor;
    private String textRightColor;
    private String textLeftClickColor;
    private String textRightClickColor;
    private Paint bgPaint;
    private Paint clickPaint;
    private Paint leftTextPaint;
    private Paint rightTextPaint;
    private String clickColor;
    private float mWidth;
    private float mHeight;
    private float mClickWidth;
    private RectF roundRect;
    private Path bgPath;
    private Path clickPath;
    private boolean checked;
    private float padding;
    private int time;
    private String textLeft;
    private String textRight;
    int rgb;
    private float mLeftTextX;
    private float mLiftTextY;
    private float mRightTextX;
    private float mRightTexty;
    private float animatorRight;
    private float animatorLift;
    private ValueAnimator anim;
    private ObjectAnimator anim2;
    private ObjectAnimator anim3;
    private ObjectAnimator anim4;
    private AnimatorSet animSet;
    private onClickCheckedListener onClickCheckedListener;

    public SwitchView(Context context) {
        this(context, (AttributeSet)null);
    }

    public SwitchView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SwitchView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.rgb = Color.rgb(255, 255, 255);
        this.init(attrs);
        this.initPaint();
        this.setOnClickListener(this);
    }

    private void init(@Nullable AttributeSet attrs) {
        TypedArray a = this.getContext().obtainStyledAttributes(attrs, R.styleable.SwitchView);
        this.bgColor = a.getColor(R.styleable.SwitchView_bgColor, this.rgb);
        this.leftColor = String.valueOf(a.getColor(R.styleable.SwitchView_leftColor, Color.rgb(34, 139, 34)));
        this.rightColor = String.valueOf(a.getColor(R.styleable.SwitchView_rightColor, Color.rgb(34, 139, 34)));
        this.textLeftColor = String.valueOf(a.getColor(R.styleable.SwitchView_textLeftColor, Color.rgb(0, 0, 0)));
        this.textRightColor = String.valueOf(a.getColor(R.styleable.SwitchView_textRightColor, Color.rgb(0, 0, 0)));
        this.textLeftClickColor = String.valueOf(a.getColor(R.styleable.SwitchView_textLeftClickColor, this.rgb));
        this.textRightClickColor = String.valueOf(a.getColor(R.styleable.SwitchView_textRightClickColor, this.rgb));
        this.checked = a.getBoolean(R.styleable.SwitchView_setChecked, false);
        this.textLeft = a.getString(R.styleable.SwitchView_textLeft);
        this.textRight = a.getString(R.styleable.SwitchView_textRight);
        this.padding = a.getDimension(R.styleable.SwitchView_padding, (float)this.dp2px(4.0F));
        this.time = a.getInteger(R.styleable.SwitchView_time, 300);
        a.recycle();
    }

    private void initPaint() {
        this.animSet = new AnimatorSet();
        this.anim = new ValueAnimator();
        this.anim2 = new ObjectAnimator();
        this.anim2.setTarget(this);
        this.anim2.setPropertyName("textLeftColor");
        this.anim3 = new ObjectAnimator();
        this.anim3.setTarget(this);
        this.anim3.setPropertyName("textRightColor");
        this.anim4 = new ObjectAnimator();
        this.anim4.setTarget(this);
        this.anim4.setPropertyName("clickColor");
        this.roundRect = new RectF();
        this.clickPath = new Path();
        this.bgPath = new Path();
        this.bgPaint = new Paint();
        this.bgPaint.setColor(this.bgColor);
        this.bgPaint.setAntiAlias(true);
        this.clickPaint = new Paint();
        this.refreshColor();
        this.clickPaint.setAntiAlias(true);
        this.leftTextPaint = new Paint();
        this.leftTextPaint.setTextSize((float)this.dp2px(12.0F));
        this.leftTextPaint.setAntiAlias(true);
        this.rightTextPaint = new Paint();
        this.rightTextPaint.setTextSize((float)this.dp2px(12.0F));
        this.rightTextPaint.setAntiAlias(true);
        if (!this.isChecked()) {
            this.leftTextPaint.setColor(Integer.parseInt(this.textLeftClickColor));
            this.rightTextPaint.setColor(Integer.parseInt(this.textRightColor));
        } else {
            this.rightTextPaint.setColor(Integer.parseInt(this.textRightClickColor));
            this.leftTextPaint.setColor(Integer.parseInt(this.textLeftColor));
        }

    }

    private void refreshColor() {
        if (!this.isChecked()) {
            this.setClickColor(Integer.parseInt(this.leftColor));
        } else {
            this.setClickColor(Integer.parseInt(this.rightColor));
        }

    }

    private void initPath() {
        this.mClickWidth = (this.mWidth - this.padding * 2.0F) / 2.0F;
        if (!this.isChecked()) {
            this.offsetWidth(0.0F);
        } else {
            this.offsetWidth(this.mClickWidth);
        }

        this.getPath(this.bgPath, 0.0F, this.mWidth, 0.0F);
        this.mLeftTextX = (this.mClickWidth - this.leftTextPaint.measureText(this.textLeft)) / 2.0F + this.padding;
        this.mRightTextX = this.mClickWidth + this.padding + (this.mClickWidth - this.rightTextPaint.measureText(this.textRight)) / 2.0F;
        Paint.FontMetrics LfontMetrics = this.leftTextPaint.getFontMetrics();
        this.mLiftTextY = this.mHeight / 2.0F + (Math.abs(LfontMetrics.ascent) - LfontMetrics.descent) / 2.0F;
        Paint.FontMetrics RfontMetrics = this.rightTextPaint.getFontMetrics();
        this.mRightTexty = this.mHeight / 2.0F + (Math.abs(RfontMetrics.ascent) - RfontMetrics.descent) / 2.0F;
        this.animatorRight = 0.0F;
        this.animatorLift = this.mClickWidth;
    }

    private void initAnim() {
        if (this.animSet != null && this.animSet.isRunning()) {
            this.animSet.cancel();
        }

        this.animSet = new AnimatorSet();
        if (this.isChecked()) {
            this.anim.setFloatValues(new float[]{this.animatorRight, this.mClickWidth});
            this.setAnimView(this.anim2, this.toHexEncoding(Integer.parseInt(this.textLeftClickColor)), this.toHexEncoding(Integer.parseInt(this.textLeftColor)));
            this.setAnimView(this.anim3, this.toHexEncoding(Integer.parseInt(this.textRightColor)), this.toHexEncoding(Integer.parseInt(this.textRightClickColor)));
            this.setAnimView(this.anim4, this.toHexEncoding(Integer.parseInt(this.leftColor)), this.toHexEncoding(Integer.parseInt(this.rightColor)));
        } else {
            this.anim.setFloatValues(new float[]{this.animatorLift, 0.0F});
            this.setAnimView(this.anim2, this.toHexEncoding(Integer.parseInt(this.textLeftColor)), this.toHexEncoding(Integer.parseInt(this.textLeftClickColor)));
            this.setAnimView(this.anim3, this.toHexEncoding(Integer.parseInt(this.textRightClickColor)), this.toHexEncoding(Integer.parseInt(this.textRightColor)));
            this.setAnimView(this.anim4, this.toHexEncoding(Integer.parseInt(this.rightColor)), this.toHexEncoding(Integer.parseInt(this.leftColor)));
        }

        this.anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            public void onAnimationUpdate(ValueAnimator animation) {
                if ((Float)animation.getAnimatedValue() != 0.0F && (Float)animation.getAnimatedValue() != SwitchView.this.mClickWidth) {
                    SwitchView.this.animatorRight = (Float)animation.getAnimatedValue();
                    SwitchView.this.animatorLift = (Float)animation.getAnimatedValue();
                } else {
                    SwitchView.this.animatorRight = 0.0F;
                    SwitchView.this.animatorLift = SwitchView.this.mClickWidth;
                }

                SwitchView.this.offsetWidth((Float)animation.getAnimatedValue());
                SwitchView.this.invalidate();
            }
        });
        this.animSet.play(this.anim).with(this.anim2).with(this.anim3).with(this.anim4);
        this.animSet.setDuration((long)this.time);
        this.animSet.start();
    }

    private void setAnimView(ObjectAnimator anim, Object... values) {
        anim.setObjectValues(values);
        anim.setEvaluator(new ColorEvaluator());
    }

    private void offsetWidth(float w) {
        this.getPath(this.clickPath, this.padding, this.mClickWidth, w);
    }

    private void getPath(Path path, float padding, float width, float offset) {
        this.roundRect.set(0.0F + padding + offset, 0.0F + padding, width + padding + offset, this.mHeight - padding);
        path.rewind();
        path.addRoundRect(this.roundRect, this.mHeight / 2.0F, this.mHeight / 2.0F, Path.Direction.CW);
    }

    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        float widthSize = (float)MeasureSpec.getSize(widthMeasureSpec);
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        float heightSize = (float)MeasureSpec.getSize(heightMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        if (widthMode == 1073741824) {
            this.mWidth = widthSize;
        }

        if (heightMode == 1073741824) {
            this.mHeight = heightSize;
        }

        this.setMeasuredDimension((int)this.mWidth, (int)this.mHeight);
        this.initPath();
    }

    protected void onDraw(Canvas canvas) {
        canvas.drawColor(0);
        canvas.drawPath(this.bgPath, this.bgPaint);
        canvas.drawPath(this.clickPath, this.clickPaint);
        canvas.drawText(this.textLeft, this.mLeftTextX, this.mLiftTextY, this.leftTextPaint);
        canvas.drawText(this.textRight, this.mRightTextX, this.mRightTexty, this.rightTextPaint);
    }

    private int dp2px(float dpValue) {
        float scale = this.getContext().getResources().getDisplayMetrics().density;
        return (int)(dpValue * scale + 0.5F);
    }

    public int sp2px(float spValue) {
        float fontScale = this.getContext().getResources().getDisplayMetrics().scaledDensity;
        return (int)(spValue * fontScale + 0.5F);
    }

    public String getTextRightColor() {
        return this.textRightColor;
    }

    public void setTextRightColor(String textRightColor) {
        this.rightTextPaint.setColor(Color.parseColor(textRightColor));
    }

    public String getTextLeftColor() {
        return this.textLeftColor;
    }

    public void setTextLeftColor(String textLeftColor) {
        this.leftTextPaint.setColor(Color.parseColor(textLeftColor));
    }

    public String getClickColor() {
        return this.clickColor;
    }

    public void setClickColor(String clickColor) {
        this.clickPaint.setColor(Color.parseColor(clickColor));
    }

    public void setClickColor(int clickColor) {
        this.clickPaint.setColor(clickColor);
    }

    public String toHexEncoding(int color) {
        StringBuffer sb = new StringBuffer();
        String R = Integer.toHexString(Color.red(color));
        String G = Integer.toHexString(Color.green(color));
        String B = Integer.toHexString(Color.blue(color));
        R = R.length() == 1 ? "0" + R : R;
        G = G.length() == 1 ? "0" + G : G;
        B = B.length() == 1 ? "0" + B : B;
        sb.append("#");
        sb.append(R.toUpperCase());
        sb.append(G.toUpperCase());
        sb.append(B.toUpperCase());
        return sb.toString();
    }

    public void onClick(View v) {
        this.setChecked(!this.checked);
    }

    public void setChecked(boolean checked) {
        if (checked != this.checked) {
            this.checked = checked;
            this.initAnim();
            if (this.onClickCheckedListener != null) {
                this.onClickCheckedListener.onClick();
            }

        }
    }

    public void setOnClickCheckedListener(onClickCheckedListener onClickCheckedListener) {
        this.onClickCheckedListener = onClickCheckedListener;
    }

    public boolean isChecked() {
        return this.checked;
    }

    public interface onClickCheckedListener {
        void onClick();
    }
}
