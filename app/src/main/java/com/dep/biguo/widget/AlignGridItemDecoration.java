package com.dep.biguo.widget;

import android.graphics.Rect;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import java.util.ArrayList;
import java.util.List;


/**<p>设置GridLayoutManager的item的对齐方式</p>
 * <p>1、默认每一个控件的宽度都是一样的，控件的宽度不一致，将导致排列错乱</p>
 * <p>2、每个子控件的最外层需要设置为wrap_content，否则导致子控件的宽度无法正常获取</p>
 * <p>3、ConstraintLayout布局下，使用约束设置RecyclerView的宽度，将导致RecyclerView的宽度无法提前获取，无法计算item的排列方式</p>
 */
public class AlignGridItemDecoration extends RecyclerView.ItemDecoration {
    public static final int ALIGN_SIDES = 0;//两边对齐
    public static final int ALIGN_SPACE_EQUAL= 1;//间距相等

    private List<Float> leftPaddingList;

    private int align;


    public AlignGridItemDecoration(int align) {
        this.align = align;
        leftPaddingList = new ArrayList<>();
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        //控件的下标
        int position = parent.getChildLayoutPosition(view);
        //列数
        int spanCount = ((GridLayoutManager) parent.getLayoutManager()).getSpanCount();
        computer(view, parent);
        //根据所在的列取边距
        if(leftPaddingList.size() > 0) {
            outRect.left = Math.abs(leftPaddingList.get(position % spanCount).intValue());
        }
    }

    public void computer(@NonNull View view, @NonNull RecyclerView parent){//控件的下标
        //列数
        int spanCount = ((GridLayoutManager) parent.getLayoutManager()).getSpanCount();

        //初始化边距，因为默认每一个控件的宽度都是一样的，所以同一列控件的左边距是一样的
        if(leftPaddingList.size() == 0) {
            //列表提供给子控件的最大宽度
            int parentWidth = parent.getMeasuredWidth() - parent.getPaddingStart() - parent.getPaddingEnd();
            //子控件的宽度
            int childWidth;
            if (view.getLayoutParams().width > 0) {
                childWidth = view.getLayoutParams().width;
            } else {
                view.measure(0, 0);
                childWidth = view.getMeasuredWidth();
            }
            //每个子控件所在格子的宽度（格子：GridLayoutManager布局管理器会根据spanCount数列平均分割RecyclerView的宽度）
            float outRectWidth = parentWidth * 1f / spanCount;
            //一行内所有子控件的宽度总和 = 控件的宽度 * 控件数量
            int allChildWidth = childWidth * spanCount;
            //两边对齐方式，求的是控件之间的间距个数
            //间距相等方式，求的是控件之间的间距个数 + 与RecyclerView的间距个数
            int spaceCount = (align == ALIGN_SIDES) ? spanCount - 1 : spanCount + 1;
            //控件间距 = (列表宽度 - 所有控件的宽度总和) / 控件的间距个数
            float padding = (parentWidth - allChildWidth) * 1f / spaceCount;
            //两边对齐方式，则不需要设置初始值
            //间距相等方式，则需要设置初始值
            float nextLeftPadding = align == ALIGN_SIDES ? 0f : padding;
            //设置每一行的第一个控件的左边距
            leftPaddingList.add(nextLeftPadding);
            //计算除每一行的第一个控件之外的控件的左边距
            for(int i=0; i<spanCount; i++) {
                //控件的右边距 = 格子长度 - 控件的左边距 - 控件的宽度
                float rightPadding = outRectWidth - nextLeftPadding - childWidth;
                //下一个控件的左边距 = 控件间距 - 控件的右边距
                nextLeftPadding = padding - rightPadding;

                leftPaddingList.add(nextLeftPadding);
            }
        }
    }

    /**清除一下计算好的数据，下次调用时会重新计算
     * */
    public void clear(){
        leftPaddingList.clear();
    }

}
