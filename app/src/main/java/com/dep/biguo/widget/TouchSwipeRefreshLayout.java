package com.dep.biguo.widget;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

/**解决SwipeRefreshLayout 与 viewPager 引发的手势冲突
 *
 */
public class TouchSwipeRefreshLayout extends SwipeRefreshLayout {
    private float downX;
    private float minMoveWidth;

    public TouchSwipeRefreshLayout(@NonNull Context context) {
        super(context);
    }

    public TouchSwipeRefreshLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        //判断用户在进行滑动操作的最小距离
        minMoveWidth = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                downX = MotionEvent.obtain(event).getX();
                break;

            case MotionEvent.ACTION_MOVE:
                final float eventX = event.getX();
                //获取水平移动距离
                float xDiff = Math.abs(eventX - downX);
                //当水平移动距离大于滑动操作的最小距离的时候就认为进行了横向滑动
                //不进行事件拦截,并将这个事件交给子View处理
                if (xDiff > minMoveWidth) {
                    return false;
                }
        }

        return super.onInterceptTouchEvent(event);
    }
}
