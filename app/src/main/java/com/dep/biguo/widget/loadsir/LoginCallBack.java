package com.dep.biguo.widget.loadsir;

import android.content.Context;
import android.view.View;

import com.dep.biguo.R;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.kingja.loadsir.callback.Callback;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/9
 * @Description:
 */
public class LoginCallBack extends Callback {

    @Override
    protected int onCreateView() {
        return R.layout.status_login;
    }

    @Override
    protected boolean onReloadEvent(Context context, View view) {
        view.findViewById(R.id.tvLogin).setOnClickListener(v -> MainAppUtils.checkLogin(context));
        return true;
    }
}
