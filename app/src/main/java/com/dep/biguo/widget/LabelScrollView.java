package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import androidx.annotation.ColorInt;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.HorizontalScrollView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class LabelScrollView extends HorizontalScrollView {

    private Context mContext;

    private View mLabelLayout;
    private TextView tvCourseCode; //课程代码
    private TextView tvTruePager; //真题
    private TextView tvCourseType; //必考选考加考
    private TextView tvCourseApply; //已报考未报考
    private TextView tvTopicCount; //题目数量
    private TextView tvBuyTime; //购买时间
    private TextView tvGroupPrice; //拼团价
    private TextView tvLookUp; //查看

    private int[] mColors = {R.color.label_color_1,
            R.color.label_color_2,
            R.color.label_color_3,
            R.color.label_color_4,
            R.color.label_color_5,
            R.color.label_color_6,
            R.color.label_color_7};

    private int mTextColor = R.color.label_color_text;

    public LabelScrollView(Context context) {
        this(context, null);
    }

    public LabelScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LabelScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;

        mLabelLayout = LayoutInflater.from(context).inflate(R.layout.label_layout, null);
        addView(mLabelLayout);
        tvCourseCode = mLabelLayout.findViewById(R.id.tvCourseCode);
        tvTruePager = mLabelLayout.findViewById(R.id.tvTruePager);
        tvCourseType = mLabelLayout.findViewById(R.id.tvCourseType);
        tvCourseApply = mLabelLayout.findViewById(R.id.tvCourseApply);
        tvTopicCount = mLabelLayout.findViewById(R.id.tvTopicCount);
        tvBuyTime = mLabelLayout.findViewById(R.id.tvBuyTime);
        tvGroupPrice = mLabelLayout.findViewById(R.id.tvGroupPrice);
        tvLookUp = mLabelLayout.findViewById(R.id.tvLookUp);
    }

    public void setCourseCode(String code) {
        if (TextUtils.isEmpty(code)) {
            tvCourseCode.setVisibility(GONE);
            return;
        }
        tvCourseCode.setVisibility(VISIBLE);
        tvCourseCode.setText(code);
        tvCourseCode.setTextColor(getColor(mTextColor));
        tvCourseCode.setBackground(getDrawableBackground(mColors[0]));
    }

    public void setLookUp(String tip) {
        if (TextUtils.isEmpty(tip)) {
            tvLookUp.setVisibility(GONE);
            return;
        }
        tvLookUp.setVisibility(VISIBLE);
        tvLookUp.setText(tip);
        tvLookUp.setTextColor(getColor(mTextColor));
        tvLookUp.setBackground(getDrawableBackground(mColors[0]));
    }

    public void setTruePager(String pager) {
        if (TextUtils.isEmpty(pager)) {
            tvTruePager.setVisibility(GONE);
            return;
        }
        tvTruePager.setVisibility(VISIBLE);
        tvTruePager.setText(pager);
        tvTruePager.setTextColor(getColor(mTextColor));
        tvTruePager.setBackground(getDrawableBackground(mColors[1]));
    }

    public void setCourseType(String type) {
        if (TextUtils.isEmpty(type)) {
            tvCourseType.setVisibility(GONE);
            return;
        }
        tvCourseType.setVisibility(VISIBLE);
        tvCourseType.setText(type);
        tvCourseType.setTextColor(getColor(mTextColor));
        tvCourseType.setBackground(getDrawableBackground(mColors[2]));
    }

    public void setCourseApply(String apply) {
        if (TextUtils.isEmpty(apply)) {
            tvCourseApply.setVisibility(GONE);
            return;
        }
        tvCourseApply.setVisibility(VISIBLE);
        tvCourseApply.setText(apply);
        tvCourseApply.setTextColor(getColor(mTextColor));
        tvCourseApply.setBackground(getDrawableBackground(mColors[3]));
    }

    public void setTopicCount(String count) {
        if (TextUtils.isEmpty(count)) {
            tvTopicCount.setVisibility(GONE);
            return;
        }
        tvTopicCount.setVisibility(VISIBLE);
        tvTopicCount.setText(count);
        tvTopicCount.setTextColor(getColor(mTextColor));
        tvTopicCount.setBackground(getDrawableBackground(mColors[4]));
    }

    public void setBuyTime(String time) {
        if (TextUtils.isEmpty(time)) {
            tvBuyTime.setVisibility(GONE);
            return;
        }
        tvBuyTime.setVisibility(VISIBLE);
        tvBuyTime.setText(time);
        tvBuyTime.setTextColor(getColor(mTextColor));
        tvBuyTime.setBackground(getDrawableBackground(mColors[5]));
    }

    public void setGroupPrice(String price) {
        if (TextUtils.isEmpty(price)) {
            tvGroupPrice.setVisibility(GONE);
            return;
        }
        tvGroupPrice.setVisibility(VISIBLE);
        tvGroupPrice.setText(price);
        tvGroupPrice.setTextColor(getColor(mTextColor));
        tvGroupPrice.setBackground(getDrawableBackground(mColors[6]));
    }

    private GradientDrawable getDrawableBackground(int color) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setStroke(DisplayHelper.dp2px(mContext, 1), getColor(color));
        drawable.setCornerRadius(DisplayHelper.dp2px(mContext, 200));
        return drawable;
    }

    private @ColorInt
    int getColor(int color) {
        return ContextCompat.getColor(mContext, color);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return false;
    }
}
