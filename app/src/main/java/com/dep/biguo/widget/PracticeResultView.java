package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.bean.AiScoreBean;
import com.dep.biguo.databinding.PracticeResultShareBinding;
import com.dep.biguo.utils.MathUtil;

public class PracticeResultView extends FrameLayout {
    private PracticeResultShareBinding binding;

    public PracticeResultView(Context context) {
        this(context, null);
    }

    public PracticeResultView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PracticeResultView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        binding = DataBindingUtil.bind(LayoutInflater.from(context).inflate(R.layout.practice_result_share, null));
        addView(binding.getRoot());
    }

    public void measure(){
        int width = DisplayHelper.dp2px(getContext(), 375);
        int height = DisplayHelper.dp2px(getContext(), 695);

        int widthMeasureSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY);
        int heightMeasureSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY);

        measure(widthMeasureSpec, heightMeasureSpec);
        layout(0, 0, width, height);
    }

    public void setChildSize(int id){
        TextView view = getTextView(id);
        Paint paint = new Paint();
        paint.setTextSize(view.getTextSize());
        float width = paint.measureText(view.getText().toString());
        float lineWidth = DisplayHelper.dp2px(getContext(), 375 - 90);
        if(width > lineWidth){
            view.measure(0,0);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            int lineCount = (int) (width / lineWidth + (width % lineWidth > 0 ? 1 : 0));
            layoutParams.height = view.getMeasuredHeight() * lineCount;
            view.setLayoutParams(layoutParams);
        }
        measure();
    }

    public TextView getTextView(int id){
        return findViewById(id);
    }

    public void setAiScore(AiScoreBean aiScore){
        if(aiScore == null) return;

        binding.aiEstimateView.setMiddleText(aiScore.getAi_score(), false);
        binding.aiEstimateView.setSweepAngle(MathUtil.StringDivFloat(aiScore.getAi_score(), "100"), false);
        binding.aiEstimateView.setRateText(aiScore.getAi_title());
        binding.moreThanPeopleView.setText(aiScore.getAi_tip_text());
    }

    public void setCorrectRate(float probability, String correctRate){
        binding.correctRateView.setMiddleText(correctRate, false);
        binding.correctRateView.setSweepAngle(probability, false);
    }
}
