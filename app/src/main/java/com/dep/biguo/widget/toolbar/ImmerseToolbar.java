package com.dep.biguo.widget.toolbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.dep.biguo.R;

public class ImmerseToolbar extends LinearLayout {
    private View leftLayout;
    private View centerLayout;
    private View rightLayout;

    //标题栏的title是否需要居中
    private boolean isCenterInCenter;
    private int left_id;
    private int center_id;
    private int right_id;

    public ImmerseToolbar(Context context) {
        super(context);
    }

    public ImmerseToolbar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initAttr(attrs, context);
    }

    public ImmerseToolbar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttr(attrs, context);
    }

    private void initAttr(AttributeSet attrs, Context context) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ImmerseToolbar);
        isCenterInCenter = typedArray.getBoolean(R.styleable.ImmerseToolbar_center_in_center, true);
        left_id = typedArray.getResourceId(R.styleable.ImmerseToolbar_left_id, -1);
        center_id = typedArray.getResourceId(R.styleable.ImmerseToolbar_center_id, -1);
        right_id = typedArray.getResourceId(R.styleable.ImmerseToolbar_right_id, -1);
        typedArray.recycle();

        //监听内部控件的宽度变化，及时调整标题的位置
        addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> layout());
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if(leftLayout == null) {
            leftLayout = this.findViewById(left_id);
        }
        if(centerLayout == null) {
            centerLayout = this.findViewById(center_id);
        }
        if(rightLayout == null) {
            rightLayout = this.findViewById(right_id);
        }

        layout();
    }

    /**给titleLayout设置左内边距或右内边距，使得titleLayout中的内容一直居中
     *
     */
    private void layout(){
        if(!isCenterInCenter) return;
        if(centerLayout == null) return;

        int leftWidth = leftLayout == null || leftLayout.getVisibility() == View.GONE ? 0 : leftLayout.getWidth();
        int rightWidth = rightLayout == null || rightLayout.getVisibility() == View.GONE ? 0 : rightLayout.getWidth();

        //给titleLayout设置左内边距或右内边距，使得titleLayout中的内容一直居中
        int disparity = leftWidth - rightWidth;
        centerLayout.setPadding(disparity < 0 ? Math.abs(disparity) : 0, 0, Math.max(disparity, 0), 0);
    }

    /**标题是否居中显示
     * @param centerInCenter true 或 false
     */
    public ImmerseToolbar setCenterInCenter(boolean centerInCenter) {
        isCenterInCenter = centerInCenter;
        layout();
        return this;
    }
}
