package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.dep.biguo.R;

public class ClearEditText extends androidx.appcompat.widget.AppCompatEditText implements View.OnFocusChangeListener, TextWatcher {

    /**
     * 删除按钮的引用
     */
    private Drawable mClearDrawable;
    /**
     * 控件是否有焦点
     */
    private boolean hasFoucs;

    private TextWatcher mTextWatcher;
    private OnFocusChangeListener mOnFocusChangeListener;

    public ClearEditText(Context context) {
        super(context);
        init();
    }

    public ClearEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ClearEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        if (mTextWatcher != null) {
            mTextWatcher.beforeTextChanged(charSequence, i, i1, i2);
        }
    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        setClearIconVisible(hasFoucs && charSequence.length() > 0);
        if (mTextWatcher != null) {
            mTextWatcher.onTextChanged(charSequence, i, i1, i2);
        }
    }

    @Override
    public void afterTextChanged(Editable editable) {
        if (mTextWatcher != null) {
            mTextWatcher.afterTextChanged(editable);
        }

    }

    @Override
    public void onFocusChange(View view, boolean focused) {
        this.hasFoucs = focused;
        setClearIconVisible(hasFoucs && !TextUtils.isEmpty(getText()));
        if (mTextWatcher != null && mOnFocusChangeListener != null) {
            mOnFocusChangeListener.onFocusChange(view, hasFoucs);
        }
    }

    private void init() {
        //获取EditText的DrawableRight,假如没有设置我们就使用默认的图片
        mClearDrawable = getCompoundDrawables()[2];
        if (mClearDrawable == null) {
//            throw new NullPointerException("You can add drawableRight attribute in XML");
            mClearDrawable = getResources().getDrawable(R.drawable.img_delete);
        }

        mClearDrawable.setBounds(0, 0, mClearDrawable.getIntrinsicWidth(), mClearDrawable.getIntrinsicHeight());
        //默认设置隐藏图标
        setClearIconVisible(false);
        //设置焦点改变的监听
        setOnFocusChangeListener(this);
        //设置输入框里面内容发生改变的监听
        addTextChangedListener(this);
    }

    private void setClearIconVisible(boolean visible) {
        if (mClearDrawable == null) return;
        Drawable right = visible ? mClearDrawable : null;
        setCompoundDrawables(getCompoundDrawables()[0], getCompoundDrawables()[1], right, getCompoundDrawables()[3]);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP && mClearDrawable != null) {
            int left = getWidth() - getTotalPaddingRight() - getCompoundDrawablePadding();
            boolean touchable = event.getX() > left && event.getX() < getWidth();
            if (touchable && hasFoucs) {
                this.setText("");
            }
        }
        return super.onTouchEvent(event);
    }

    public void setTextWatcher(TextWatcher mTextWatcher) {
        if (mTextWatcher instanceof ClearEditText) {
            super.addTextChangedListener(mTextWatcher);
        } else {
            this.mTextWatcher = mTextWatcher;
        }

    }

    public void setOnFocusChangeListener(OnFocusChangeListener mOnFocusChangeListener) {
        if (mOnFocusChangeListener instanceof ClearEditText) {
            super.setOnFocusChangeListener(mOnFocusChangeListener);
        } else {
            this.mOnFocusChangeListener = mOnFocusChangeListener;
        }

    }
}
