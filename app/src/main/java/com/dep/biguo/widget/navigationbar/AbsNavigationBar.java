package com.dep.biguo.widget.navigationbar;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import com.google.android.material.appbar.AppBarLayout;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.mmkv.UserCache;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/21
 * @Description: 通用标题栏
 */
public abstract class AbsNavigationBar<P extends AbsNavigationBar.Builder.AbsNavigationParams> implements INavigationBar {

    private static final String TAG = "AbsNavigationBar";

    private P mParams;
    private View mNavigationView;

    public AbsNavigationBar(P params) {
        this.mParams = params;
        createAndBindView();
    }

    /**
     * 绑定和创建View
     */
    public void createAndBindView() {

        if (mParams.mParent == null) {
            //处理AppCompatActivity ，未处理Activity
            ViewGroup activityRoot = (ViewGroup) ((Activity) mParams.mContext).getWindow().getDecorView();
            mParams.mParent = (ViewGroup) activityRoot.getChildAt(0);
        }

        if (mParams.mParent == null) return;

        //1. 创建View
        mNavigationView = LayoutInflater.from(mParams.mContext)
                .inflate(bindLayoutId(), mParams.mParent, false);

        //2. 添加
        mParams.mParent.addView(mNavigationView, 0);

        applyView();
    }

    public P getParams() {
        return mParams;
    }

    /**
     * 设置背景色
     *
     * @param resId R.id
     * @param color 背景色
     */
    public void setColor(int resId, @ColorInt int color) {
        View view = findViewById(resId);
        if (view != null) {
            if (view instanceof ViewGroup) {
                if(mParams.mContext instanceof Activity) {
                    StatusBarHelper.setStatusBarColor((Activity) mParams.mContext, ContextCompat.getColor(mParams.mContext, R.color.tran));
                }
                view.setBackgroundColor(color);
            } else if (view instanceof TextView && color != 0) {
                ((TextView) view).setTextColor(color);
            }
        }
    }

    /**
     * 设置文字
     *
     * @param resId R.id
     * @param text  内容
     */
    public void setText(int resId, CharSequence text) {
        TextView tv = findViewById(resId);
        if (TextUtils.isEmpty(text)) {
            tv.setVisibility(View.GONE);
        } else {
            tv.setVisibility(View.VISIBLE);
            tv.setText(text);
        }
    }

    /**
     * 设置图片
     *
     * @param resId R.id
     * @param icon  图片资源
     */
    public void setIcon(int resId, @DrawableRes int icon) {
        ImageView iv = findViewById(resId);
        if (icon == 0) {
            iv.setVisibility(View.GONE);
        } else {
            iv.setVisibility(View.VISIBLE);
            iv.setImageResource(icon);
        }
    }

    public void setElevation(float elevation){
        mNavigationView.setElevation(elevation);
    }

    public void setOnClickListener(@IdRes int resId, View.OnClickListener listener) {
        if (listener == null) return;
        findViewById(resId).setOnClickListener(listener);
    }

    /**
     * findViewById
     *
     * @param resId R.id
     * @param <T>
     * @return View
     */
    public <T extends View> T findViewById(int resId) {
        return (T) mNavigationView.findViewById(resId);
    }

    /**返回测量的高度
     * @return
     */
    public int getMeasureHeight(){
        mNavigationView.measure(0,0);
        return mNavigationView.getMeasuredHeight();
    }

    /**返回高度
     * @return
     */
    public int getHeight(){
        return mNavigationView.getHeight();
    }

    /**设置背景随指定控件滚动距离变化透明度
     * 指定控件往下滚动时，不出现自身与指定控件叠加在一起的情况
     * @param scrollView
     */
    public void setFollowScrollBackground(NestedScrollView scrollView){
        if(mParams.mContext instanceof Activity){
            //设置状态栏背景颜色透明
            StatusBarHelper.setStatusBarColor((Activity) mParams.mContext, ContextCompat.getColor(mParams.mContext, R.color.tran));
        }
        //以滚动布局中的子布局下的第一个控件作为目标，让出一个导航栏的高度，其余控件跟随这个控件一起移动
        ViewGroup firstLayout = (ViewGroup) scrollView.getChildAt(0);
        firstLayout.getChildAt(0).setPadding(scrollView.getPaddingLeft(),scrollView.getTop()+getMeasureHeight(),scrollView.getPaddingRight(),scrollView.getPaddingBottom());

        scrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, i, y, i2, i3) -> {
            int alpha = (int) (1f*y/getHeight()*255);//除法运算有小数，乘以1f是为了保留小数，从而提高精度
            int rgb = UserCache.isDayNight() ? 0 : 255;
            mNavigationView.setBackgroundColor(Color.argb(Math.min(alpha, 255),rgb,rgb,rgb));
        });
    }

    /**设置背景随指定控件滚动距离变化透明度
     * 指定控件往下滚动时，不出现自身与指定控件叠加在一起的情况
     * @param appBarLayout
     */
    public void setFollowScrollBackground(AppBarLayout appBarLayout){
        if(mParams.mContext instanceof Activity){
            //设置状态栏背景颜色透明
            StatusBarHelper.setStatusBarColor((Activity) mParams.mContext, ContextCompat.getColor(mParams.mContext, R.color.tran));
        }
        appBarLayout.setPadding(appBarLayout.getPaddingLeft(),appBarLayout.getTop()+getMeasureHeight(),appBarLayout.getPaddingRight(),appBarLayout.getPaddingBottom());
        appBarLayout.addOnOffsetChangedListener((appBarLayout1, y) -> {
            int alpha = (int) (-1f*y/getHeight()*255);//除法运算有小数，乘以1f是为了保留小数，从而提高精度
            int rgb = UserCache.isDayNight() ? 0 : 255;
            mNavigationView.setBackgroundColor(Color.argb(Math.min(alpha, 255),rgb,rgb,rgb));
        });
    }

    public abstract static class Builder {

        AbsNavigationParams P;

        public Builder(Context context, ViewGroup parent) {
            P = new AbsNavigationParams(context, parent);
        }

        public abstract AbsNavigationBar builder();

        public static class AbsNavigationParams {

            public Context mContext;
            public ViewGroup mParent;

            public AbsNavigationParams(Context context, ViewGroup parent) {
                this.mContext = context;
                this.mParent = parent;
            }
        }
    }
}
