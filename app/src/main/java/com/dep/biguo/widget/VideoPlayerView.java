package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.databinding.VideoPlayViewBinding;
import com.dep.biguo.utils.image.ImageLoader;
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder;
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack;
import com.shuyu.gsyvideoplayer.utils.CommonUtil;
import com.shuyu.gsyvideoplayer.utils.OrientationUtils;
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView;

public class VideoPlayerView extends FrameLayout implements LifecycleObserver, ViewTreeObserver.OnPreDrawListener {
    public static VideoPlayerView videoPlayerView;

    private AppCompatActivity activity;
    private VideoPlayViewBinding binding;
    private ImageView thumbImageView;
    private int systemUiVisibility;
    private boolean isFullScreen;
    private boolean isBackgroundRun;

    private String videoUrl;
    private OnPlayEndListener onPlayEndListener;
    private OrientationUtils orientationUtils;

    //GSYVideoADManager.backFromWindowFull(context)不起作用，只能根据这个方法backFromWindowFull里的代码，手动寻路调用
    public static boolean quitFullscreen(Context context){
        if(videoPlayerView != null && videoPlayerView.isFullScreen){
            //设置状态栏
            CommonUtil.hideNavKey(context);
            //退出全屏
            videoPlayerView.binding.videoPlayerView.onBackFullscreen();
            return true;
        }
        return false;
    }

    public VideoPlayerView(@NonNull Context context) {
        super(context);
        createVideoPlayer();
    }

    public VideoPlayerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        createVideoPlayer();
    }

    public VideoPlayerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        createVideoPlayer();
    }

    private void createVideoPlayer(){
        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.video_play_view, this, true);
        //监听播放器是否在可见范围内，不在可见范围就暂停播放
        binding.getRoot().getViewTreeObserver().addOnPreDrawListener(this);

        //封面图
        thumbImageView = new ImageView(getContext());
        thumbImageView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        //不要返回按钮
        binding.videoPlayerView.getBackButton().setImageDrawable(null);
        //错误
        binding.errorLayout.setOnClickListener(v -> {
            binding.errorLayout.setVisibility(GONE);
            binding.videoPlayerView.onVideoResume();
            play();
        });

        new GSYVideoOptionBuilder()
                .setThumbImageView(thumbImageView)//添加封面图
                .setIsTouchWiget(true)//滑动界面改变进度
                .setRotateViewAuto(false)//自动旋转
                .setRotateWithSystem(false)//屏幕跟随系统方向
                .setShowFullAnimation(true)//使用全屏动画效果
                .setHideKey(false)//隐藏虚拟按键
                .setCacheWithPlay(true)//缓存
                .setVideoAllCallBack(new GSYSampleCallBack(){
                    @Override
                    public void onAutoComplete(String url, Object... objects) {
                        super.onAutoComplete(url, objects);
                        if(onPlayEndListener != null){
                            onPlayEndListener.onPlayEnd();
                        }
                    }

                    @Override
                    public void onEnterFullscreen(String url, Object... objects) {
                        super.onEnterFullscreen(url, objects);
                        videoPlayerView = VideoPlayerView.this;
                    }

                    @Override
                    public void onQuitFullscreen(String url, Object... objects) {
                        super.onQuitFullscreen(url, objects);
                        orientationUtils.backToProtVideo();
                        activity.getWindow().getDecorView().setSystemUiVisibility(systemUiVisibility);
                        isFullScreen = false;
                    }

                    @Override
                    public void onPlayError(String url, Object... objects) {
                        //播放出错，显示错误控件
                        binding.errorLayout.setVisibility(VISIBLE);
                    }
                })
                .build(binding.videoPlayerView);

        binding.videoPlayerView.getFullscreenButton().setOnClickListener(v -> {
            isFullScreen = true;
            orientationUtils.resolveByClick();
            systemUiVisibility = activity.getWindow().getDecorView().getSystemUiVisibility();
            binding.videoPlayerView.startWindowFullscreen(activity, true, true);
        });
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        //播放地址为空的时候，拦截掉点击事件向下传递
        return AppUtil.isEmpty(videoUrl);
    }

    @Override
    public boolean onPreDraw() {
        //监听播放器是否在可见范围内，不在可见范围就暂停播放
        Rect globalVisibleRect = new Rect();
        boolean isVisible = getGlobalVisibleRect(globalVisibleRect);
        //不使用binding.videoPlayerView.isIfCurrentIsFullscreen(),是因为屏幕旋转结束才会改变值，太迟了，
        //所以在点击全屏按钮时做，用变量记录下来
        if (isVisible && !isFullScreen) {
            // 获取控件的总面积
            int totalArea = getMeasuredWidth() * getMeasuredHeight();
            // 获取可见区域的面积
            int visibleArea = globalVisibleRect.width() * globalVisibleRect.height();
            // 计算可见面积的比例
            float visiblePercentage = (float) visibleArea / totalArea;
            // 控件的可见面积小于 50%
            if (visiblePercentage < 0.5) {
                binding.videoPlayerView.release();
            }
        }
        return true; // 返回 true 以继续绘制
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    public void onResume() {
        if(isBackgroundRun) {
            binding.videoPlayerView.getCurrentPlayer().onVideoResume();
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    public void onPause() {
        // Activity 暂停时，视图的操作
        if(binding.videoPlayerView.getCurrentState() == GSYVideoView.CURRENT_STATE_PLAYING){
            isBackgroundRun = true;
        }
        binding.videoPlayerView.getCurrentPlayer().onVideoPause();
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void onDestroy() {
        // Activity 销毁时，视图的操作
        orientationUtils.backToProtVideo();
        binding.videoPlayerView.getCurrentPlayer().release();
        videoPlayerView = null;
        activity.getLifecycle().removeObserver(this);
        activity = null;
    }

    public VideoPlayerView bindActivityLifecycle(AppCompatActivity activity){
        this.activity = activity;
        //外部辅助的旋转，帮助全屏
        orientationUtils = new OrientationUtils(activity, binding.videoPlayerView);
        //初始化不打开外部的旋转
        orientationUtils.setEnable(false);
        //外部辅助的旋转，帮助全屏
        activity.getLifecycle().addObserver(this);
        return this;
    }

    public VideoPlayerView setThumbImageUrl(String thumbImageUrl) {
        ImageLoader.loadImage(thumbImageView, thumbImageUrl, Color.WHITE);
        return this;
    }

    public VideoPlayerView setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
        binding.videoPlayerView.setUp(videoUrl, true, "");
        setPlayButtonVisibility(true);
        return this;
    }

    public VideoPlayerView setPlayEndListener(OnPlayEndListener listener){
        this.onPlayEndListener = listener;
        return this;
    }

    public VideoPlayerView setShowEmptyUrl(){
        binding.errorLayout.setVisibility(VISIBLE);
        binding.errorMessage.setText("播放地址不存在");
        setPlayButtonVisibility(false);
        return this;
    }

    public VideoPlayerView setPlayButtonVisibility(boolean isVisibility){
        binding.videoPlayerView.getStartButton().setAlpha(isVisibility ? 1 : 0);
        return this;
    }

    public VideoPlayerView play(){
        if(!AppUtil.isEmpty(this.videoUrl)){
            //当重新设置地址时，就隐藏错误页面
            binding.errorLayout.setVisibility(GONE);
            binding.videoPlayerView.startPlayLogic();
        }else {
            setShowEmptyUrl();
        }
        return this;
    }

    public interface OnPlayEndListener{
        void onPlayEnd();
    }
}
