package com.dep.biguo.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ScrollView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.utils.PracticeHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/6
 * @Description:
 */
public class SearchFilterView extends FrameLayout {

    private Context mContext;

    //筛选布局
    private View mFilterView;

    //阴影布局
    private View mShadowView;

    //滚动
    private NestedScrollView scrollView;

    //课程代码
    private RecyclerView rvCode;
    private CodeAdapter mCodeAdapter;
    //题型
    private RecyclerView rvType;
    private TypeAdapter mTypeAdapter;

    private int mFilterHeight; //筛选框高度

    //筛选数据
    private Map<String, List<Integer>> mSearchFilterData;
    //题型最多的
    private List<Integer> mFilterTypeData;
    private Map<Integer, String> mFilterTypeNameData;

    private boolean mIsOpen = false; //是否开启

    private OnFinishClickListener mOnFinishClickListener;

    public SearchFilterView(@NonNull Context context) {
        this(context, null);
    }

    public SearchFilterView(@NonNull Context context, @NonNull AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SearchFilterView(@NonNull Context context, @NonNull AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;

        initLayout();
        initCode();
        initType();
    }

    private void initLayout() {
        mShadowView = new View(mContext);
        mShadowView.setBackgroundColor(0x88888888);
        addView(mShadowView);
        mShadowView.setVisibility(GONE);
        mShadowView.setOnClickListener
                (v -> closeFilter());

        mFilterView = LayoutInflater.from(mContext).inflate(R.layout.search_filter_layout, null);
        addView(mFilterView);
        mFilterView.setVisibility(GONE);

        scrollView = mFilterView.findViewById(R.id.scrollView);
        rvCode = mFilterView.findViewById(R.id.rvCode);
        rvType = mFilterView.findViewById(R.id.rvType);

        mFilterView.findViewById(R.id.tvReset).setOnClickListener(v -> {
            mCodeAdapter.clearPosition();
            mTypeAdapter.clearPosition();
        });

        mFilterView.findViewById(R.id.tvFinish).setOnClickListener(v -> {
            if (mOnFinishClickListener != null) {
                mOnFinishClickListener.onFinish(mCodeAdapter.getCode(), mTypeAdapter.getType(), mTypeAdapter.getTypeName());
            }
            closeFilter();
        });
    }

    private void initCode() {
        mCodeAdapter = new CodeAdapter(new ArrayList<>());
        mCodeAdapter.bindToRecyclerView(rvCode);
        rvCode.setLayoutManager(new GridLayoutManager(mContext, 4));
    }

    private void initType() {
        mTypeAdapter = new TypeAdapter(new ArrayList<>());
        mTypeAdapter.bindToRecyclerView(rvType);
        rvType.setLayoutManager(new GridLayoutManager(mContext, 4));
    }

    public void setOnFinishClickListener(OnFinishClickListener onFinishClickListener) {
        this.mOnFinishClickListener = onFinishClickListener;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mFilterHeight = MeasureSpec.getSize(heightMeasureSpec) * 75 / 100;
        if (mFilterHeight != 0 && mFilterView.getHeight() <= 0) {
            // 高度占整个父View的75%
            mFilterView.getLayoutParams().height = mFilterHeight;
        }
    }

    //开启筛选
    public void openFilter() {
        if (mSearchFilterData == null) return;
        //数据改变才做变化
//        if (mCodeAdapter.getItemCount() != mSearchFilterData.size()) {
        mCodeAdapter.setNewData(new ArrayList<>(mSearchFilterData.keySet()));
        mTypeAdapter.setNewData(mFilterTypeData);
        mTypeAdapter.setNewTypeNameMap(mFilterTypeNameData);
        mCodeAdapter.clearPosition();
        mTypeAdapter.clearPosition();
//        }
        if (mIsOpen) {
            closeFilter();
        } else {
            startOpenAnimator();
        }
    }

    //关闭筛选
    public void closeFilter() {
        startCloseAnimator();
    }

    //开启动画执行
    private void startOpenAnimator() {
        ObjectAnimator animatorFilter = ObjectAnimator.ofFloat(mFilterView, "translationY", -mFilterHeight, 0);
        ObjectAnimator animatorShadow = ObjectAnimator.ofFloat(mShadowView, "alpha", 0.5f, 1);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(animatorFilter, animatorShadow);
        animatorSet.setDuration(500);

        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                mIsOpen = true;
                mShadowView.setVisibility(VISIBLE);
                mFilterView.setVisibility(VISIBLE);
            }
        });

        animatorSet.start();
    }

    //关闭动画执行
    private void startCloseAnimator() {
        ObjectAnimator animatorFilter = ObjectAnimator.ofFloat(mFilterView, "translationY", 0, -mFilterHeight);
        ObjectAnimator animatorShadow = ObjectAnimator.ofFloat(mShadowView, "alpha", 1, 0.5f);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(animatorFilter, animatorShadow);
        animatorSet.setDuration(500);

        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                mIsOpen = false;
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mShadowView.setVisibility(GONE);
                mFilterView.setVisibility(GONE);
            }
        });

        animatorSet.start();
    }

    /**
     * 开启筛选之前，先设置数据
     *
     * @param data
     */
    public void setData(Map<String, List<Integer>> data) {
        this.mSearchFilterData = data;
    }

    public void setTypeData(List<Integer> data) {
        this.mFilterTypeData = data;
    }

    public void setTypeNameData(Map<Integer, String> data) {
        this.mFilterTypeNameData = data;
    }

    public boolean isOpen() {
        return mIsOpen;
    }

    private class CodeAdapter extends CommonAdapter<String> {

        private int mSelectPosition = -1;

        public CodeAdapter(@Nullable List<String> data) {
            super(R.layout.search_filter_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, String item) {
            holder.setText(R.id.tvLabel, item);

            holder.setBackgroundRes(R.id.tvLabel, mSelectPosition == holder.getLayoutPosition() ? R.drawable.bg_round_200_blue_light : R.drawable.bg_round_200_bgc);
            holder.setTextColor(R.id.tvLabel, ContextCompat.getColor(mContext, mSelectPosition == holder.getLayoutPosition() ? R.color.blue : R.color.tblack));

            holder.getView(R.id.tvLabel).setOnClickListener(v -> {
                //取消选中
                mTypeAdapter.clearPosition();

                if (mSelectPosition == holder.getLayoutPosition()) {
                    mSelectPosition = -1;
                    mTypeAdapter.setNewData(mFilterTypeData);
                    mTypeAdapter.setNewTypeNameMap(mFilterTypeNameData);
                } else {
                    scrollView.fullScroll(ScrollView.FOCUS_DOWN);
                    mSelectPosition = holder.getLayoutPosition();
                    mTypeAdapter.setNewData(mSearchFilterData.get(item));
                }

                notifyDataSetChanged();
            });
        }

        public void clearPosition() {
            this.mSelectPosition = -1;
            notifyDataSetChanged();
        }

        public String getCode() {
            if (mSelectPosition == -1) return "";
            return getItem(mSelectPosition);
        }
    }

    private class TypeAdapter extends CommonAdapter<Integer> {

        private int mSelectPosition = -1;
        private Map<Integer, String> typeNameMap = new HashMap<>();

        public void setNewTypeNameMap(Map<Integer, String> typeNameMap) {
            this.typeNameMap = typeNameMap;
        }

        public TypeAdapter(@Nullable List<Integer> data) {
            super(R.layout.search_filter_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, Integer item) {
            holder.setText(R.id.tvLabel, typeNameMap.get(item));
            //holder.setText(R.id.tvLabel, PracticeHelper.getTopicType(item));

            holder.setBackgroundRes(R.id.tvLabel, mSelectPosition == holder.getLayoutPosition() ? R.drawable.bg_round_200_blue_light : R.drawable.bg_round_200_bgc);
            holder.setTextColor(R.id.tvLabel, ContextCompat.getColor(mContext, mSelectPosition == holder.getLayoutPosition() ? R.color.blue : R.color.tblack));

            holder.getView(R.id.tvLabel).setOnClickListener(v -> {
                //取消选中
                if (mSelectPosition == holder.getLayoutPosition()) {
                    mSelectPosition = -1;
                    notifyDataSetChanged();
                } else {
                    mSelectPosition = holder.getLayoutPosition();
                    notifyDataSetChanged();
                }
            });
        }

        public void clearPosition() {
            this.mSelectPosition = -1;
            notifyDataSetChanged();
        }

        public int getType() {
            if (mSelectPosition == -1) return 0;
            return getItem(mSelectPosition);
        }

        public String getTypeName() {
            if (mSelectPosition == -1) return "";
            return typeNameMap.get(getItem(mSelectPosition));
        }
    }

    public interface OnFinishClickListener {
        void onFinish(String code, int type, String typeName);
    }

}
