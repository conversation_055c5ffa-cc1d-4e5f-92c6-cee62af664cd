package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.dep.biguo.databinding.TableItemViewBinding;
import com.dep.biguo.utils.imp.TextWatcherImp;

public class TableItemView extends LinearLayout {
    public static final int INPUT = 0;
    public static final int DOWN = 1;
    public static final int SELECT = 2;
    public static final int FILL = 3;

    private TableItemViewBinding binding;

    private CharSequence title;
    private CharSequence hint;
    private int type;
    private CharSequence value;
    private CharSequence selectText;
    public TableItemView(Context context) {
        super(context);
        init(context, null, 0);
    }

    public TableItemView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public TableItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, @Nullable AttributeSet attrs, int defStyleAttr){
        if(attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.TableItemView, defStyleAttr, 0);

            title = a.getString(R.styleable.TableItemView_title);
            hint = a.getString(R.styleable.TableItemView_hintText);
            type = a.getInt(R.styleable.TableItemView_type, 0);
            value = a.getString(R.styleable.TableItemView_value);
            selectText = a.getString(R.styleable.TableItemView_selectText);
            a.recycle();
        }

        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.table_item_view, this, true);
        binding.titleView.setText(title);
        if(type == INPUT){
            inputMode();
        }else if(type == DOWN){
            downMode();
        }else if(type == SELECT){
            selectMode();
        }else if(type == FILL){
            fillMode();
        }
    }

    private void inputMode(){
        binding.textView.setVisibility(VISIBLE);
        binding.textView.setVisibility(GONE);

        binding.downView.setVisibility(GONE);
        binding.selectView.setVisibility(GONE);
        binding.clearView.setVisibility(!TextUtils.isEmpty(value) ? VISIBLE : GONE);

        binding.editView.setText(value);
        binding.editView.setHint(hint);
        binding.editView.addTextChangedListener(new TextWatcherImp() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                super.onTextChanged(s, start, before, count);
                binding.clearView.setVisibility(s.length() > 0 ? VISIBLE : GONE);
            }
        });
        binding.clearView.setOnClickListener(v -> binding.editView.setText(""));
    }

    private void downMode(){
        binding.textView.setVisibility(GONE);
        binding.textView.setVisibility(VISIBLE);

        binding.downView.setVisibility(VISIBLE);
        binding.selectView.setVisibility(GONE);
        binding.clearView.setVisibility(GONE);

        binding.textView.setText(value);
        binding.textView.setHint(hint);
    }

    private void selectMode(){
        binding.textView.setVisibility(GONE);
        binding.textView.setVisibility(VISIBLE);

        binding.downView.setVisibility(GONE);
        binding.selectView.setVisibility(VISIBLE);
        binding.clearView.setVisibility(GONE);

        binding.textView.setText(value);
        binding.textView.setHint(hint);

        binding.selectView.setText(selectText);
    }

    private void fillMode(){
        binding.textView.setVisibility(GONE);
        binding.textView.setVisibility(VISIBLE);

        binding.downView.setVisibility(GONE);
        binding.selectView.setVisibility(GONE);
        binding.clearView.setVisibility(GONE);

        binding.textView.setText(value);
        binding.textView.setGravity(Gravity.CENTER_VERTICAL|Gravity.END);
    }

    public TableItemView setTitle(CharSequence title) {
        this.title = title;
        return this;
    }

    public TableItemView setHint(CharSequence hint) {
        this.hint = hint;
        if(type == INPUT){
            binding.editView.setHint(hint);
        }else {
            binding.textView.setHint(hint);
        }
        return this;
    }

    public TableItemView setValue(CharSequence value) {
        this.value = value;
        if(type == INPUT){
            binding.editView.setText(value);
        }else {
            binding.textView.setText(value);
        }
        return this;
    }

    public TableItemView setSelectText(CharSequence selectText){
        if(type == SELECT){
            binding.selectView.setText(selectText);
        }
        return this;
    }

    public String getValue(){
        if(type == INPUT){
            return binding.editView.getText().toString();
        }else {
            return binding.textView.getText().toString();
        }
    }

}
