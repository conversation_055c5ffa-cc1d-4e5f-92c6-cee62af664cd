package com.dep.biguo.widget.pagemenu;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class PageMenuLayout extends RecyclerView {
    private LinearLayoutManager manager;
    private int previousPage = 0;
    private int pageCount;

    private int rowSpace;
    private OnPageListener onPageListener;

    public PageMenuLayout(@NonNull Context context) {
        super(context);
        init();
    }

    public PageMenuLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PageMenuLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init(){
        //设置RecyclerView每次只滚动一个Item
        PagerSnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(this);

        manager = new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false);
        setLayoutManager(manager);
    }

    public <T> void  setHolderCreator(List<T> list, int spanCount, int rowCount, int rowSpace, PageMenuViewHolderCreator<T> holderCreator) {
        PageMenuAdapter<T> menuAdapter = new PageMenuAdapter<>();
        setAdapter(menuAdapter);
        menuAdapter.setRowSpace(rowSpace);
        menuAdapter.setList(spanCount, rowCount, list);
        menuAdapter.setHolderCreator(holderCreator);
        this.pageCount = menuAdapter.getItemCount();
    }

    public void setOnPageListener(OnPageListener onPageListener) {
        this.onPageListener = onPageListener;
        addOnScrollListener(new RecyclerView.OnScrollListener(){
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                // 获取当前显示的第一个和最后一个可见项的索引
                int firstVisibleItem = manager.findFirstVisibleItemPosition();

                // 判断是否翻页
                if (firstVisibleItem != previousPage) {
                    // 如果页码发生变化，打印新的页码
                    previousPage = firstVisibleItem;
                    if(PageMenuLayout.this.onPageListener != null){
                        PageMenuLayout.this.onPageListener.onPageSelected(previousPage);
                    }
                }
            }
        });
    }

    public int getCurrentPage(){
        return previousPage;
    }

    public int getPageCount(){
        return pageCount;
    }

    public interface OnPageListener{
        void onPageSelected(int position);
    }
}
