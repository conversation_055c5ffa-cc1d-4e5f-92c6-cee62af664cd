package com.dep.biguo.widget.flow;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

public class FlowLayout extends ViewGroup {

    private List<List<View>> mLineViews;
    private List<Integer> mLineHeights;

    private FlowBaseAdapter mAdapter;

    public FlowLayout(Context context) {
        this(context, null);
    }

    public FlowLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FlowLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mLineViews = new ArrayList<>();
        mLineHeights = new ArrayList<>();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //onMeasure会执行多次
        mLineViews.clear();
        mLineHeights.clear();

        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        int realWidth = 0;
        int realHeight = 0;

        //match_parent 或 精确值
        if (widthMode == MeasureSpec.EXACTLY && heightMode == MeasureSpec.EXACTLY) {
            realWidth = widthSize;
            realHeight = heightSize;
        }
        //wrap_content
        else {
            int lineWidth = 0; //当前行宽
            int lineHeight = 0; //当前行高
            List<View> lineViews = new ArrayList<>(); //用于存储每一行的View集合

            measureChildren(widthMeasureSpec, heightMeasureSpec);

            for (int i = 0; i < getChildCount(); i++) {
                View childView = getChildAt(i);

                MarginLayoutParams params = (MarginLayoutParams) childView.getLayoutParams();

                int childWidth = childView.getMeasuredWidth() + params.leftMargin + params.rightMargin;
                int childHeight = childView.getMeasuredHeight() + params.topMargin + params.bottomMargin;

                //当前行宽大于父布局宽度
                if (lineWidth + childWidth > widthSize) {
                    realWidth = Math.max(lineWidth, widthSize);
                    realHeight += lineHeight;
                    mLineHeights.add(lineHeight); //添加当前行高，用于排列
                    mLineViews.add(lineViews); //添加当前行所有View，用于排列

                    lineViews = new ArrayList<>(); //重新初始化
                    lineViews.add(childView);
                    lineWidth = childWidth;
                } else {
                    lineWidth += childWidth; //宽度累加
                    realWidth = Math.max(realWidth, lineWidth); // 这里赋值一次，防止当行数只有一行时
                    lineHeight = Math.max(lineHeight, childHeight); //高度取最大值
                    lineViews.add(childView); //添加进当前行的View集合
                }

                //最后一个View时，宽度并未大于父布局
                if (i == getChildCount() - 1) {
                    mLineViews.add(lineViews);
                    realWidth = Math.max(childWidth, realWidth);
                    realHeight += childHeight;
                    mLineHeights.add(childHeight);
                }
            }
        }

        setMeasuredDimension(realWidth, realHeight);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        int left = 0; //开始的位置
        int top = 0;

        int lineCount = mLineViews.size(); //共几行

        for (int i = 0; i < lineCount; i++) {
            int lineHeight = mLineHeights.get(i); //行高
            List<View> views = mLineViews.get(i); //每行的View

            for (int j = 0; j < views.size(); j++) {
                View view = views.get(j);
                MarginLayoutParams params = (MarginLayoutParams) view.getLayoutParams();
                int vl = left + params.leftMargin;
                int vt = top + params.topMargin;
                int vr = vl + view.getMeasuredWidth();
                int vb = vt + view.getMeasuredHeight();
                view.layout(vl, vt, vr, vb);
                left += view.getMeasuredWidth() + params.leftMargin + params.rightMargin;
            }
            left = 0;
            top += lineHeight;
        }
    }

    @Override
    public LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new MarginLayoutParams(getContext(), attrs);
    }

    public void setAdapter(FlowBaseAdapter adapter) {
        if (adapter == null) {
            throw new NullPointerException("FlowLayout adapter cann't Null!");
        }

        removeAllViews();

        mAdapter = adapter;

        for (int i = 0; i < mAdapter.getItemCount(); i++) {
            addView(adapter.getItemView(this, i));
        }
        requestLayout();
    }

    public FlowBaseAdapter getAdapter() {
        return mAdapter;
    }
}
