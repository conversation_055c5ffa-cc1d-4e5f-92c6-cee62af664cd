package com.dep.biguo.widget;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.MotionEvent;

public class HorizontalRecyclerView extends RecyclerView {
    private float downX;
    private float downY;

    public HorizontalRecyclerView(@NonNull Context context) {
        super(context);
    }

    public HorizontalRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public HorizontalRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        float removingX;//垂直方向移动距离
        float removingY;//水平方向移动距离

        switch (ev.getAction()){
            case MotionEvent.ACTION_DOWN:
                downX=ev.getX();
                downY=ev.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                removingX = downX - ev.getX();
                removingY = downY - ev.getY();
                //第一次移动触点，且移动的距离大于10像素
                if(Math.sqrt(removingX*removingX + removingY*removingY) > 10f){
                    /*根据子控件是否拦截，以及移动方向(触点移动，X坐标可能变化，根据垂直方向距离与水平方向距离判断)判断是否拦截事件，
                    之后所有事件都按照本次判断结果处理*/
                    return Math.abs(removingY) < Math.abs(removingX) - 40;
                }
                break;
            case MotionEvent.ACTION_UP:
                //手指抬起后，清除本次触摸时间的拦截结果
        }
        return super.onInterceptTouchEvent(ev);
    }
}
