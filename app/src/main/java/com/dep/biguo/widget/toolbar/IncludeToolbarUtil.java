package com.dep.biguo.widget.toolbar;

import android.app.Activity;
import android.view.View;

import androidx.fragment.app.Fragment;

public class IncludeToolbarUtil extends ToolbarUtil<IncludeToolbarUtil>{
    public IncludeToolbarUtil(Activity activity) {
        super(activity);
    }

    public IncludeToolbarUtil(Fragment fragment, View fragmentRootView) {
        super(fragment, fragmentRootView);
    }

    /**引入的标题布局
     * @param includeId 引入的标题布局ID
     */
    public IncludeToolbarUtil setIncludeId(int includeId) {
        this.includeId = includeId;
        immerseLayout.setInclude(includeId);
        return this;
    }

    /**获取引入的标题布局
     * @return 引入的标题布局
     */
    public View getIncludeView(){
        return immerseLayout.getInclude();
    }
}
