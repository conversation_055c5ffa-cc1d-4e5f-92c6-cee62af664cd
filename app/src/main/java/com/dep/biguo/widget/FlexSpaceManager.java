package com.dep.biguo.widget;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.flexbox.FlexboxLayoutManager;

public class FlexSpaceManager extends RecyclerView.LayoutManager {
    //计算得到的变量
    private int lastWidth;
    private int minHorizontalMargin;
    private int minVerticalMargin;
    private int itemWidth;
    private int lineCount;

    //可以设置的变量
    private int spaceHorizontal;
    private int spaceVertical;
    private int itemStandardWidth;
    private int itemStandardHeight;
    private boolean isKeepScale;

    @Override
    public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        return new FlexboxLayoutManager.LayoutParams(-2, -2);
    }
    /**
     * 计算每个item的实际宽度，并记录一行可以放多少个item
     */
    private void initComputer(int width) {
        //int width = getWidth() - getPaddingStart() - getPaddingEnd();
        //if(lastWidth == width) return;
        if (itemStandardWidth == 0) return;

        //当获取的宽度不等于上一次获取到的宽度时，就重新进行计算,降低计算的次数
        lastWidth = width;

        //计算出在标准宽度下，最多一行最多可以放几个item
        lineCount = Math.max(1, width / itemStandardWidth);
        //水平方向的间距的数量
        int spaceCount = lineCount - 1;
        //裁掉每个item之间必须占用的间距之后，实际可用的总宽度
        int realWidth = width - (spaceCount * spaceHorizontal);
        //计算出每个item实际要占用的宽度
        itemWidth = realWidth / lineCount;
        //itemWidth = Math.min(itemStandardWidth, itemWidth);

        //当间距个数为0时，就代表没有间距，所以直接设置为0
        if (spaceCount == 0) {
            minHorizontalMargin = spaceHorizontal;
        } else {
            //预设最小边距不足以填充剩余的空间，就把剩余的空间平均分配到每个边距上
            minHorizontalMargin = Math.max(spaceHorizontal, (width - itemWidth * lineCount ) / spaceCount);
        }

        //minHorizontalMargin = spaceHorizontal;
        minVerticalMargin = spaceVertical;
    }

    @Override
    public void measureChildWithMargins(@NonNull View child, int widthUsed, int heightUsed) {
        super.measureChildWithMargins(child, widthUsed, heightUsed);
        if(itemStandardWidth == 0){
            itemStandardHeight = getDecoratedMeasuredWidth(child);
        }
    }
}
