package com.dep.biguo.widget.toolbar;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.R;
import com.dep.biguo.utils.image.TextDrawableLoader;

public class NormalToolbarUtil extends ToolbarUtil<NormalToolbarUtil>{
    private TextView leftView;
    private TextView centerView;
    private TextView rightView;

    public NormalToolbarUtil(Activity activity) {
        super(activity);
        immerseLayout.setInclude(R.layout.normal_toolbar_layout);
        leftView = immerseLayout.getInclude().findViewById(R.id.leftView);
        centerView = immerseLayout.getInclude().findViewById(R.id.titleView);
        rightView = immerseLayout.getInclude().findViewById(R.id.rightView);

        //在activity中，默认左边控件时返回按钮
        setLeftOnClickListener(view -> activity.finish());
    }

    public NormalToolbarUtil(Fragment fragment, View fragmentRootView) {
        super(fragment, fragmentRootView);
        immerseLayout.setInclude(R.layout.normal_toolbar_layout);
        leftView = immerseLayout.getInclude().findViewById(R.id.leftView);
        centerView = immerseLayout.getInclude().findViewById(R.id.titleView);
        rightView = immerseLayout.getInclude().findViewById(R.id.rightView);
    }

    /**<P>适用普通界面可以滚动的情况，有沉浸于toolbar底部的情况，请参考{@link #alphaToWhite(float)}，
     * 若不满足要求，请重写一个方法实现</P>
     *
     * <P>当向下滚动之后，右边图标、标题、左边图标三者朝同一颜色变化</P>
     *
     * <P>正常模式：左边图标从白色变为黑色，标题和右边文字 从透明文字变成 白色文字；</P>
     * <P>深色模式：左边图标不变化，标题和右边文字 从透明文字变成 黑色文字</P>
     * @param changeRate 变化率
     */
    public void whiteToBlack(float changeRate){
        //计算图标的颜色，深色模式,图标只需要白色
        int iconRgb = Math.max(102, 255 - (int) ((isDayNight() ? 0 : changeRate) * 255));
        int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

        //计算图标的颜色，深色模式,文字只需要白色
        int textRgb = Math.max(51, 255 - (int) ((isDayNight() ? 0 : changeRate) * 255));
        int textColor = Color.rgb(textRgb, textRgb, textRgb);

        setIconAndTextColor(iconColor, textColor, iconColor);
    }

    /**<P>适用于背景图片沉浸到toolbar底部，且界面可以滚动的情况</P>
     * <P>当向下滚动之后，toolbar背景变为不透明，标题栏浮现，左边图标与标题朝同一颜色变化</P>
     *
     * <P>正常模式：左边图标从白色变为黑色，标题和右边文字【从透明文字】变成【白色文字】</P>
     * <P>深色模式：左边图标不变化，标题和右边文字【从透明文字】变成【黑色文字】</P>
     *
     * @param changeRate 变化率
     */
    public void alphaToWhite(float changeRate){
        //计算图标的颜色，深色模式,图标只需要白色
        int iconRgb = Math.max(102, 255 - (int) ((isDayNight() ? 0 : changeRate) * 255));
        int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

        //计算文字的颜色，深色模式,文字只需要白色
        int textAlpha = Math.max(0, (int) (changeRate * 255));
        int textRgb = Math.max(51, 255 - (int) ((isDayNight() ? 0 : changeRate) * 255));
        int textColor = Color.argb(textAlpha, textRgb, textRgb, textRgb);

        setIconAndTextColor(iconColor, textColor, iconColor);
    }

    /**给标题栏的图标和文字着色
     * @param leftColor        左边控件的文字颜色和图标颜色
     * @param centerTextColor  标题文字颜色
     * @param rightColor   右边控件的文字颜色和图标颜色
     * @return
     */
    public NormalToolbarUtil setIconAndTextColor(int leftColor, int centerTextColor, int rightColor){
        TintDrawableUtil.StartTintDrawable(leftView, leftColor);
        leftView.setTextColor(leftColor);
        centerView.setTextColor(centerTextColor);
        rightView.setTextColor(rightColor);
        TintDrawableUtil.EndTintDrawable(rightView, rightColor);
        return this;
    }

    public NormalToolbarUtil setLeftText(CharSequence leftText){
        leftView.setText(leftText);
        return this;
    }

    public NormalToolbarUtil setLeftDrawableRes(int leftDrawableRes) {
        if(leftDrawableRes == -1){
            leftView.setCompoundDrawablesRelative(null, null, null, null);
        }else {
            TextDrawableLoader.loadLeft(leftView.getContext(), leftView, leftDrawableRes);
        }
        return this;
    }


    public NormalToolbarUtil setLeftBackground(Drawable drawable){
        leftView.setBackground(drawable);
        return this;
    }

    public NormalToolbarUtil setLeftOnClickListener(View.OnClickListener leftOnClickListener) {
        leftView.setOnClickListener(leftOnClickListener);
        return this;
    }

    public NormalToolbarUtil setLeftVisibility(int leftVisibility) {
        leftView.setVisibility(leftVisibility);
        return this;
    }

    public NormalToolbarUtil setLeftPadding(int left, int top, int right, int bottom){
        leftView.setPadding(left, top, right, bottom);
        return this;
    }

    public NormalToolbarUtil setLeftMargin(int left, int top, int right, int bottom){
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) leftView.getLayoutParams();
        marginLayoutParams.setMargins(left, top, right, bottom);
        return this;
    }

    public NormalToolbarUtil setCenterText(CharSequence centerText) {
        centerView.setText(centerText);
        return this;
    }

    public NormalToolbarUtil setCenterDrawableRes(int leftDrawableRes) {
        if(leftDrawableRes == -1){
            centerView.setCompoundDrawablesRelative(null, null, null, null);
        }else {
            TextDrawableLoader.loadRight(centerView.getContext(), centerView, leftDrawableRes);
        }
        return this;
    }

    public NormalToolbarUtil setCenterTextSize(int centerTextSize) {
        centerView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, centerTextSize);
        return this;
    }

    public NormalToolbarUtil setCenterTextColor(int centerTextColor) {
        centerView.setTextColor(centerTextColor);
        return this;
    }

    public NormalToolbarUtil setCenterOnClickListener(View.OnClickListener centerOnClickListener) {
        centerView.setOnClickListener(centerOnClickListener);
        return this;
    }

    public NormalToolbarUtil setCenterVisibility(int centerVisibility) {
        centerView.setVisibility(centerVisibility);
        return this;
    }

    public NormalToolbarUtil setCenterPadding(int left, int top, int right, int bottom){
        centerView.setPadding(left, top, right, bottom);
        return this;
    }

    public NormalToolbarUtil setCenterMargin(int left, int top, int right, int bottom){
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) centerView.getLayoutParams();
        marginLayoutParams.setMargins(left, top, right, bottom);
        return this;
    }

    public NormalToolbarUtil setCenterBackground(Drawable drawable){
        centerView.setBackground(drawable);
        return this;
    }

    public NormalToolbarUtil setRightText(CharSequence rightText) {
        rightView.setText(rightText);
        return this;
    }

    public NormalToolbarUtil setRightTextSize(int rightTextSize) {
        rightView.setTextSize(rightTextSize);
        return this;
    }

    public NormalToolbarUtil setRightTextColor(int rightTextColor) {
        rightView.setTextColor(rightTextColor);
        return this;
    }

    public NormalToolbarUtil setRightDrawableRes(int rightDrawableRes) {
        if(rightDrawableRes == -1){
            rightView.setCompoundDrawablesRelative(null, null, null, null);
        }else {
            TextDrawableLoader.loadRight(rightView.getContext(), rightView, rightDrawableRes);
        }
        return this;
    }

    public NormalToolbarUtil setRightBackground(Drawable drawable){
        rightView.setBackground(drawable);
        return this;
    }

    public NormalToolbarUtil setRightDrawablePadding(int drawablePadding){
        rightView.setCompoundDrawablePadding(drawablePadding);
        return this;
    }

    public NormalToolbarUtil setRightOnClickListener(View.OnClickListener rightOnClickListener) {
        rightView.setOnClickListener(rightOnClickListener);
        return this;
    }

    public NormalToolbarUtil setRightVisibility(int rightVisibility) {
        rightView.setVisibility(rightVisibility);
        return this;
    }

    public NormalToolbarUtil setRightPadding(int left, int top, int right, int bottom){
        rightView.setPadding(left, top, right, bottom);
        return this;
    }

    public NormalToolbarUtil setRightMargin(int left, int top, int right, int bottom){
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) rightView.getLayoutParams();
        marginLayoutParams.setMargins(left, top, right, bottom);
        return this;
    }

    public TextView getLeftView() {
        return leftView;
    }

    public TextView getCenterView() {
        return centerView;
    }

    public TextView getRightView() {
        return rightView;
    }
}
