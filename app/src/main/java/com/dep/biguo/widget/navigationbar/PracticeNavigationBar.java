package com.dep.biguo.widget.navigationbar;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.appcompat.widget.Toolbar;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.PracticeHelper;

public class PracticeNavigationBar<D extends PracticeNavigationBar.Builder.DefaultNavigationParams> extends AbsNavigationBar<PracticeNavigationBar.Builder.DefaultNavigationParams> {

    public PracticeNavigationBar(D params) {
        super(params);
    }

    @Override
    public int bindLayoutId() {
        return R.layout.navigationbar_practice;
    }

    @Override
    public void applyView() {
        //绑定效果

        //标题可滚动
        setTextMarquee(findViewById(R.id.navigation_tv_title));

        //设置文字
        setText(R.id.navigation_tv_title, getParams().mTitle);
        setText(R.id.navigation_tv_left, getParams().mLeftText);
        setText(R.id.navigation_tv_right, getParams().mRightText);
        setText(R.id.navigation_tv_code, getParams().mCode);
        setText(R.id.navigation_tv_type, TextUtils.isEmpty(getParams().mSubTitle) ? PracticeHelper.getPracticeType(getParams().mPracticeType) : getParams().mSubTitle);

        //设置图标
        setIcon(R.id.navigation_iv_left, getParams().mLeftIcon);
        setIcon(R.id.navigation_iv_right, getParams().mRightIcon);

        //设置颜色
        setColor(R.id.navigation_tv_title, getParams().mTitleTextColor);
        setColor(R.id.navigation_tv_left, getParams().mLeftTextColor);
        setColor(R.id.navigation_tv_right, getParams().mRightTextColor);
        setColor(R.id.navigatioinBar, getParams().mBackgroundColor);

        //设置点击事件
        setOnClickListener(R.id.navigation_iv_left, getParams().mLeftClickListener);
        setOnClickListener(R.id.navigation_tv_left, getParams().mLeftClickListener);
        setOnClickListener(R.id.navigation_tv_right, getParams().mRightClickListener);
        setOnClickListener(R.id.navigation_iv_right, getParams().mRightClickListener);

        //设置层次
        setElevation(getParams().mElevation);

        //因为设置状态栏颜色是添加一个View到DecorView中
        Toolbar toolbar = getParams().mParent.findViewById(R.id.navigatioinBar);
        ViewGroup.LayoutParams params = toolbar.getLayoutParams();
        params.height += DisplayHelper.getStatusBarHeight(getParams().mContext);
        toolbar.setPadding(0, DisplayHelper.getStatusBarHeight(getParams().mContext), 0, 0);
        toolbar.setLayoutParams(params);
    }


    /**
     * 设置标题可滚动
     *
     * @param textView
     */
    public static void setTextMarquee(TextView textView) {
        if (textView != null) {
            textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            textView.setSingleLine(true);
            textView.setSelected(true);
            textView.setFocusable(true);
            textView.setFocusableInTouchMode(true);
        }
    }

    public static class Builder extends AbsNavigationBar.Builder {

        DefaultNavigationParams P;

        public Builder(Context context, ViewGroup parent) {
            super(context, parent);
            P = new DefaultNavigationParams(context, parent);
        }

        public Builder(Context context) {
            super(context, null);
            P = new DefaultNavigationParams(context, null);
        }

        public Builder setBackgroundColor(@ColorInt int color) {
            P.mBackgroundColor = color;
            return this;
        }

        public Builder setTitleTextColor(@ColorInt int color) {
            P.mTitleTextColor = color;
            return this;
        }

        public Builder setRightTextColor(@ColorInt int color) {
            P.mRightTextColor = color;
            return this;
        }

        public Builder setLeftTextColor(@ColorInt int color) {
            P.mLeftTextColor = color;
            return this;
        }

        public Builder setTitle(CharSequence text) {
            P.mTitle = text;
            return this;
        }

        public Builder setLeftText(CharSequence text) {
            P.mLeftText = text;
            return this;
        }

        public Builder setRightText(CharSequence text) {
            P.mRightText = text;
            return this;
        }

        public Builder setLeftIcon(@DrawableRes int resId) {
            P.mLeftIcon = resId;
            return this;
        }

        public Builder setRightIcon(@DrawableRes int resId) {
            P.mRightIcon = resId;
            return this;
        }

        public Builder setLeftClickListener(View.OnClickListener listener) {
            P.mLeftClickListener = listener;
            return this;
        }

        public Builder setRightClickListener(View.OnClickListener listener) {
            P.mRightClickListener = listener;
            return this;
        }

        public Builder setCode(CharSequence code) {
            P.mCode = code;
            return this;
        }

        public Builder setPracticeType(int practiceType) {
            P.mPracticeType = practiceType;
            return this;
        }

        public Builder setSubTitle(CharSequence subTitle) {
            P.mSubTitle = subTitle;
            return this;
        }

        public Builder setElevation(float elevation) {
            P.mElevation = elevation;
            return this;
        }

        @Override
        public PracticeNavigationBar builder() {
            PracticeNavigationBar navigationBar = new PracticeNavigationBar(P);
            return navigationBar;
        }

        public static class DefaultNavigationParams extends AbsNavigationParams {

            public @ColorInt
            int mBackgroundColor;
            public @ColorInt
            int mRightTextColor;
            public @ColorInt
            int mLeftTextColor;
            public @ColorInt
            int mTitleTextColor;
            public CharSequence mTitle;
            public CharSequence mLeftText;
            public CharSequence mRightText;
            public CharSequence mCode;
            public CharSequence mSubTitle;
            public int mPracticeType;
            //默认返回按钮
            public @DrawableRes
            int mLeftIcon = R.drawable.arrow_back;
            public @DrawableRes
            int mRightIcon;
            float mElevation;

            public View.OnClickListener mRightClickListener;

            //默认的返回效果
            public View.OnClickListener mLeftClickListener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ((Activity) mContext).finish();
                }
            };

            public DefaultNavigationParams(Context context, ViewGroup parent) {
                super(context, parent);
            }
        }
    }

    public void setRightIcon(@DrawableRes int icon) {
        setIcon(R.id.navigation_iv_right, icon);
    }

}
