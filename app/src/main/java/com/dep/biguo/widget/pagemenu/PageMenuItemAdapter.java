package com.dep.biguo.widget.pagemenu;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class PageMenuItemAdapter<T> extends RecyclerView.Adapter<PageMenuItemAdapter.Holder<T>> {
    private PageMenuViewHolderCreator<T> holderCreator;

    private List<T> list;

    public void setHolderCreator(PageMenuViewHolderCreator<T> holderCreator) {
        this.holderCreator = holderCreator;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    @NonNull
    @Override
    public Holder<T> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(holderCreator.getLayoutId(), null);
        if(holderCreator != null) {
            holderCreator.initView(view);
        }
        return new Holder<>(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder<T> holder, int position) {
        if(list.size() > position) {
            holder.itemView.setVisibility(View.VISIBLE);
            if(holderCreator != null) {
                holderCreator.bindView(holder, list.get(position), position);
            }
        }else {
            holder.itemView.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    public static class Holder<T> extends RecyclerView.ViewHolder {
        public Holder(@NonNull View itemView) {
            super(itemView);
            itemView.post(new Runnable() {
                @Override
                public void run() {

                }
            });
        }
    }
}
