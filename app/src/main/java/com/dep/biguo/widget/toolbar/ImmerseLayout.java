package com.dep.biguo.widget.toolbar;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.hjq.toast.ToastUtils;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;

public class ImmerseLayout extends LinearLayout {
    public static final String STATUS_BAR = "statusBar";

    //通过app:anchorId属性设置的控件
    private View anchorView;
    private View includeView;

    //要往下移动的目标控件ID
    private int anchorId = NO_ID;

    //延迟检测标题栏在应用窗口的位置，和添加标题栏高度，因为onMeasure()是在窗口变化之前调用，此时还是按照变化之前的状态进行的调整，不会有效果
    private Handler handler;

    //上一次设置anchorId中的控件的margin时，记录下的标题栏高度
    private int lastMeasuredHeight;

    public ImmerseLayout(Context context) {
        super(context);
        init(null);
    }

    public ImmerseLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public ImmerseLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if(attrs != null) {
            TypedArray typedArray = getContext().obtainStyledAttributes(attrs, R.styleable.ImmerseLayout);
            int includeId = typedArray.getResourceId(R.styleable.ImmerseLayout_include_id, NO_ID);
            anchorId = typedArray.getResourceId(R.styleable.ImmerseLayout_anchor_id, NO_ID);
            typedArray.recycle();

            if(includeId != NO_ID) {
                setInclude(includeId);
            }
        }

        //添加一个子控件的添加或移除监听，方便记录includeView的变化
        setOnHierarchyChangeListener(new OnHierarchyChangeListener() {
            @Override
            public void onChildViewAdded(View parent, View child) {
                if(child instanceof ImmerseToolbar){
                    includeView = child;
                }
            }

            @Override
            public void onChildViewRemoved(View parent, View child) {
                if(child instanceof ImmerseToolbar){
                    includeView = null;
                }
            }
        });

        //使用eventbus传递插入状态栏高度的事件，使得所有状态栏在不可见的情况下也能调整高度
        EventBus.getDefault().register(this);
        //监听状态栏高度插入事件，通过eventbus分发这个事件
        ViewCompat.setOnApplyWindowInsetsListener(this, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.statusBars());
            setPadding(getPaddingLeft(), systemBars.top, getPaddingRight(), getPaddingBottom());
            return insets;
        });

        //初始化延迟检测
        handler = new Handler();
    }

    /*@Subscriber(tag = STATUS_BAR)
    public void addStatusBar(int top){
        //接收eventbus分发的插入状态栏高度的事件，可以接收来自其它状态栏分发的
        setPadding(getPaddingLeft(), top, getPaddingRight(), getPaddingBottom());
    }*/

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        //延迟一段时间，等待测量完成，才可以拿到尺寸，对anchorView设置marginTop
        handler.postDelayed(this::addMarginToLabelView, 10);
    }

    /**为xml里anchorId属性中的控件添加标题栏高度的外边距，防止标题栏遮挡
     *
     */
    private void addMarginToLabelView(){
        //控件被隐藏时，去掉顶部外边距
        boolean isAddMarginTop = getVisibility() != View.GONE;

        //查找到xml里anchorId属性中的控件
        if(anchorView == null){
            anchorView = findAnchorView(this);
        }
        if(anchorView == null) return;

        MarginLayoutParams layoutParams = (MarginLayoutParams) anchorView.getLayoutParams();
        //去除上一次添加的标题栏高度，就是当前anchorId属性中的控件的实际外边距
        layoutParams.topMargin = Math.max(0, layoutParams.topMargin - lastMeasuredHeight);
        //重新增加一个标题栏高度的外边距，使得标题栏不遮挡住这个控件
        layoutParams.topMargin = layoutParams.topMargin + (isAddMarginTop ? getHeight() : 0);
        anchorView.setLayoutParams(layoutParams);
        //记录本次添加的标题栏高度
        lastMeasuredHeight = (isAddMarginTop ? getHeight() : 0);
    }

    /**添加内容布局
     * @param includeId 内容布局ID
     */
    protected void setInclude(int includeId) throws Resources.NotFoundException{
        if(includeView != null){
            removeView(includeView);
        }
        setInclude(LayoutInflater.from(getContext()).inflate(includeId, this, false));
    }

    /**引入布局
     * @param include 布局
     */
    protected void setInclude(View include){
        if(includeView != null){
            removeView(includeView);
        }
        addView(include, 0);
    }

    /**获取引入的布局
     * @return
     */
    public View getInclude(){
        return includeView;
    }

    /**指定要往下移动的目标控件
     * @param anchorId 目标控件ID
     */
    public void setAnchor(int anchorId) {
        this.anchorId = anchorId;
        addMarginToLabelView();
    }

    /**指定要往下移动的目标控件
     * @param anchorView 目标控件
     */
    protected void setAnchor(View anchorView){
        this.anchorView = anchorView;
        addMarginToLabelView();
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        addMarginToLabelView();
    }

    /**查找到xml里anchorId属性中的控件
     * @param view 查找范围
     */
    private View findAnchorView(View view){
        if(view.getParent() instanceof View){
            View parentView = (View) view.getParent();
            View labelView = parentView.findViewById(anchorId);
            if(labelView != null){
                return labelView;
            }else {
                return findAnchorView(parentView);
            }
        }
        return null;
    }
}
