package com.dep.biguo.widget;

import android.content.Context;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.res.ResourcesCompat;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.imp.TextWatcherImp;

import java.util.List;

/**专用于圈子正文内容
 *
 */
public class CircleMomentTextView extends AppCompatTextView {
    private CircleBean.Moment moment;
    private OnClickTopicListener onClickTopicListener;

    private static final String ELLIPSIS = "...详情";
    private boolean isFoldText = true;

    public CircleMomentTextView(@NonNull Context context) {
        super(context);
        //必须添加，否则无法相应点击事件
        setMovementMethod(LinkMovementMethod.getInstance());
    }

    public CircleMomentTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        //必须添加，否则无法相应点击事件
        setMovementMethod(LinkMovementMethod.getInstance());
    }

    public CircleMomentTextView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //必须添加，否则无法相应点击事件
        setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    protected void onTextChanged(CharSequence text, int start, int lengthBefore, int lengthAfter) {
        //文字有变化的时候，重新设置一下
        setEllipsizedText(text);
    }

    private void setEllipsizedText(CharSequence text) {
        if (getLayout() == null) return;

        if (isFoldText && getLayout().getLineCount() > getMaxLines()) {
            int lastCharShown = getLayout().getLineVisibleEnd(getMaxLines() - 1) - 1;
            String newText = text.subSequence(0, lastCharShown) + ELLIPSIS;

            //setText(addSpan(newText));
        }
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        //布局确定的时候也要重新设置一下
        setEllipsizedText(getText());
    }

    /**设置动态内容
     * @param moment 动态
     */
    private void setContent(CircleBean.Moment moment){
        if(isFoldText) {
            int contentMaxLine = !AppUtil.isEmpty(moment.getFiles()) ? 3 : 4;
            setMaxLines(contentMaxLine);
        }

        String recommendPlaceholder = moment.getRecommend() == StartFinal.YES ? "　　 " : "";
        String content = String.format("%s%s", recommendPlaceholder, moment.getContent());
        setText(content);
        /*StringBuilder hotBuilder = new StringBuilder();
        for(CircleBean.Topic topic : moment.getHot_topic()){
            hotBuilder.append(String.format("#%s# ", topic.getName()));
        }

        String recommendPlaceholder = moment.getRecommend() == StartFinal.YES ? "　　 " : "";
        String content = String.format("%s%s%s", recommendPlaceholder, hotBuilder, moment.getContent());
        setText(addHotClickSpannable(content, moment.getHot_topic()));*/

    }
    public SpannableStringBuilder addSpan(CharSequence content){
        //创建一个富文本，并给话题添加点击事件
        SpannableStringBuilder spannableString = addHotClickSpannable(content, moment.getHot_topic());
        //给“详情”文本设置颜色。不超过两行的，不会追加“详情”两个字
        if(content.toString().contains(ELLIPSIS)) {
            int start = content.length() - 2;//“...”不需要变颜色
            int end = content.length();
            int color = ResourcesCompat.getColor(getResources(), R.color.tblack3, getContext().getTheme());
            spannableString = SpannableUtil.setColorString(spannableString, start, end, color);
        }
        return spannableString;
    }

    /**创建一个富文本，并给话题添加点击事件
     * @param content 文本
     * @param topicList 话题列表
     * @return 富文本
     */
    private SpannableStringBuilder addHotClickSpannable(CharSequence content, List<CircleBean.Topic> topicList) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(content);

        String contentStr = content.toString();
        //给话题添加一个点击事件
        for(CircleBean.Topic topic : topicList) {
            String topicName = String.format("#%s#", topic.getName());
            int start = contentStr.indexOf(topicName);
            int end = start + topicName.length();
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    if(onClickTopicListener != null) {
                        onClickTopicListener.onClickTopic(topic);
                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    //给话题添加独属颜色
                    int hotColor = ResourcesCompat.getColor(getResources(), R.color.invite_main_bg, getContext().getTheme());
                    ds.setColor(hotColor);
                    //不要下划线
                    ds.setUnderlineText(false);
                }
            }, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }
        return spannableString;
    }

    public CircleMomentTextView setFoldText(boolean foldText) {
        isFoldText = foldText;
        setMaxLines(Integer.MAX_VALUE);
        return this;
    }

    public CircleMomentTextView setMoment(CircleBean.Moment moment) {
        this.moment = moment;
        if(moment != null) {
            setContent(moment);
        }
        return this;
    }

    public CircleMomentTextView setOnClickTopicListener(OnClickTopicListener onClickTopicListener) {
        this.onClickTopicListener = onClickTopicListener;
        return this;
    }

    public interface OnClickTopicListener{
        void onClickTopic(CircleBean.Topic topic);
    }
}
