package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.ImageLoader;

public class IconView extends RelativeLayout {
    private ImageView iconView;
    private ImageView tipView;
    private TextView textView;

    private String type;//标识icon，根据该值响应点击事件
    private String xcx_path;//小程序的路径
    private String target_url;//用来保存h5链接
    private boolean isRound;//是否使用圆形图片
    private int position;

    public IconView(Context context) {
        super(context);
        init(context, null, 0);
    }

    public IconView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public IconView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr){
        String text = "";
        Drawable icon = null;
        float width = DisplayHelper.dp2px(context, 40);
        float height = DisplayHelper.dp2px(context, 40);
        if(attrs != null) {
            TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.IconView, defStyleAttr, 0);
            text = a.getString(R.styleable.IconView_icon_text);
            icon = a.getDrawable(R.styleable.IconView_icon_icon);
            width = a.getDimension(R.styleable.IconView_icon_icon_width, width);
            height = a.getDimension(R.styleable.IconView_icon_icon_height, height);
            a.recycle();
        }

        addView(LayoutInflater.from(getContext()).inflate(R.layout.icon_view, this, false));
        iconView = findViewById(R.id.iconView);
        tipView = findViewById(R.id.tipView);
        textView = findViewById(R.id.textView);

        iconView.getLayoutParams().width = (int) width;
        iconView.getLayoutParams().height = (int) height;
        iconView.setImageDrawable(icon);
        textView.setText(text);
    }

    public void setData(String icon, String title){
        ImageLoader.loadImageNoPlaceholder(iconView, icon);
        textView.setText(title);
    }

    public void setRoundData(String icon, String title){
        ImageLoader.loadRadiusImage(iconView, icon, iconView.getLayoutParams().width);
        textView.setText(title);
    }

    public void setData(int icon, String title){
        Glide.with(getContext()).load(icon).into(iconView);
        textView.setText(title);
    }

    public void setIconWidth(int width){
        iconView.getLayoutParams().width = width;
    }

    public void setIconHeight(int height){
        iconView.getLayoutParams().height = height;
    }

    public void setRoundData(int icon, String title){
        ImageLoader.loadRadiusImage(iconView, icon, iconView.getLayoutParams().width);
        textView.setText(title);
    }

    public void show(boolean isShow){
        setVisibility(isShow ? VISIBLE : INVISIBLE);
        setEnabled(isShow);
    }

    public void setShowTipImage(Object tipImage){
        Glide.with(getContext()).load(tipImage).into(tipView);
    }

    public void setShowTipImage(Object tipImage, int errorRes){
        Glide.with(getContext()).load(tipImage).error(errorRes).into(tipView);
    }

    public void setSpace(int space){
        MarginLayoutParams layoutParams = ((MarginLayoutParams)textView.getLayoutParams());
        layoutParams.setMargins(0, space, 0, 0);
        textView.setLayoutParams(layoutParams);
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public String getXcx_path() {
        return xcx_path;
    }

    public void setXcx_path(String xcx_path) {
        this.xcx_path = xcx_path;
    }

    public String getTarget_url() {
        return target_url;
    }

    public void setTarget_url(String target_url) {
        this.target_url = target_url;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int getPosition() {
        return position;
    }
}
