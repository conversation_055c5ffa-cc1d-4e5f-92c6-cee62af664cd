package com.dep.biguo.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.SimpleItemAnimator;

import android.media.AudioManager;
import android.media.MediaPlayer;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.CardBean;
import com.dep.biguo.bean.PracticeVideoBean;
import com.dep.biguo.bean.QuestionBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.QuestionViewBinding;
import com.dep.biguo.mvp.ui.activity.AiAnalysisActivity;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.mvp.ui.activity.ImageActivity;
import com.dep.biguo.mvp.ui.activity.PracticeV3Activity;
import com.dep.biguo.mvp.ui.adapter.practice.OptionAdapter;
import com.dep.biguo.mvp.ui.adapter.practice.PracticeV2ItemAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;
import com.dep.biguo.utils.html.HtmlUtil;
import com.dep.biguo.utils.html.TagBean;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.imp.TextWatcherImp;
import com.dep.biguo.utils.mmkv.UserCache;
import com.hjq.toast.ToastUtils;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class QuestionView extends LinearLayout implements View.OnClickListener, View.OnLongClickListener{
    private QuestionViewBinding binding;
    private OptionAdapter optionAdapter;//选项适配器
    private MediaPlayer mediaPlayer;

    private final Setting setting=new Setting();//保存设置
    private Animation animation;//AI解析加载中的动画

    public QuestionView(Context context) {
        super(context);
        init();
    }

    public QuestionView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public QuestionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.question_view,null,false);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        binding.setOnLongClickListener(this);
        binding.getRoot().setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        for (Activity activity : AppManager.getAppManager().getActivityList()){
            if(activity instanceof PracticeV3Activity) {
                binding.videoView.bindActivityLifecycle((PracticeV3Activity)activity);
                break;
            }
        }
        addView(binding.getRoot());

        //取消刷新动画
        SimpleItemAnimator simpleItemAnimator = ((SimpleItemAnimator) binding.optionsRecyclerView.getItemAnimator());
        assert simpleItemAnimator != null;
        simpleItemAnimator.setSupportsChangeAnimations(false);

        //失去焦点的时候关闭软键盘，当按了返回键，会弹出一个弹窗，点击确认离开，会隐藏这道题目的UI，然后立即显示，主要是因为隐藏操作会触发该焦点监听事件
        binding.inputAnswerView.setOnFocusChangeListener((v, hasFocus) -> {
            if(!hasFocus){
                String answer = binding.inputAnswerView.getText().toString();
                setting.topicBean.setSelect_answer(answer);
                if(isModeSimu()) {
                    int is_correct = setting.topicBean.getIs_correct();
                    checkAnswer(answer, false);
                    //答题后回调
                    setting.onResultListener.onDoCount(setting.topicBean, doCount(is_correct));
                    //答题后回调
                    setting.onResultListener.onAnswerResult(setting.topicBean, true);
                }
                //答题后回调
                KeyboardUtils.hideKeyboard(v);
            }
        });
        binding.inputAnswerView.addTextChangedListener(new TextWatcherImp() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if(s.length() > 0){
                    binding.inputAnswerView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, PracticeHelper.getTextSize(16));
                }else {
                    binding.inputAnswerView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                }
            }
        });

        animation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        animation.setRepeatCount(Animation.INFINITE);
        animation.setDuration(1000);
    }

    @Override
    public void onClick(View view) {
        if(setting.onResultListener == null) return;

        if(view == binding.commitAnswerView){
            //多选状态下，按钮的点击事件
            if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI){
                if (TextUtils.isEmpty(setting.topicBean.getSelect_answer())) return;

            }else {
                //不可作答
                //使用GsonUtils是防止输入框中有反斜杠“\”导致解析出错
                String answer = binding.inputAnswerView.getText().toString();
                setting.topicBean.setSelect_answer(answer);
            }
            //记录本次答题的状态：是否是已选中
            int is_correct = setting.topicBean.getIs_correct();
            //检查答案
            checkAnswer(setting.topicBean.getSelect_answer(), true);
            //答题后回调
            setting.onResultListener.onAnswerResult(setting.topicBean, false);
            setting.onResultListener.onDoCount(setting.topicBean, doCount(is_correct));
            //刷新一下数据的显示
            refreshView(false);

        }else if(view == binding.videoView || view == binding.resetPlayView){
            //视频封面（获取播放地址）
            if(AppUtil.isEmpty(setting.questionBean.getVideo_parse_url())) {
                setting.onResultListener.onLookVideoAnalyze(setting.position);
            }else {
                binding.endPlayLayout.setVisibility(GONE);
                binding.noPlayLayout.setVisibility(GONE);
                binding.videoView
                        .setVideoUrl(setting.questionBean.getVideo_parse_url())
                        .play();
            }

        }else if(view == binding.openMembershipLayout
                || view == binding.endPlayOpenMembershipView
                || view == binding.noPlayOpenMembershipView){
            //视频封面（获取播放地址）
            if(!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(BiguoVipOpenActivity.class);

        }else if(view == binding.reportErrorView){
            //纠错
            setting.onResultListener.onReportError(setting.position);

        }else if(view== binding.commentView){
            //评论
            setting.onResultListener.onAddComment(setting.position);

        }else if(view == binding.aiAnalyzeView){
            if(MainAppUtils.checkLogin(getContext())) {
                Map<String, Object> map = new HashMap<>();
                map.put("course_code", setting.practiceManager.mCode);
                map.put("course_name", setting.practiceManager.mTitle);
                map.put("mainType", setting.topicBean.getMainType()+"");
                map.put("topic_id", setting.topicBean.getId()+"");
                map.put("topic_type", setting.topicBean.getTopic_type()+"");
                map.put("topic_type_name", PracticeHelper.getTopicType(setting.topicBean.getTopic_type()));
                AiAnalysisActivity.Start(getContext(), map, setting.questionBean.getQuestionAsk());
            }
        }
    }

    public int doCount(int lastStatus){
        if(lastStatus == Constant.ANSWER_NONE
                && (setting.topicBean.getIs_correct() == Constant.ANSWER_WRITE_HIDE || setting.topicBean.getIs_correct() == Constant.ANSWER_WRITE_SHOW)){
            //从未答变成已答
            return 1;
        }else if(setting.topicBean.getIs_correct() == Constant.ANSWER_NONE && (lastStatus == Constant.ANSWER_WRITE_HIDE || lastStatus == Constant.ANSWER_WRITE_SHOW)){
            //从已答变成未答
            return -1;
        }else if(setting.topicBean.getIs_correct() == Constant.ANSWER_CORRECT ||  setting.topicBean.getIs_correct() == Constant.ANSWER_ERROR){
            //已经判断正误
            return 1;
        }else {
            //未答变成未答，已答变成已答
            return 0;
        }
    }

    public boolean isModeSimu(){
        return setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SIMU;
    }

    /**设置解析、评论是否显示
     */
    public void setQuestionUiShow() {
        //是否可以作答模式
        boolean isNeedAnswer = setting.practiceManager.mPracticeMode == PracticeHelper.MODE_DEFAULT//做题模式
                || setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SIMU;//模拟模式
        //是否已作答
        boolean isAnswer = !TextUtils.isEmpty(setting.topicBean.getSelect_answer()); //不为空
        //是否已判断正误
        boolean isJudge = setting.topicBean.getIs_correct() == Constant.ANSWER_CORRECT
                || setting.topicBean.getIs_correct() == Constant.ANSWER_ERROR
                || setting.topicBean.getIs_correct() == Constant.ANSWER_WRITE_SHOW;
        //是否显示答案解析
        boolean isShowResult = !isNeedAnswer //不可以作答
                || isJudge;//判断了正误
        //是否显示确定（查看）按钮
        boolean isShowCommitAnswerView = (setting.practiceManager.mPracticeMode == PracticeHelper.MODE_DEFAULT && !isJudge)//是做题模式且未判断正误
                || (setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SIMU && setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI);//是模拟模式且是多选题
        //是否显示我的答案
        boolean isShowMyAnswer = (setting.practiceManager.mPracticeMode == PracticeHelper.MODE_DEFAULT && isJudge)//是做题模式且判断了正误
                || (setting.practiceManager.mPracticeMode == PracticeHelper.MODE_ALL);//是解析模式

        //根据题目的类型设置UI的显示
        if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_SINGLE
                || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_JUDGE){
            //单选、判断
            binding.commitAnswerView.setVisibility(GONE);//隐藏完成按钮
            binding.resultLayout.setVisibility(isShowResult ? VISIBLE : GONE);//显示答题结果，或者隐藏
            binding.commentLayout.setVisibility(binding.resultLayout.getVisibility());//评论跟随答题结果显示或隐藏
            binding.optionsRecyclerView.setVisibility(VISIBLE);//显示选项列表
            binding.myAnswerView.setVisibility(GONE);//隐藏我输入的答案
            binding.inputAnswerView.setVisibility(GONE);//隐藏输入答案的输入框
            binding.remarksView.setVisibility(GONE);//隐藏对输入的答案的处理提示
            binding.correctAnswerView.setVisibility(isShowResult ? VISIBLE : GONE);//隐藏选择题的作答答案

        }else if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI){
            //多选 模拟模式最后一道题是多选题，则变成交卷按钮
            if(setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SIMU
                    && setting.position + 1 == setting.practiceManager.mTotalCount){
                binding.commitAnswerView.setText("交卷");
            }else {
                binding.commitAnswerView.setText("确认答案");
            }
            binding.commitAnswerView.setVisibility(isShowCommitAnswerView ? VISIBLE : GONE);//查看答案按钮隐藏或显示
            setCommitAnswerViewEnable(isAnswer);
            binding.resultLayout.setVisibility(isShowResult ? VISIBLE : GONE);//显示答题结果，或者隐藏
            binding.commentLayout.setVisibility(binding.resultLayout.getVisibility());//评论跟随答题结果显示或隐藏
            binding.optionsRecyclerView.setVisibility(VISIBLE);//显示选项列表
            binding.myAnswerView.setVisibility(GONE);//隐藏我输入的答案
            binding.inputAnswerView.setVisibility(GONE);//隐藏输入答案的输入框
            binding.remarksView.setVisibility(GONE);//隐藏对输入的答案的处理提示
            binding.correctAnswerView.setVisibility(isShowResult ? VISIBLE : GONE);//隐藏选择题的作答答案

        }else {
            //问答、名词解释、填空
            binding.commitAnswerView.setText("确认答案");
            setCommitAnswerViewEnable(true);
            binding.commitAnswerView.setVisibility(isShowCommitAnswerView ? VISIBLE : GONE);//查看答案按钮隐藏或显示
            binding.resultLayout.setVisibility(isShowResult ? VISIBLE : GONE);//显示或隐藏解析
            binding.commentLayout.setVisibility(binding.resultLayout.getVisibility());//分割线跟随答题结果显示或隐藏
            binding.optionsRecyclerView.setVisibility(GONE);//隐藏选项列表
            binding.myAnswerView.setVisibility(isShowMyAnswer ? binding.resultLayout.getVisibility() : GONE);//我的答案 优先判断是否是背题模式，再判断是否跟随答题结果显示或隐藏
            binding.inputAnswerView.setVisibility(isShowResult ? GONE : VISIBLE);//输入答案的输入框 跟随答题结果显示或隐藏
            binding.remarksView.setVisibility(isShowResult ? GONE : VISIBLE);//对输入的答案的处理提示 跟随答题结果显示或隐藏
            binding.correctAnswerView.setVisibility(GONE);//隐藏选择题的作答答案
        }

        //包含图片的题目不显示AI解析，或者后台指定不显示
        boolean hasImgTag = AppUtil.isEmpty(setting.questionBean.getQuestionAsk(), "").matches(".*<img.*src=.*/>.*");
        binding.aiAnalyzeView.setVisibility(hasImgTag ? GONE : VISIBLE);
    }

    /**刷新题目
     */
    public void refreshView(boolean isRefreshQuestion) {
        //当焦点在输入框上，且软键盘打开导致布局有变动,需要整个界面上移，但这需要父布局自己操作，所以通过回调对象实现
        binding.inputAnswerView.setOnTouchClickListener(setting.onTouchMyAnswerClickListener);

        //设置解析、评论是否显示
        setQuestionUiShow();

        //设置字体大小
        PracticeHelper.changeSize(binding.questionView, 16);
        PracticeHelper.changeSize(binding.myAnswerView, 16);
        PracticeHelper.changeSize(binding.inputAnswerView, 16);
        PracticeHelper.changeSize(binding.answerView, 16);
        PracticeHelper.changeSize(binding.analyzeView, 16);

        //设置题目文本
        if(isRefreshQuestion) {
            int questionViewWidth = DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), 20);
            String question = "<font color=\"#999999\">"
                    + "<br/>题库版本号:" + setting.practiceManager.version
                    + "<br/>题型："+ setting.questionBean.getTopic_type_name()
                    + "<br/>题目ID："+setting.topicBean.getId()
                    + "</font><br/>";
            //分数
            String score = setting.questionBean.getMark() > 0 ? String.format("；本题：%s分", setting.questionBean.getMark()) : "";
            //题目
            String questionText = AppUtil.isEmpty(setting.questionBean.getQuestionAsk(), "题目缺失" + HtmlUtil.addHtmlFontSizeTag(question, 12));
            String mainTypeName = PracticeHelper.getErrorOrCollTypeName(setting.practiceManager, setting.topicBean);
            String topicName = String.format("(来源：%s%s)", mainTypeName, score);
            //自定义了size标签处理font设置字体大小没变化的问题
            String rgbColor = getHtmlFontColorTag(HtmlUtil.addHtmlFontSizeTag(topicName, 12), R.color.tblack3);
            String audioPlayImg = "";
            if(mediaPlayer != null && mediaPlayer.isPlaying()){
                audioPlayImg = String.format("<img src=\"%s\"/>", HtmlUtil.AUDIO_PLAYING);
            }else if(!AppUtil.isEmpty(setting.questionBean.getAudio_url())){
                audioPlayImg = String.format("<img src=\"%s\"/>", HtmlUtil.AUDIO_PREPARE);
            }
            String spanString = String.format("%s %s %s",  audioPlayImg, questionText, rgbColor);

            //当题目中有上标或下标时，换行显示才能完整，并设置负数margin，使得多出来的这一段空白藏起来
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) binding.questionView.getLayoutParams();
            if(spanString.contains("<sup>") || spanString.contains("<sub>")){
                spanString = "<br/>" + spanString;
                layoutParams.topMargin = (int) -binding.questionView.getTextSize();
            }else {
                layoutParams.topMargin = DisplayHelper.dp2px(getContext(), 10);
            }
            binding.questionView.setLayoutParams(layoutParams);

            loadRichText(binding.questionView, questionViewWidth, PracticeHelper.filterLabel(spanString));

        }

        //设置我的答案
        String myAnswer = setting.topicBean.getSelect_answer();
        boolean isModeShow = setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SHOW;//是否是背题模式
        boolean isModeAll = setting.practiceManager.mPracticeMode == PracticeHelper.MODE_ALL;//是否是解析模式
        boolean isAnswerShow = setting.topicBean.getIs_correct() == Constant.ANSWER_WRITE_SHOW//是否已填入并显示答案
                ||setting.topicBean.getIs_correct() == Constant.ANSWER_CORRECT//正确
                ||setting.topicBean.getIs_correct() == Constant.ANSWER_ERROR;//错误

        //控件宽度
        int viewWidth = DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), 54);

        if((isAnswerShow || isModeShow || isModeAll)){
            binding.inputAnswerView.setText("");
            //可以显示答案，或者是背题模式
            if(TextUtils.isEmpty(myAnswer) || !isAnswerShow) {
                myAnswer = getHtmlFontColorTag("未作答", R.color.tblack3);
            }
            String title = HtmlUtil.addHtmlFontStroke(HtmlUtil.addHtmlFontSizeTag("我的答案：", 16), TagBean.BOLD);
            String text = HtmlUtil.addHtmlFontStroke(myAnswer, TagBean.NORMAL);
            loadRichText(binding.myAnswerView, viewWidth, String.format("%s<br/>%s", title, text));

        }else {
            binding.inputAnswerView.setText(myAnswer);
            binding.myAnswerView.setText("");
        }

        //设置参考答案
        if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_SINGLE
                || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_JUDGE
                || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI){
            //单选、判断、多选
            String answer = PracticeHelper.filterLabel(AppUtil.isEmpty(setting.questionBean.getCorrectOption(), "暂无"));
            if(setting.practiceManager.mPracticeMode == PracticeHelper.MODE_SHOW){
                //背题模式没有作答，所以不需要展示“您选择”这部分内容
                loadRichText(binding.correctAnswerView, viewWidth, "答案："+ getHtmlFontColorTag(answer, R.color.option_correct_text));
            }else {
                String selectedAnswer = AppUtil.isEmpty(setting.topicBean.getSelect_answer(), "未选择");

                String referenceAnswerHtml = getHtmlFontColorTag(answer, R.color.option_correct_text);
                String mySelectedAnswerHtml ;
                if(AppUtil.isEmpty(setting.topicBean.getSelect_answer())){
                    mySelectedAnswerHtml = getHtmlFontColorTag(selectedAnswer, R.color.tblack3);
                }else if(setting.topicBean.getIs_correct() == Constant.ANSWER_CORRECT){
                    mySelectedAnswerHtml = getHtmlFontColorTag(selectedAnswer, R.color.option_correct_text);
                }else {
                    mySelectedAnswerHtml = getHtmlFontColorTag(selectedAnswer, R.color.option_error_text);
                }
                String spanString = String.format("答案 %s　您的答案 %s", referenceAnswerHtml, mySelectedAnswerHtml);
                loadRichText(binding.correctAnswerView, viewWidth, spanString);
            }
            binding.answerView.setVisibility(GONE);

            //设置解析文本
            int analyzeViewWidth = DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), 54);
            String analyzeText = AppUtil.isEmpty(setting.questionBean.getExplanation(), "暂无");
            String builder = HtmlUtil.addHtmlFontStroke(analyzeText, TagBean.NORMAL);
            loadRichText(binding.analyzeView, analyzeViewWidth, PracticeHelper.filterLabel(builder));

        }else {
            binding.answerView.setVisibility(VISIBLE);
            //问答、名词解释、填空
            String answerText = PracticeHelper.filterLabel(AppUtil.isEmpty(setting.questionBean.getCorrectOption(), "暂无"));
            String answerBuilder = HtmlUtil.addHtmlFontStroke(HtmlUtil.addHtmlFontSizeTag("参考答案", 16), TagBean.BOLD) +
                    "<br/>" +
                    HtmlUtil.addHtmlFontStroke(answerText, TagBean.NORMAL);
            loadRichText(binding.answerView, viewWidth, answerBuilder);

            String analyzeText = AppUtil.isEmpty(setting.questionBean.getExplanation(), "暂无");
            String analyzeBuilder = HtmlUtil.addHtmlFontStroke(analyzeText, TagBean.NORMAL);
            loadRichText(binding.analyzeView, viewWidth, analyzeBuilder);
        }

        //初始化选项列表
        if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_SINGLE
                || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_JUDGE
                || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI){
            initOptionsAdapter();
        }

        //初始化视频解析控件
        String videoThumb = setting.questionBean.getVideo_parse_cover();
        loadRichText(binding.videoTextView, viewWidth, HtmlUtil.addHtmlFontStroke(HtmlUtil.addHtmlFontSizeTag("AI视频解析", 16), TagBean.BOLD));
        binding.videoTextView.setVisibility((isAnswerShow || isModeShow || isModeAll) && !TextUtils.isEmpty(videoThumb) ? VISIBLE : GONE);
        binding.videoLayout.setVisibility(binding.videoTextView.getVisibility());
        binding.videoView.setThumbImageUrl(videoThumb)
                .setVideoUrl(setting.questionBean.getVideo_parse_url());

    }

    public void playAudio(String url){
        try {
            if(AppUtil.isEmpty(url)){
                if(mediaPlayer != null){
                    mediaPlayer.stop();
                    mediaPlayer = null;
                }
                refreshView(true);
                return;
            }

            if(mediaPlayer == null) {
                mediaPlayer = new MediaPlayer();
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
                mediaPlayer.setDataSource(url);
                mediaPlayer.setOnPreparedListener(mp -> {
                    mediaPlayer.start();
                    refreshView(true);
                });
                mediaPlayer.setOnCompletionListener(mp -> {
                    mediaPlayer.stop();
                    mediaPlayer = null;
                    refreshView(true);
                });
                mediaPlayer.prepareAsync();

            }else if(mediaPlayer.isPlaying()){
                mediaPlayer.stop();
                mediaPlayer = null;
                refreshView(true);
            }

        } catch (IOException e) {
            ToastUtils.show("读题失败");
            refreshView(true);
        }
    }

    //开始播放视频
    public void playVideo(PracticeVideoBean videoBean){
        if(AppUtil.isEmpty(videoBean.getVideo_parse_url())){
            //未开通折扣卡，且试看次数为0
            if(videoBean.getIs_membership() == 0 && videoBean.getWatch_num() == 0){
                //不是会员，且没有观看次数了
                binding.videoView.setPlayButtonVisibility(false);
                binding.videoNoPlayMessageView.setText(videoBean.getIllustrate_text());
                binding.noPlayLayout.setVisibility(VISIBLE);
                binding.endPlayLayout.setVisibility(GONE);
                return;
            }

            //不是会员，但还有观看次数，或者是会员
            binding.videoView.setShowEmptyUrl();
            return;

        }else {
            setting.questionBean.setVideo_url(videoBean.getVideo_parse_url());
            binding.videoView
                    .setVideoUrl(videoBean.getVideo_parse_url())
                    .setPlayEndListener(() -> {
                        //播放完成
                        binding.videoMessageView.setText(videoBean.getIllustrate_text());
                        binding.endPlayLayout.setVisibility(videoBean.getIs_membership() == 0 ? VISIBLE : GONE);
                        binding.noPlayLayout.setVisibility(GONE);
                        //播放完成后，若未开通折扣卡，把播放按钮设置为隐藏，避免透过浮现的界面干扰阅读
                        binding.videoView.setPlayButtonVisibility(videoBean.getIs_membership() != 0);
                    })
                    .play();
        }

        //推荐开通折扣卡的控件淡入淡出
        if(videoBean.getIs_membership() == 0 && !AppUtil.isEmpty(videoBean.getIllustrate_text())){
            binding.openMembershipTextView.setText(videoBean.getIllustrate_text());
            binding.openMembershipLayout.setVisibility(VISIBLE);
            // 首先设置目标视图的透明度为0（完全透明）
            binding.openMembershipLayout.setAlpha(0f);

            // 1秒钟内淡入（透明度从0到1）
            ObjectAnimator fadeIn = ObjectAnimator.ofFloat(binding.openMembershipLayout, "alpha", 0f, 1f);
            fadeIn.setDuration(1000); // 1秒

            // 延迟3秒后开始淡出（透明度从1到0）
            ObjectAnimator fadeOut = ObjectAnimator.ofFloat(binding.openMembershipLayout, "alpha", 1f, 0f);
            fadeOut.setDuration(1000); // 1秒
            fadeOut.setStartDelay(6000); // 3秒后开始
            fadeOut.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    binding.openMembershipLayout.setVisibility(GONE);
                }
            });

            // 顺序执行淡入和淡出
            fadeIn.start();
            fadeOut.start();

        }
    }

    //添加html标签，该标签是设置文字颜色的
    public String getHtmlFontColorTag(String str, int colorId){
        String rgb16 = "#333333";
        if (colorId == R.color.tblack3){
            rgb16 = UserCache.isDayNight() ? "#767676" : "#999999";
        }else if (colorId == R.color.option_correct_text){
            rgb16 = "#2FB865";
        }else if (colorId == R.color.option_error_text){
            rgb16 = "#D7565A";
        }
        return String.format("<font color=\"%s\">%s</font>", rgb16, str);
    }

    //设置是否可以点击提交按钮
    private void setCommitAnswerViewEnable(boolean isEnabled){
        binding.commitAnswerView.setEnabled(isEnabled);
        if(isEnabled){
            binding.commitAnswerView.setBackgroundResource(R.drawable.bg_round_200_theme);
        }else {
            binding.commitAnswerView.setBackgroundResource(R.drawable.bg_round_200_alpha_theme);
        }
    }

    //初始化选项适配器
    public void initOptionsAdapter(){
        //不要重复创建适配器
        if(optionAdapter != null) {
            optionAdapter.setQuestionBean(setting.questionBean);
            optionAdapter.setTopicBean(setting.topicBean);

        }else {
            optionAdapter = new OptionAdapter(setting.practiceManager);
            optionAdapter.setQuestionBean(setting.questionBean);
            optionAdapter.setTopicBean(setting.topicBean);
            binding.optionsRecyclerView.setAdapter(optionAdapter);
            //选中并确定
            optionAdapter.setOnItemClickListener((adapter, view, position) -> {
                optionAdapter.setSelectAnswer(position);
                //设置提交按钮是否可以被点击
                setCommitAnswerViewEnable(!TextUtils.isEmpty(setting.topicBean.getSelect_answer()));

                //记录本次答题的状态：是否是已选中
                int is_correct = setting.topicBean.getIs_correct();
                //模拟模式下，多选题才可以进入检查答案，主要设置已作答但不显示答案状态
                if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI && isModeSimu()){
                    //检查答案
                    checkAnswer(setting.topicBean.getSelect_answer(), false);
                    //如果是多选，那么点击选项是只触发选中效果，且与检查答案之前的状态不同才会回调
                    if(is_correct != setting.topicBean.getIs_correct()) {
                        //答题后回调
                        setting.onResultListener.onAnswerResult(setting.topicBean, true);
                        setting.onResultListener.onDoCount(setting.topicBean, doCount(is_correct));
                    }

                }else if(setting.topicBean.getTopic_type() == PracticeHelper.TYPE_SINGLE
                        || setting.topicBean.getTopic_type() == PracticeHelper.TYPE_JUDGE){
                    //检查答案
                    checkAnswer(setting.topicBean.getSelect_answer(), true);
                    //若与检查答案之前的状态不同才会回调
                    if(is_correct != setting.topicBean.getIs_correct()) {
                        //答题后回调
                        setting.onResultListener.onAnswerResult(setting.topicBean, false);
                        setting.onResultListener.onDoCount(setting.topicBean, doCount(is_correct));
                    }
                }
                //刷新一下数据的显示
                refreshView(false);
            });
        }
    }

    /**检测答案，所选的所有答案与参考答案完全匹配才算正确
     * @param selectAnswer 选中的答案
     */
    private void checkAnswer(String selectAnswer, boolean isShowAnswer) {
        int isCorrect;

        if(isModeSimu()) {
            isCorrect = TextUtils.isEmpty(selectAnswer)
                    ? Constant.ANSWER_NONE //未答
                    : Constant.ANSWER_WRITE_HIDE;//答了但不显示答案

        }else {
            if(PracticeHelper.isSupportSelect(setting.topicBean.getTopic_type())) {
                //题型支持选择选项，则用正则表达式剔除非选项中的字符
                String correctOption = AppUtil.isEmpty(setting.questionBean.getCorrectOption(), "")
                        .replaceAll("(?![A-F]).", "");

                    isCorrect = TextUtils.equals(selectAnswer, correctOption)
                            ? Constant.ANSWER_CORRECT //正确
                            : Constant.ANSWER_ERROR; //错误

            }else {
                isCorrect = isShowAnswer ? Constant.ANSWER_WRITE_SHOW : Constant.ANSWER_WRITE_HIDE; //答了并显示答案
            }
        }

        //设置答题结果
        setting.topicBean.setIs_correct(isCorrect);
    }

    /**加载富文本
     * @param textView 显示富文本的控件
     * @param htmlString html字符串
     */
    private void loadRichText(TextView textView, int viewWidth, String htmlString) {
        HtmlUtil.form(htmlString, viewWidth)
                .setOnImageClickListener((position, url) -> {
                    if(TextUtils.equals(url, HtmlUtil.AUDIO_PREPARE) || TextUtils.equals(url, HtmlUtil.AUDIO_PLAYING)){
                        playAudio(setting.questionBean.getAudio_url());
                    }else {
                        ImageActivity.mPaths.add(url);
                        ImageActivity.start(textView.getContext(), position);
                    }
                })
                .setTargetView(textView);
    }


    public Setting getSetting() {
        return setting;
    }

    @Override
    public boolean onLongClick(View v) {
        if(v instanceof TextView){
            TextView targetView = (TextView) v;
            if(v == binding.myAnswerView){
                //所选答案不为空，并且所选答案不是“#”,则复制
                if(!TextUtils.isEmpty(setting.topicBean.getSelect_answer()) && !setting.topicBean.getSelect_answer().equals("#")){
                    AppUtil.copyText(getContext(), setting.topicBean.getSelect_answer());
                    ToastUtils.show("复制成功");
                }

            }else {
                AppUtil.copyText(getContext(), targetView.getText().toString());
                ToastUtils.show("复制成功");
            }
        }
        return false;
    }

    public static class Setting{
        QuestionBean questionBean;//题目，正确答案取题目里的，因为答题卡缺失问答、填空、名词解释的答案
        CardBean.Topic topicBean;//答题卡
        int position;//记录自身在列表中位置
        PracticeManager practiceManager;//对答题界面做的本地设置
        PracticeV2ItemAdapter.OnResultListener onResultListener;//单选题、多选题、判断题选择答案后的回调
        LayoutUpScrollEditView.OnTouchClickListener onTouchMyAnswerClickListener;//触摸我的答案输入框时，需要列表往上滚动

        public Setting setQuestionBean(QuestionBean questionBean) {
            this.questionBean = questionBean;
            return this;
        }

        public Setting setTopicBean(CardBean.Topic topicBean) {
            this.topicBean = topicBean;
            return this;
        }

        public Setting setPosition(int position){
            this.position=position;
            return this;
        }

        public Setting setPracticeManager(PracticeManager practiceManager){
            this.practiceManager=practiceManager;
            return this;
        }

        public Setting setOnResultListener(PracticeV2ItemAdapter.OnResultListener onResultListener){
            this.onResultListener=onResultListener;
            return this;
        }
        public Setting setOnTouchMyAnswerClickListener(LayoutUpScrollEditView.OnTouchClickListener onTouchMyAnswerClickListener){
            this.onTouchMyAnswerClickListener=onTouchMyAnswerClickListener;
            return this;
        }
    }
}
