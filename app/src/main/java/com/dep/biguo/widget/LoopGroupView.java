package com.dep.biguo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.core.content.ContextCompat;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.widget.StyleTextView;
import com.biguo.utils.widget.StyleViewAttr;
import com.dep.biguo.R;
import com.weigan.loopview.LoopView;
import com.weigan.loopview.OnItemScrollListener;

import java.util.ArrayList;
import java.util.List;

public class LoopGroupView extends RelativeLayout implements OnItemScrollListener {
    List<String> dateList = new ArrayList<>();

    private OnDateChangeListener onDateChangeListener;//日期变化监听对象
    private LoopView loopView;//滚轮

    private int minYear = 2010;//最小年份

    private int lastScrollY;//用于记录年份上一次滚动的位置
    private int monthLastScrollY;//用于记录月份上一次滚动的位置

    public LoopGroupView(Context context) {
        super(context);
        initView();
        initData();
    }

    public LoopGroupView(Context context, AttributeSet attributeset) {
        super(context, attributeset);
        initView();
        initData();
    }

    public LoopGroupView(Context context, AttributeSet attributeset, int defStyleAttr) {
        super(context, attributeset, defStyleAttr);
        initView();
        initData();
    }

    private void initView(){
        int selectedDateViewWidth = DisplayHelper.dp2px(getContext(), 260);
        int selectedDateViewHeight = DisplayHelper.dp2px(getContext(), 30);
        int selectedRound = DisplayHelper.dp2px(getContext(), 5);
        int loopViewWidth = DisplayHelper.dp2px(getContext(), 130);
        int loopPadding = DisplayHelper.dp2px(getContext(), 30);

        //选中的背景
        StyleTextView selectedDateView = new StyleTextView(getContext());
        selectedDateView.setId(View.generateViewId());
        LayoutParams selectedDateParams = new LayoutParams(selectedDateViewWidth, selectedDateViewHeight);
        selectedDateView.setLayoutParams(selectedDateParams);
        selectedDateParams.addRule(CENTER_IN_PARENT);
        //设置背景样式
        StyleViewAttr selectedDateAttr = new StyleViewAttr();
        selectedDateAttr.setAllRound(selectedRound);
        selectedDateAttr.setBgGradientColor(ContextCompat.getColor(getContext(), R.color.bgc));
        selectedDateView.setStyleViewAttr(selectedDateAttr);
        addView(selectedDateView);

        //滚轮
        loopView = new LoopView(getContext());
        LayoutParams yearParams = new LayoutParams(loopViewWidth, LayoutParams.MATCH_PARENT);
        yearParams.addRule(ALIGN_START, selectedDateView.getId());
        loopView.setLayoutParams(yearParams);
        loopView.setPadding(loopPadding, 0, 0, 0);
        loopView.setDividerColor(getContext().getResources().getColor(R.color.tran));
        loopView.setOnItemScrollListener(this);
        loopView.setNotLoop();
        loopView.setTextSize(18);
        addView(loopView);
    }

    private void initData(){
        //初始年份滚轮数据
        for(int i = minYear; i <= 5; i++){
            dateList.add("item");
        }
        loopView.setItems(dateList);
    }

    public void setItems(List<String> list){
        dateList.clear();
        dateList.addAll(list);
        loopView.setItems(dateList);
    }

    @Override
    public void onItemScrollStateChanged(LoopView loopView, int currentPassItem, int oldScrollState, int scrollState, int totalScrollY) {
        //滚轮停止滚动回调，此处再次调用，是防止两个滚轮同时滚动导致标题显示的日期与滚动控件的不一致
        String select = dateList.get(this.loopView.getSelectedItem());
        //未停止滚动时，或不是用户设置的值引起回调，则禁止返回到日历页面
        boolean isEnable = scrollState == LoopView.SCROLL_STATE_IDLE || scrollState == LoopView.SCROLL_STATE_SETTING;

        LogUtil.d("dddd", isEnable);
        if(onDateChangeListener != null) {
            onDateChangeListener.onDateChange(select, isEnable);
        }
    }

    @Override
    public void onItemScrolling(LoopView loopView, int currentPassItem, int scrollState, int totalScrollY) {
        boolean isEnable;

        isEnable = lastScrollY == totalScrollY;
        lastScrollY = totalScrollY;

        //滚轮滚动中回调
        if(onDateChangeListener != null) {
            onDateChangeListener.onDateChange(dateList.get(this.loopView.getSelectedItem()), isEnable);
        }
    }

    /**返回当前选中的年份
     * @return
     */
    public String getCurrentSelected(){
        return dateList.get(loopView.getSelectedItem());
    }

    /**设置日期变化监听
     * @param onDateChangeListener 监听对象
     */
    public void setOnDateChangeListener(OnDateChangeListener onDateChangeListener) {
        this.onDateChangeListener = onDateChangeListener;
    }

    public interface OnDateChangeListener{
        void onDateChange(String select,  boolean isDateEnable);
    }
}
