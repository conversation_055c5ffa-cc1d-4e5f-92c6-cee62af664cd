package com.dep.biguo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.webkit.WebView;

import com.biguo.utils.util.LogUtil;

public class ScrollListenerWebView extends WebView {
    private OnScrollChangeListener onScrollChangeListener;
    private int contentHeight;

    public ScrollListenerWebView(Context context) {
        super(context);
    }

    public ScrollListenerWebView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ScrollListenerWebView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * @param offsetLeft    WebView的左边缘相对于视图左边缘的X轴偏移量。
     * @param offsetTop     WebView的顶部边缘相对于视图顶部边缘的Y轴偏移量。
     * @param oldOffsetLeft WebView的旧左边缘相对于视图左边缘的X轴偏移量。
     * @param oldOffsetTop  WebView的旧顶部边缘相对于视图顶部边缘的Y轴偏移量。
     */
    @Override
    protected void onScrollChanged(int offsetLeft, int offsetTop, int oldOffsetLeft, int oldOffsetTop) {
        super.onScrollChanged(offsetLeft, offsetTop, oldOffsetLeft, oldOffsetTop);
        if(contentHeight == 0){
            measure(0,0);
            contentHeight = getMeasuredHeight();
        }
        if (getScrollY() >= (contentHeight - getHeight())) {
            LogUtil.d("dddd", "处于底端");
            //处于底端
            onScrollChangeListener.onPageBottom(offsetLeft, offsetTop, oldOffsetLeft, oldOffsetTop);
        } else if (getScrollY() == 0) {
            LogUtil.d("dddd", "处于顶端");
            //处于顶端
            onScrollChangeListener.onPageTop(offsetLeft, offsetTop, oldOffsetLeft, oldOffsetTop);
        } else {
            LogUtil.d("dddd", "处于中间");
            onScrollChangeListener.onScrollChanged(offsetLeft, offsetTop, oldOffsetLeft, oldOffsetTop);
        }
    }

    public void setOnScrollChangeListener(OnScrollChangeListener onScrollChangeListener) {
        this.onScrollChangeListener = onScrollChangeListener;
    }

    public static abstract class OnScrollChangeListener{
        public void onPageBottom(int offsetLeft, int offsetTop, int oldOffsetLeft, int oldOffsetTop){

        }

        public void onPageTop(int offsetLeft, int offsetTop, int oldOffsetLeft, int oldOffsetTop){

        }

        public void onScrollChanged(int offsetLeft, int offsetTop, int oldOffsetLeft, int oldOffsetTop){

        }
    }
}
