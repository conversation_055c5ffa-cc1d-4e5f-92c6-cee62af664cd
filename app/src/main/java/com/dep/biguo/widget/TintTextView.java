package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.drawable.DrawableCompat;

import com.dep.biguo.R;

public class TintTextView extends androidx.appcompat.widget.AppCompatTextView {
    private int tint;

    public TintTextView(@NonNull Context context) {
        super(context);
        init(context, null, 0);
    }

    public TintTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public TintTextView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr){
        if(attrs != null) {
            TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.TintTextView, defStyleAttr, 0);
            tint = a.getColor(R.styleable.TintTextView_drawableTintColor, Color.TRANSPARENT);
            a.recycle();
        }else {
            tint = Color.TRANSPARENT;
        }

        setTint(tint);
    }

    /**给指定的drawable进行着色,并设置宽高
     * @param drawable 指定图标
     * @param tintColor 颜色
     */
    private static Drawable TintDrawable(Drawable drawable, int tintColor){
        if(drawable != null) {
            //若图片本身有设置宽高，则优先选用设置的宽高，否则使用默认宽高，即图片本身的宽高
            int width = drawable.getBounds().right > 0 ? drawable.getBounds().right : drawable.getIntrinsicWidth();
            int height = drawable.getBounds().bottom > 0 ? drawable.getBounds().bottom : drawable.getIntrinsicHeight();

            drawable = DrawableCompat.wrap(drawable).mutate();
            drawable.setBounds(0,0, width, height);
            DrawableCompat.setTint(drawable, tintColor);

            return drawable;
        }
        return null;
    }

    public void setTint(@ColorInt int tint) {
        this.tint = tint;

        Drawable[] drawables = getCompoundDrawablesRelative();
        for(int i=0; i<drawables.length; i++){
            drawables[i] = TintDrawable(drawables[i], tint);
        }
        setCompoundDrawablesRelative(drawables[0], drawables[1], drawables[2], drawables[3]);
    }
}
