package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import com.biguo.utils.util.DisplayHelper;

/**
 * 答题正确率
 */
public class PracticeRateView extends View {

    /**
     * 底色
     */
    private int mBottomColor = Color.BLUE;

    /**
     * 进度条颜色
     */
    private int mProgressColor = Color.RED;

    /**
     * 字体颜色
     */
    private int mTextColor = Color.RED;

    /**
     * 圆环大小
     */
    private int mBorderWidth = 20;

    /**
     * 字体大小
     */
    private int mTextSize = 30;
    /**
     * 百分号大小
     */
    private int mRoteTextSize = 30;

    private Paint mBottomPaint;
    private Paint mProgressPaint;
    private Paint mTextPaint;

    private int progress = 0;

    public PracticeRateView(Context context) {
        this(context, null);
    }

    public PracticeRateView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PracticeRateView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mBottomColor = Color.parseColor("#F1F1F1");
        mProgressColor = Color.parseColor("#FBBA00");
        mTextColor = Color.BLACK;
        mBorderWidth = DisplayHelper.dp2px(context, 7);
        mTextSize = DisplayHelper.dp2px(context, 24);
        mRoteTextSize = DisplayHelper.dp2px(context, 16);

        mBottomPaint = getPaint(mBottomColor);
        mProgressPaint = getPaint(mProgressColor);

        mTextPaint = new Paint();
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setColor(mTextColor);
        mTextPaint.setAntiAlias(true);
    }

    private Paint getPaint(int color) {
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(mBorderWidth);
        paint.setStrokeCap(Paint.Cap.ROUND);
        return paint;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        //宽度高度不一致，取最小值
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);

        setMeasuredDimension(width > height ? height : width, width > height ? height : width);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float centerX = getWidth() / 2;
        float centerY = getHeight() / 2;
        float raduis = getWidth() / 2 - mBorderWidth / 2;

        canvas.drawCircle(centerX, centerY, raduis, mBottomPaint);

        RectF rectF = new RectF(mBorderWidth / 2, mBorderWidth / 2, getWidth() - mBorderWidth / 2, getHeight() - mBorderWidth / 2);
        float sweepAngle = progress * 1.0f / 100 * 360;
        canvas.drawArc(rectF, -90, sweepAngle, false, mProgressPaint);

        drawCenterText(canvas);
    }

    /**
     * 画中间文字
     */
    private void drawCenterText(Canvas canvas) {
        String progressText = progress + "%";
        //文字居中，控件宽度的一半 - 文字宽度的一半 = x的位置
        Rect textRect = new Rect();
        mTextPaint.getTextBounds(progressText, 0, progressText.length(), textRect);

        float x = getWidth() / 2 - textRect.width() / 2;

//        //算出基线的位置
//        Paint.FontMetricsInt metricsInt = mTextPaint.getFontMetricsInt();
//        float dy = (metricsInt.bottom - metricsInt.top) / 2;
//        float baseLine = getHeight() / 2 + dy;

        mTextPaint.setTextSize(mTextSize);
        canvas.drawText(progress+"", x, getTextBaseline(), mTextPaint);
        //绘制百分号
        x = x + mTextPaint.measureText(progress+"");
        mTextPaint.setTextSize(mRoteTextSize);
        canvas.drawText("%", x, getTextBaseline() + DisplayHelper.dp2px(getContext(), 3), mTextPaint);
    }

    private float getTextBaseline() {
        // 获取文字的Metrics 用来计算基线
        Paint.FontMetricsInt fontMetrics = mTextPaint.getFontMetricsInt();
        //获取文字高度
        int fontTotalHeight = fontMetrics.bottom - fontMetrics.top;
        // 计算基线到中心点的位置
        int offY = fontTotalHeight / 2 - fontMetrics.bottom;
        //计算基线位置
        int baseline = getMeasuredHeight() / 2 + offY;
        return baseline;
    }

    public void setProgress(int progress) {
        this.progress = progress;
        invalidate();
    }

}
