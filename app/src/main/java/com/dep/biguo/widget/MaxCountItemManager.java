package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.google.android.flexbox.AlignItems;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexLine;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;

import java.util.Arrays;
import java.util.List;

public class MaxCountItemManager extends FlexboxLayoutManager {
    //计算得到的变量
    private int minHorizontalMargin;
    private int minVerticalMargin;
    private int itemWidth;
    private int lineCount;

    //可以设置的变量
    private int spaceHorizontal;
    private int spaceVertical;
    private int itemStandardWidth;
    private int itemStandardHeight;
    private boolean isKeepScale;
    private OnLineCountListener onLineCountListener;

    public MaxCountItemManager(Context context) {
        super(context);
        init();
    }

    public MaxCountItemManager(Context context, int flexDirection) {
        super(context, flexDirection);
        init();
    }

    public MaxCountItemManager(Context context, int flexDirection, int flexWrap) {
        super(context, flexDirection, flexWrap);
        init();
    }

    public MaxCountItemManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init(){
        setFlexDirection(FlexDirection.ROW);//设置主轴排列方式
        setFlexWrap(FlexWrap.WRAP);//设置是否换行
        setAlignItems(AlignItems.STRETCH);
    }

    /**
     * 计算每个item的实际宽度，并记录一行可以放多少个item
     */
    private void initComputer(int width) {
        if (itemStandardWidth == 0) return;

        //计算出在标准宽度下，最多一行最多可以放几个item
        lineCount = Math.max(1, width / itemStandardWidth);
        if (onLineCountListener != null) {
            onLineCountListener.onLineCount(lineCount);
        }
        //水平方向的间距的数量
        int spaceCount = lineCount - 1;
        //裁掉每个item之间必须占用的间距之后，实际可用的总宽度
        int realWidth = width - (spaceCount * spaceHorizontal);
        //计算出每个item实际要占用的宽度
        itemWidth = realWidth / lineCount;

        //当间距个数为0时，就代表没有间距，所以直接设置为0
        if (spaceCount == 0) {
            minHorizontalMargin = spaceHorizontal;
        } else {
            //预设最小边距不足以填充剩余的空间，就把剩余的空间平均分配到每个边距上
            minHorizontalMargin = Math.max(spaceHorizontal, (width - itemWidth * lineCount ) / spaceCount);
        }

        //minHorizontalMargin = spaceHorizontal;
        minVerticalMargin = spaceVertical;
    }


    @Override
    public int getChildWidthMeasureSpec(int widthSpec, int padding, int childDimension) {
        //在没有手动设置标准宽度时，使用测量出来的
        if(itemStandardWidth == 0){
            itemStandardWidth = childDimension;
        }

        //开始计算各项初始值
        initComputer(getWidth());

        //如果没有计算出实际的宽度，就使用测量的
        if(itemWidth == 0){
            return super.getChildWidthMeasureSpec(widthSpec, padding, childDimension);
        }
        //有计算出实际宽度，就使用计算出来的宽度
        return super.getChildWidthMeasureSpec(widthSpec, padding, itemWidth);
    }

    @Override
    public int getChildHeightMeasureSpec(int heightSpec, int padding, int childDimension) {//在没有手动设置标准宽度时，使用测量出来的
        if(itemStandardHeight == 0){
            itemStandardHeight = childDimension;
        }
        //实际占用高度同比放大
        float scale = isKeepScale ? (1f * itemWidth / itemStandardWidth) : 1f;
        return super.getChildHeightMeasureSpec(heightSpec, padding, (int) (itemStandardHeight * scale));
    }

    @Override
    public int getLeftDecorationWidth(@NonNull View child) {
        if(lineCount == 0) return super.getLeftDecorationWidth(child);

        int leftMargin = 0;
        int position = getPosition(child);
        //只要不是每一行的开头，都需要左边距
        if(position % lineCount != 0){
            leftMargin = minHorizontalMargin;
        }
        return super.getLeftDecorationWidth(child) + leftMargin;
    }

    @Override
    public int getTopDecorationHeight(@NonNull View child) {
        if(lineCount == 0) return super.getTopDecorationHeight(child);

        int topMargin = 0;
        int position = getPosition(child);
        //从第二行开始，每个都需要上边距
        if(position / lineCount > 0){
            topMargin = minVerticalMargin;
        }
        return super.getTopDecorationHeight(child) + topMargin;
    }

    /**设置水平方向的item之间的间距，说明白点就是从左往右看item的间距
     * @param spaceHorizontal 间距
     */
    public void setSpaceHorizontal(int spaceHorizontal) {
        this.spaceHorizontal = spaceHorizontal;
    }

    /**设置垂直方向的item之间的间距，说明白点就是从上往下看item的间距
     * @param spaceVertical 间距
     */
    public void setSpaceVertical(int spaceVertical) {
        this.spaceVertical = spaceVertical;
    }

    /**设置标准尺寸，以及高度是否与宽度保持同比例
     * <p>manager会根据这个宽度计算一行可以放下多少个item</p>
     * @param itemStandardWidth 标准宽度
     * @param isKeepScale 高度是否同比放大缩小
     */
    public void setItemStandard(int itemStandardWidth, int itemStandardHeight, boolean isKeepScale) {
        this.itemStandardWidth = itemStandardWidth;
        this.itemStandardHeight = itemStandardHeight;
        this.isKeepScale = isKeepScale;
    }

    /**设置一行的item个数变化时的监听对象
     * @param onLineCountListener 监听对象
     */
    public void setOnLineCountListener(OnLineCountListener onLineCountListener) {
        this.onLineCountListener = onLineCountListener;
    }

    /**当一行的个数有变化时回调
     *
     */
    public interface OnLineCountListener{
        void onLineCount(int lineCount);
    }
}
