package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.SweepGradient;
import androidx.annotation.FloatRange;

import android.util.AttributeSet;
import android.view.View;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;

public class StudyRateView extends View {
    //画布
    private RectF rectF;
    private RectF pointRectF;
    //进度条画笔
    private Paint mProgressPaint;
    //进度条的端点的画笔
    private Paint mEndpointPaint;

    //圆环直径
    private int mBorderWidth;

    //进度条的渐变色
    private SweepGradient sweepGradient;
    //进度条颜色
    private int mStartProgressColor = Color.parseColor("#FFA5A5");
    private int mEndProgressColor = Color.parseColor("#FE3C41");

    //进度值
    private float progress = 0f;

    public StudyRateView(Context context) {
        this(context, null);
    }

    public StudyRateView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public StudyRateView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mBorderWidth = DisplayHelper.dp2px(context, 6);

        mProgressPaint = getPaint();
        mEndpointPaint = new Paint();
        rectF = new RectF();
        pointRectF = new RectF();

        //进度条的起点是从第二象限开始的，因此反向旋转90度
        setRotation(-90);
    }

    private Paint getPaint() {
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(mBorderWidth);
        paint.setStrokeCap(Paint.Cap.ROUND);
        return paint;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        //宽度高度不一致，取最小值
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);

        setMeasuredDimension(Math.min(width, height), Math.min(width, height));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        updateSweep();

        //进度条宽度的半径
        float r = mBorderWidth / 2f;
        //设置尺寸大小
        rectF.set(r, r, getWidth() - r, getHeight() - r);

        //根据进度计算进度条的弧线长度
        float sweepAngle = progress / 100f * 360;

        //绘制进度
        drawProgress(canvas, sweepAngle);

        //绘制起始端点
        drawStartPoint(canvas, sweepAngle);


        //进度快满时，绘制结束端点
        if(sweepAngle > 300) {
            drawEndPoint(canvas, sweepAngle);
        }
    }

    /**进度条渐变色，需要获取到控件尺寸之后再初始化，尺寸影响到画的弧度长度
     *
     */
    private void updateSweep(){
        if(sweepGradient == null){
            float centerX = getWidth() / 2f;
            float centerY = getHeight() / 2f;
            int[] colors = {mStartProgressColor, mEndProgressColor};
            float[] positions = {0f, progress / 100f};
            sweepGradient = new SweepGradient(centerX,centerY, colors, positions);
            mProgressPaint.setShader(sweepGradient);
        }
    }

    /**绘制进度条
     * @param canvas 画布
     * @param sweepAngle 进度（弧度值）
     */
    private void drawProgress(Canvas canvas, float sweepAngle){
        canvas.drawArc(rectF, 0, sweepAngle, false, mProgressPaint);
    }

    /**绘制起点的端点
     * @param canvas 画布
     * @param sweepAngle 进度（弧度值）
     */
    private void drawStartPoint(Canvas canvas, float sweepAngle){
        mEndpointPaint.setColor(mStartProgressColor);
        mEndpointPaint.setStrokeCap(Paint.Cap.ROUND);
        mEndpointPaint.setStrokeWidth(0);
        mEndpointPaint.setStyle(Paint.Style.FILL);
        pointRectF.set(getWidth() - mBorderWidth, (getHeight() - mBorderWidth) / 2f, getWidth(), getHeight() / 2f + mBorderWidth / 2f);
        float angle = sweepAngle == 0f ? 360f : -180f;
        canvas.drawArc(pointRectF, 0, angle, true, mEndpointPaint);
    }

    /**绘制终点的端点
     * @param canvas 画布
     * @param startAngle 进度（弧度值）
     */
    private void drawEndPoint(Canvas canvas, float startAngle){
        mEndpointPaint.setColor(mEndProgressColor);
        mEndpointPaint.setStrokeCap(Paint.Cap.ROUND);
        mEndpointPaint.setStrokeWidth(mBorderWidth);
        mEndpointPaint.setStyle(Paint.Style.STROKE);
        canvas.drawArc(rectF, startAngle, 0.0001f, false, mEndpointPaint);
    }

    /**设置进度值
     * @param progress 进度值
     */
    public void setProgress(@FloatRange(from = 0f, to = 100f) float progress) {
        if(progress < 0f){
            this.progress = 0f;
        }else if(progress > 100f){
            this.progress = 100f;
        }else {
            this.progress = progress;
        }
        //清空渐变色，在用的时候重新创建渐变色
        sweepGradient = null;

        invalidate();
    }
}