package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class HorizontalProgressView extends View {
    private Paint paint;

    private int progressBg;
    private int progressColor;
    private int progressHeight;
    private float progress = 1f;

    public HorizontalProgressView(Context context) {
        super(context);
        init(context, null, 0);
    }

    public HorizontalProgressView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public HorizontalProgressView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);

    }

    public void init(Context context, AttributeSet attrs, int defStyleAttr){
        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.HorizontalProgressView, defStyleAttr, 0);
        progressBg = a.getInt(R.styleable.HorizontalProgressView_progressBg, getResources().getColor(R.color.gray));
        progressColor = a.getInt(R.styleable.HorizontalProgressView_progressColor, getResources().getColor(R.color.theme));
        progressHeight = a.getDimensionPixelSize(R.styleable.HorizontalProgressView_progressHeight,  4);
        progress = a.getFloat(R.styleable.HorizontalProgressView_progress, 0);
        a.recycle();

        checkProgress();

        paint = new Paint();
        paint.setStrokeWidth(progressHeight);
        paint.setAntiAlias(true);
        paint.setStrokeCap(Paint.Cap.ROUND);
    }


    @Override
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float startX = paint.getStrokeWidth()/2f;
        float startY = getHeight()/2f;
        float stopX = getWidth() - startX;
        float stopY = getHeight()/2f;
        paint.setColor(progressBg);
        canvas.drawLine(startX,startY,stopX,stopY, paint);
        if(progress > 0f) {
            //进度条的长度（去除掉左右两端的圆角，圆角由画笔自动补充的，圆角大小由线条粗细决定）
            float progressWidth = getWidth() - paint.getStrokeWidth();
            //当前进度百分比
            float progressRate = progressWidth * (progress / 100);
            //进度的结束位置
            float currentProgressStopX = progressRate + startX;
            paint.setColor(progressColor);
            canvas.drawLine(startX, startY, currentProgressStopX , stopY, paint);
        }
    }

    /**
     * 检查数据是否超出边界值
     */
    private void checkProgress(){
        if(progress < 0){
            progress = 0;
        }else if(progress > 100f){
            progress = 100f;
        }
    }

    public void setProgress(float progress) {
        this.progress = progress;
        checkProgress();
        invalidate();
    }
}
