package com.dep.biguo.widget;

import android.content.Context;
import android.util.AttributeSet;

/**
 * 正方形ImageView
 * Created by JX on 2018/1/22.
 */

public class RectImageView extends androidx.appcompat.widget.AppCompatImageView {

    public RectImageView(Context context) {
        super(context);
    }

    public RectImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public RectImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        int measuredWidth = getMeasuredWidth();
        int measuredHeight = getMeasuredHeight();

        if (measuredWidth > measuredHeight) {
            measuredHeight = measuredWidth;
        } else {
            measuredWidth = measuredHeight;
        }

        setMeasuredDimension(measuredWidth, measuredHeight);
    }
}
