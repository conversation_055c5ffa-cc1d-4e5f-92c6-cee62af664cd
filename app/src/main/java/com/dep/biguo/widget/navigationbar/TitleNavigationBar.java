package com.dep.biguo.widget.navigationbar;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.core.content.ContextCompat;
import androidx.appcompat.widget.Toolbar;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.StatusBarHelper;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/21
 * @Description: 标题栏实现类
 */
public class TitleNavigationBar<D extends TitleNavigationBar.Builder.DefaultNavigationParams> extends AbsNavigationBar<TitleNavigationBar.Builder.DefaultNavigationParams> {

    public TitleNavigationBar(D params) {
        super(params);
    }

    @Override
    public int bindLayoutId() {
        return R.layout.navigationbar;
    }

    @Override
    public void applyView() {
        //绑定效果

        //标题可滚动
        setTextMarquee(findViewById(R.id.navigation_tv_title), getParams().isTitleTextScroll);

        //设置文字
        setText(R.id.navigation_tv_title, getParams().mTitle);
        setText(R.id.navigation_tv_left, getParams().mLeftText);
        setText(R.id.navigation_tv_right, getParams().mRightText);

        //设置图标
        setIcon(R.id.navigation_iv_left, getParams().mLeftIcon);
        setIcon(R.id.navigation_iv_right, getParams().mRightIcon);

        //设置颜色
        setColor(R.id.navigation_tv_title, getParams().mTitleTextColor);
        setColor(R.id.navigation_tv_left, getParams().mLeftTextColor);
        setColor(R.id.navigation_tv_right, getParams().mRightTextColor);
        setColor(R.id.navigatioinBar, getParams().mBackgroundColor);
        setBackgroundResource(R.id.navigatioinBar,getParams().mBackgroundDrawable);

        //设置点击事件
        setOnClickListener(R.id.navigation_iv_left, getParams().mLeftClickListener);
        setOnClickListener(R.id.navigation_tv_left, getParams().mLeftClickListener);
        setOnClickListener(R.id.navigation_tv_right, getParams().mRightClickListener);
        setOnClickListener(R.id.navigation_iv_right, getParams().mRightClickListener);

        //设置层次
        setElevation(getParams().mElevation);

        //通过增加自身高度，再设置一个状态栏高度的topPadding，解决被系统状态栏覆盖的问题
        Toolbar toolbar = getParams().mParent.findViewById(R.id.navigatioinBar);
        ViewGroup.LayoutParams params = toolbar.getLayoutParams();
        params.height += DisplayHelper.getStatusBarHeight(getParams().mContext);
        toolbar.setPadding(0, DisplayHelper.getStatusBarHeight(getParams().mContext), 0, 0);
        toolbar.setLayoutParams(params);
    }

    /**全屏时设置隐藏
     * @param visibility
     */
    public void setVisibility(int visibility){
        Toolbar toolbar = getParams().mParent.findViewById(R.id.navigatioinBar);
        toolbar.setVisibility(visibility);
    }

    /**
     * 设置背景图片
     *
     * @param resId R.id
     * @param icon  图片资源
     */
    public void setBackgroundResource(int resId, @DrawableRes int icon) {
        View view = findViewById(resId);
        if (icon == 0) {
        } else {
            if(getParams().mContext instanceof Activity) {
                StatusBarHelper.setStatusBarColor((Activity) getParams().mContext, ContextCompat.getColor(getParams().mContext, R.color.tran));
            }
            view.setBackgroundResource(icon);
        }
    }

    /**
     * 设置标题可滚动
     *
     * @param textView
     */
    public static void setTextMarquee(TextView textView, boolean isScroll) {
        if (textView != null) {
            textView.setEllipsize(isScroll ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
            textView.setSingleLine(true);
            textView.setSelected(true);
            textView.setFocusable(true);
            textView.setFocusableInTouchMode(true);
        }
    }


    public static class Builder extends AbsNavigationBar.Builder {

        DefaultNavigationParams P;

        public Builder(Context context, ViewGroup parent) {
            super(context, parent);
            P = new DefaultNavigationParams(context, parent);
        }

        public Builder(Context context) {
            super(context, null);
            P = new DefaultNavigationParams(context, null);
        }

        public Builder setBackgroundColor(@ColorInt int color) {
            P.mBackgroundColor = color;
            return this;
        }

        public Builder setTitleTextColor(@ColorInt int color) {
            P.mTitleTextColor = color;
            return this;
        }

        public Builder setRightTextColor(@ColorInt int color) {
            P.mRightTextColor = color;
            return this;
        }

        public Builder setLeftTextColor(@ColorInt int color) {
            P.mLeftTextColor = color;
            return this;
        }

        public Builder setTitle(CharSequence text) {
            P.mTitle = text;
            return this;
        }

        public Builder setLeftText(CharSequence text) {
            P.mLeftText = text;
            return this;
        }

        public Builder setRightText(CharSequence text) {
            P.mRightText = text;
            return this;
        }

        public Builder setLeftIcon(@DrawableRes int resId) {
            P.mLeftIcon = resId;
            return this;
        }

        public Builder setRightIcon(@DrawableRes int resId) {
            P.mRightIcon = resId;
            return this;
        }

        public Builder setLeftClickListener(View.OnClickListener listener) {
            P.mLeftClickListener = listener;
            return this;
        }

        public Builder setRightClickListener(View.OnClickListener listener) {
            P.mRightClickListener = listener;
            return this;
        }
        public Builder setBackgroundDrawable(@DrawableRes int resId) {
            P.mBackgroundDrawable = resId;
            return this;
        }

        public Builder setElevation(float elevation) {
            P.mElevation = elevation;
            return this;
        }

        public Builder setTitleTextScroll(boolean isTitleTextScroll) {
            P.isTitleTextScroll = isTitleTextScroll;
            return this;
        }

        public DefaultNavigationParams getP() {
            return P;
        }

        public void setP(DefaultNavigationParams p) {
            P = p;
        }

        @Override
        public TitleNavigationBar builder() {
            return new TitleNavigationBar(P);
        }

        public static class DefaultNavigationParams extends AbsNavigationParams {

            public @ColorInt
            int mBackgroundColor;
            public @DrawableRes
            int mBackgroundDrawable;
            public @ColorInt
            int mRightTextColor;
            public @ColorInt
            int mLeftTextColor;
            public @ColorInt
            int mTitleTextColor;
            public CharSequence mTitle;
            public CharSequence mLeftText;
            public CharSequence mRightText;
            //默认返回按钮
            public @DrawableRes
            int mLeftIcon = R.drawable.arrow_back;
            public @DrawableRes
            int mRightIcon;
            float mElevation;//用于拔升自身在布局中的层次，当调用父类的setFollowScrollBackground()方法时，必须使用该属性
            boolean isTitleTextScroll;//是否使用跑马灯，即文字滚动效果

            public View.OnClickListener mRightClickListener;

            //默认的返回效果
            public View.OnClickListener mLeftClickListener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ((Activity) mContext).finish();
                }
            };

            public DefaultNavigationParams(Context context, ViewGroup parent) {
                super(context, parent);
            }
        }
    }

    public void setRightText(CharSequence text) {
        setText(R.id.navigation_tv_right, text);
    }

    public void setRightText(@ColorInt int color) {
        setColor(R.id.navigation_tv_right, color);
    }

    public void setRightIcon(@DrawableRes int icon) {
        setIcon(R.id.navigation_iv_right, icon);
    }

    public void setTitleText(CharSequence text) {
        setText(R.id.navigation_tv_title, text);
    }
}