package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class CKUserItemLayout extends LinearLayout {

    private Paint mPaint;
    private Context mContext;

    public CKUserItemLayout(Context context) {
        this(context, null);
    }

    public CKUserItemLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CKUserItemLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        if (getBackground() != null)
            mPaint.setColor(((ColorDrawable) getBackground()).getColor());
        setBackgroundColor(0);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CKUserItemLayout, defStyleAttr, 0);
        Drawable icon = a.getDrawable(R.styleable.CKUserItemLayout_topImg);
        String text = a.getString(R.styleable.CKUserItemLayout_botText);
        a.recycle();

        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER);

        addView(getImageView(icon));
        addView(getTextView(text));
    }

    private ImageView getImageView(Drawable icon) {
        ImageView iv = new ImageView(mContext);
        iv.setImageDrawable(icon);
        iv.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        return iv;
    }

    private TextView getTextView(String text) {
        TextView tv = new TextView(mContext);
        MarginLayoutParams params = new MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        int px = DisplayHelper.dp2px(getContext(), (int) getResources().getDimension(R.dimen.dp_5));
        params.topMargin = px;
        tv.setLayoutParams(params);
        tv.setTextColor(ContextCompat.getColor(mContext, R.color.tblack2));
        tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        tv.setText(text);
        return tv;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        RectF rect = new RectF(0, 0, getMeasuredWidth(), getMeasuredHeight());
        int px = DisplayHelper.dp2px(getContext(), (int) getResources().getDimension(R.dimen.dp_5));
        canvas.drawRoundRect(rect, px, px, mPaint);
    }
}
