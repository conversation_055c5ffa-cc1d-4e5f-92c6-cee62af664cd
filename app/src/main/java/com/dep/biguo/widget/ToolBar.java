package com.dep.biguo.widget;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import androidx.annotation.IdRes;
import androidx.annotation.IntDef;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.biguo.utils.util.AppUtil;
import com.google.android.material.appbar.AppBarLayout;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.mmkv.UserCache;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Arrays;

public class ToolBar{
    private LinearLayout[] layout = new LinearLayout[3];
    private Builder builder;
    private View view;

    private Paint paint;

    public ToolBar(Builder builder) {
        this.builder = builder;
        paint = new Paint();
        try {
            createAndBindView();
        }catch (Exception e){
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
    }

    public View getLayoutView() {
        return view;
    }

    /**将自身添加到对应的布局中
     * 不指定布局，则添加到context所在的父布局内，且成为第一个控件
     *
     */
    private void createAndBindView() {
        if(!(builder.context instanceof Activity)) return;

        if (builder.parent == null) {
            //处理AppCompatActivity ，未处理Activity
            ViewGroup activityRoot = (ViewGroup) ((Activity) builder.context).getWindow().getDecorView();
            builder.parent = (ViewGroup) activityRoot.getChildAt(0);
            //适配小屏或分屏模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && !AppUtil.isFillScreen(builder.context)) {
                if(!(builder.parent instanceof LinearLayout)){
                    builder.parent = (ViewGroup) builder.parent.getChildAt(0);
                }
            }
            //当获取到的builder.parent依然为空，则结束
            if(builder.parent == null) return;
        }

        view = LayoutInflater.from(getContext()).inflate(R.layout.tool_bar_view, builder.parent, false);
        if(builder.parent.indexOfChild(view) < 0) {
            //向builder.parent指向的对象添加新的控件
            builder.parent.addView(view, 0);
        }

        init();

        setParamsLayout();

        //背景颜色
        setBackgroundColor(getContext().getResources().getColor(builder.backgroundColorRes));
        view.getLayoutParams().height = getMeasureHeight();
        screenSizeChange();
    }

    public void screenSizeChange(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && AppUtil.isFillScreen(builder.context)) {
            //通过增加自身高度，再设置一个状态栏高度的topPadding，解决被系统状态栏覆盖的问题
            view.setPadding(0, DisplayHelper.getStatusBarHeight(getContext()), 0, 0);
        }else {
            view.setPadding(0, 0, 0, 0);
        }
        view.getLayoutParams().height = getMeasureHeight();
        view.setMinimumHeight(DisplayHelper.dp2px(builder.context, 44));
    }

    public void showViewName(View view, int layer){
        LogUtil.d("ddddd", getTab(layer)+view.getClass().getName());
        if(view instanceof ViewGroup){
            for(int i=0;i<((ViewGroup)view).getChildCount();i++){
                showViewName(((ViewGroup)view).getChildAt(i), layer+1);
            }
        }
    }

    public String getTab(int layer){
        String tab = "";
        for(int i=0;i<layer;i++){
            tab += "\t";
        }

        return tab;
    }

    /**重新设置布局
     *
     */
    private void setParamsLayout(){
        if(builder.parent.getChildCount() > 1) {
            View brotherView = builder.parent.getChildAt(1);
            brotherView.measure(0,0);
            view.measure(0,0);
            if (view.getLayoutParams() instanceof ConstraintLayout.LayoutParams && !builder.isAlignScreenTop) {//协调者布局
                //重新为toolbar设置布局
                ConstraintLayout.LayoutParams viewParams = (ConstraintLayout.LayoutParams) view.getLayoutParams();
                viewParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                viewParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                viewParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                //重新为其同级的控件设置对齐目标
                ConstraintLayout.LayoutParams brotherParams = (ConstraintLayout.LayoutParams) brotherView.getLayoutParams();
                brotherParams.topToBottom = view.getId();
                brotherParams.topToTop = -1;

            }else if(view.getLayoutParams() instanceof FrameLayout.LayoutParams && !builder.isAlignScreenTop){//帧布局
                //帧布局使用margin控制就足够了
                FrameLayout.LayoutParams brotherParams = (FrameLayout.LayoutParams) brotherView.getLayoutParams();
                brotherParams.topMargin = view.getMeasuredHeight();

            }else if(view.getLayoutParams() instanceof RelativeLayout.LayoutParams && !builder.isAlignScreenTop){//相对布局
                //重新为其同级的控件设置对齐目标
                RelativeLayout.LayoutParams brotherParams = (RelativeLayout.LayoutParams) brotherView.getLayoutParams();
                brotherParams.addRule(RelativeLayout.BELOW, view.getId());

            }
        }
    }

    /**设置控件的属性
     *
     */
    public void init(){
        layout[Builder.LEFT] = view.findViewById(R.id.leftLayout);
        layout[Builder.TITLE] = view.findViewById(R.id.titleLayout);
        layout[Builder.RIGHT] = view.findViewById(R.id.rightLayout);
        
        initChildView(Builder.LEFT);
        initChildView(Builder.TITLE);
        initChildView(Builder.RIGHT);

        for(int visibility : builder.visibility) {
            if (visibility == View.GONE) return;
        }

        requestLayout();
    }

    /**重新排版
     *
     */
    public void requestLayout(){
        //给titleLayout设置左内边距或右内边距，使得titleLayout中的内容一直居中
        layout[Builder.LEFT].measure(0,0);
        layout[Builder.RIGHT].measure(0,0);
        int disparity = layout[Builder.LEFT].getMeasuredWidth() - layout[Builder.RIGHT].getMeasuredWidth();
        layout[Builder.TITLE].setPadding(disparity < 0 ? Math.abs(disparity) : 0, 0, Math.max(disparity, 0), 0);
    }

    
    public void initChildView(int target){
        if(builder.viewId[target] > 0){
            layout[target].removeAllViews();
            builder.view[target] = LayoutInflater.from(builder.context).inflate(builder.viewId[target], layout[target], true);
            builder.view[target].setOnClickListener(builder.onClickListener[target]);

        }else if(builder.view[target] != null){
            layout[target].removeAllViews();
            layout[target].addView(builder.view[target]);
            builder.view[target].setOnClickListener(builder.onClickListener[target]);

        }else {
            TextView childView = (TextView) layout[target].getChildAt(0);
            childView.setVisibility(builder.visibility[target]);
            if(builder.visibility[target] == View.GONE) return;

            refreshText(target);
        }
    }

    /**刷新文本
     * @param target
     */
    private void refreshText(int target){
        TextView childView = (TextView) layout[target].getChildAt(0);
        childView.setOnClickListener(builder.onClickListener[target]);
        childView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, builder.textSize[target]);
        childView.setTextColor(getResources().getColor(builder.textColor[target]));
        childView.setMaxLines(builder.textMaxLine[target]);
        childView.setText(builder.text[target]);
        childView.setCompoundDrawablesRelative(getDrawable(builder.drawablesRes[target][0]), getDrawable(builder.drawablesRes[target][1]), getDrawable(builder.drawablesRes[target][2]), getDrawable(builder.drawablesRes[target][3]));
        childView.setCompoundDrawablePadding(DisplayHelper.dp2px(getContext(), builder.drawablePadding[target]));
        if(builder.textMaxLength[target] > 0) {//根据指定显示 中文文字长度 和 图片占用长度，设置控件的长度
            paint.setTextSize(DisplayHelper.dp2px(getContext(), builder.textSize[target]));
            //根据设定文字个数来显示文字长度
            int drawableWidth = (int) (paint.measureText("测") * builder.textMaxLength[target]);
            //计算图片在控件水平方向占用的长度，不考虑上下方向同时存在图片时的叠加的值，因为不需要
            for(Drawable drawable : childView.getCompoundDrawablesRelative()){
                drawableWidth += (drawable!=null ? drawable.getMinimumWidth() : 0);
            }
            //设置控件宽度，文字宽度和所有图片宽度的总和 + 内边距
            childView.getLayoutParams().width = drawableWidth + DisplayHelper.dp2px(getContext(), 10);
        }
    }

    /**设置背景随指定控件滚动距离变化透明度
     * 指定控件往下滚动时，不出现自身与指定控件叠加在一起的情况
     * @param appBarLayout 仅可以接受{@link AppBarLayout}
     */
    public ToolBar setFollowScrollBackground(AppBarLayout appBarLayout, Builder.FollowScrollListener followScrollListener){
        if(builder.context instanceof Activity){
            //设置状态栏背景颜色透明
            StatusBarHelper.setStatusBarColor((Activity) builder.context, ContextCompat.getColor(builder.context, R.color.tran));
        }
        view.setBackgroundColor(Color.TRANSPARENT);
        view.setElevation(1);
        int paddingTop = (builder.isAlignScreenTop ? 0 : getMeasureHeight()) + appBarLayout.getPaddingTop();
        appBarLayout.setPadding(appBarLayout.getPaddingLeft(), paddingTop, appBarLayout.getPaddingRight(), appBarLayout.getPaddingBottom());
        appBarLayout.addOnOffsetChangedListener((appBarLayout1, y) -> {
            int effectiveRange = Math.min(-y, getHeight());//y为负数，需要变为正数，选取有效范围 0 - getHeight()之间
            float changeRate =1f * effectiveRange / getHeight();//变化了百分之几，除法运算有小数，乘以-1f是为了保留小数，从而提高精度
            int alpha = (int) (changeRate * 255);
            int rgb = UserCache.isDayNight() ? 41 : 255;
            //View.setBackgroundColor()方法在鸿蒙4.0上，设置argb值时，a为0的情况下，偶发出现不透明，但鸿蒙3.0正常
            view.setBackground(new ColorDrawable(Color.argb(Math.min(alpha, 255), rgb, rgb, rgb)));
            if(followScrollListener != null){
                followScrollListener.onFollowScroll(this, effectiveRange, changeRate);
            }
        });
        return this;
    }

    /**设置背景随指定控件滚动距离变化透明度
     * 指定控件往下滚动时，不出现自身与指定控件叠加在一起的情况
     * @param scrollView 仅可以接受{@link NestedScrollView}
     */
    public ToolBar setFollowScrollBackground(NestedScrollView scrollView, Builder.FollowScrollListener followScrollListener){
        if(builder.context instanceof Activity){
            //设置状态栏背景颜色透明
            StatusBarHelper.setStatusBarColor((Activity) builder.context, ContextCompat.getColor(builder.context, R.color.tran));
        }
        view.setBackground(new ColorDrawable(Color.TRANSPARENT));
        view.setElevation(1);
        //以滚动布局中的子布局下的第一个控件作为目标，让出一个导航栏的高度，其余控件跟随这个控件一起移动
        ViewGroup firstLayout = (ViewGroup) scrollView.getChildAt(0);
        int paddingTop = getMeasureHeight() + firstLayout.getPaddingTop();//(builder.isAlignScreenTop ? 0 : getMeasureHeight()) + firstLayout.getPaddingTop();
        firstLayout.getChildAt(0).setPadding(scrollView.getPaddingLeft(),paddingTop,scrollView.getPaddingRight(),scrollView.getPaddingBottom());

        scrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, i, y, i2, i3) -> {
            int effectiveRange = Math.min(y, getHeight());//选取有效范围 0 - getHeight()之间
            float changeRate = 1f * effectiveRange / getHeight();//变化了百分之几，除法运算有小数，乘以-1f是为了保留小数，从而提高精度
            int alpha = (int) (changeRate * 255);
            int rgb = UserCache.isDayNight() ? 41 : 255;
            //View.setBackgroundColor()方法在鸿蒙4.0上，设置argb值时，a为0的情况下，偶发出现不透明，但鸿蒙3.0正常
            view.setBackground(new ColorDrawable(Color.argb(Math.min(alpha, 255), rgb, rgb, rgb)));
            if(followScrollListener != null){
                followScrollListener.onFollowScroll(this, effectiveRange, changeRate);
            }
        });
        return this;
    }


    /**返回测量的高度
     * @return
     */
    public int getMeasureHeight(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && AppUtil.isFillScreen(builder.context)) {
            return Math.max(DisplayHelper.dp2px(builder.context, 44), view.getMinimumHeight() + DisplayHelper.getStatusBarHeight(getContext()));
        }else {
            return Math.max(DisplayHelper.dp2px(builder.context, 44), view.getMinimumHeight());
        }
    }

    /**返回高度
     * @return
     */
    public int getHeight(){
        return view.getHeight();
    }

    public <T extends View> T getViewById(@IdRes int id) {
        return view.findViewById(id);
    }

    public TextView getViewByTarget(@ENUM_TYPE int target){
        if(target == Builder.LEFT){
            return view.findViewById(R.id.leftView);
        }else if(target == Builder.TITLE){
            return view.findViewById(R.id.titleView);
        }else if(target == Builder.RIGHT){
            return view.findViewById(R.id.rightView);
        }
        return null;
    }

    /**根据图片资源ID返回drawable对象
     * @param resId
     * @return
     */
    public Drawable getDrawable(int resId){
        if(resId == 0) return null;
        Drawable drawable = getResources().getDrawable(resId);
        drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
        return drawable;
    }


    public void setBackgroundColor(int color) {
        view.setBackgroundColor(color);
    }

    public Resources getResources(){
        return getContext().getResources();
    }

    public Context getContext(){
        return builder.context;
    }

    public static class Builder{
        public static final int LEFT = 0;
        public static final int TITLE = 1;
        public static final int RIGHT = 2;
        private ToolBar bindView;//持有ToolBarView对象，方便在build后再次设置属性

        private int target;//指向当前正在设置控件
        private Context context;//用于创建ToolBarView
        private ViewGroup parent;//ToolBarView所在的父布局，当为null时，将使用context获取父布局
        private int backgroundColorRes = R.color.white;//改属性针对的是ToolBarView
        private boolean isAlignScreenTop = false;//是否沉浸

        //自定义控件
        private View[] view = new View[3];//用于接收自定义左边的控件
        private int[] viewId = new int[3];//用于接收自定义左边的控件ID

        //以下所有属性，当所属控件为自定义控件时，将会失效
        //文字
        private CharSequence[] text = new CharSequence[]{"","",""};
        //指定控件中文字符的显示个数, -1表示不设置
        private int[] textMaxLength= new int[]{0, 0, 0};
        //指定控件中文字最大显示行数
        private int[] textMaxLine= new int[]{1, 1, 1};
        //文字大小,单位是dp
        private int[] textSize = new int[]{12,16,12};
        //文字颜色
        private int[] textColor = new int[]{R.color.tblack2, R.color.tblack, R.color.tblack2};
        //文字对应各个方向上的图片
        private int[][] drawablesRes = new int[3][4];
        //文字与图片的间距,单位是dp
        private int[] drawablePadding = new int[]{4,4,4};
        //隐藏控件
        private int[] visibility = {View.VISIBLE, View.VISIBLE, View.VISIBLE};
        //每个控件的的点击事件,初始化第一个对象是返回事件
        private View.OnClickListener[] onClickListener = {new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(context instanceof Activity){
                    ((Activity)context).finish();
                }
            }
        }, null, null};


        /**添加到当前context所在父布局下，且成为第一个控件
         * @param context
         */
        public Builder(Context context) {
            this.context = context;
            drawablesRes[0][0] = R.drawable.arrow_back;
        }

        /**添加到指定的布局内
         * @param context 上下文
         * @param parent 指定的布局
         */
        public Builder(Context context, ViewGroup parent) {
            this.context = context;
            this.parent = parent;
            drawablesRes[0][0] = R.drawable.arrow_back;
        }

        public Builder setBackgroundColorRes(int backgroundColorRes) {
            this.backgroundColorRes = backgroundColorRes;
            return this;
        }

        public Builder setAlignScreenTop(boolean alignScreenTop) {
            isAlignScreenTop = alignScreenTop;
            return this;
        }

        /**切换当前正在设置的控件
         * @param target
         * @return
         */
        public Builder setTarget(@ENUM_TYPE int target){
            this.target = target;
            return this;
        }

        /**创建ToolBarView
         * @return
         */
        public ToolBar build(){
            bindView = new ToolBar(this);
            return bindView;
        }

        /**重新计算布局
         *
         */
        public void requestLayout(){
            bindView.requestLayout();
        }

        public Builder setViewLayout(int layout) {
            this.viewId[target] = layout;
            return this;
        }

        public Builder setViewLayout(View view) {
            this.view[target] = view;
            return this;
        }

        public Builder setText(CharSequence text) {
            this.text[target] = text;
            return this;
        }

        public Builder setTextMaxLength(int textMaxLength) {
            this.textMaxLength[target] = textMaxLength;
            return this;
        }

        public Builder setTextMaxLine(int textMaxLine) {
            this.textMaxLine[target] = textMaxLine;
            return this;
        }

        public Builder setTextSize(int textSize) {
            this.textSize[target] = textSize;
            return this;
        }

        public Builder setTextColor(int textColor) {
            this.textColor[target] = textColor;
            return this;
        }

        public Builder setDrawablesRes(int[] drawablesRes) {
            this.drawablesRes[target] = drawablesRes;
            return this;
        }

        public Builder setDrawablePadding(int drawablePadding) {
            this.drawablePadding[target] = drawablePadding;
            return this;
        }

        public Builder setVisibility(int visibility) {
            this.visibility[target] = visibility;
            return this;
        }

        public Builder setOnClickListener(View.OnClickListener onClickListener) {
            this.onClickListener[target] = onClickListener;
            return this;
        }

        public CharSequence getText(int target) {
            return text[target];
        }

        public static interface FollowScrollListener{
            void onFollowScroll(ToolBar toolBar, int effectiveRange, float changeRate);
        }
    }

    @IntDef(value = {Builder.LEFT, Builder.TITLE, Builder.RIGHT})
    @Retention(RetentionPolicy.SOURCE)
    private @interface ENUM_TYPE{}
}
