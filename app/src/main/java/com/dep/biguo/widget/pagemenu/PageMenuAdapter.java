package com.dep.biguo.widget.pagemenu;

import android.graphics.Color;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

public class PageMenuAdapter<T> extends RecyclerView.Adapter<PageMenuAdapter.Holder<T>> {
    private PageMenuViewHolderCreator<T> holderCreator;
    private List<List<T>> pageList;
    private int spanCount;
    private int rowCount;

    private int rowSpace;

    public void setHolderCreator(PageMenuViewHolderCreator<T> holderCreator) {
        this.holderCreator = holderCreator;
    }

    public void setRowSpace(int rowSpace) {
        this.rowSpace = rowSpace;
    }

    public void setList(int spanCount, int rowCount, List<T> list) {
        this.spanCount = spanCount;
        this.rowCount = rowCount;
        this.pageList = new ArrayList<>();

        int totalItems = list.size();
        int itemsPerPage = rowCount * spanCount;

        for (int i = 0; i < totalItems; i += itemsPerPage) {
            List<T> page = new ArrayList<>();
            // j 从当前页的起始位置 (i) 开始，直到当前页应该包含的最大元素位置 (i + itemsPerPage)
            // 同时，确保 j 不超过 totalItems 的大小，避免访问超出范围
            for (int j = i; j < i + itemsPerPage && j < totalItems; j++) {
                page.add(list.get(j));
            }
            pageList.add(page);
        }
    }

    @NonNull
    @Override
    public Holder<T> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        RecyclerView recyclerView = new RecyclerView(parent.getContext());
        recyclerView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        recyclerView.setLayoutManager(new GridLayoutManager(parent.getContext(), spanCount));
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.top = parent.indexOfChild(view) < spanCount ? 0 : rowSpace;
            }
        });
        return new Holder<>(recyclerView);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder<T> holder, int position) {
        if(pageList.size() > position) {
            holder.setHolderCreator(holderCreator);
            holder.itemView.setVisibility(View.VISIBLE);
            holder.itemAdapter.setList(pageList.get(position));
        }else {
            holder.itemView.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return pageList == null ? 0 : pageList.size();
    }

    public static class Holder<T> extends RecyclerView.ViewHolder {
        private final PageMenuItemAdapter<T> itemAdapter;
        private RecyclerView recyclerView;
        public Holder(@NonNull View itemView) {
            super(itemView);
            this.recyclerView = (RecyclerView) itemView;

            itemAdapter = new PageMenuItemAdapter<>();
            recyclerView.setAdapter(itemAdapter);
        }

        public void setHolderCreator(PageMenuViewHolderCreator<T> holderCreator) {
            itemAdapter.setHolderCreator(holderCreator);
        }
    }
}
