package com.dep.biguo.widget.sortlist;

import androidx.core.content.ContextCompat;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;

import java.util.List;

public class SortAdapter extends CommonAdapter<SortModel> {
    private int province_id;

    public SortAdapter(List<SortModel> data) {
        super(R.layout.item_sort, data);
    }

    @Override
    protected void convert(BaseViewHolder holder, SortModel item) {
        //根据position获取分类的首字母的char ascii值
        int section = item.getSortLetters().charAt(0);
        //如果当前位置等于该分类首字母的Char的位置 ，则认为是第一次出现
        if (holder.getLayoutPosition() == getPositionForSection(section)) {
            holder.getView(R.id.tvLetter).setVisibility(View.VISIBLE);
            holder.getView(R.id.tvLetter).setPadding(0, DisplayHelper.dp2px(mContext, 12), 0, 0);
            holder.setText(R.id.tvLetter, item.getSortLetters());
        } else {
            holder.getView(R.id.tvLetter).setVisibility(View.GONE);
        }
//        holder.setText(R.id.tvLetter, item.getSortLetters());

        holder.setTextColor(R.id.tvTitle, province_id == item.getId() ? ContextCompat.getColor(mContext, R.color.theme) : ContextCompat.getColor(mContext, R.color.tblack));

        holder.setText(R.id.tvTitle, item.getName());
    }


    /**
     * 根据分类的首字母的Char ascii值获取其第一次出现该首字母的位置
     */
    public int getPositionForSection(int section) {
        for (int i = 0; i < getData().size(); i++) {
            String sortStr = getData().get(i).getSortLetters();
            char firstChar = sortStr.toUpperCase().charAt(0);
            if (firstChar == section) {
                return i;
            }
        }
        return -1;
    }

    public void setProvinceId(int province_id) {
        this.province_id = province_id;
    }
}