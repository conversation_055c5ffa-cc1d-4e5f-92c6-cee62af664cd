package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;

import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.method.KeyListener;
import android.text.method.MovementMethod;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

/**
 * 左边文字对齐左边，右边文字对齐右边或紧挨左边文字，
 */
public class SideAlignTextView extends ConstraintLayout {
    //左边的文字或右边的文字在水平方向上的位置
    private static final int TOP = -1;
    private static final int CENTER = 0;
    private static final int BOTTOM = 1;

    //仅右边的文字在水平方向上的位置
    private static final int START = -1;
    private static final int END = 1;

    private int leftTextSize;
    private int leftTextColor;
    private Drawable leftDrawable;
    private CharSequence leftText;
    private CharSequence leftHint;
    private int rightTextSize;
    private int rightTextColor;
    private boolean rightHasInput;
    private int rightHintColor;
    private int rightLines;
    private int rightTextMaxLength;
    private Drawable rightDrawable;
    private CharSequence rightText;
    private CharSequence rightHint;
    private int textAlain;
    private int rightHorizontal;
    private int leftDrawablePadding;
    private int rightDrawablePadding;
    private int leftStyle;
    private int rightStyle;

    private TextView leftView;
    private EditText rightView;

    private MovementMethod rightMovementMethod;
    private KeyListener rightKeyListener;

    public SideAlignTextView(Context context) {
        super(context);
        init(context, null,0);
    }

    public SideAlignTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs,0);
    }

    public SideAlignTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs,defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        if(attrs != null) {
            TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.SideAlignTextView, defStyleAttr, 0);
            leftTextSize = a.getDimensionPixelSize(R.styleable.SideAlignTextView_leftTextSize, DisplayHelper.dp2px(context, 14));
            leftTextColor = a.getColor(R.styleable.SideAlignTextView_leftTextColor, getResources().getColor(R.color.tblack));
            leftText = a.getString(R.styleable.SideAlignTextView_leftText);
            leftHint = a.getString(R.styleable.SideAlignTextView_leftHint);
            rightTextSize = a.getDimensionPixelSize(R.styleable.SideAlignTextView_rightTextSize, DisplayHelper.dp2px(context, 14));
            rightTextColor = a.getColor(R.styleable.SideAlignTextView_rightTextColor, getResources().getColor(R.color.tblack));
            rightHasInput = a.getBoolean(R.styleable.SideAlignTextView_rightHasInput, false);
            rightHint = a.getString(R.styleable.SideAlignTextView_rightHint);
            rightHintColor = a.getColor(R.styleable.SideAlignTextView_rightHintColor, getResources().getColor(R.color.tblack3));
            rightLines = a.getInt(R.styleable.SideAlignTextView_rightLines, -1);
            rightTextMaxLength = a.getInt(R.styleable.SideAlignTextView_rightTextMaxLength, -1);
            rightText = a.getString(R.styleable.SideAlignTextView_rightText);
            textAlain = a.getInt(R.styleable.SideAlignTextView_textAlain, CENTER);
            rightHorizontal = a.getInt(R.styleable.SideAlignTextView_rightHorizontal, END);
            leftDrawable = a.getDrawable(R.styleable.SideAlignTextView_leftDrawable);
            leftDrawablePadding = a.getDimensionPixelSize(R.styleable.SideAlignTextView_leftDrawablePadding, 0);
            rightDrawable = a.getDrawable(R.styleable.SideAlignTextView_rightDrawable);
            rightDrawablePadding = a.getDimensionPixelSize(R.styleable.SideAlignTextView_rightDrawablePadding, 0);
            leftStyle = a.getInt(R.styleable.SideAlignTextView_leftStyle, R.style.normalText);
            rightStyle = a.getInt(R.styleable.SideAlignTextView_rightStyle, R.style.normalText);
            a.recycle();

            if (leftDrawable != null) {
                leftDrawable.setBounds(0, 0, leftDrawable.getMinimumWidth(), leftDrawable.getMinimumHeight());
            }
            if (rightDrawable != null) {
                rightDrawable.setBounds(0, 0, rightDrawable.getMinimumWidth(), rightDrawable.getMinimumHeight());
            }
        }

        initView();
    }

    private void setStyle(int style, TextView textView){
        if(style == 0){
            textView.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
        }else if(style == 2){
            textView.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        }else {
            textView.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
        }
    }

    private void initView() {
        inflate(getContext(),R.layout.side_align_text_view,this);
        leftView = findViewById(R.id.leftView);
        leftView.setTextColor(leftTextColor);
        leftView.setTextSize(TypedValue.COMPLEX_UNIT_PX,leftTextSize);
        leftView.setText(leftText);
        leftView.setHint(leftHint);
        leftView.setCompoundDrawablePadding(leftDrawablePadding);
        leftView.setCompoundDrawables(null, null, leftDrawable, null);
        leftView.setGravity(textAlain == TOP ? Gravity.TOP : textAlain == CENTER ? Gravity.CENTER : Gravity.BOTTOM);
        setStyle(leftStyle, leftView);

        rightView = findViewById(R.id.rightView);
        if(rightLines > 0) {
            rightView.setMinLines(rightLines);
        }
        if(rightTextMaxLength > 0){
            rightView.setFilters(new InputFilter[]{new InputFilter.LengthFilter(rightTextMaxLength)});;
        }
        rightView.setTextColor(rightTextColor);
        rightView.setTextSize(TypedValue.COMPLEX_UNIT_PX,rightTextSize);
        rightView.setText(rightText);
        rightView.setHint(rightHint);
        rightView.setHintTextColor(rightHintColor);
        rightMovementMethod = rightView.getMovementMethod();
        rightKeyListener = rightView.getKeyListener();
        rightView.setMovementMethod(rightHasInput ? rightMovementMethod : null);
        rightView.setKeyListener(rightHasInput ? rightKeyListener : null);
        rightView.setCompoundDrawablePadding(rightDrawablePadding);
        rightView.setCompoundDrawables(null, null, rightDrawable, null);
        rightView.setGravity(rightHorizontal == START ? Gravity.START : Gravity.END);
        setStyle(rightStyle, rightView);

    }

    public String getLeftText() {
        return leftView.getText().toString();
    }

    public void setLeftText(CharSequence leftText) {
        this.leftText = leftText;
        leftView.setText(leftText);
    }

    public String getRightText() {
        return rightView.getText().toString();
    }

    public void setRightText(CharSequence rightText) {
        this.rightText = TextUtils.isEmpty(rightText) ? "" : rightText;
        rightView.setText(rightText);
    }

    public void setRightTextColor(@ColorInt int color){
        this.rightTextColor = color;
        rightView.setTextColor(color);
    }

    public void setRightHint(CharSequence rightHint){
        this.rightHint = rightHint;
        rightView.setHint(rightHint);
    }

    public void setLeftDrawable(Drawable leftDrawable) {
        this.leftDrawable = leftDrawable;
        if(rightDrawable != null) {
            leftDrawable.setBounds(0, 0, leftDrawable.getMinimumWidth(), leftDrawable.getMinimumHeight());
        }
        leftView.setCompoundDrawables(null, null, leftDrawable, null);
    }

    public void setRightDrawable(Drawable rightDrawable) {
        this.rightDrawable = rightDrawable;
        if(rightDrawable != null) {
            rightDrawable.setBounds(0, 0, rightDrawable.getMinimumWidth(), rightDrawable.getMinimumHeight());
        }
        rightView.setCompoundDrawables(null, null, rightDrawable, null);
    }

    public void setLeftDrawablePadding(int leftDrawablePadding) {
        this.leftDrawablePadding = leftDrawablePadding;
        leftView.setCompoundDrawablePadding(leftDrawablePadding);

    }

    public void setRightDrawablePadding(int rightDrawablePadding) {
        this.rightDrawablePadding = rightDrawablePadding;
        rightView.setCompoundDrawablePadding(rightDrawablePadding);
    }

    public void setLeftOnClickListener(View.OnClickListener onClickListener){
        leftView.setOnClickListener(onClickListener);
    }

    public void setRightOnClickListener(View.OnClickListener onClickListener){
        rightView.setOnClickListener(onClickListener);
    }
}
