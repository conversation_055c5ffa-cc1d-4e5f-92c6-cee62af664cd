package com.dep.biguo.widget.sortlist;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import com.biguo.utils.util.DisplayHelper;

public class SideBar extends View {
    // 触摸事件
    private OnTouchingLetterChangedListener onTouchingLetterChangedListener;
    // 26个字母
    public static String[] b = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
            "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z", "#"};
    private int choose = -1;// 选中
    private Paint paint = new Paint();
    private Rect rect = new Rect();
    private TextView mTextDialog;


    /**
     * 为SideBar设置显示字母的TextView
     *
     * @param mTextDialog
     */
    public void setTextView(TextView mTextDialog) {
        this.mTextDialog = mTextDialog;
    }


    public SideBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public SideBar(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SideBar(Context context) {
        super(context);
    }

    /**
     * 重写这个方法
     */
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // 获取焦点改变背景颜色.
        float charHeight = getCharHeight();// 获取每一个字母的高度
        float charWidth = getCharWidth(); // 获取对应宽度

        for (int i = 0; i < b.length; i++) {
            paint.setARGB(0x99, 0x99, 0x99, 0x99);
            paint.setAntiAlias(true);
            paint.setTextSize(DisplayHelper.dp2px(getContext(), 12));
            // 选中的状态
            if (i == choose) {
                paint.setTextSize(DisplayHelper.dp2px(getContext(), 18));
                paint.setColor(Color.parseColor("#D53E43"));
                paint.setFakeBoldText(true);
            }
            // x坐标等于中间-字符串宽度的一半.
            float xPos = (charWidth - paint.measureText(b[i])) / 2f;
            float yPos = (charHeight * i + charHeight);
            paint.getTextBounds(b[i], 0, 1, rect);
            int dp1 = DisplayHelper.dp2px(getContext(), 1);
            canvas.drawText(b[i], xPos, yPos + dp1, paint);
            paint.reset();// 重置画笔
        }

    }

    private float getCharHeight(){
        int height = getHeight() - getPaddingTop() - getPaddingBottom();// 获取对应高度
        return height * 1f / b.length;
    }

    private float getCharWidth(){
        return getWidth() - getPaddingStart() - getPaddingEnd(); // 获取对应宽度
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        float itemHeight = getCharHeight();//平均每个字母占据的高度
        int c = (int) (event.getY() / itemHeight);

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
                if (choose != c) {
                    if (c >= 0 && c < b.length) {
                        if (onTouchingLetterChangedListener != null) {
                            onTouchingLetterChangedListener.onTouchingLetterChanged(b[c]);
                        }
                        if (mTextDialog != null) {
                            mTextDialog.setText(b[c]);
                            mTextDialog.setVisibility(View.VISIBLE);
                        }

                        choose = c;
                        invalidate();
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                setBackground(new ColorDrawable(0x00000000));
                choose = -1;//
                invalidate();
                if (mTextDialog != null) {
                    mTextDialog.setVisibility(View.INVISIBLE);
                }
                break;

        }
        return true;
    }

    /**
     * 向外公开的方法
     *
     * @param onTouchingLetterChangedListener
     */
    public void setOnTouchingLetterChangedListener(OnTouchingLetterChangedListener onTouchingLetterChangedListener) {
        this.onTouchingLetterChangedListener = onTouchingLetterChangedListener;
    }

    /**
     * 接口
     *
     * <AUTHOR>
     */
    public interface OnTouchingLetterChangedListener {
        void onTouchingLetterChanged(String s);
    }

}