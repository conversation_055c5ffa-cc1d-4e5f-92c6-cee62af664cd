package com.dep.biguo.widget;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**用于获取手势的X坐标，方便{@link QuestionView}控件响应长按评论时弹窗的箭头指向的位置
 *
 */
public class TouchLayout extends ConstraintLayout {
    private int downX;
    public TouchLayout(Context context) {
        super(context);
    }

    public TouchLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TouchLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        downX = (int) event.getX();

        return super.onInterceptTouchEvent(event);
    }

    public int getDownX() {
        return downX;
    }

    public void setDownX(int downX) {
        this.downX = downX;
    }
}
