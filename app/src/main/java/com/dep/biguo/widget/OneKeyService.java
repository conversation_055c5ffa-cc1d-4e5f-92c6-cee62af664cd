package com.dep.biguo.widget;

import android.app.Application;
import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.jess.arms.integration.AppManager;

public class OneKeyService extends Service {
    private static final int INTERVAL_MS = 100; // 100ms
    private Handler handler;
    private Runnable runnable;

    // 静态方法启动服务
    public static void startService(Application context) {
        //如果服务运行过一次，那么本次安装就不在运行服务
        if(KVHelper.getBoolean(OneKeyService.class.getName())) return;
        KVHelper.putValue(OneKeyService.class.getName(), true);

        LogUtil.d("dddd", "启动一个服务用于检查一键登录是否初始化并获取预取号成功");
        Intent intent = new Intent(context, OneKeyService.class);
        context.startService(intent);
    }

    // 静态方法停止服务
    public static void stopService(Application context) {
        LogUtil.d("dddd", "结束一键登录检查服务");
        try {
            Intent intent = new Intent(context, OneKeyService.class);
            context.stopService(intent);
        }catch (Exception e){

        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        handler = new Handler();
        runnable = new Runnable() {
            @Override
            public void run() {
                LogUtil.d("dddd", "一键登录检查中...");
                // 调用 DeviceCache.getOneKeyLoginPreCode() 并检查返回值
                int result = DeviceCache.getOneKeyLoginPreCode();
                LogUtil.d("dddd", "一键登录检查结果："+result);
                if(result > 0) {
                    //已经有结果，就不要再继续执行轮询了
                    stopService(getApplication());
                }

                if(result == 7000){
                    stopService(getApplication());
                    LogUtil.d("dddd", "拉起登录页面");
                    MainAppUtils.checkLogin(AppManager.getAppManager().getTopActivity());
                }

                // 如果需要继续检查，则重新安排下一个执行
                handler.postDelayed(this, INTERVAL_MS);
            }
        };
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 开始周期性任务
        LogUtil.d("dddd", "开始检查...");
        handler.post(runnable);
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 停止周期性任务
        handler.removeCallbacks(runnable);
    }

    @Override
    public IBinder onBind(Intent intent) {
        // 如果不需要绑定服务，返回 null
        return null;
    }
}

