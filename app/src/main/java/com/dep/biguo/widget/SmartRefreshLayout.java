package com.dep.biguo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.LogUtil;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smart.refresh.header.MaterialHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

/**
 * SmartRefreshLayout自带的加载更多效果没有BaseQuickAdapter的好，
 * 主要原因SmartRefreshLayout自带的加载在内容不足以撑满屏幕时，不能自动请求下一页，因此组合一下
 *
 * 带有记忆当前已获取页数功能，在请求成功后，调用finishLoadMore()方法，页数自动增加1
 *
 * 使用SmartRefreshLayout的原因是SwipeRefreshLayout与CoordinatorLayout有滑动冲突
 */
public class SmartRefreshLayout extends com.scwang.smart.refresh.layout.SmartRefreshLayout implements OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private int page = 0;
    private OnRefreshLoadMoreListener onRefreshLoadMoreListener;
    private BaseQuickAdapter adapter;
    private boolean isRefresh;

    public SmartRefreshLayout(Context context) {
        super(context);
        init();
    }

    public SmartRefreshLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init(){
        setEnableLoadMore(false);
        addRefreshHead();
        setOnRefreshListener(this);
    }

    private void addRefreshHead(){
        MaterialHeader materialHeader = new MaterialHeader(getContext());
        materialHeader.setLayoutParams(new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        setRefreshHeader(materialHeader);
    }

    public void bindAdapter(BaseQuickAdapter adapter, RecyclerView recyclerView, OnRefreshLoadMoreListener onRefreshLoadMoreListener){
        if(recyclerView != null) {
            recyclerView.setAdapter(adapter);
        }
        this.onRefreshLoadMoreListener = onRefreshLoadMoreListener;
        if(adapter != null) {
            this.adapter = adapter;
            this.adapter.setOnLoadMoreListener(this, recyclerView);
        }
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        if(onRefreshLoadMoreListener != null) {
            isRefresh = true;
            onRefreshLoadMoreListener.loadMore(1);
        }
    }

    @Override
    public RefreshLayout setEnableLoadMore(boolean enabled) {
        if(adapter != null) adapter.setEnableLoadMore(enabled);

        return super.setEnableLoadMore(enabled);
    }

    @Override
    public void onLoadMoreRequested() {
        if(onRefreshLoadMoreListener != null && adapter != null && adapter.isLoadMoreEnable()) {
            //如果adapter不为null，则需要检测是否可以加载更多
            isRefresh = false;
            onRefreshLoadMoreListener.loadMore(page + 1);

        }else if(onRefreshLoadMoreListener != null && adapter == null){
            isRefresh = false;
            onRefreshLoadMoreListener.loadMore(page + 1);
        }
    }

    @Override
    public boolean isRefreshing() {
        return isRefresh;
    }

    @Override
    public RefreshLayout finishRefresh(int delayed, boolean success, Boolean noMoreData) {
        isRefresh = false;
        page = 1;
        return super.finishRefresh(delayed, success, noMoreData);
    }

    public RefreshLayout finishLoadMore(boolean success, boolean noMoreData) {
        return finishLoadMore(0, success, noMoreData);
    }

    public int getCurrentPage() {
        return Math.max(1,page);
    }

    @Override
    public RefreshLayout finishLoadMore(int delayed, boolean success, boolean noMoreData) {
        if(isRefreshing()){
            //刷新，不管失败还是成功，都结束动画
            finishRefresh();
        }else if(success && !noMoreData){
            //加载更多，成功的时候，并且还有更多数据时，页码自加1
            page++;
        }

        if(adapter == null) {
            return super.finishLoadMore(delayed, success, noMoreData);
        }else if (noMoreData) {
            adapter.loadMoreEnd();
        }else if(success){
            adapter.loadMoreComplete();
        } else{
            adapter.loadMoreFail();
        }

        return super.finishLoadMore(delayed, success, noMoreData);
    }

    public interface OnRefreshLoadMoreListener {
        void loadMore(int page);
    }
}
