package com.dep.biguo.widget;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;

import com.hjq.toast.ToastUtils;

/**输入金额，最多保留两位小数
 *
 */
public class DecimalEditView extends androidx.appcompat.widget.AppCompatEditText implements TextWatcher {
    private final String regex = "^([0-9]+(\\.)?[0-9]{0,2})?$";

    public DecimalEditView(Context context) {
        super(context);
        init();
    }

    public DecimalEditView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public DecimalEditView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init(){
        
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        String text=s.toString();
        if(!text.matches(regex)) {
            setText(text.substring(0,text.length()-count));
        }
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    @Override
    protected void onSelectionChanged(int selStart, int selEnd) {
        super.onSelectionChanged(selStart, selEnd);
        Editable editable=getText();
        //保证光标始终在最后面
        setSelection(editable==null ? 0 : editable.length());
    }

    /**获取金额
     * @return 获取的金额
     */
    public float getValue(){
        Editable editable=getText();
        String text = editable != null ? editable.toString() : "";
        return TextUtils.isEmpty(text) ? 0f : Float.parseFloat(text);
    }

    /**设置金额
     * @param text 金额
     */
    public void setValue(@Nullable String text){
        if(text == null || !text.matches(regex)){
            ToastUtils.show("输入有误");
            return;
        }
        setText(text);
    }
}
