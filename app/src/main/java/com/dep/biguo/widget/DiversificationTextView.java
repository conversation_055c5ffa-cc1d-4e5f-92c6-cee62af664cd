package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.util.AttributeSet;

import com.dep.biguo.R;

public class DiversificationTextView extends androidx.appcompat.widget.AppCompatTextView {
    private String startChar;//字体变化的起始字符串
    private String endChar;//字体变化的终止字符串
    private boolean isStartFlag;//起始位置的字符串是否纳入变化范围
    private boolean isEndFlag;//终止位置的字符串是否纳入变化范围
    private int start;//起始下标
    private int end;//终止下标
    private int size;//字体大小
    private int changeFontStyle;//字体粗细
    private int color;//字体颜色
    private int line;//上划线、中划线、下划线
    private ClickableSpan clickableSpan;//对选中文字的监听

    public static final int TOP_LINE = 0x08;
    public static final int MIDDLE_LINE = 0x10;
    public static final int BOTTOM_LINE = 0x20;

    public DiversificationTextView(Context context) {
        this(context,null);
    }

    public DiversificationTextView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public DiversificationTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs,defStyleAttr);
    }

    public void init(Context context, AttributeSet attrs, int defStyleAttr){
        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.DiversificationTextView, defStyleAttr, 0);
        startChar = a.getString(R.styleable.DiversificationTextView_startChar);
        endChar = a.getString(R.styleable.DiversificationTextView_endChar);
        isStartFlag = a.getBoolean(R.styleable.DiversificationTextView_isStartFlag,false);
        isEndFlag = a.getBoolean(R.styleable.DiversificationTextView_isEndFlag, false);
        start = a.getInteger(R.styleable.DiversificationTextView_start, -1);
        end = a.getInteger(R.styleable.DiversificationTextView_end, -1);
        size = a.getDimensionPixelSize(R.styleable.DiversificationTextView_size, (int) getTextSize());
        changeFontStyle = a.getInt(R.styleable.DiversificationTextView_changeFontStyle, getTypeface().getStyle());
        color = a.getColor(R.styleable.DiversificationTextView_changeColor, getCurrentTextColor());
        line = a.getInt(R.styleable.DiversificationTextView_line_position, -1);
        a.recycle();

        if(line!=-1) {
            getPaint().setFlags(line);
            getPaint().setAntiAlias(true);
        }

        setText(getText());
    }

    /**Java代码设置text，请调用{@link DiversificationTextView#setText(int),DiversificationTextView#setText(CharSequence)}
     * @param text
     * @param type
     */
    @Override
    public void setText(CharSequence text, BufferType type) {
        if(TextUtils.isEmpty(text)){//必须设置一个值，否则会报android.text.BoringLayout.isBoring的异常
            super.setText(text, type);

        }else {
            SpannableString spannableString = new SpannableString(text);

            if (!TextUtils.isEmpty(startChar)) {
                start = text.toString().indexOf(startChar) + (isStartFlag ? 0 : 1);
            }

            if (!TextUtils.isEmpty(endChar)) {
                end = text.toString().indexOf(endChar)  + (isEndFlag ? 1 : 0);
            }

            if ((start >= 0 && end > start && text.length() >= end) || (start > 0 && end == -1)) {
                spannableString.setSpan(new AbsoluteSizeSpan(size), start, (end > 0 ? end : text.length()), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                spannableString.setSpan(new ForegroundColorSpan(color), start, (end > 0 ? end : text.length()), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                spannableString.setSpan(new StyleSpan(changeFontStyle), start, (end > 0 ? end : text.length()), Spannable.SPAN_INCLUSIVE_INCLUSIVE); //正常

                //添加一个点击事件
                if (clickableSpan != null) {
                    spannableString.setSpan(clickableSpan, start, (end > 0 ? end : text.length()), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    setMovementMethod(LinkMovementMethod.getInstance());//必须设置否则无法点击
                }
            }
            super.setText(spannableString, type);
        }
    }


    public void setStartChar(String startChar) {
        this.startChar = startChar;
    }

    public void setEndChar(String endChar) {
        this.endChar = endChar;
    }

    public void setStartFlag(boolean startFlag) {
        isStartFlag = startFlag;
    }

    public void setEndFlag(boolean endFlag) {
        isEndFlag = endFlag;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public void setColor(int color) {
        this.color = color;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public void setClickableSpan(ClickableSpan clickableSpan) {
        this.clickableSpan = clickableSpan;
        setText(getText());
    }
}
