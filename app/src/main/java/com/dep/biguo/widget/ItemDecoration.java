package com.dep.biguo.widget;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

import java.util.HashMap;
import java.util.Map;

public class ItemDecoration extends RecyclerView.ItemDecoration{
    public static final int Horizontal = 0;//横向分割线
    public static final int Vertical = 1;//纵向分割线

    private int orientation;
    private int size = 1;
    private int colorRes = R.color.bgc;
    private int backgroundColorRes = R.color.tran;
    private int leftMargin;
    private int topMargin;
    private int rightMargin;
    private int bottomMargin;
    private boolean retainTopMargin;//是否给顶部也添加分割线
    private Map<Integer, Integer> skipDrawDecorationMap = new HashMap<>();


    private final Paint paint;

    public ItemDecoration(int orientation) {
        this.orientation = orientation;
        paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildLayoutPosition(view);

        if(orientation == Horizontal) {
            if(position == 0 && !retainTopMargin){
                outRect.top =  0;
            }else {
                //第一item，当没有设置保留顶部，则设置边距为0
                outRect.top = DisplayHelper.dp2px(parent.getContext(), size);
            }
        }else {
            outRect.left = position > 0 ? DisplayHelper.dp2px(parent.getContext(), size) : 0;
        }
    }

    @Override
    public void onDraw(@NonNull Canvas canvas, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(canvas, parent, state);

        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        for (int i = 0; i < parent.getChildCount(); i++) {
            //当map集合中有当前下标，表示该下标无需绘制分割线
            if(isSkipDraw(parent.getChildLayoutPosition(parent.getChildAt(i)))) continue;

            View childView = parent.getChildAt(i);

            if(orientation == Horizontal){
                paint.setColor(parent.getContext().getResources().getColor(colorRes));
                int topDecorationHeight = layoutManager.getTopDecorationHeight(childView);
                canvas.drawRect(childView.getLeft()+DisplayHelper.dp2px(parent.getContext(), leftMargin),
                        childView.getTop()-topDecorationHeight,
                        childView.getRight()-DisplayHelper.dp2px(parent.getContext(), rightMargin),
                        childView.getTop(),
                        paint);

                //绘制背景，当分割线不能填充父控件宽度，可以把不能填充的部分用背景色填充
                if(backgroundColorRes != R.color.tran) {
                    paint.setColor(parent.getContext().getResources().getColor(backgroundColorRes));
                    canvas.drawRect(parent.getLeft(),
                            childView.getTop() - topDecorationHeight,
                            parent.getRight(),
                            childView.getTop(),
                            paint);
                }
            }else {
                paint.setColor(parent.getContext().getResources().getColor(colorRes));
                int leftDecorationWidth = layoutManager.getLeftDecorationWidth(childView);
                canvas.drawRect(childView.getLeft() + leftDecorationWidth,
                        childView.getTop()+DisplayHelper.dp2px(parent.getContext(), topMargin),
                        childView.getLeft(),
                        childView.getBottom()-DisplayHelper.dp2px(parent.getContext(), bottomMargin),
                        paint);

                //绘制背景，当分割线不能填充父控件高度，可以把不能填充的部分用背景色填充
                if(backgroundColorRes != R.color.tran) {
                    canvas.drawRect(childView.getLeft() + leftDecorationWidth,
                            parent.getTop(),
                            childView.getLeft(),
                            childView.getBottom(),
                            paint);
                }
            }
        }
    }

    @Override
    public void onDrawOver(@NonNull Canvas canvas, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(canvas, parent, state);

        paint.setColor(parent.getContext().getResources().getColor(colorRes));

        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        for (int i = 0; i < parent.getChildCount(); i++) {
            //当map集合中有当前下标，表示该下标无需绘制分割线
            if(isSkipDraw(parent.getChildLayoutPosition(parent.getChildAt(i)))) continue;

            View childView = parent.getChildAt(i);

            if(orientation == Horizontal){
                int topDecorationHeight = layoutManager.getTopDecorationHeight(childView);
                canvas.drawRect(childView.getLeft()+DisplayHelper.dp2px(parent.getContext(), leftMargin),
                        childView.getTop()-topDecorationHeight,
                        childView.getRight()-DisplayHelper.dp2px(parent.getContext(), rightMargin),
                        childView.getTop(),
                        paint);
            }else {
                int leftDecorationWidth = layoutManager.getLeftDecorationWidth(childView);
                canvas.drawRect(childView.getLeft() + leftDecorationWidth,
                        childView.getTop()+DisplayHelper.dp2px(parent.getContext(), topMargin),
                        childView.getLeft(),
                        childView.getBottom()-DisplayHelper.dp2px(parent.getContext(), bottomMargin),
                        paint);
            }
        }
    }

    private boolean isSkipDraw(int i){
        //当map集合中有当前下标，表示该下标无需绘制分割线
        return skipDrawDecorationMap.containsKey(i);
    }

    public ItemDecoration addSkipDraw(int position){
        skipDrawDecorationMap.put(position, position);
        return this;
    }

    public ItemDecoration setSize(int size) {
        this.size = size;
        return this;
    }

    public ItemDecoration setColorRes(int colorRes) {
        this.colorRes = colorRes;
        return this;
    }

    public ItemDecoration setBackgroundColorRes(int backgroundColorRes) {
        this.backgroundColorRes = backgroundColorRes;
        return this;
    }

    public ItemDecoration setLeftMargin(int leftMarginDp) {
        this.leftMargin = leftMarginDp;
        return this;
    }

    public ItemDecoration setTopMargin(int topMarginDp) {
        this.topMargin = topMarginDp;
        return this;
    }

    public ItemDecoration setRightMargin(int rightMarginDp) {
        this.rightMargin = rightMarginDp;
        return this;
    }

    public ItemDecoration setBottomMargin(int bottomMarginDp) {
        this.bottomMargin = bottomMarginDp;
        return this;
    }

    public ItemDecoration setRetainTopMargin(boolean retainTopMargin) {
        this.retainTopMargin = retainTopMargin;
        return this;
    }
}
