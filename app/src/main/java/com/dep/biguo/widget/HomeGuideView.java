package com.dep.biguo.widget;

import android.content.Context;

import androidx.constraintlayout.widget.ConstraintLayout;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.activity.CourseActivity;
import com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.hjq.toast.ToastUtils;

public class HomeGuideView extends ConstraintLayout implements View.OnClickListener {
    private View startStudyView;
    private ImageView guideKnowView;

    private Paint paint;
    private PorterDuffXfermode porterDuffXfermode;

    public HomeGuideView(Context context) {
        super(context);
    }

    public HomeGuideView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HomeGuideView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    private void init() {
        //ViewGroup要想调用onDraw()方法，必须要加这一句
        setWillNotDraw(false);

        LayoutInflater.from(getContext()).inflate(R.layout.guide_view, this, true);
        startStudyView = findViewById(R.id.startStudyView);
        guideKnowView = findViewById(R.id.guideKnowView);

        startStudyView.setOnClickListener(this);
        guideKnowView.setOnClickListener(this);
        //啥也不干，就是拦截触摸事件
        setOnClickListener(this);

        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.FILL);
        porterDuffXfermode = new PorterDuffXfermode(PorterDuff.Mode.SRC_IN);
    }


    @Override
    public void onClick(View view) {
        if(view == startStudyView){
            if(UserCache.getSchool() == null || UserCache.getProfession() == null){
                ToastUtils.show("请先选择学校和专业");
                ProfessionSchoolActivity.Start(getContext());
                return;
            }
            //埋点开始学习按钮的点击次数
            new UmengEventUtils(getContext())
                    .addParams("icon_type", "开始学习")
                    .pushEvent(UmengEventUtils.CLICK_ICON);

            //隐藏引导手势
            KVHelper.putValue(UserCache.HOME_GUIDE, true);
            setVisibility(View.GONE);
            //跳转到首次报考页面
            CourseActivity.start(getContext(), StartFinal.MANAGER_ENROLL);

        }else if(view == guideKnowView){
            //隐藏引导手势
            KVHelper.putValue(UserCache.HOME_GUIDE, true);
            setVisibility(View.GONE);

        }
    }

    @Override
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //镂空图形必须要有的语句
        int layoutId = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        //绘制半透明背景
        paint.setXfermode(null);
        paint.setColor(Color.parseColor("#99000000"));
        canvas.drawRect(0, 0, getWidth(), getHeight(), paint);

        //绘制需要镂空的形状
        paint.setXfermode(porterDuffXfermode);
        paint.setColor(Color.parseColor("#00000000"));
        float radius = startStudyView.getHeight() / 2f;
        canvas.drawCircle(startStudyView.getX() + radius, startStudyView.getY() + radius, radius, paint);

        //镂空图形必须要有的语句
        canvas.restoreToCount(layoutId);
    }

    public void refresh(int topMargin){
        /*if(!KVHelper.getBoolean(UserCache.HOME_GUIDE, false)){
            int toolBarHeight = DisplayHelper.dp2px(getContext(), 44);
            topMargin += toolBarHeight + DisplayHelper.getStatusBarHeight(getContext());
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) startStudyView.getLayoutParams();
            layoutParams.topMargin = topMargin;
            startStudyView.setLayoutParams(layoutParams);
            setVisibility(VISIBLE);
            //展示过一次就隐藏引导手势
            KVHelper.putValue(UserCache.HOME_GUIDE, true);
            invalidate();
        }*/
    }
}
