package com.dep.biguo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.activity.ImageActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HtmlWebView extends WebView {
    private Paint paint = new Paint();

    private int contentHeight;

    public HtmlWebView(@NonNull Context context) {
        super(context);
        initWebView();
    }

    public HtmlWebView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initWebView();
    }

    public HtmlWebView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initWebView();
    }

    public HtmlWebView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initWebView();
    }

    public void initWebView(){
        //禁止长按选中功能
        setOnLongClickListener(v -> true);

        WebSettings settings = getSettings();
        // 设置WebView支持JavaScript
        settings.setJavaScriptEnabled(true);
        //支持自动适配
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setSupportZoom(false);  //支持放大缩小
        settings.setBuiltInZoomControls(true); //显示缩放按钮
        settings.setBlockNetworkImage(false);// 把图片加载放在最后来加载渲染
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING);
        settings.setTextZoom(90);
        //设置不让其跳转浏览器
        setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                return false;
            }
        });

        // 添加 JavaScript 接口
        addJavascriptInterface(new Object() {
            @JavascriptInterface
            public void onImageClick(String imageUrl) {
                // 处理图片点击事件，并显示放大的图片
                ImageActivity.mPaths.add(imageUrl);
                ImageActivity.start(getContext(), 0);
            }
        }, "imageHandler");
    }

    //给 <img> 标签添加点击事件
    private String addClickEventToImages(String htmlContent) {
        // 正则表达式匹配 <img> 标签
        Pattern pattern = Pattern.compile("<img\\s+[^>]*src=[\"']([^\"']+)[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);

        // 替换 <img> 标签，添加 onclick 事件
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String imgTag = matcher.group(0);  // 获取整个 <img> 标签
            String imgSrc = matcher.group(1);  // 获取 src 属性的值
            String imgTagWithOnClick = imgTag.replace(">", " onclick=\"window.imageHandler.onImageClick('" + imgSrc + "')\">");
            matcher.appendReplacement(sb, imgTagWithOnClick);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    public void setHtml(@NonNull String data){
        String htmlAddClick = addClickEventToImages(data);
        //给图片添加点击事件
        loadDataWithBaseURL(null, htmlAddClick, "text/html" , "utf-8", null);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawWatermark(canvas);
    }

    public void drawWatermark(Canvas canvas){
        paint.setColor(Color.parseColor("#000000"));
        paint.setTextSize(80);
        paint.setAntiAlias(true);
        paint.setAlpha(30); // 设置透明度
        paint.setTextAlign(Paint.Align.LEFT);

        String watermarkText = "自考笔果题库";
        int width = getWidth();
        int height = computeVerticalScrollRange();
        int stepX = 500;  // 水平方向间隔
        int stepY = 800;  // 垂直方向间隔

        // 绘制多个水印
        for (int x = 0; x < width; x += stepX) {
            for (int y = 0; y < height; y += stepY) {
                canvas.save();
                canvas.rotate(-45, x, y);  // 旋转水印
                canvas.drawText(watermarkText, x, y, paint);
                canvas.restore();
            }
        }
    }
}
