package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class TimeView extends LinearLayout {
    private Paint paint;

    public TimeView(Context context) {
        this(context,null);
    }

    public TimeView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public TimeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs,defStyleAttr);
    }

    public void init(Context context, AttributeSet attrs, int defStyleAttr){
        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.TimeView, defStyleAttr, 0);
        String format = a.getString(R.styleable.TimeView_format);
        int size = a.getDimensionPixelSize(R.styleable.TimeView_fontSize,DisplayHelper.dp2px(getContext(), 14));
        int color = a.getColor(R.styleable.TimeView_color, getContext().getResources().getColor(R.color.tblack3));
        a.recycle();

        paint = new Paint();
        paint.setTextSize(size);

        for(int i=0; i<format.length(); i++){
            addView(createView(String.valueOf(format.charAt(i)), size, color));
        }
    }

    private TextView createView(String text, int size ,int color){
        String measureText = text.contains(".") ? "." : "0";
        int width = (int)paint.measureText(measureText);
        TextView textView = new TextView(getContext());
        textView.setLayoutParams(new LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT));
        textView.setGravity(Gravity.CENTER);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);
        textView.setText(text);
        textView.setTextColor(color);
        return textView;
    }

    public void setText(String text){
        for(int i=0; i<text.length(); i++){
            if(i >= getChildCount()) return;

            ((TextView)getChildAt(i)).setText(String.valueOf(text.charAt(i)));
        }
    }
}
