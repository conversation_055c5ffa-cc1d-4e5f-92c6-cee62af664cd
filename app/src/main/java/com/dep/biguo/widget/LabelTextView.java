package com.dep.biguo.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;

import androidx.core.content.ContextCompat;
import android.util.AttributeSet;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class LabelTextView extends androidx.appcompat.widget.AppCompatTextView {

    private int mLabelWidth = 4;
    private Paint mPaint;
    private int mColor;
    private RectF rectF;

    public LabelTextView(Context context) {
        this(context, null);
    }

    public LabelTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LabelTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.LabelTextView, defStyleAttr, 0);
        mColor = a.getColor(R.styleable.LabelTextView_label_color, ContextCompat.getColor(context, R.color.theme));
        a.recycle();

        rectF = new RectF();

        mPaint = new Paint();
        mPaint.setColor(mColor);
        mPaint.setDither(true);
        mPaint.setAntiAlias(true);

        int paddingLeft = getPaddingLeft();
        mLabelWidth = DisplayHelper.dp2px(getContext(), mLabelWidth);
        int dp10 = DisplayHelper.dp2px(getContext(), 10);
        if (paddingLeft == 0)
            setPadding(dp10 + mLabelWidth, getPaddingTop(), getPaddingRight(), getPaddingBottom());
        else
            setPadding(paddingLeft + mLabelWidth, getPaddingTop(), getPaddingRight(), getPaddingBottom());
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float labelHeight = (getMeasuredHeight()-getPaddingTop()-getPaddingBottom()) * 0.7f;
        float padding = (getMeasuredHeight()-getPaddingTop()-getPaddingBottom() - labelHeight) / 2;
        float top = getPaddingTop() + padding;
        float bottom = getPaddingTop() + padding + labelHeight;
        rectF.set(0, top, mLabelWidth, bottom);
        canvas.drawRoundRect(rectF, 10, 10, mPaint);
    }
}
