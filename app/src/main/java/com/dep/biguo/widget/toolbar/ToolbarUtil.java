package com.dep.biguo.widget.toolbar;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.ColorInt;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;

import com.dep.biguo.R;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.mmkv.UserCache;

public abstract class ToolbarUtil<T> {
    protected Activity activity;
    protected Fragment fragment;
    protected View fragmentRootView;

    private FrameLayout rootView;
    protected ImmerseLayout immerseLayout;
    protected int includeId;
    protected View anchorView;

    private boolean isMustDay;//当前页面是否强制为正常模式（不适配深色模式）

    protected OnScrollChangeRateListener<T> changeRateListener;

    public ToolbarUtil(Activity activity) {
        this.activity = activity;
        init();
    }

    public ToolbarUtil(Fragment fragment, View fragmentRootView) {
        this.fragment = fragment;
        this.fragmentRootView = fragmentRootView;
        if(!(fragmentRootView instanceof ViewGroup)){
            throw new ClassCastException("this fragmentRootView must is ViewGroup");
        }
        if(((ViewGroup)fragmentRootView).getChildCount() > 1){
            throw new IllegalStateException("this fragmentRootView can host only one direct child");
        }

        init();
    }

    private void init(){
        if(activity != null){
            //获得窗口对象
            Window window = activity.getWindow();
            //获取根布局
            ViewGroup decorView = window.getDecorView().findViewById(android.R.id.content);
            //包裹一层布局
            packageLayout(decorView);

        }else if(fragment != null){
            packageLayout(fragmentRootView);
        }
    }

    /**统一在内容布局外包裹一层布局
     */
    private void packageLayout(View parentView){
        ViewGroup parentGroup = (ViewGroup) parentView;
        //添加标题栏，获取setContentView()添加的布局
        ViewGroup contentView = (ViewGroup)parentGroup.getChildAt(0);
        //将旧的布局从根布局中移除
        parentGroup.removeView(contentView);
        //包裹一层布局，同时添加标题栏进去
        View newContentView = createContentView(contentView);
        parentGroup.addView(newContentView);
    }

    /**创建一层布局，把标题栏和内容布局添加进去
     */
    private ViewGroup createContentView(ViewGroup oldContentView){
        //创建新布局
        rootView = new FrameLayout(getContext());
        //新布局延用旧布局的布局方式，实现无缝替换
        rootView.setLayoutParams(oldContentView.getLayoutParams());

        //创建标题栏
        immerseLayout = createToolbarView(oldContentView);
        //添加标题栏到新布局中
        rootView.addView(immerseLayout);

        //旧布局替换成新的布局方式
        oldContentView.setLayoutParams(new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        //添加到最前面是为了不遮盖住标题栏
        rootView.addView(oldContentView, 0);

        return rootView;
    }

    /**创建标题栏布局
     */
    private ImmerseLayout createToolbarView(ViewGroup oldContentView){
        ImmerseLayout immerseLayout = new ImmerseLayout(getContext());
        immerseLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        immerseLayout.setBackgroundColor(ResourcesCompat.getColor(getContext().getResources(), R.color.white, getContext().getTheme()));
        //添加标题栏的布局
        if(includeId > 0) {
            immerseLayout.setInclude(includeId);
        }
        //如果没有指定锚点控件，就默认使用原始的旧布局作为锚点
        if(anchorView == null) {
            anchorView = oldContentView;
        }
        immerseLayout.setAnchor(anchorView);

        return immerseLayout;
    }

    /**<p>当布局可以滚动，且需要打开软键盘时，可通过此方法设置布局不被软键盘遮挡，并且可上下滚动</p>
     * <p><b color=#c9c9c9c>注：</b>需要配合{@link #setAnchorView(View)}设置值为null</p>
     */
    public T setSoftKeyboard(){
        if(activity != null){
            //获取标题栏的高度，因为setFitsSystemWindows(true)还没执行，所以获取到的高度不会包含一个状态栏高度
            immerseLayout.measure(0, 0);
            int marginTop = immerseLayout.getMeasuredHeight();
            //设置一个标题栏高度的外边距，使得内容不会被标题栏遮挡
            View contentView = rootView.getChildAt(1);
            contentView.setFitsSystemWindows(true);
            activity.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            //((FrameLayout.LayoutParams) contentView.getLayoutParams()).setMargins(0, marginTop, 0, 0);

        }else {
            //TODO 没测试过fragment这样设置是否可以实现在activity的效果
            fragment.getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            //获取标题栏的高度，因为setFitsSystemWindows(true)还没执行，所以获取到的高度不会包含一个状态栏高度
            immerseLayout.measure(0, 0);
            int marginTop = immerseLayout.getMeasuredHeight();
            //设置一个标题栏高度的外边距，使得内容不会被标题栏遮挡
            fragmentRootView.setFitsSystemWindows(true);
            ((FrameLayout.LayoutParams) fragmentRootView.getLayoutParams()).setMargins(0, marginTop, 0, 0);
        }
        return getT();
    }

    protected Context getContext(){
        if(activity != null){
            return activity;
        }

        if(fragment != null){
            return fragment.getContext();
        }
        return null;
    }

    /**设置锚点控件，这个控件会相对标题栏往下移动一个标题栏的高度，
     * @param anchorView 锚点控件，传递null表示沉浸到状态栏底部
     *
     * <p color=#c9c9c9c>注：若不设置背景颜色为透明，则可能看不出效果</p>
     */
    public T setAnchorView(View anchorView) {
        this.anchorView = anchorView;
        immerseLayout.setAnchor(anchorView);
        return getT();
    }

    /**设置标题隐藏或显示
     * @param visibility {@link View#VISIBLE},{@link View#INVISIBLE}，{@link View#GONE}
     */
    public T setVisibility(int visibility){
        immerseLayout.setVisibility(visibility);
        return getT();
    }

    public T setBackgroundColor(@ColorInt int color){
        immerseLayout.setBackgroundColor(color);
        return getT();
    }

    /**背景颜色跟随滚动变化
     * @param nestedScrollView 滚动布局
     */
    public T setFollowScrollListener(NestedScrollView nestedScrollView, OnScrollChangeRateListener<T> changeRateListener){
        this.changeRateListener = changeRateListener;

        //添加滚动监听
        NestedScrollView.OnScrollChangeListener scrollChangeListener = (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            //计算 toolbar的高度 与 滚动高度 的 比例
            int effectiveRange = Math.min(scrollY, immerseLayout.getHeight());//选取有效范围 0 - getHeight()之间
            float changeRate = 1f * effectiveRange / immerseLayout.getHeight();

            //设置toolbar的背景颜色
            setBackgroundWithChangeHeight(changeRate);

            if(this.changeRateListener != null){
                this.changeRateListener.onScrollChangeRate(getT(), changeRate, isDayNight());
            }
        };
        nestedScrollView.setOnScrollChangeListener(scrollChangeListener);

        //调用一次，相当于执行初始化
        scrollChangeListener.onScrollChange(nestedScrollView, 0, 0, 0, 0);

        return getT();
    }

    /**是否强制为正常模式（不适配深色模式），某些页面适配深色模式太丑，因此干脆不适配
     * @return
     */
    protected boolean isDayNight(){
        //不适配深色模式，则直接返回false
        if(isMustDay){
            return false;
        }
        return UserCache.isDayNight();
    }

    /**设置是否强制为正常模式（不适配深色模式）
     * @param mustDay 是否
     */
    public T setMustDay(Activity activity, boolean mustDay) {
        isMustDay = mustDay;
        //根据是否是深色模式，设置状态栏文字颜色和资源文件, 当强制不适配时，再根据APP的自身的模式进行选择适配
        if (isDayNight()) {
            StatusBarHelper.setStatusBarDarkMode(activity);
        } else {
            StatusBarHelper.setStatusBarLightMode(activity);
        }
        return getT();
    }

    /**根据 <b color=#c9c9c9c>toolbar控件高度</b> 与 <b color=#c9c9c9c>所依赖滚动控件的滚动距离</b> 的 <b color=#c9c9c9c>比值</b> 设置背景颜色
     * @param changeRate 比值
     */
    public void setBackgroundWithChangeHeight(float changeRate){
        //计算Toolbar的背景颜色
        int alpha = (int) (changeRate * 255);
        int rgb = isDayNight() ? ResourcesCompat.getColor(getContext().getResources(), R.color.white, getContext().getTheme()) : Color.WHITE;
        int color = Color.argb(alpha, Color.red(rgb), Color.green(rgb), Color.blue(rgb));

        //View.setBackgroundColor()方法在鸿蒙4.0上，设置argb值时，a为0的情况下，偶发出现不透明，但鸿蒙3.0正常
        immerseLayout.setBackground(new ColorDrawable(color));
    }

    public interface OnScrollChangeRateListener<T>{
        /**回调 <b color=#c9c9c9c>toolbar控件高度</b> 与 <b color=#c9c9c9c>所依赖滚动控件的滚动距离</b> 的 <b color=#c9c9c9c>比值</b>
         * @param changeRate 比值
         * @param isDay      是否是正常模式（非深色模式）
         */
        void onScrollChangeRate(T toolbar, float changeRate, boolean isDay);
    }

    /**返回泛型对象
     * @return 泛型对象
     */
    private T getT(){
        return (T) this;
    }
}
