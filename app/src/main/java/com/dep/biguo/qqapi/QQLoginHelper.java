package com.dep.biguo.qqapi;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.Nullable;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.hjq.toast.ToastUtils;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.tencent.tauth.UiError;

public class QQLoginHelper implements IUiListener {
    private AuthorizeListener listener;

    public void goAuthorize(Activity activity, AuthorizeListener listener){
        this.listener = listener;

        String APP_ID = "**********";
        Context application = activity.getApplicationContext();
        String authorities = activity.getPackageName()+".fileprovider";

        Tencent tencent = Tencent.createInstance(APP_ID, application, authorities);
        if(tencent.isQQInstalled(activity) && !tencent.isSessionValid()){
            tencent.login(activity, "", this);
        }else {
            ToastUtils.show("未安装QQ");
        }
    }

    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data){
        Tencent.onActivityResultData(requestCode, resultCode, data, this);
    }

    @Override
    public void onComplete(Object o) {
        LogUtil.d("dddd", o.toString());
        listener.success(o);
    }

    @Override
    public void onError(UiError uiError) {
        LogUtil.d("dddd", "QQ登录错误：" + GsonUtils.toJson(uiError));
    }

    @Override
    public void onCancel() {
        LogUtil.d("dddd", "QQ登录取消");
    }

    @Override
    public void onWarning(int i) {
        LogUtil.d("dddd", "QQ登录警告：" + i);
    }

    public interface AuthorizeListener{
        void success(Object o);
    }
}
