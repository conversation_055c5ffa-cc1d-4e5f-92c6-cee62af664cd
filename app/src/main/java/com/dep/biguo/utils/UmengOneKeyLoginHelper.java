package com.dep.biguo.utils;

import android.content.Context;

/*import com.biguo.utils.util.LogUtil;
import com.umeng.umverify.UMVerifyHelper;
import com.umeng.umverify.listener.UMTokenResultListener;*/

public class UmengOneKeyLoginHelper {
    public static void oneKeyLogin(Context context){
        /*UMVerifyHelper umVerifyHelper = UMVerifyHelper.getInstance(context, new UMTokenResultListener() {
            @Override
            public void onTokenSuccess(String s) {
                LogUtil.d("dddd", "友盟获取Token Success : "+s);

            }

            @Override
            public void onTokenFailed(String s) {
                LogUtil.d("dddd", "友盟获取Token Failed : "+s);

            }
        });
        umVerifyHelper.setAuthSDKInfo("RoRObq3oqEH+Gvrq265s7lpHquN5nRTxJV7y3IicO1jOtn26DqZ79aco55knnYBxvJ1OEqZyBMHZnpxBLPCszGpH8St4V+/rsUfAIIg3BoQrs0ZxO1ZxYIRKL8C4r2v9vP0NTmuv2g+sXSsfXCfq2I8+lNWCX3GffRsTM7eTmWgnnDx+0rCc6ZA5lybRuv0oNLvPS3+rS3ob+V8swaat+8x/70exUSlCKORHER/9SBYGsOqsT/9dSnb921W0RhjHcsJeKlKXPs3aLIusn32XQiTD3rA6VIZ3OMrDeTqEoELdmOwzrKDEjg==");
        umVerifyHelper.getLoginToken(context, 5000);*/
    }
}
