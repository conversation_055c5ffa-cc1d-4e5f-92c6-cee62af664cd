package com.dep.biguo.utils;

import android.content.Context;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.utils.mmkv.UserCache;
import com.umeng.commonsdk.UMConfigure;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

public class UrlAddParamUtil {
    /**url拼接公共参数
     * @param context 上下文
     * @param url 链接
     * @return 返回拼接好的链接
     */
    public static String addPublicParams(Context context, String url, boolean isShowTitle){
        String[] splitUrl = AppUtil.isEmpty(url, "").split("\\?");
        String scheme = splitUrl.length >= 1 ? splitUrl[0] : "";
        String params = splitUrl.length >= 2 ? splitUrl[1] : "";
        StringBuilder urlBuilder = new StringBuilder(AppUtil.isEmpty(scheme) ? "" : (scheme+"?"))
                .append(AppUtil.isEmpty(params) ? "" : params+"&")
                .append("device=1")
                .append(url.contains("channel") ? "" : String.format("&channel=%s", UMConfigure.sChannel))
                .append("&deviceModel=").append(DeviceHelper.getSystemModel())
                .append("&deviceVersion=").append(DeviceHelper.getSystemVersion())
                .append("&appVersion=").append(DeviceHelper.getVersionName(context))
                .append("&themeAuto=").append(UserCache.isDayNight() ? 1 : 0)
                .append("&showTitle=").append(isShowTitle ? 1 : 0);

        if (UserCache.getProvince() != null && !url.contains("province_id")) {
            urlBuilder.append(String.format("&province_id=%s", UserCache.getProvince().getId()));
        }
        if (UserCache.getUserCache() != null && !url.contains("users_id")) {
            urlBuilder.append(String.format("&users_id=%s", UserCache.getUserCache().getUser_id()));
        }
        if (UserCache.getUserCache() != null && !url.contains("token")) {
            UserBean userBean = UserCache.getUserCache();
            String token = AESEncrypt.encrypt(userBean.getToken(), userBean.getUser_id()+"");
            urlBuilder.append(String.format("&token=%s", token));
        }
        return urlBuilder.toString();
    }

    /**微信小程序链接拼接公共参数
     * @param context 上下文
     * @param path 链接
     * @param paramsUrl
     * @return
     */
    public static String addWechatPublicParams(Context context, String path, String paramsUrl){
        StringBuilder urlBuilder = new StringBuilder(path.contains("?") ? "&" : "?")
                .append("device=1")
                .append(path.contains("channel") ? "" : String.format("&channel=%s", encode(UMConfigure.sChannel)))
                .append("&deviceModel=").append(encode(DeviceHelper.getSystemModel()))
                .append("&deviceVersion=").append(encode(DeviceHelper.getSystemVersion()))
                .append("&appVersion=").append(encode(DeviceHelper.getVersionName(context)));
        if (UserCache.getProvince() != null && !path.contains("province_id=")) {
            urlBuilder.append(String.format("&province_id=%s", encode(UserCache.getProvince().getId()+"")));
        }
        if (UserCache.getCity() != null && !path.contains("city_id=")) {
            urlBuilder.append(String.format("&city_id=%s", encode(UserCache.getCity().getCity_id()+"")));
        }
        if (UserCache.getUserCache() != null && !path.contains("users_id=")) {
            urlBuilder.append(String.format("&users_id=%s", encode(UserCache.getUserCache().getUser_id()+"")));
        }
        if (UserCache.getUserCache() != null && !path.contains("token=")) {
            UserBean userBean = UserCache.getUserCache();
            urlBuilder.append(String.format("&token=%s", encode(userBean.getToken())));
        }
        if (!TextUtils.isEmpty(paramsUrl) && !path.contains("url=")) {
            urlBuilder.append(String.format("&url=%s", encode(paramsUrl)));
        }

        String encode = urlBuilder.toString().replaceAll("^\r|\n|\\s$", "");
        return String.format("%s%s", path, encode);
    }

    public static String encode(String value){
        try {
            return URLEncoder.encode(value, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String decode(String value){
        try {
            return URLDecoder.decode(value, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }
}
