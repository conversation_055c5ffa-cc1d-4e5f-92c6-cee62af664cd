package com.dep.biguo.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AppCompatActivity;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.cmic.gen.sdk.view.GenLoginAuthActivity;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.mvp.ui.activity.ZkLoginActivity;
import com.dep.biguo.utils.database.util.RealQuery;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.DiversificationTextView;
import com.biguo.utils.widget.LoadingDialog;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.wxapi.WxMinApplication;
import com.google.gson.reflect.TypeToken;
import com.hjq.toast.ToastUtils;
import com.jess.arms.integration.AppManager;
import com.readystatesoftware.chuck.ChuckInterceptor;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;

import org.simple.eventbus.EventBus;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.jiguang.verifysdk.CtLoginActivity;
import cn.jiguang.verifysdk.api.AuthPageEventListener;
import cn.jiguang.verifysdk.api.JVerificationInterface;
import cn.jiguang.verifysdk.api.JVerifyUIConfig;
import cn.jiguang.verifysdk.api.PrivacyBean;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class OneKeyLoginHelper {
    public static final int AUTH_METHOD1 = 100;//从userframent跳转的一键登录为方式1
    public static final int AUTH_METHOD2 = 200;//从loginactivity跳转的一键登录为方式2

    public static final int CODE_LOGIN_SUCCESS = 6000;
    public static final int CODE_LOGIN_CANCELD = 6002;

    private static OneKeyLoginHelper helper;
    private OneKeyShowListener oneKeyShowListener;
    private Call call;
    private static TextView tvPhoneLogin;//通过该控件被添加到授权页，逆向获取其父控件，再通过父控件获取到协议的勾选按钮，从而修改按钮的样式

    public static OneKeyLoginHelper getInstance(OneKeyShowListener listener){
        if(helper == null){
            helper = new OneKeyLoginHelper();
            helper.oneKeyShowListener = listener;
        }
        return helper;
    }


    private OkHttpClient.Builder createClient(Context context){
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(10000, TimeUnit.SECONDS)
                .readTimeout(10000, TimeUnit.SECONDS)
                .writeTimeout(10000, TimeUnit.SECONDS);

        if(!Api.isReleaseService() || LogUtil.isPrintLog()) {
            builder.addInterceptor(new ChuckInterceptor(context));
        }

        return builder;
    }

    private Callback getCallback(){
        return new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
                ToastUtils.show("登录失败，请检查网络");
                destroy();
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) {
                ResponseBody body = response.body();
                if (body != null) {
                    BaseResponse<UserBean> userBeanResponse;
                    String result;
                    try {
                        result = body.string();
                        LogUtil.d("dddd", result);
                        userBeanResponse = GsonUtils.fromJson(result, new TypeToken<BaseResponse<UserBean>>(){}.getType());
                    } catch (Exception e) {
                        LoadingDialog.hideLoadingDialog();
                        ToastUtils.show("解析失败，请重试！");
                        destroy();
                        return;
                    }

                    if(userBeanResponse.isSuccess()) {
                        loginSuccess(userBeanResponse.getData());

                    }else {
                        LoadingDialog.hideLoadingDialog();
                        if (userBeanResponse.isVersionLow()) {
                            String content = "APP版本过低，请更新后重试";
                            String negativeText = "";
                            String positiveText = "确定";
                            showMessageDialog(content, negativeText, positiveText,v -> System.exit(0));

                        } else if (userBeanResponse.isInBlackList()) {
                            String content = userBeanResponse.getResult_info();
                            String negativeText = "取消";
                            String positiveText = "确定";
                            showMessageDialog(content, negativeText, positiveText,v -> {
                                AppCompatActivity activity = (AppCompatActivity) AppManager.getAppManager().getCurrentActivity();
                                WxMinApplication.StartWechat(activity);
                            });

                        }else {
                            ToastUtils.show(userBeanResponse.getResult_info());
                        }
                    }
                } else {
                    LoadingDialog.hideLoadingDialog();
                    ToastUtils.show("数据未请求到！");
                }
                destroy();
            }
        };
    }

    private void showMessageDialog(String content, String negativeText, String positiveText, View.OnClickListener onPositiveClickListener){
        if (AppManager.getAppManager().getCurrentActivity() != null){
            AppCompatActivity activity = (AppCompatActivity) AppManager.getAppManager().getCurrentActivity();
            new MessageDialog.Builder(activity.getSupportFragmentManager())
                    .setContent(content)
                    .setNegativeText(negativeText)
                    .setPositiveText(positiveText)
                    .setPositiveClickListener(onPositiveClickListener)
                    .builder()
                    .show();

        }
        destroy();
    }

    /**获取首次安装APP时的参数
     * @return
     */
    private Map<String, String> getAppDownloadChannel(){
        //友盟超链携带的参数
        Map<String, String> linkParamsMap = GsonUtils.fromJson(UserCache.getLinkParams(), new TypeToken<HashMap<String, String>>(){}.getType());
        if(linkParamsMap == null) {
            linkParamsMap = new HashMap<>();
        }
        if(!linkParamsMap.containsKey("download_channel")) {
            linkParamsMap.put("download_channel", UMConfigure.sChannel);
        }
        linkParamsMap.put("deviceToken", DeviceCache.getUmengDeviceToken());
        return linkParamsMap;
    }

    private Request createRequest(String baseUrl, String appVersion, String androidID, String token){
        Map<String, String> map = new HashMap<>();
        map.put("access_token", token);
        map.put("address_details", UserCache.getAddress());
        map.put("longitude", UserCache.getLongitude());
        map.put("latitude", UserCache.getLatitude());
        map.put("city_id", UserCache.getCity() != null ? UserCache.getCity().getCity_id()+"" : "");
        map.put("province_id", UserCache.getProvince() != null ? UserCache.getProvince().getId()+"" : "");
        map.put("school_id", UserCache.getSchool() != null ? UserCache.getSchool().getId()+"" : "");
        map.put("professions_id", UserCache.getProfession() != null ? UserCache.getProfession().getId() + "" : "");
        map.put("layer_id", UserCache.getProfession() != null ? UserCache.getProfession().getLayer()+"" : "");
        map.put("adult_professions_id", UserCache.getProfession() != null ? UserCache.getProfession().getAdult_professions_id()+"" : "");
        if(!AppUtil.isEmpty(UserCache.getInstallTime())) {
            map.put("install_time", UserCache.getInstallTime());
        }
        map.putAll(getAppDownloadChannel());
        RequestBody body=RequestBody.create(MediaType.get("application/json"), GsonUtils.toJson(map));

        Request.Builder builder = new Request.Builder()
                .url(baseUrl+"user/oneKeyLogin")
                .header("device", "1")
                .header("channel", UMConfigure.sChannel)
                .header("deviceModel", DeviceHelper.getSystemModel())
                .header("deviceVersion", DeviceHelper.getSystemVersion())
                .header("appVersion", appVersion)
                .header("ua", AppUtil.isEmpty(DeviceCache.getUserAgent(), ""))
                .header("oaid", AppUtil.isEmpty(DeviceCache.getOAID(), ""))
                .header("imei", AppUtil.isEmpty(DeviceCache.getIMEI(), ""))
                .header("androidid", AppUtil.isEmpty(androidID, ""))
                .header("device_unique_id", AppUtil.isEmpty(androidID, ""))
                .header("hardware", AppUtil.isEmpty(DeviceCache.getHardwareInfo(), ""))
                .header("emulator", AppUtil.isEmpty(DeviceHelper.isEmulator(), ""))
                .post(body);

        //友盟超链的链接中的参数，需要在header头中也添加这两个参数
        String[] linkParamsKey = {"_linkid_", "um_from_appkey"};
        String[] linkParamsToKey = {"umLinkid", "umFromAppkey"};
        for(int i=0;i<linkParamsKey.length;i++) {
            String value = map.get(linkParamsKey[i]);
            if (value != null) {
                builder.header(linkParamsToKey[i], value);
            }
        }

        return builder.build();
    }

    private void loginSuccess(UserBean userBean){
        //登录成功后，判断是否是上次登录的账号，若不是，则删除缓存
        if(UserCache.isCacheLastUser(userBean)){
            RealQuery.clear();
        }

        JuliangHelper.reportRegister(JuliangHelper.ONE_KEY_REGISTER, userBean.getIs_create());

        UserCache.cacheUser(userBean);
        LogUtil.d("dddd", getClass().getName());
        UserCache.cacheLinkParams(null);
        MobclickAgent.onProfileSignIn(String.valueOf(userBean.getUser_id()));
        LoadingDialog.hideLoadingDialog();
        ToastUtils.show("登录成功");
        MainAppUtils.loginSuccessStartSurvey();
        EventBus.getDefault().post(userBean, EventBusTags.LOGIN_SUCCESS);
        destroy();

    }

    private void destroy(){
        if(call != null && !call.isCanceled()) call.cancel();
        tvPhoneLogin=null;
        call = null;
        if(oneKeyShowListener != null) {
            oneKeyShowListener.dismiss();
            oneKeyShowListener = null;
        }
        helper = null;
    }

    public void login(Context context, String appVersion, String token){
        for(int i = AppManager.getAppManager().getActivityList().size() - 1; i >= 0; i--){
            Activity activity = AppManager.getAppManager().getActivityList().get(i);
            //移动卡是GenLoginAuthActivity，联通卡或联通卡是CtLoginActivity
            if(!(activity instanceof GenLoginAuthActivity) && !(activity instanceof CtLoginActivity)){
                LoadingDialog.showLoadingDialog(activity);
                break;
            }
        }

        String androidID = AppUtil.isEmpty(DeviceCache.getAndroidId(context), "");
        Request request = createRequest(Api.BASE_URL, appVersion, androidID, token);
        call = createClient(context)
                .build()
                .newCall(request);
        call.enqueue(getCallback());
    }

    /**
     * 竖屏配置一键登录界面
     *
     * @return
     */
    public static JVerifyUIConfig getFullScreenPortraitConfig(Context mContext, int method, OneKeyShowListener listener) {
        JVerifyUIConfig.Builder uiConfigBuilder = new JVerifyUIConfig.Builder();
        if (null == mContext) return uiConfigBuilder.build();

        ImageView privateBackView = new ImageView(mContext);
        int padding = DisplayHelper.dp2px(mContext, 12);
        privateBackView.setPadding(padding, 0, padding, 0);
        privateBackView.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT));
        privateBackView.setImageResource(R.drawable.ic_close_black_24dp);

        List<PrivacyBean> privacyBeanList = new ArrayList<>();
        privacyBeanList.add(new PrivacyBean("隐私政策", Constant.AGREEMENT_USER4,""));
        privacyBeanList.add(new PrivacyBean("用户协议", Constant.AGREEMENT_USER,""));

        uiConfigBuilder
                .setAuthBGImgPath("bg_rect_cwhite")//设置整个页面的背景颜色
                .setStatusBarColorWithNav(true)//设置状态栏跟随导航栏颜色
                .setStatusBarDarkMode(!UserCache.isDayNight())//设置是否是暗色模式
                .setNavText("")//设置导航栏文字
                .setNavColor(mContext.getResources().getColor(R.color.white))//设置导航栏颜色
                .setNavReturnImgPath("ic_close_black_24dp")//设置导航栏返回按钮图片
                .setLogoImgPath("one_key_login")//设置logo
                .setLogoWidth(80)//设置logo宽度（单位：dp）
                .setLogoHeight(80)//设置logo高度（单位：dp）
                .setLogoOffsetY(122)//设置logo相对于标题栏下边缘y偏移（单位：dp）
                .setNumberColor(ContextCompat.getColor(mContext, R.color.tblack))//设置手机号码字体颜色
                .setNumberSize(24)//设置手机号码字体大小
                .setNumberTextBold(true)//设置手机号码字体是否加粗
                .setNumFieldOffsetY(232)//设置号码栏相对于标题栏下边缘y偏移（单位：dp）
                .setSloganTextColor(ContextCompat.getColor(mContext, R.color.tblack))//设置认证商文字颜色
                .setSloganTextSize(12)//设置认证商字体大小
                .setSloganOffsetY(265)//设置认证商相对于标题栏下边缘y偏移（单位：dp）
                .setLogBtnImgPath("selector_v2_click")//设置授权登录按钮图片
                .setLogBtnText("本机号码一键登录")//设置登录按钮文字
                .setLogBtnTextColor(Color.WHITE)//设置登录按钮文字颜色
                .setLogBtnTextSize(16)//设置登录按钮字体大小
                .setLogBtnWidth(295)//设置登录按钮宽度（单位：dp）
                .setLogBtnHeight(40)//设置登录按钮高度（单位：dp）
                .setLogBtnOffsetY(330)//设置登录按钮相对于标题栏下边缘y偏移（单位：dp）
                .enableHintToast(true, null)//协议栏checkbox未选中时，点击登录按钮是否弹出toast提示用户勾选协议
                .setCheckedImgPath("one_key_login_agree")//设置复选框选中时图片
                .setUncheckedImgPath("one_key_login_un_agree")//设置复选框未选中时图片
                .setPrivacyCheckboxSize(50)
                .setAppPrivacyColor(Color.GRAY, ContextCompat.getColor(mContext, R.color.theme))//设置隐私条款名称颜色(基础文字颜色，协议文字颜色)
                .setPrivacyText("我已阅读并同意","")//设置隐私条款名称外的文字
                .setPrivacyNameAndUrlBeanList(privacyBeanList)//自定义隐私条款
                .setPrivacyCheckboxInCenter(true)//设置隐私条款checkbox是否相对协议文字纵向居中。默认居顶
                .setPrivacyCheckboxSize(18)//设置隐私条款checkbox尺寸（单位：dp）
                .setPrivacyMarginL(20)//设置底部隐私政策距离屏幕左边的距离
                .setPrivacyMarginR(20)//设置底部隐私政策距离屏幕右边的距离
                .setPrivacyWithBookTitleMark(true)//设置隐私条款运营商.协议名是否加书名号
                .setPrivacyTextSize(12)//设置隐私条款文字字体大小（单位：sp）
                .setPrivacyNavTitleTextColor(mContext.getResources().getColor(R.color.tblack))//设置协议展示web页面导航栏标题文字颜色
                .setPrivacyNavTitleTextSize(16)//设置协议展示web页面导航栏标题文字大小
                .setPrivacyStatusBarDarkMode(!UserCache.isDayNight())//设置授权协议web页面状态栏暗色模式
                .setPrivacyStatusBarColorWithNav(true)//设置授权协议web页面状态栏与导航栏同色
                .setPrivacyNavReturnBtn(privateBackView)//设置协议展示web页面导航栏返回按钮图标
                .enablePrivacyCheckDialog(true)//支持设置未勾选隐私时打开弹窗；当设置 enablePrivacyCheckDialog 为 true 时，enableHintToast 也要同时设为 true
                .setPrivacyCheckDialogTitleTextSize(18)//协议二次弹窗标题字体大小
                .setPrivacyCheckDialogContentTextSize(12)//协议二次弹窗协议内容字体大小
                .setPrivacyCheckDialogContentTextGravity(Gravity.CENTER_HORIZONTAL)//协议二次弹窗协议内容对⻬方式
                .setPrivacyCheckDialogLogBtnImgPath("selector_v2_click")//协议二次弹窗登录按钮的背景图片
                .setPrivacyCheckDialogLogBtnWidth(295)//协议二次弹窗登录按钮的宽（单位：dp）
                .setPrivacyCheckDialogLogBtnHeight(40)//协议二次弹窗登录按钮的高（单位：dp）
                .setPrivacyCheckDialogOffsetY(300)
                .setPrivacyNavColor(mContext.getResources().getColor(R.color.white));//设置协议展示web页面导航栏背景颜色

        int dp15 = DisplayHelper.dp2px(mContext, 15);
        int dp18 = DisplayHelper.dp2px(mContext, 18);
        int dp5 = DisplayHelper.dp2px(mContext, 5);
        // 其它手机登录按钮
        RelativeLayout.LayoutParams layoutParamOtherPhoneLogin = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        int dp30 = DisplayHelper.dp2px(mContext, 30);
        layoutParamOtherPhoneLogin.setMargins(dp30, DisplayHelper.dp2px(mContext, 380), dp30, 0);
        layoutParamOtherPhoneLogin.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
        layoutParamOtherPhoneLogin.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        TextView tvOtherPhoneLogin = new TextView(mContext);
        tvOtherPhoneLogin.setText("验证码登录");
        tvOtherPhoneLogin.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        tvOtherPhoneLogin.setTextColor(ContextCompat.getColor(mContext, R.color.tblack3));
        tvOtherPhoneLogin.setLayoutParams(layoutParamOtherPhoneLogin);
        tvOtherPhoneLogin.setPadding(dp18, dp5, dp18, dp5);
        uiConfigBuilder.addCustomView(tvOtherPhoneLogin, true, (context, view) -> {
            if (AUTH_METHOD1 == method) {
                ZkLoginActivity.start(mContext, true);
                listener.dismiss();
            }
        });

        //问候语
        RelativeLayout.LayoutParams layoutParamsGreet = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsGreet.setMargins(dp15, 0, dp15, 0);
        layoutParamsGreet.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
        TextView tvGreet = new TextView(mContext);
        tvGreet.setId(5999);
        tvGreet.setLayoutParams(layoutParamsGreet);
        tvGreet.setText("");
        tvGreet.setTextColor(mContext.getResources().getColor(R.color.tblack));
        tvGreet.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 24);
        uiConfigBuilder.addCustomView(tvGreet, false, null);
        //广告
        RelativeLayout.LayoutParams layoutParamsAdvertisement = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsAdvertisement.setMargins(dp15, DisplayHelper.dp2px(mContext, 42), dp15, 0);
        layoutParamsAdvertisement.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
        DiversificationTextView tvAdvertisement = new DiversificationTextView(mContext);
        tvAdvertisement.setLayoutParams(layoutParamsAdvertisement);
        tvAdvertisement.setTextColor(mContext.getResources().getColor(R.color.tblack3));
        tvAdvertisement.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
        tvAdvertisement.setText("");
        uiConfigBuilder.addCustomView(tvAdvertisement, false, null);

        // 密码登录按钮
        RelativeLayout.LayoutParams layoutParamPhoneLogin = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamPhoneLogin.setMargins(0, 0, 0, DisplayHelper.dp2px(mContext, 30));
        layoutParamPhoneLogin.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        layoutParamPhoneLogin.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        tvPhoneLogin = new TextView(mContext);
        tvPhoneLogin.setText("密码登录");
        tvPhoneLogin.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        tvPhoneLogin.setTextColor(ContextCompat.getColor(mContext, R.color.tblack3));
        tvPhoneLogin.setLayoutParams(layoutParamPhoneLogin);
        tvPhoneLogin.setBackgroundResource(R.drawable.bg_round_200_bgc);
        tvPhoneLogin.setPadding(dp18, dp5, dp18, dp5);
        uiConfigBuilder.addCustomView(tvPhoneLogin, true, (context, view) -> {
            if (AUTH_METHOD1 == method) {
                ZkLoginActivity.start(mContext, false);
                listener.dismiss();
            }
        });


        return uiConfigBuilder.build();
    }

    /**一键登录需要开启移动流量，若由物联卡开启移动流量，则无法获取到token
     */
    public static void oneKeyLogin(Context context, OneKeyShowListener listener){
        if(MainAppUtils.isOnlySmsVerifyLogin()){
            ZkLoginActivity.start(context, true);
            listener.dismiss();
            return;
        }

        new UmengEventUtils(context)
                .addParams("count", "login")
                .addParams("systemModel", DeviceCache.getSystemModel())
                .pushEvent(UmengEventUtils.CLICK_GO_TO_LOGIN);

        //调起一键登录需要时间，弹出一个等待弹窗
        LoadingDialog.showLoadingDialog(context);

        JVerifyUIConfig uiConfig = getFullScreenPortraitConfig(context, AUTH_METHOD1, listener);
        JVerificationInterface.setCustomUIWithConfig(uiConfig, null);
        JVerificationInterface.loginAuth(context, true, (code, content, operator, jsonObject) -> {
            LogUtil.d("dddd", code+" "+content+" "+operator);
            if (code == CODE_LOGIN_SUCCESS) {
                if (TextUtils.isEmpty(content)) return;
                new UmengEventUtils(context)
                        .addParams("count", "one_key_login_authorize")
                        .addParams("systemModel", DeviceCache.getSystemModel())
                        .pushEvent(UmengEventUtils.CLICK_ONE_KEY_LOGIN_AUTHORIZE);

                OneKeyLoginHelper.getInstance(listener).login(context, DeviceHelper.getVersionName(context), content);

            }else if(code != CODE_LOGIN_CANCELD) {
                new UmengEventUtils(context)
                        .addParams("code", code+"")
                        .addParams("error", content)
                        .addParams("systemModel", DeviceCache.getSystemModel())
                        .pushEvent(UmengEventUtils.CLICK_ONE_KEY_LOGIN_CODE);

                //当登录页没有被调起时，关闭等待弹窗
                LoadingDialog.hideLoadingDialog();
                toFailedWithMsg(code, content);
                ZkLoginActivity.start(context, true);
                listener.dismiss();
            }
        }, new AuthPageEventListener() {
            @Override
            public void onEvent(int code, String error) {
                LogUtil.d("dddd", code + " " + error);

                if (code == 1) {//一键登录页面被关闭时
                    //将持有的控件释放掉，避免内存泄漏
                    tvPhoneLogin = null;

                } else if (code == 2) {//一键登录页面被打开时
                    new UmengEventUtils(context)
                            .addParams("count", "one_key_login_open")
                            .addParams("systemModel", DeviceCache.getSystemModel())
                            .pushEvent(UmengEventUtils.CLICK_ONE_KEY_LOGIN_OPEN);

                    //当页面被打开时，等待弹窗关闭
                    LoadingDialog.hideLoadingDialog();

                    //运营商继承的Activity属于android.app.Activity，AndroidX项目不适用,无法通过config.setAuthBGImgPath()方法适配夜间模式自动切换
                    //此处添加一个View实现夜间模式
                    View backgroundView = new View(context);
                    backgroundView.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                    backgroundView.setBackgroundResource(R.color.white);
                    ((ViewGroup) tvPhoneLogin.getParent().getParent()).addView(backgroundView, 0);

                    //隐私政策移动到=登录按钮下方
                    ViewGroup privateLayout = (ViewGroup) findCheckboxView((ViewGroup) tvPhoneLogin.getParent()).getParent().getParent();
                    RelativeLayout.LayoutParams layoutParamPhoneLogin = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    int dp30 = DisplayHelper.dp2px(context, 30);
                    layoutParamPhoneLogin.setMargins(dp30, DisplayHelper.dp2px(context, 420), dp30, 0);
                    layoutParamPhoneLogin.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
                    layoutParamPhoneLogin.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
                    privateLayout.setLayoutParams(layoutParamPhoneLogin);

                    //打开一键登录页面之前处于普通登录页面，则关闭普通页面
                    if (context instanceof ZkLoginActivity) {
                        ((ZkLoginActivity) context).finish();
                    }
                }
            }
        });
    }

    public static void toFailedWithMsg(int code, String errorMsg) {
        String msg = "";
        if (code == 2003) {
            msg = "网络连接不通";
        } else if (code == 2005) {
            msg = "请求超时";
        } else if (code == 2016) {
            msg = "当前网络环境不支持认证";
        } else if (code == 2010) {
            msg = "未开启读取手机状态权限";
        }else if (code == 6001) {
            msg = "";
        }
        if(!TextUtils.isEmpty(msg)) {
            ToastUtils.show(msg);
        }
    }

    public interface OneKeyShowListener{
        void dismiss();
    }

    public static void showViewName(View view, int layer){
        if(view instanceof TextView){
            LogUtil.d("ddddd", getTab(layer) + view.getClass().getName()+"    "+((TextView) view).getText().toString());

        }else {
            LogUtil.d("ddddd", getTab(layer) + view.getClass().getName());
        }
        if(view instanceof ViewGroup){
            for(int i=0;i<((ViewGroup)view).getChildCount();i++){
                showViewName(((ViewGroup)view).getChildAt(i), layer+1);
            }
        }
    }

    public static String getTab(int layer){
        String tab = "";
        for(int i=0;i<layer;i++){
            tab += "\t";
        }

        return tab;
    }
    /**直接递归找到CheckBox类型的组件
     * @param parentView 自定义组件所在的父控件
     * @return 同意协议的CheckBox组件
     */
    public static View findCheckboxView(View parentView){
        if(parentView instanceof ViewGroup){
            for(int i=0;i<((ViewGroup)parentView).getChildCount();i++){
                View privateCheckView = findCheckboxView(((ViewGroup)parentView).getChildAt(i));
                if(privateCheckView instanceof CheckBox){
                    return privateCheckView;
                }
            }
        }
        return parentView;
    }
}
