package com.dep.biguo.utils.imp;

import android.text.method.DigitsKeyListener;

import androidx.annotation.Nullable;

import java.util.Locale;

public class KeyboardImp extends DigitsKeyListener {
    private int inputType;
    private String digits;

    public KeyboardImp(int inputType, String digits) {
        this.inputType = inputType;
        this.digits = digits;
    }

    @Override
    public int getInputType() {
        return super.getInputType();
    }

    @Override
    protected char[] getAcceptedChars() {
        return digits.toCharArray();
    }
}
