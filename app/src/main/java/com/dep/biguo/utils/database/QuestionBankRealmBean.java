package com.dep.biguo.utils.database;

import io.realm.RealmObject;
import io.realm.annotations.Required;

public class QuestionBankRealmBean extends RealmObject {
    private int type;//题库类型
    private String name;//题库名称
    private int total;//题库总数
    private int cal_nums;//可答题数
    private int answered;//已答题数
    private int has;//是否已上架
    private String desc;//广告文字
    private String intro;//网络助学需要用到
    private long version;//题库版本号

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getCal_nums() {
        return cal_nums;
    }

    public void setCal_nums(int cal_nums) {
        this.cal_nums = cal_nums;
    }

    public int getAnswered() {
        return answered;
    }

    public void setAnswered(int answered) {
        this.answered = answered;
    }

    public int getHas() {
        return has;
    }

    public void setHas(int has) {
        this.has = has;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }
}
