package com.dep.biguo.utils.umengPush;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.dep.biguo.BuildConfig;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.PushBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.CKMainActivity;
import com.dep.biguo.mvp.ui.activity.FeedbackListActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.JSZMainActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.MessageListActivity;
import com.dep.biguo.mvp.ui.activity.MyActivity;
import com.dep.biguo.mvp.ui.activity.MyCouponActivity;
import com.dep.biguo.mvp.ui.activity.MyVideoActivity;
import com.dep.biguo.mvp.ui.activity.OrderDetailActivity;
import com.dep.biguo.mvp.ui.activity.PayTuitionHistoryActivity;
import com.dep.biguo.mvp.ui.activity.RechargeDetailActivity;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.jiguangPush.JPushHelper;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.jess.arms.integration.AppManager;
import com.taobao.accs.ACCSClient;
import com.taobao.accs.AccsClientConfig;
import com.taobao.agoo.TaobaoRegister;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.message.PushAgent;
import com.umeng.message.UmengMessageHandler;
import com.umeng.message.UmengNotificationClickHandler;
import com.umeng.message.api.UPushRegisterCallback;
import com.umeng.message.api.UPushTagCallback;
import com.umeng.message.common.inter.ITagManager;
import com.umeng.message.entity.UMessage;

import org.android.agoo.huawei.HuaWeiRegister;
import org.android.agoo.oppo.OppoRegister;
import org.android.agoo.vivo.VivoRegister;
import org.android.agoo.xiaomi.MiPushRegistar;
import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

public class UmengPushHelper {
    public static final String TAG = UmengPushHelper.class.getSimpleName();

    public static final String USER_ALIAS_TYPE = "USER";//别名类别
    public static final String USER_ALIAS = "user:";//用户别名

    public static final String PROVINCE_TAG = "province:";//省份标签
    public static final String SCHOOL_TAG = "school:";//学校标签
    public static final String PROFESSIONS_TAG = "professions:";//专业标签
    public static final String GRADE_TAG = "grade:";//证书标签
    public static final String USER_TAG = "users:";//购买商品类型标签
    public static final String COURSE_TAG = "courses:";//课程记录
    public static final String REFUND_TAG = "refund:";//申请退款时间的标签
    public static final String REGISTER_TAG = "register:";//注册时间标签

    /**
     * 预初始化，已添加子进程中初始化sdk。
     * 使用场景：用户未同意隐私政策协议授权时，延迟初始化
     *
     * @param context 应用上下文
     */
    public static void preInit(Context context) {
        try {
            //解决推送消息显示乱码的问题
            AccsClientConfig.Builder builder = new AccsClientConfig.Builder();
            builder.setAppKey("umeng:" + PushConstants.APP_KEY);
            builder.setAppSecret(PushConstants.MESSAGE_SECRET);
            builder.setTag(AccsClientConfig.DEFAULT_CONFIGTAG);
            ACCSClient.init(context, builder.build());
            TaobaoRegister.setAccsConfigTag(context, AccsClientConfig.DEFAULT_CONFIGTAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
        UMConfigure.preInit(context, PushConstants.APP_KEY, null);
        UMConfigure.submitPolicyGrantResult(context, true);
    }

    /**
     * 初始化。
     * 场景：用户已同意隐私政策协议授权时
     *
     * @param context 应用上下文
     */
    public static void init(Context context) {
        new Thread(() -> {
            // 在此处调用基础组件包提供的初始化函数 相应信息可在应用管理 -> 应用信息 中找到 http://message.umeng.com/list/apps
            // 参数一：当前上下文context；
            // 参数二：应用申请的Appkey；
            // 参数三：渠道名称；
            // 参数四：设备类型，必须参数，传参数为UMConfigure.DEVICE_TYPE_PHONE则表示手机；传参数为UMConfigure.DEVICE_TYPE_BOX则表示盒子；默认为手机；
            // 参数五：Push推送业务的secret 填充Umeng Message Secret对应信息
            UMConfigure.setLogEnabled(BuildConfig.DEBUG);
            UMConfigure.init(context, UMConfigure.DEVICE_TYPE_PHONE, PushConstants.MESSAGE_SECRET);

            pushAdvancedFunction(context);

            PushAgent pushAgent = PushAgent.getInstance(context);
            pushAgent.onAppStart();
            //注册推送服务，每次调用register方法都会回调该接口
            pushAgent.register(new UPushRegisterCallback() {
                @Override
                public void onSuccess(String deviceToken) {
                    //注册成功会返回deviceToken deviceToken是推送消息的唯一标志
                    LogUtil.d("dddd", "deviceToken --> " + deviceToken);
                    DeviceCache.cacheUmengDeviceToken(deviceToken);
                    registerDeviceChannel(context);
                }

                @Override
                public void onFailure(String errCode, String errDesc) {
                    LogUtil.d("dddd", "register failure：--> " + "code:" + errCode + ",desc:" + errDesc);
                }
            });
        }).start();

    }

    /**
     * 注册设备推送通道（小米、华为等设备的推送）
     *
     * @param context 应用上下文
     */
    private static void registerDeviceChannel(Context context) {
        //小米通道，填写您在小米后台APP对应的xiaomi id和key
        MiPushRegistar.register(context, PushConstants.MI_ID, PushConstants.MI_KEY, false);
        //华为，注意华为通道的初始化参数在minifest中配置
        HuaWeiRegister.register((Application) context.getApplicationContext());
        //OPPO，填写您在OPPO后台APP对应的app key和secret
        OppoRegister.register(context, PushConstants.OPPO_KEY, PushConstants.OPPO_SECRET);
        //vivo，注意vivo通道的初始化参数在minifest中配置
        VivoRegister.register(context);
    }

    /**根据订单类型返回对应的所属列表类型
     *
     */
    public static int getListType(String orderType){
        if(PayUtils.BOOK.equals(orderType)){
            return PayUtils.BOOK_LIST;
        }else if(PayUtils.MEMBERSHIP.equals(orderType) || PayUtils.SUPER_VIP.equals(orderType)){
            return PayUtils.SERVICE_LIST;
        }else {
            return PayUtils.CONSUMPTION_LIST;
        }
    }

    public static String getDeviceToken(Context context){
        PushAgent pushAgent = PushAgent.getInstance(context);
        return pushAgent.getRegistrationId();
    }

    /**用户登录时设置别名
     * @param context 上下文
     */
    public static void setLoginAlias(Context context){
        if(UserCache.getUserCache() == null) return;

        PushAgent pushAgent = PushAgent.getInstance(context);
        pushAgent.setAlias(USER_ALIAS + UserCache.getUserCache().getUser_id(), USER_ALIAS_TYPE, (success, message) -> {
            LogUtil.d("ddddd", "is register "+ success+", "+message);
        });
        JPushHelper.setLoginAlias(context);
    }

    /**用户登录时设置标签
     * @param context 上下文
     */
    public static void setLoginTag(Context context, List<String> tags){
        if(UserCache.getUserCache() == null) return;
        String date = UserCache.getUserCache().getCreated_at();
        if(TextUtils.isEmpty(date) || tags == null) return;

        tags.add(REGISTER_TAG + date.substring(0, date.indexOf("-")));
        tags.add(REGISTER_TAG + date.substring(0, date.lastIndexOf("-")));
        tags.add(REGISTER_TAG + date);

        PushAgent pushAgent = PushAgent.getInstance(context);
        pushAgent.getTagManager().addTags((b, result) -> {
            getTags(context);
            LogUtil.d("dddd", "登录添加用户相关标签"+b);
            EventBus.getDefault().post(tags.toArray(new String[0]), EventBusTags.ADD_PUSH_TAG);
        }, tags.toArray(new String[0]));

        JPushHelper.setLoginTag(context, tags);
    }

    /**退出登录时删除别名
     * @param context 上下文
     * @param userId 用户ID
     */
    public static void deleteLoginAlias(Context context, int userId){
        PushAgent pushAgent = PushAgent.getInstance(context);
        pushAgent.deleteAlias(USER_ALIAS + userId, USER_ALIAS_TYPE, (success, message) -> {
            LogUtil.d("dddd","delete alias is "+success);
            LogUtil.d("dddd","message is "+message);
        });
        deleteLoginTag(context);

        JPushHelper.deleteLoginAlias(context, userId);
    }

    /**退出登录时删除私有标签
     * @param context 上下文
     */
    public static void deleteLoginTag(Context context){
        PushAgent pushAgent = PushAgent.getInstance(context);
        pushAgent.getTagManager().getTags((getTagSuccess, list) -> {
            if(!getTagSuccess) return;

            //筛选出公共标签,其余标签全部删除
            List<String> removeTags = new ArrayList<>();
            for(String tag : list){
                if(!tag.startsWith(PROVINCE_TAG) && !tag.startsWith(SCHOOL_TAG) && !tag.startsWith(PROFESSIONS_TAG) && !tag.startsWith(GRADE_TAG)){
                    removeTags.add(tag);
                }
            }

            //删除所有的标签
            pushAgent.getTagManager().deleteTags((b, result) -> {
                LogUtil.d("dddd", "delete all is "+b);
                LogUtil.d("dddd","message is "+new Gson().toJson(result));
            }, removeTags.toArray(new String[0]));
        });

        JPushHelper.deleteLoginTag(context);
    }

    /**添加标签前，先获取所有标签，根据前缀剔除不需要的标签，再追加标签
     * @param pushAgent 推送消息实例
     * @param callBack 删除标签的回调对象
     * @param startWithArray 标签的前缀的数组
     */
    public static void deleteTag(PushAgent pushAgent, UPushTagCallback<ITagManager.Result> callBack, String...startWithArray){
        pushAgent.getTagManager().getTags((getTagSuccess, list) -> {
            if(!getTagSuccess) return;

            //获取到的标签数量是0，则不需要剔除标签
            if(list.size() == 0){
                callBack.onMessage(true, new ITagManager.Result());
                return;
            }

            //记录删除的数量
            int deleteCount = 0;
            //筛选不需要的标签
            List<String> deleteList = new ArrayList<>();
            for(String tag : list){
                for (String startWidth : startWithArray){
                    if(tag.startsWith(startWidth)) {
                        deleteList.add(tag);
                        deleteCount++;
                        break;
                    }
                }
                //当删除的数量等于匹配集合的数量，则认为全部删除了，结束循环
                if(deleteCount == startWithArray.length) break;
            }

            //删除不需要的标签
            pushAgent.getTagManager().deleteTags(callBack, deleteList.toArray(new String[0]));
        });
    }

    public static void getTags(Context context){
        if(!BuildConfig.DEBUG) return;
        PushAgent pushAgent = PushAgent.getInstance(context);
        pushAgent.getTagManager().getTags((b, list) -> LogUtil.d("dddd", "tags：" + new Gson().toJson(list)));

        JPushHelper.getTags(context);
    }

    /**注册省份、学校、专业的标签
     * @param context 上下文
     */
    public static void addExamInfoTag(Context context){
        if(UserCache.getProvince() == null || UserCache.getSchool() == null || UserCache.getProfession() == null){
            return;
        }

        PushAgent pushAgent = PushAgent.getInstance(context);
        deleteTag(pushAgent, (b, result) -> {
            //追加新的标签
            String[] tags = new String[3];
            tags[0] = PROVINCE_TAG + UserCache.getProvince().getId();
            tags[1] = SCHOOL_TAG + UserCache.getSchool().getId();
            tags[2] = PROFESSIONS_TAG + UserCache.getProfession().getId();
            pushAgent.getTagManager().addTags((addTagSuccess, result1) -> {
                if(addTagSuccess){
                    EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
                }
            }, tags);
        }, PROVINCE_TAG, SCHOOL_TAG, PROFESSIONS_TAG);

        JPushHelper.addExamInfoTag(context);
    }

    /**注册证书类型标签
     *
     * @param context 上下文
     * @param type 证书类型：zk自考 adult成考 skill教师
     */
    public static void addGradeTag(Context context, String type){
        if(TextUtils.isEmpty(type)) return;

        String[] tags = new String[]{GRADE_TAG + type};
        PushAgent pushAgent = PushAgent.getInstance(context);
        //追加新的标签
        pushAgent.getTagManager().addTags((addTagSuccess, result1) -> {
            if(addTagSuccess){
                EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
            }
        }, tags);

        JPushHelper.addGradeTag(context, type);
    }

    /**注册购买商品的标签
     * @param context 上下文
     * @param type 商品类型，参考{@link com.dep.biguo.utils.pay.PayUtils}中的订单类型
     * @param code 课程代码
     */
    public static void addBuyGoodTag(Context context, String type, String code){
        PushAgent pushAgent = PushAgent.getInstance(context);
        String[] tags;
        //具有课程代码的商品才需要注册COURSE_TAG标签
        if(PayUtils.YAMI.equals(type) && Constant.CK.equals(UserCache.getAppType())){
            //成考的押密是所有课程整合在一起的，没有课程代码，打不了标签
            return;

        }else if(PayUtils.VIP.equals(type) || PayUtils.YAMI.equals(type) || PayUtils.VIDEO.equals(type) ||
                PayUtils.YAMI_RESERVE.equals(type) || PayUtils.REAL_PAPER.equals(type) ||
                PayUtils.CHAPTER.equals(type)) {
            tags = new String[]{USER_TAG+type, COURSE_TAG+code+"_"+type};

        }else {
            tags = new String[]{USER_TAG+type};
        }

        //追加新的标签
        pushAgent.getTagManager().addTags((addTagSuccess, result1) -> {
            if(addTagSuccess){
                EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
            }
        }, tags);

        JPushHelper.addBuyGoodTag(context, type, code);
    }

    //推送高级功能集成说明
    private static void pushAdvancedFunction(Context context) {
        PushAgent pushAgent = PushAgent.getInstance(context);

        //设置通知栏显示通知的最大个数（0～10），0：不限制个数
        pushAgent.setDisplayNotificationNumber(0);

        //推送消息处理
        UmengMessageHandler msgHandler = new UmengMessageHandler() {
            //处理通知栏消息
            @Override
            public void dealWithNotificationMessage(Context context, UMessage msg) {
                super.dealWithNotificationMessage(context, msg);
                LogUtil.i(TAG, "notification receiver:" + msg.getRaw().toString());
                EventBus.getDefault().post(0, EventBusTags.PUSH_MESSAGE_RECEIVER);
            }

            //处理透传消息
            @Override
            public void dealWithCustomMessage(Context context, UMessage msg) {
                super.dealWithCustomMessage(context, msg);
                LogUtil.i(TAG, "custom receiver:" + msg.getRaw().toString());
            }
        };
        pushAgent.setMessageHandler(msgHandler);

        //推送消息点击处理
        pushAgent.setNotificationClickHandler(new UmengNotificationClickHandler() {
            /**openActivity对应后台的go_activity
             * @param context 上下文
             * @param msg
             */
            @Override
            public void openActivity(Context context, UMessage msg) {
                receiverNotification(context, msg);
            }

            @Override
            public void handleMessage(Context context, UMessage msg) {
                if(!msg.dismiss) {
                    receiverNotification(context, msg);
                }
            }
        });
    }

    public static void receiverNotification(Context context, UMessage msg){
        LogUtil.d("dddd", msg);
        Intent mainIntent = null;
        if(AppManager.getAppManager().getCurrentActivity() == null){
            if(Constant.CK.equals(UserCache.getAppType())){
                mainIntent = new Intent(context, CKMainActivity.class);
            }else if(Constant.JSZ.equals(UserCache.getAppType())){
                mainIntent = new Intent(context, JSZMainActivity.class);
            }else{
                mainIntent = new Intent(context, MainActivity.class);
            }
            //无activity时，需要添加一个新的栈，否则热启动方式无法启动应用
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        PushBean extraBean = GsonUtils.fromJson(GsonUtils.toJson(msg.extra), PushBean.class);
        startActivity(context, mainIntent, extraBean);
    }

    /**
     * @param context 上下文
     * @param mainIntent 跳转到首页的意图
     * @param extraBean 通知
     */
    public static void startActivity(Context context, Intent mainIntent, PushBean extraBean){
        if(PushBean.ORDER_PAGE.equals(extraBean.page)){
            //跳转到订单详情
            OrderDetailActivity.start(context,mainIntent, extraBean.order_id, getListType(extraBean.type));

        }else if(PushBean.INFO_CENTER.equals(extraBean.page)){
            //跳转到消息列表，根据type获取对应的消息
            MessageListActivity.start(context, mainIntent, Integer.parseInt(extraBean.type));

        }else if(PushBean.COURSE.equals(extraBean.page)){
            //跳转到我的题库，根据type选择题库的列表展示
            if(extraBean.type.equals("vip")) {
                MyActivity.start(context, mainIntent, 0);

            }else if(extraBean.type.equals("yami")){
                MyActivity.start(context, mainIntent, 1);

            }else if(extraBean.type.equals("video")){
                MyVideoActivity.start(context);

            }

        }else if(PushBean.FRUITCOIN.equals(extraBean.page)){
            //跳转到果币明细
            RechargeDetailActivity.start(context, mainIntent, RechargeDetailActivity.RECHARGE);

        }else if(PushBean.INTEGRAL.equals(extraBean.page)){
            //积分变动不需要打标签，后台会通过别名推送，因此此处也要做相应的跳转
            RechargeDetailActivity.start(context, mainIntent, RechargeDetailActivity.INTEGRAL);

        }else if(PushBean.COUPONS.equals(extraBean.page)){
            //跳转到优惠券页面
            MyCouponActivity.start(context, mainIntent);

        }else if(PushBean.URL.equals(extraBean.page)){
            //根据链接打开h5页面
            HtmlActivity.start(context, mainIntent, extraBean.url);

        }else if(PushBean.FEED_BACK.equals(extraBean.page)){
            //跳转到反馈列表
            FeedbackListActivity.start(context, mainIntent);

        }else if(PushBean.TUITION.equals(extraBean.page)){
            //跳转到缴费历史页面
            PayTuitionHistoryActivity.start(context, mainIntent);

        }
    }

}
