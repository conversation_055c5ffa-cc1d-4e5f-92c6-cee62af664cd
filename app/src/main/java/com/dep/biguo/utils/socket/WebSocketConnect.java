package com.dep.biguo.utils.socket;

import android.os.CountDownTimer;

import com.dep.biguo.mvp.model.api.Api;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

public class WebSocketConnect {
    private static final int ON_CREATE = 0;//创建状态
    private static final int ON_RESUME = 1;//连接中
    private static final int ON_PAUSE = 2;//暂停（实际长链接已断开）
    private static final int ON_RECONNECT = 3;//重连状态，只有在还是ON_RESUME状态，但长链接断开时，才会变为重连
    private static final int ON_DESTROY = -1;//销毁

    private static final int SEND_HEARTBEAT_TIME = 50;//发送心跳包的时间间隔
    private static final int RECONNECT_SHORT_TIME = 5;//前三次的重连时间间隔
    private static final int RECONNECT_LONG_TIME = 10;//前三次重连失败后的重连时间间隔
    private static final int COUNT_DOWN_TICK = 1;//计时器的间隔,单位：秒

    private WebSocket mWebSocket;
    private OkHttpClient client;
    private Request request;
    private int sendHeartbeatCount;//心跳包发送次数，用于日志输出
    private int sendHeartbeatTime;//记录距离下一次发送心跳包已过去的时间
    private int reconnectCount;//重连次数
    private int reconnectTime;//记录距离下一次重连已过去的时间
    private int status = ON_DESTROY;//长链接状态
    private CountDownTimer timer = new CountDownTimer(COUNT_DOWN_TICK*1000000, COUNT_DOWN_TICK * 1000) {
        @Override
        public void onTick(long millisUntilFinished) {
            if(status == ON_RESUME && sendHeartbeatTime > SEND_HEARTBEAT_TIME) {
                sendHeartbeatCount++;
                sendHeartbeatTime = COUNT_DOWN_TICK;
                mWebSocket.send(sendHeartbeatCount + "");
                LogUtil.d("kkkk", "心跳包发送完成");

            }else if(status == ON_RESUME) {
                LogUtil.d("kkkk",String.format("%s秒后发送心跳包", SEND_HEARTBEAT_TIME - sendHeartbeatTime));
                //记录距离下一次发送心跳包已过去的时间
                sendHeartbeatTime += COUNT_DOWN_TICK;

            }else if(status == ON_RECONNECT && reconnectTime > RECONNECT_SHORT_TIME && reconnectCount <= 3){
                //间隔5秒后，发起重连
                LogUtil.d("kkkk",String.format("正在尝试第%s次重连", reconnectCount));
                reconnectTime = COUNT_DOWN_TICK;
                reconnect();

            }else if(status == ON_RECONNECT && reconnectTime > RECONNECT_LONG_TIME){
                //前三次重连失败后，间隔了60秒再次发起重连尝试
                LogUtil.d("kkkk",String.format("正在尝试第%s次重连", reconnectCount));
                reconnectTime = COUNT_DOWN_TICK;
                reconnect();

            }else if(status == ON_RECONNECT){
                //记录距离下一次重连已过去的时间
                LogUtil.d("kkkk",String.format("%s秒后尝试重连", (reconnectCount <= 3 ? RECONNECT_SHORT_TIME : RECONNECT_LONG_TIME) - reconnectTime));
                reconnectTime += COUNT_DOWN_TICK;
            }
        }

        @Override
        public void onFinish() {
            //ON_RESUME状态下实现自启
            timer.start();
        }
    };


    /**初始化
     * @param mainType 题库类型（免费题库1 真题2 模拟试卷3 VIP题库4 章节训练5 押密7 视频8  高频题库11）
     * @param topic_type 题型（单选、多选、问答等，参考{@link PracticeHelper#TYPE_SINGLE}, 没有题型的默认为0）
     */
    public void create(String code, int mainType, int topic_type){
        status = ON_CREATE;

        String url = new StringBuilder("ws://ws.biguotk.com:")
                .append(Api.isReleaseService() ? "9503" : "9502")
                .append("?users_id=").append(UserCache.getUserCache().getUser_id())
                .append("&code=").append(code)
                .append("&mainType=").append(mainType)
                .append("&topic_type=").append(topic_type).toString();

        LogUtil.d("dddd", "socket:"+url);

        client = new OkHttpClient.Builder()
                .readTimeout(60, TimeUnit.SECONDS)//设置读取超时时间
                .writeTimeout(60, TimeUnit.SECONDS)//设置写的超时时间
                .connectTimeout(60, TimeUnit.SECONDS)//设置连接超时时间
                .build();
        request = new Request.Builder().url(url).build();
    }

    /**初始化(给技能证视频调用)
     * @param cert_type   考试类型：0自考，1成考，2技能证，9职场提升
     * @param source_type 来源类型：默认0笔果，1必过，2时刻
     * @param skill_id  第三方专业id（目前仅用于技能证、职场提升）ps:技能证、职场提升必传
     */
    public void create(String code, int cert_type, int source_type, int skill_id, int product_id){
        status = ON_CREATE;

        String url = new StringBuilder("ws://ws.biguotk.com:")
                .append(Api.isReleaseService() ? "9503" : "9502")
                .append("?users_id=").append(UserCache.getUserCache().getUser_id())
                .append("&code=").append(code)
                .append("&mainType=").append(8)
                .append("&cert_type=").append(cert_type)
                .append("&source_type=").append(source_type)
                .append("&product_id=").append(product_id)
                .append("&professions_id=").append(UserCache.getProfession() != null ? UserCache.getProfession().getId() : "")
                .append("&adult_professions_id=").append(UserCache.getProfession() != null ? UserCache.getProfession().getAdult_professions_id() : "")
                .append("&skill_id=").append(skill_id).toString();

        LogUtil.d("dddd", "socket:"+url);

        client = new OkHttpClient.Builder()
                .readTimeout(60, TimeUnit.SECONDS)//设置读取超时时间
                .writeTimeout(60, TimeUnit.SECONDS)//设置写的超时时间
                .connectTimeout(60, TimeUnit.SECONDS)//设置连接超时时间
                .build();
        request = new Request.Builder().url(url).build();
    }

    /**
     * 重连
     * */
    public void reconnect(){
        status = ON_RESUME;
        if(client != null) {
            mWebSocket = client.newWebSocket(request, createListener());
        }
    }

    /**
     * 恢复长链接
     */
    public void resume(){
        reconnect();
        if(timer != null) {
            timer.start();
        }
    }

    /**
     * 停止长链接
     */
    public void pause(){
        status = ON_PAUSE;
        if(timer != null){
            timer.cancel();
        }
        if(mWebSocket != null) {
            mWebSocket.cancel();
        }
    }

    /**
     * 销毁长链接
     */
    public void destroy(){
        status = ON_DESTROY;
        timer = null;
        mWebSocket = null;
        client = null;
    }

    /**长链接的监听
     * @return
     */
    private WebSocketListener createListener() {
        return new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                super.onOpen(webSocket, response);
                LogUtil.d("kkkk", "已连接:" + response.toString());
                reconnectCount = 0;
                sendHeartbeatTime = 0;
                mWebSocket = webSocket;
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                super.onMessage(webSocket, text);
                LogUtil.d("kkkk", "接收到:" + text);

            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                super.onMessage(webSocket, bytes);
                LogUtil.d("kkkk", "接收到:" + bytes.toString());
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                super.onClosing(webSocket, code, reason);
                LogUtil.d("kkkk", "关闭中:" + code+", "+reason);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                super.onClosed(webSocket, code, reason);
                LogUtil.d("kkkk", "已关闭:" + code+", "+reason);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                super.onFailure(webSocket, t, response);
                //只要断开，就停止发送心跳包
                //当状态等于ON_RESUME,需要重连，其它不管
                if(status == ON_RESUME){
                    //记录重连的次数
                    reconnectCount++;
                    //设置为重连状态，计时器会每秒检测一次该状态，根据状态来执行代码
                    status = ON_RECONNECT;
                    LogUtil.d("kkkk", reconnectCount == 1 ? "长链接意外断开" : "长链接重连失败");

                }else {
                    if(status == ON_PAUSE) {
                        LogUtil.d("kkkk", "离开当前页面，长链接断开");
                    }else if(status == ON_DESTROY){
                        LogUtil.d("kkkk", "销毁当前页面，长链接断开");
                    }
                }
                //LogUtil.e("kkkk", "连接异常，具体情况请看Debug等级的日志");
                LogUtil.e("kkkk", Arrays.toString(t.getStackTrace()), t);
            }
        };
    }

    public interface OnErrorListener{
        void onError(String message);
    }

}
