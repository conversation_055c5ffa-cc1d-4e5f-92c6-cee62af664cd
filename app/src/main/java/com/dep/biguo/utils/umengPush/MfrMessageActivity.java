package com.dep.biguo.utils.umengPush;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.dep.biguo.bean.PushBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.CKMainActivity;
import com.dep.biguo.mvp.ui.activity.JSZMainActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.umeng.message.UmengNotifyClick;
import com.umeng.message.entity.UMessage;


public class MfrMessageActivity extends Activity {

    private final UmengNotifyClick mNotificationClick = new UmengNotifyClick() {
        @Override
        public void onMessage(UMessage msg) {
            LogUtil.d("dddd", msg);
            PushBean extraBean = GsonUtils.fromJson(GsonUtils.toJson(msg.extra), PushBean.class);
            Intent mainIntent = new Intent(MfrMessageActivity.this, getMainClass());
            UmengPushHelper.startActivity(MfrMessageActivity.this, mainIntent, extraBean);
            finish();
        }
    };
    @Override
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        LogUtil.d("dddd", "新创建");
        mNotificationClick.onCreate(this, getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        LogUtil.d("dddd", "已打开");
        mNotificationClick.onNewIntent(intent);
    }

    /**根据缓存的首页信息，返回跳转回首页的目标
     * @return
     */
    public Class getMainClass(){
        if(Constant.CK.equals(UserCache.getAppType())){
            return CKMainActivity.class;
        }else if(Constant.JSZ.equals(UserCache.getAppType())){
            return JSZMainActivity.class;
        }else{
            return MainActivity.class;
        }
    }
}
