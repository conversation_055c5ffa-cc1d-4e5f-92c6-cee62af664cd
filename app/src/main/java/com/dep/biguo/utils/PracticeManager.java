package com.dep.biguo.utils;

import com.dep.biguo.bean.CardBean;
import com.dep.biguo.bean.QuestionBean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class PracticeManager implements Serializable {

    public static List<QuestionBean> mTotalData;
    public static List<CardBean.Topic> mCardData;//答题卡，用于交卷时，传递到答题报告页面，若通过intent传递，当数据量过大时，将会崩溃
    public static Map<String, QuestionBean> questionMap;

    public static final int SIZE = 0; //每页100题

    public boolean showContinue = true; //是否加载继续答题 表示为可上传答题记录
    public int mIsContinue = 0; //是否继续答题 0既不获取,也不保存 1继续 2重新
    public int endId = 0; //答题记录最后一题的题目id
//    public int endPage = 1; //答题记录所在的页数

    //需要初始化传入的
    public String mTitle = ""; //显示的标题
    public String mCode = ""; //课程代码
    public String courseName = "";//课程名称
    public String mPaperId = ""; //试卷id
    public String mChapterSectionId = ""; //章节的小节id
    public String mTestPaperId = ""; //模拟试题的试卷id
    public int mMainType; //做题类型
    public int mTopicType; //题型
    public String mTopicTypeName; //题型名称
    public int mTotalCount; //总题数
    public int mPracticeMode; //做题模式
    public int mPracticeType; //自定义MainType
    public long version;//题库版本

    public String mSubTitle; //子标题

    public int mCurrentPage = 0; // 当前页数
    public int mCurrentPosition = 0; //当前位置
    public int mCorrectCount = 0; //正确的题目数量
    public int mErrorCount = 0; //错误的题目数量
    public int mDoCount = 0; //已作答但未判断正误的题目数量

    public boolean mIsShowTimer = false; //是否显示计时
    public boolean mIsShowCorrectErrorCount = false; //是否显示对错数字
    public boolean mIsShowCommit = false; //是否显示交卷按钮
    public boolean mIsSupportCommit = false; //是否支持交卷
    public boolean mIsSupportPractice = false; //是否为可作答模式
    public boolean mIsOnlySelect = false; //是否只标记为选中
    public int mRecordType = 0; //保存答题记录的方式
    public boolean isShowAI;//是否显示AI解析

    //加载更多
    public boolean showLoadNext = false; //显示下一页加载
    public boolean showLoadPrev = false; //显示上一页加载


    //答题设置
    public boolean autoNext = true; //答题后自动下一题
    public boolean autoCorrectNext = true; //答题正确后自动下一题
    public boolean autoRemoveError = true; //错题集自动移除答对的题

    //真题所需
    public boolean mTrueShareLock = false; //真题是否需要分享解锁

    //每日打卡完成后，弹窗显示奖励内容
    public String dayCardPrize = "";

    //题库有效期
    public long expire_time;

    public String getOtherId(){
        if(mMainType == PracticeHelper.PRACTICE_CHAPTER){
            return mChapterSectionId;
        }else if(mMainType == PracticeHelper.PRACTICE_TRUE){
            return mPaperId;
        }else if(mMainType == PracticeHelper.PRACTICE_SIMU){
            return mTestPaperId;
        }else {
            return "";
        }
    }

    /**是否是收藏
     * @return
     */
    public boolean isErrorPractice(){
        return mPracticeType == PracticeHelper.PRACTICE_ERROR;
    }

    /**是否是错题
     * @return
     */
    public boolean isCollPractice(){
        return mPracticeType == PracticeHelper.PRACTICE_COLL;
    }

    /**是否是章节训练
     * @return
     */
    public boolean isChapter(){
        return mPracticeType == PracticeHelper.PRACTICE_CHAPTER;
    }

    /**是否是历年真题
     * @return
     */
    public boolean isTruePaper(){
        return mPracticeType == PracticeHelper.PRACTICE_TRUE;
    }

    /**是否是模拟试卷
     * @return
     */
    public boolean isSumi(){
        return mPracticeType == PracticeHelper.PRACTICE_SIMU;
    }

    /**是否是每日打卡
     * @return
     */
    public boolean isDayCard(){
        return mPracticeType == PracticeHelper.PRACTICE_DAYCARD;
    }

    /**是否是答题模式
     * @return
     */
    public boolean isDefaultMode(){
        return mPracticeMode == PracticeHelper.MODE_DEFAULT;
    }

    /**是否是背题模式
     * @return
     */
    public boolean isShowMode(){
        return mPracticeMode == PracticeHelper.MODE_SHOW;
    }

    /**是否是模拟模式
     * @return
     */
    public boolean isSimuMode(){
        return mPracticeMode == PracticeHelper.MODE_SIMU;
    }

    /**是否是解析模式
     * @return
     */
    public boolean isAllMode(){
        return mPracticeMode == PracticeHelper.MODE_ALL;
    }
}
