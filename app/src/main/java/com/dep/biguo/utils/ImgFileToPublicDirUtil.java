package com.dep.biguo.utils;

import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;

import com.biguo.utils.util.LogUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Locale;

public class ImgFileToPublicDirUtil {
    public static boolean saveImage(Context context, Bitmap bitmap, String fileName){
        try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                return ImgFileToPublicDirUtil.saveImageInQBefore(context, bitmap, fileName);
            }else {
                return ImgFileToPublicDirUtil.saveImageInQAfter(context, bitmap, fileName);
            }
        } catch (Exception e) {
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
            return false;
        }
    }

    /**保存图片到相册 android 29 以下调用
     * @param context 上下文
     * @param bitmap 图片
     * @return 是否保存成功
     */
    private static boolean saveImageInQBefore(Context context, Bitmap bitmap, String name) throws IOException{
        // 保存图片至指定路径
        File appDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
        //检查是否有这个文件夹
        if (!appDir.exists()) appDir.mkdirs();
        //检查文件是否已创建
        File file = new File(appDir, name);
        if(!file.exists()) file.createNewFile();
        //创建一个文件输出流
        FileOutputStream fos = new FileOutputStream(file);
        //通过io流的方式来压缩保存图片(100表示不压缩，80代表压缩20%)
        boolean isSuccess = bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
        fos.flush();
        fos.close();

        //发送广播通知系统图库刷新数据
        Uri uri = Uri.fromFile(file);
        context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));
        return isSuccess;
    }

    /**保存图片到相册 android 29 及以上调用
     * @param context 上下文
     * @param bitmap 图片
     * @return 是否保存成功
     */
    private static boolean saveImageInQAfter(Context context, Bitmap bitmap, String name) throws Exception {
        FileOutputStream outputStream = getImgFileOutputStream(context, name);
        if(outputStream!=null) {
            //创建对应的流对象
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            //将流对象与Bitmap对象进行关联。
            bitmap.compress(Bitmap.CompressFormat.JPEG,100,byteArrayOutputStream);
            //使用流对象，将Bitmap对象转换为byte[]数组
            byte[] bt = byteArrayOutputStream.toByteArray();
            outputStream.write(bt, 0, bt.length);
            outputStream.close();
            return true;
        }
        return false;
    }

    /**插入一条记录到媒体库,android 10或以上 才能使用
     * @param context 上下文
     * @return 返回一个文件输出流
     */
    private static FileOutputStream getImgFileOutputStream(Context context, String name) throws Exception{
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContentValues contentValues = new ContentValues();
            /*contentValues.put(MediaStore.Downloads.DISPLAY_NAME, name);
            contentValues.put(MediaStore.Downloads.MIME_TYPE,"image/jpg");
            contentValues.put(MediaStore.Downloads.DATE_TAKEN, System.currentTimeMillis());

            //返回一个等待使用的Uri，不对此Uri写入数据，则记录不会插入成功
            Uri backupPath=context.getContentResolver().insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues);
*/
            contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, name);
            //此处媒体类型选了"image/jpg"格式
            contentValues.put(MediaStore.Images.Media.MIME_TYPE,"image/jpg");
            contentValues.put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis());

            //返回一个等待使用的Uri，不对此Uri写入数据，则记录不会插入成功
            Uri backupPath=context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);

            AssetFileDescriptor assetFileDescriptor=context.getContentResolver().openAssetFileDescriptor(backupPath,"w");
            if(assetFileDescriptor==null){
                return null;
            }
            return assetFileDescriptor.createOutputStream();
        }
        return null;
    }

}
