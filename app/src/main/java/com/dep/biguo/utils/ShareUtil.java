package com.dep.biguo.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.mvp.ui.activity.AppInfoActivity;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.umengPush.PushConstants;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.umeng.socialize.Config;
import com.umeng.socialize.ShareAction;
import com.umeng.socialize.UMShareListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMMin;
import com.umeng.socialize.media.UMWeb;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class ShareUtil {
    /**所有分享平台*/
    public static final SHARE_MEDIA[] mediaValueArray = {SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE, SHARE_MEDIA.QQ, SHARE_MEDIA.QZONE};
    public static final String[] mediaNameArray = {"微信", "朋友圈", "QQ", "QQ空间"};

    public static String getShareUrl(){
        String aesKey = (UserCache.getUserCache() == null ? 0 : UserCache.getUserCache().getUser_id())+"";
        return Constant.SHARE_APP +
                "?um_chnnl=share" +
                "&inviter_id=" + aesKey +
                "&appkey=" + encode(AESEncrypt.encrypt(PushConstants.APP_KEY, aesKey));
    }

    public static String getShareUrl(String scheme){
        String aesKey = (UserCache.getUserCache() == null ? 0 : UserCache.getUserCache().getUser_id())+"";
        return scheme +
                "?um_chnnl=share" +
                "&inviter_id=" + aesKey +
                "&appkey=" + encode(AESEncrypt.encrypt(PushConstants.APP_KEY, aesKey));
    }

    public static String encode(String value){
        try {
            return URLEncoder.encode(value, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getNameByMedia(SHARE_MEDIA media){
        for (int i = 0; i < mediaValueArray.length; i++) {
            if(mediaValueArray[i] == media) return mediaNameArray[i];
        }
        return "";
    }

    /**检测权限
     * @param builder
     */
    @SuppressLint("CheckResult")
    public static void isCheckPermissions(Builder builder) {
        //分享纯图到QQ/QQ空间，若是图片被压缩或模糊不清，可以先申请存储权限，再分享
        toShare(builder);
    }

    public static void Share(Builder builder){
        if (builder.media == SHARE_MEDIA.QQ || builder.media == SHARE_MEDIA.QZONE) {
            isCheckPermissions(builder);
        } else {
            toShare(builder);
        }
    }

    private static void toShare(Builder builder){
        UMShareListener shareListener = new UMShareListener() {
            @Override
            public void onStart(SHARE_MEDIA share_media) {

            }

            @Override
            public void onResult(SHARE_MEDIA share_media) {
                if(builder.onShareListener != null) builder.onShareListener.onShare(share_media);
            }

            @Override
            public void onError(SHARE_MEDIA share_media, Throwable throwable) {

            }

            @Override
            public void onCancel(SHARE_MEDIA share_media) {

            }
        };
        if(builder.share_type == SHARE_TYPE.IMAGE) {
            new ShareAction(builder.activity)
                    .setPlatform(builder.media)
                    .withMedia(getShareImageWeb(builder))
                    .setCallback(shareListener)
                    .share();
        }else if(builder.share_type == SHARE_TYPE.MINI) {
            new ShareAction(builder.activity)
                    .setPlatform(builder.media)
                    .withMedia(getShareMini(builder))
                    .setCallback(shareListener)
                    .share();
        }else {
            new ShareAction(builder.activity)
                    .setPlatform(builder.media)
                    .withMedia(getShareWeb(builder))
                    .setCallback(shareListener)
                    .share();
        }
    }


    /**设置分享的内容
     * @param builder
     * @return
     */
    private static UMWeb getShareWeb(Builder builder) {
        UMWeb web = new UMWeb(builder.url);
        web.setTitle(builder.title);
        web.setDescription(builder.content);
        if(builder.iconBitmap != null) {
            web.setThumb(new UMImage(builder.activity, builder.iconBitmap));
        }else {
            web.setThumb(new UMImage(builder.activity, builder.iconRes));
        }
        return web;
    }

    /**设置分享的内容
     * @param builder
     * @return
     */
    private static UMImage getShareImageWeb(Builder builder) {
        UMImage umImage = new UMImage(builder.activity, builder.iconBitmap);
        umImage.compressFormat = Bitmap.CompressFormat.JPEG;
        umImage.setThumb(new UMImage(builder.activity, builder.iconBitmap));
        return umImage;
    }

    /**设置分享的内容
     * @param builder
     * @return
     */
    private static UMMin getShareMini(Builder builder) {
        UMMin umMin = new UMMin(Api.APP_HOST);
        umMin.setThumb(new UMImage(builder.activity, R.drawable.mini_cover));
        umMin.setTitle("在自考？快试试我在用的这款自考神器吧~\uD83D\uDC47\uD83D\uDC47");
        umMin.setPath(ShareUtil.getShareUrl("pages/index/H5/invitation"));
        umMin.setUserName(Constant.WX_LUNCH_MINI_ID);
        Config.setMini();
        return umMin;
    }

    public static class Builder{
        private Activity activity;
        private String url;
        private String title;
        private String content;
        private int iconRes;
        private Bitmap iconBitmap;
        private SHARE_TYPE share_type;
        private SHARE_MEDIA media;
        private OnShareListener onShareListener;

        public Builder(Activity activity, SHARE_MEDIA media) {
            this.activity = activity;
            this.setUrl(getShareUrl());
            this.setTitle("自考笔果题库-我的自考神器");
            this.setContent("下载笔果，考试必过");
            this.setIconRes(R.drawable.app_icon);
            this.setMedia(media);
            this.setShare_type(SHARE_TYPE.LINK);
        }

        public Builder setUrl(String url) {
            this.url = url;
            if(!this.url.contains("cert_type=")){
                if(this.url.contains("?")){
                    this.url = String.format("%s&cert_type=%s", this.url, UserCache.getAppType());
                }else {
                    this.url = String.format("%s?cert_type=%s", this.url, UserCache.getAppType());
                }
            }
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContent(String content) {
            this.content = content;
            return this;
        }

        public Builder setIconRes(int iconRes) {
            if(iconRes == 0) return this;
            this.iconRes = iconRes;
            return this;
        }

        public Builder setIconBitmap(Bitmap iconBitmap) {
            this.iconBitmap = iconBitmap;
            return this;
        }

        public Builder setShare_type(SHARE_TYPE share_type) {
            this.share_type = share_type;
            return this;
        }

        public Builder setMedia(SHARE_MEDIA media) {
            this.media = media;
            return this;
        }

        public Builder setOnShareListener(OnShareListener onShareListener) {
            this.onShareListener = onShareListener;
            return this;
        }
    }

    public interface OnShareListener{
        void onShare(SHARE_MEDIA media);
    }

    public enum SHARE_TYPE{
        IMAGE,//分享图片
        LINK,//分享链接
        MINI//分享微信小程序
    }
}
