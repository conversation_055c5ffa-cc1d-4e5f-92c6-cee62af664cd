package com.dep.biguo.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import com.biguo.utils.util.LogUtil;
import com.hjq.toast.ToastUtils;
import com.umeng.commonsdk.UMConfigure;

import java.util.List;

//跳转应用市场指导文档：https://blog.csdn.net/fumeidonga/article/details/134607924
public class StartAppStoreUtil {

    public static boolean findAppStore(Activity context){
        boolean isSuccess = false;
        try {

            if(TextUtils.equals(Build.MANUFACTURER.toLowerCase(), "vivo")){
                isSuccess = launchVivoMarket(context);

            }else if(TextUtils.equals(Build.MANUFACTURER.toLowerCase(), "oppo")){
                isSuccess = launchOppoMarket(context);

            }else if(TextUtils.equals(Build.MANUFACTURER.toLowerCase(), "xiaomi")){
                isSuccess = launchXiaomiMarket(context);

            }else if(TextUtils.equals(Build.MANUFACTURER.toLowerCase(), "huawei")){
                isSuccess = launchHuaweiMarket(context);

            }else {
                isSuccess = launchOtherMarket(context);
            }

            if(!isSuccess){
                ToastUtils.show("跳转应用市场失败");
            }
        }catch (Exception e){
            ToastUtils.show("跳转应用市场失败");
        }

        return isSuccess;
    }

    //跳转到vivo的评论页面
    public static boolean launchVivoMarket(Context context) throws Exception{
        String url = "market://details?id=" + context.getPackageName() + "&th_name=need_comment";

        Uri uri = Uri.parse(url);
        Intent intent= new Intent(Intent.ACTION_VIEW,uri);
        intent.setPackage("com.bbk.appstore");
        context.startActivity(intent);

        return true;
    }

    //在APP内拉起OPPO的评论页面
    public static boolean launchOppoMarket(Activity context) throws Exception{
        // 此处一定要传入调用方自己的包名，不能给其他应用拉起评论页。
        String url = "oaps://mk/developer/comment?pkg=" + context.getPackageName();

        //com.heytap.market是Q版本之后的软件商店包名，com.oppo.market是Q之前的软件商店包名
        //优先判断com.heytap.market
        String[] oppoStoreName = {"com.heytap.market", "com.oppo.market"};
        for(int i = 0; i < oppoStoreName.length; i++) {
            PackageInfo info = context.getPackageManager().getPackageInfo(oppoStoreName[0], PackageManager.GET_META_DATA);
            if (info != null) {
                long versionCode = Build.VERSION.SDK_INT >= Build.VERSION_CODES.P ? info.getLongVersionCode() : info.versionCode;
                //软件商店的版本号(versionCode) >= 84000才支持直接调起评论
                if (versionCode > 84000) {
                    Intent intent = new Intent();
                    intent.setAction(Intent.ACTION_VIEW);
                    intent.addCategory(Intent.CATEGORY_DEFAULT);
                    intent.setPackage(oppoStoreName[0]);
                    intent.setData(Uri.parse(url));
                    // 建议采用startActivityForResult 方法启动商店页面，requestCode由调用方自定义且必须大于0，软件商店不关注
                    context.startActivityForResult(intent, 100);
                    return true;
                }
            }
        }

        return false;
    }

    //跳转到小米的评论页面
    public static boolean launchXiaomiMarket(Context context){
        String url = "market://details?id=" + context.getPackageName();

        Uri uri = Uri.parse(url);
        Intent intent= new Intent(Intent.ACTION_VIEW,uri);
        intent.addCategory(Intent.CATEGORY_BROWSABLE);
        intent.setPackage("com.xiaomi.market");
        context.startActivity(intent);

        return true;
    }

    //跳转到华为的评论页面
    public static boolean launchHuaweiMarket(Context context){
        Intent intent = new Intent("com.huawei.appmarket.intent.action.AppDetail");
        intent.setPackage("com.huawei.appmarket");
        intent.putExtra("APP_PACKAGENAME", context.getPackageName());
        context.startActivity(intent);

        return true;
    }

    //跳转到其它应用市场
    public static boolean launchOtherMarket(Context context){
        Uri uri = Uri.parse("market://details?id=" + context.getPackageName());
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);

        return true;
    }
}
