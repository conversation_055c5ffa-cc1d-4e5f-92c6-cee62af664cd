package com.dep.biguo.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;

import java.util.ArrayList;
import java.util.List;

public class OpenOtherMapUtil {

    /**检查手机上是否安装了指定的软件
     * @param packageName：应用包名
     */
    public static boolean isAvilible(Context context, String packageName) {
        // 获取packagemanager
        PackageManager packageManager = context.getPackageManager();
        try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    public static boolean goToBaiduMap(Context context, String address, String mLatitude, String mLongitude){
        if (isAvilible(context, "com.baidu.BaiduMap")) {// 传入指定应用包名
            Intent intent = new Intent();
            intent.setData(Uri.parse("baidumap://map/direction?destination=" + gcj02ToBd09(mLatitude, mLongitude)+ "|name:" + address + "&mode=driving"));
            context.startActivity(intent); // 启动调用
            return true;
        }
        return false;
    }

    public static boolean goToGaodeMap(Context context, String address, String mLatitude, String mLongitude){
        if (isAvilible(context, "com.autonavi.minimap")) {// 传入指定应用包名
            Intent intent = new Intent();
            intent.setData(Uri.parse("amapuri://route/plan/?dlat=" + mLatitude + "&dlon=" + mLongitude+"&dname=" + address + "&dev=0&t=0"));
            context.startActivity(intent); // 启动调用
            return true;
        }
        return false;
    }

    /**
     * 腾讯地图
     */
    public static boolean goToTencentMap(Context context, String address, String mLatitude, String mLongitude) {
        if (isAvilible(context, "com.autonavi.minimap")) {// 传入指定应用包名
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            //将功能Scheme以URI的方式传入data
            Uri uri = Uri.parse("qqmap://map/routeplan?type=drive&to=" + address + "&tocoord=" + mLatitude + "," + mLongitude);
            intent.setData(uri);
            if (intent.resolveActivity(context.getPackageManager()) != null) {
                //启动该页面即可
                context.startActivity(intent);
            }
            return true;
        }
        return false;
    }

    /**坐标系转换
     */
    public static String gcj02ToBd09(String lat, String lng){
        double latitude = Double.parseDouble(lat);
        double longitude = Double.parseDouble(lng);
        double x_PI = 3.14159265358979324 * 3000.0 / 180.0;
        double z = Math.sqrt(longitude * longitude + latitude * latitude) + 0.00002 * Math.sin(latitude * x_PI);
        double theta = Math.atan2(latitude, longitude) + 0.000003 * Math.cos(longitude * x_PI);
        double bd_lng = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        return bd_lat+","+bd_lng;
    };
}
