package com.dep.biguo.utils;

public class StartFinal {
    /**跳转页面的请求码*/
    public static final int REQUEST_CODE = 10086;

    /**分享链接中action对应的值*/
    public static final String GROUP = "Group";//跳转到拼团详情页面（GroupDetailActivity）
    public static final String DETAIL = "Detail";//跳转到商品详情页面（GroupGoodsActivity）
    public static final String LIVE = "Live";//跳转到商品详情页面（GroupGoodsActivity）
    public static final String CIRCLE_DETAIL = "CircleDetail";//跳转到动态详情页面（GroupGoodsActivity）

    /**所有的键*/
    public static final String ACTIVITY_NAME = "activity_name";//上一个页面的名字
    public static final String GOODS_TYPE = "goodsType";//拼团的商品类型
    public static final String COURSE_NAME = "course_name";//课程名称
    public static final String COURSE_CODE = "course_code";//课程代码
    public static final String MAIN_TYPE = "mainType";//题库类型
    public static final String COURSE_ID = "course_id";//课程ID
    public static final String PRODUCT_ID = "course_id";//必过视频的商品ID
    public static final String GROUP_ID = "group_id";//拼团的团ID
    public static final String GROUP_STATUS = "groupStatus";//拼团状态
    public static final String IS_BUY = "is_buy";//是否已购买
    public static final String RID = "r_id";//拼团发起人的ID
    public static final String COURSE_MANAGE_MODE = "course_manage_mode";//课程管理的显示模式
    public static final String COURSE_MANAGE_IS_RETURN = "course_manage_is_return";//课程管理的首次报考页面是否不进入管理页面，而是直接返回
    public static final String POSITION = "position";//下标
    public static final String TYPE = "type";//类型
    public static final String SOURCE_TYPE = "source_type";//视频类型
    public static final String CLASSROOM_ID = "classroom_id";//课堂ID
    public static final String NEWCOMERS = "newcomers";//是否是新用户
    public static final String SKILL_ID = "skill_id";//技能证的ID

    /**对应{@link StartFinal#GOODS_TYPE}的值*/
    public static final String PRAC = "prac";//免费题库
    public static final String VIP = "vip";//VIP题库
    public static final String HIGH_FREQUENCY = "high_frequency";//高频考点
    public static final String YAMI = "yami";//考前压密
    public static final String YAMI_RESERVE = "yami_reserve";//预订押密
    public static final String VIDEO = "video";//视频
    public static final String VIDEO0 = "video0";//笔果精讲视频
    public static final String VIDEO1 = "video1";//必过精讲视频
    public static final String VIDEO2 = "video2";///必过直播课视频
    public static final String VIDEO4 = "video4";//必过直播特训班/必过串讲视频
    public static final String VIDEO55 = "video55";//成考必过全科全程视频
    public static final String VIDEO3 = "video3";
    public static final String SKILL_VIDEO = "skill_video";//时刻套餐
    public static final String VOCATION_VIDEO = "vocation_video";//职场提升
    public static final String DEDUCTION_CARD = "study_room_card";//自习室抵扣卡
    public static final String INTERNET_STUDY = "online_assistance";//网络助学
    public static final String BOOK = "book";//书籍
    public static final String COUNSELLING_DAZIKAO = "tutorial_class_self_exam";//线下辅导班之大自考
    public static final String COUNSELLING_ZIXUAN = "tutorial_class_single_subject";//线下辅导班之自选
    public static final String INS_TUTORIAL_CLASS = "ins_tutorial_class";//机构助学班

    /**积分明细或果币明细的类型*/
    public static final int RECHARGE = 1;//果币
    public static final int INTEGRAL = 2;//积分
    /**积分或果币对应的fragment下标*/
    public static final int ALL = 0;//全部明细
    public static final int GET = 1;//已获得
    public static final int MAKE = 2;//已使用


    /**对应{@link StartFinal#COURSE_MANAGE_MODE}的值*/
    public static final int SHOW_SCORE = 1;//显示成绩
    public static final int MANAGER_ENROLL = 2;//管理报考

    /**所有通过数字来表示boolean类型的判断，都可以用*/
    public static int NO = 0;//表示否定
    public static int YES = 1;//表示肯定


    public static final String MEMBERSHIP = "membership";//笔果折扣卡
}
