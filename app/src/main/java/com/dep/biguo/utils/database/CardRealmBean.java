package com.dep.biguo.utils.database;

import com.biguo.utils.util.GsonUtils;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class CardRealmBean extends RealmObject {
    @PrimaryKey private String primaryKey;//主键，mainType+"_"+id
    @Required private String code;
    private int mainType;//题库类型，vip、真题、章节训练、免费、打卡
    private int topic_type;//题目类型1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
    private String topic_type_name;//题目类型1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
    private int id;//题库id
    private String paperId;//真题题库ID
    private String correctOption;//正确答案
    private String select_answer;//用户选项 为空即代表未答
    private int is_correct;//是否已检验对错，0选中（蓝），1正确（绿），2错误（红），3题目有误
    private int isCollection;//是否已收藏

    private int sort;//从服务器返回时的数据位置，用于排序

    public CardRealmBean() {

    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getTopic_type_name() {
        return topic_type_name;
    }

    public void setTopic_type_name(String topic_type_name) {
        this.topic_type_name = topic_type_name;
    }

    public String getCorrectOption() {
        return correctOption;
    }

    public void setCorrectOption(String correctOption) {
        this.correctOption = correctOption;
    }

    public String getSelect_answer() {
        return select_answer;
    }

    public void setSelect_answer(String select_answer) {
        this.select_answer = select_answer;
    }

    public int getIs_correct() {
        return is_correct;
    }

    public void setIs_correct(int is_correct) {
        this.is_correct = is_correct;
    }

    public int getIsCollection() {
        return isCollection;
    }

    public void setIsCollection(int isCollection) {
        this.isCollection = isCollection;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"primaryKey\":\"")
                .append(primaryKey).append('\"');
        sb.append(",\"code\":\"")
                .append(code).append('\"');
        sb.append(",\"mainType\":")
                .append(mainType);
        sb.append(",\"topic_type\":")
                .append(topic_type);
        sb.append(",\"topic_type_name\":")
                .append(topic_type_name);
        sb.append(",\"id\":")
                .append(id);
        sb.append(",\"paperId\":\"")
                .append(paperId).append('\"');
        sb.append(",\"correctOption\":")
                .append(replaceQuotationMarks(correctOption));
        sb.append(",\"select_answer\":")
                .append(replaceQuotationMarks(select_answer));
        sb.append(",\"is_correct\":")
                .append(is_correct);
        sb.append(",\"isCollection\":")
                .append(isCollection);
        sb.append(",\"sort\":")
                .append(sort);
        sb.append('}');
        return sb.toString();
    }

    public String replaceQuotationMarks(String string){
        return GsonUtils.toJson(string);
    }
}
