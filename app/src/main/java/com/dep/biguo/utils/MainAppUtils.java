package com.dep.biguo.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.net.Uri;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;

import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;

import com.dep.biguo.R;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.InformationEnterActivity;
import com.dep.biguo.mvp.ui.activity.LoginTypeActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity;
import com.dep.biguo.mvp.ui.activity.SurveyActivity;
import com.dep.biguo.mvp.ui.activity.ZkLoginActivity;
import com.dep.biguo.mvp.ui.activity.ZkOperatePhoneActivity;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.dialog.MessageDialog;
import com.hjq.toast.ToastUtils;
import com.jess.arms.integration.AppManager;
import com.umeng.commonsdk.UMConfigure;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/21
 * @Description: 统一工具类
 */
public class MainAppUtils {
    public static void copyWechat(Context context, FragmentManager manager, String wechat) {
        copyWechat(context, manager, wechat, v -> {
            if (!AppUtil.isInstallWechat(context)) {
                ToastUtils.show("未安装微信");
            } else {
                AppUtil.isInstallWechat(context);
            }
        });
    }

    public static void copyWechat(Context context, FragmentManager manager, String wechat, View.OnClickListener onClickListener) {
        AppUtil.copyText(context, wechat);
        new MessageDialog.Builder(manager)
                .setContent("微信号：" + wechat + "已复制，打开微信->添加朋友->粘贴微信号")
                .setNegativeText("取消")
                .setPositiveText("打开微信")
                .setPositiveClickListener(onClickListener)
                .builder()
                .show();
    }

    public static void copyWechatPublic(Context context, FragmentManager manager, String wechat, View.OnClickListener onClickListener) {
        AppUtil.copyText(context, wechat);
        new MessageDialog.Builder(manager)
                .setContent("微信号：" + wechat + "已复制，打开微信->添加朋友->粘贴微信号，关注公众号即可")
                .setNegativeText("取消")
                .setPositiveText("打开微信")
                .setPositiveClickListener(onClickListener)
                .builder()
                .show();
    }

    public static void copyStartWechat(Context context, String content) {
        AppUtil.copyText(context, content);
        if (!AppUtil.isInstallWechat(context)) {
            ToastUtils.show("未安装微信");
        } else {
            AppUtil.startWechat(context);
        }
    }

    /**检查是否已登录
     * @param context 上下文
     * @return
     */
    public static boolean checkLogin(Context context) {
        if (UserCache.getUserCache() == null) {
            LoginTypeActivity.start(context);
            //OneKeyLoginHelper.oneKeyLogin(context);
            //UmengOneKeyLoginHelper.oneKeyLogin(context);
            return false;
        }
        return true;
    }

    //登录成功后跳转到调查表
    public static void loginSuccessStartSurvey(){
        if(isStartSurvey()) {
            Activity topActivity = AppManager.getAppManager().getTopActivity();
            SurveyActivity.Start(topActivity, false);
        }
    }

    public static boolean isStartSurvey(){
        UserBean user = UserCache.getUserCache();
        if(user == null) return false;

        //登录后，根据是否填写过调查表来选择是否展示调查表页面
        Activity topActivity = AppManager.getAppManager().getTopActivity();
        if(topActivity instanceof MainActivity
                || topActivity instanceof InformationEnterActivity
                || topActivity instanceof ProfessionSchoolActivity
                || topActivity instanceof ZkLoginActivity
                || topActivity instanceof LoginTypeActivity
                || topActivity instanceof ZkOperatePhoneActivity) {

            if (!UserCache.getIsInputTable() || user.getIs_label() == StartFinal.NO) {
                if(UserCache.getCity() != null && UserCache.getSchool() != null) {
                    return true;
                }
            }
        }
        return false;
    }

    /**是否是只需要验证码登录的渠道包
     * @return
     */
    public static boolean isOnlySmsVerifyLogin(){
        List<String> list = new ArrayList<>();
        list.add("juliang_xinxiliu");
        list.add("bzhan");
        return list.contains(UMConfigure.sChannel);
    }

    /**
     * 给RecyclerView中的最后一个itemView设置bottomPadding
     *
     * @param context
     * @param view
     * @param size
     * @param position
     */
    public static void setBottomPadding(Context context, View view, int size, int position) {
        setBottomPadding(context, view, size, position, DisplayHelper.dp2px(context, 15));
    }

    /**
     * 给RecyclerView中的最后一个itemView设置bottomPadding
     *
     * @param context
     * @param view
     * @param size
     * @param position
     */
    public static void setBottomPadding(Context context, View view, int size, int position, int bottomMargin) {
        RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) view.getLayoutParams();
        if (position == size - 1) {
            params.bottomMargin = bottomMargin;
        } else {
            params.bottomMargin = DisplayHelper.dp2px(context, 0);
        }
        view.setLayoutParams(params);
    }

    /**
     * 拼接跳转快递查询网页
     * @param context
     * @param item
     * @return
     */
    public static Uri getExpressInfo(Context context, String item) {
        @SuppressLint("StringFormatMatches")
        Uri uri = !TextUtils.isEmpty(item) ? Uri.parse(String.format(context.getString(R.string.url_express),
                item.split("-")[1])) : Uri.parse(String.format(context.getString(R.string.url_express), "笔果题库"));
        return uri;
    }

    public static String getAppProvince() {
        String province = UserCache.JSZ_PROVINCE;
        switch (UserCache.getAppType()) {
            case Constant.JSZ:
                province = UserCache.JSZ_PROVINCE;
                break;
            case Constant.KJ:
                province = UserCache.KJ_PROVINCE;
                break;
            case Constant.JZS:
                province = UserCache.JZS_PROVINCE;
                break;
            case Constant.RLZY:
                province = UserCache.RLZY_PROVINCE;
                break;
            case Constant.YYDJ:
                province = UserCache.YYDJ_PROVINCE;
                break;
        }
        return province;
    }

    public static String getAppProfession() {
        String profession = "";//UserHelper.JSZ_PROFESSION;
        switch (UserCache.getAppType()) {
            case Constant.JSZ:
                //profession = UserHelper.JSZ_PROFESSION;
                break;
            case Constant.KJ:
                profession = UserCache.KJ_PROFESSION;
                break;
            case Constant.JZS:
                profession = UserCache.JZS_PROFESSION;
                break;
            case Constant.RLZY:
                profession = UserCache.RLZY_PROFESSION;
                break;
            case Constant.YYDJ:
                profession = UserCache.YYDJ_PROFESSION;
                break;
        }
        return profession;
    }

    public static String getAppType() {
        String type = UserCache.JSZ_TYPE;
        switch (UserCache.getAppType()) {
            case Constant.JSZ:
                type = UserCache.JSZ_TYPE;
                break;
            case Constant.KJ:
                type = UserCache.KJ_TYPE;
                break;
            case Constant.JZS:
                type = UserCache.JZS_TYPE;
                break;
            case Constant.RLZY:
                type = UserCache.RLZY_TYPE;
                break;
            case Constant.YYDJ:
                type = UserCache.YYDJ_TYPE;
                break;
        }
        return type;
    }

    public static String getAppTypeName(){
        if(TextUtils.equals(Constant.ZK, UserCache.getAppType())){
            return "自考";
        }else if(TextUtils.equals(Constant.CK, UserCache.getAppType())){
            return "成考";
        }else if(TextUtils.equals(Constant.JSZ, UserCache.getAppType())){
            return "教师资格证";
        }else {
            return "";
        }
    }
    
    public static String getSecretAgreement(){
        if(TextUtils.equals(UserCache.getAppType(), Constant.CK)){
            return Constant.AGREEMENT_CK_SECRET;
        }else {
            return Constant.AGREEMENT_SECRET;
        }
    }

    public static String getSecretAgreementTitle(){
        if(TextUtils.equals(UserCache.getAppType(), Constant.CK)){
            return "成考考前押密服务协议";
        }else {
            return "自考考前押密服务协议";
        }
    }
}
