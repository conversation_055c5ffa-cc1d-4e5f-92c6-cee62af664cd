package com.dep.biguo.utils.mmkv;

import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.bean.JoinWechatGroupBean;
import com.dep.biguo.bean.OpenMembershipBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.bean.ZkHomeBean;
import com.dep.biguo.bean.ck.CKProvinceBean;
import com.dep.biguo.bean.jsz.JSZProvinceBean;
import com.dep.biguo.common.Constant;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.TimeFormatUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.TimeZone;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/24
 * @Description: 用户操作类
 */
public class UserCache {
    public static final String USER = "user"; //用户信息
    public static final String LAST_USER = "last_user"; //上一个登录的用户信息
    private static final String USERNAME = "username"; //登录成功后的手机号码
    public static final String NEGATIVE_LOCATION_PERMISSION = "negativeLocationPermission"; //是否拒绝过定位权限
    public static final String PROVINCE = "province"; //当前省份
    public static final String CITY = "city"; //当前城市
    public static final String PROFESSION = "profession"; //当前专业
    private static final String ADDRESS = "address";//当前用户所在地址
    private static final String LONGITUDE = "longitude";//经度
    private static final String LATITUDE = "latitude";//纬度
    public static final String COURSE = "course"; //当前课程
    public static final String SCHOOL = "school"; //当前学校
    private static final String SEARCHKEY = "search_key"; //搜索关键字
    private static final String SIZEMODE = "size_mode"; //做题字体大小模式
    private static final String PRACTICE_AUTO = "practice_auto"; //做题自动跳转下一题
    private static final String PRACTICE_CORRECT_AUTO = "practice_correct_auto"; //做题正确自动跳转下一题
    private static final String PRACTICE_ERROR_REMOVE = "practice_error_remove"; //错题集内自动移除答对的题
    private static final String PRACTICE_ERROR_REMOVE_COUNT = "practice_error_remove_count"; //错题集内答对多少次自动移除的题目
    public static final String PRACTICE_ERROR_REMOVE_HINT = "practice_error_remove_hint"; //错题集内自动移除答对的题-提示
    private static final String PRACTICE_MODE = "practice_mode"; //答题模式
    private static final String DAY_NIGHT = "day_night"; //夜间模式
    public static final String MAIN_AGREEMENT = "main_agreement"; //首次弹出用户协议
    public static final String MAIN_AGREEMENT_TIME = "main_agreement_time"; //首次弹出用户协议的系统时间
    public static final String MAIN_AGREEMENT_VERSION = "main_agreement_version"; //用户协议的版本号
    public static final String MAIN_AGREEMENT_IS_REVIEW = "main_agreement_is_review"; //版本审核是否过审
    public static final String GUIDE = "guide"; //引导页
    public static final String REGUIDE = "reguide"; //所有的app重新显示引导页
    public static final String HOME_GUIDE = "home_guide"; //首页引导
    public static final String PRACTICE_SCROLL_GUIDE = "practice_scroll_guide"; //做题左右滑动引导
    public static final String PRACTICE_TIME = "practice_time"; //首页考试时间
    public static final String PRACTICE_LATEST_TIME = "practice_latest_time"; //最近的考试时间
    private static final String APP_TYPE = "app_type"; // 类型 -> 自考 成考等
    public static final String CK_PROVINCE = "ck_province"; // 成考省份
    public static final String CK_CITY = "ck_city"; // 成考城市
    public static final String CK_SHOOL = "ck_school"; // 成考学校
    public static final String CK_LAYER = "ck_layer"; // 成考层次
    public static final String CK_PROFESSION = "ck_profession"; // 成考专业
    public static final String CK_COURSE = "ck_course"; // 成考课程
    public static final String DAY_AD_YAMI_DIALOG = "day_ad_yami_dialog";   //每日押密弹窗
    public static final String DAY_AD_SHARE_DIALOG = "day_ad_share_dialog";   //每日押密弹窗
    public static final String UPDATE_QUESTION_BAN_DIALOG = "update_question_ban_dialog";   //题库更新弹窗

    public static final String JSZ_PROVINCE = "jsz_province"; // 教师资格证考试省份
    public static final String JSZ_TYPE = "jsz_type"; // 教师资格证考试类型
    //public static final String JSZ_PROFESSION = "jsz_profession"; //教师资格证考试学段
    public static final String JSZ_COURSE = "jsz_course"; //教师证课程

    public static final String KJ_PROVINCE = "kj_province"; // 会计资格证考试省份
    public static final String KJ_TYPE = "kj_type"; // 会计资格证考试类型
    public static final String KJ_PROFESSION = "kj_profession"; //会计资格证考试学段
    public static final String KJ_COURSE = "kj_course"; //会计 证课程

    public static final String JZS_PROVINCE = "jzs_province"; // 建造师资格证考试省份
    public static final String JZS_TYPE = "jzs_type"; // 建造师资格证考试类型
    public static final String JZS_PROFESSION = "jzs_profession"; //建造师资格证考试学段
    public static final String JZS_COURSE = "jzs_course"; //建造师课程

    public static final String RLZY_PROVINCE = "rlzy_province"; // 人力资源资格证考试省份
    public static final String RLZY_TYPE = "rlzy_type"; // 人力资源资格证考试类型
    public static final String RLZY_PROFESSION = "rlzy_profession"; //人力资源资格证考试学段
    public static final String RLZY_COURSE = "rlzy_course"; //人力资源证课程

    public static final String YYDJ_PROVINCE = "yydj_province"; // 英语等级考试省份
    public static final String YYDJ_TYPE = "yydj_type"; //  英语等级考试类型
    public static final String YYDJ_PROFESSION = "yydj_profession"; // 英语等级考试学段
    public static final String YYDJ_COURSE = "yydj_course"; // 英语等级课程

    public static final String IS_INPUT_TABLE = "is_input_table"; //是否填写调查表（LABEL，QUALIFICATIONS，EXAM_PREPARATION，REASONS_FOR_DEGREE）
    public static final String LABEL = "label"; // 标签
    public static final String QUALIFICATIONS = "qualifications"; //学历
    public static final String EXAM_PREPARATION = "exam_preparation"; //备考情况
    public static final String REASONS_FOR_DEGREE = "reasons_for_degree"; //考学历的理由
    public static final String ADV_NEW_USERS = "adv_new_users"; //新用户弹窗
    public static final String PUSH_TAG = "push_tag";//推送标签
    public static final String IS_CLOSE_ZK_HOME_DISCOUNT = "push_tag";//是否关闭了自考首页的优惠券按钮
    public static final String JOIN_WECHAT_GROUP = "join_wechat_group";//加入微信群的按钮
    public static final String IS_SHOW_JOIN_WECHAT_GROUP = "is_show_join_wechat_group";//是否显示加入微信群的弹窗

    public static String COMMENT_STR = "";

    public static final String ENROLL_COURSE = "enroll_course";//报考课程列表

    public static final String RECHARGE_IS_SHOW = "recharge_id_show";//是否主动展示过果币明细弹窗
    public static final String DAY_QUESTION_GET = "day_question_get";//客服常见问题的更新时间

    public static final String INSTALL_PARAMS = "install_params";//首次安装的参数

    public static final String HAS_SHOW_MAKE_PLAN_DIALOG = "hasShowMakePlanDialog";//是否显示过指定计划的弹窗

    public static final String PROFESSION_CHANGE_DIALOG = "professionChangeDialog";//是否显示过某专业课程改革弹窗

    public static final String IS_SHOW_GOOD_APP = "isShowGoodApp";//是否显示过好评的弹窗

    public static final String IS_SHOW_OPEN_MEMBERSHIP = "isShowOpenMembership";//是否显示过开通折扣卡的弹窗

    public static final String HOME_SHOW_ENROLL_COURSE = "home_show_enroll_course";//首页是否显示过报考弹窗
    public static final String HOME_CODE = "home_code";//首页选中的课程代码

    /**
     * 清除全部的缓存
     */
    public static void removeValues(String...retainKeys){
        StringBuilder retainKeyStr = new StringBuilder();
        for(String retainKey : retainKeys){
            retainKeyStr.append(retainKey);
        }
        String[] keys = KVHelper.getAllKey();
        for(String key : keys){
            if(!retainKeyStr.toString().contains(key)){
                KVHelper.removeValues(key);
            }
        }
    }


    public static String getAppType() {
        String appType = KVHelper.getString(APP_TYPE);
        if(TextUtils.isEmpty(appType)){
            return Constant.ZK;
        }else {
            return appType;
        }
    }

    public static void setAppType(String type) {
        KVHelper.putValue(APP_TYPE, type);
    }

    public static float getFruitNum(){
        if(getUserCache() == null){
            return 0f;
        }else {
            try {
                return Float.parseFloat(getUserCache().getFree_fruit_coin()) + Float.parseFloat(getUserCache().getFruit_coin());
            }catch (Exception e){
                LogUtil.d("dddd", getUserCache().getFree_fruit_coin()+" + "+getUserCache().getFruit_coin());
            }
            return 0f;
        }
    }
    /**
     * 获取用户缓存
     *
     * @return
     */
    public static UserBean getUserCache() {
        String user = KVHelper.getString(USER);
        return GsonUtils.fromJson(user, UserBean.class);
    }

    /**
     * 添加用户缓存
     *
     * @param user
     */
    public static void cacheUser(UserBean user) {
        LogUtil.d("dddd","添加缓存");
        UserBean cacheUser = getUserCache();
        if (cacheUser != null && !TextUtils.isEmpty(cacheUser.getToken())) {
            user.setToken(cacheUser.getToken());
        }
        KVHelper.putValue(USER, GsonUtils.toJson(user));
    }

    public static void cacheInstallTime(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00")); // 设置时区为东八区
        String formattedDate = sdf.format(Calendar.getInstance().getTime());
        KVHelper.putValue(UserCache.MAIN_AGREEMENT_TIME, formattedDate);
    }

    public static String getInstallTime(){
        return KVHelper.getString(UserCache.MAIN_AGREEMENT_TIME);
    }

    /**
     * 清除用户缓存
     */
    public static void removeUserCache() {
        LogUtil.d("dddd","清除缓存");
        KVHelper.removeValue(USER);
    }

    /**缓存上一个登录用户的信息
     * @param user
     */
    public static void cacheLastUser(UserBean user){
        KVHelper.putValue(LAST_USER, GsonUtils.toJson(user));
    }


    /**获取上一个登录用户的信息
     * @return
     */
    public static boolean isCacheLastUser(UserBean userBean){
        UserBean lastUserBean = GsonUtils.fromJson(KVHelper.getString(LAST_USER), UserBean.class);
        return lastUserBean != null && userBean.getUser_id() == lastUserBean.getUser_id();
    }

    /**
     * 获取登录成功的缓存账号
     *
     * @return
     */
    public static String getUserName() {
        return KVHelper.getString(USERNAME);
    }

    /**
     * 登录成功缓存账号
     *
     * @param username
     */
    public static void cacheUserName(String username) {
        KVHelper.putValue(USERNAME, username);
    }

    /**
     * 缓存是否拒获取绝定位权限
     *
     * @param isNegative
     */
    public static void cacheNegativeLocationPermission(boolean isNegative) {
        KVHelper.putValue(NEGATIVE_LOCATION_PERMISSION, isNegative);
    }

    /**
     * 获取是否拒获取绝定位权限
     *
     * @return
     */
    public static boolean getNegativeLocationPermission() {
        return KVHelper.getBoolean(NEGATIVE_LOCATION_PERMISSION, false);
    }

    /**
     * 缓存省份
     *
     * @param province
     */
    public static void cacheProvince(ProvinceBean province) {
        KVHelper.putValue(PROVINCE, new Gson().toJson(province));
    }

    /**
     * 获取省份
     *
     * @return
     */
    public static ProvinceBean getProvince() {
        return new Gson().fromJson(KVHelper.getString(PROVINCE), ProvinceBean.class);
    }

    /**
     * 获取JSZ省份
     *
     * @return
     */
    public static JSZProvinceBean getJSZProvince() {
        return new Gson().fromJson(KVHelper.getString(UserCache.JSZ_PROVINCE), JSZProvinceBean.class);
    }

    /**
     * 缓存JSZ省份
     *
     * @return
     */
    public static void cacheJSZProvince(JSZProvinceBean jszProvinceBean) {
        KVHelper.putValue(UserCache.JSZ_PROVINCE, new Gson().toJson(jszProvinceBean));
    }

    /**
     * 获取JSZ考试类型
     *
     * @return
     */
    public static JSZProvinceBean getJSZTestType() {
        return new Gson().fromJson(KVHelper.getString(UserCache.JSZ_TYPE), JSZProvinceBean.class);
    }

    /**
     * 缓存JSZ考试类型
     *
     * @return
     */
    public static void cacheJSZTestType(JSZProvinceBean jszTestType) {
        KVHelper.putValue(UserCache.JSZ_TYPE, new Gson().toJson(jszTestType));
    }

    /**
     * 获取JSZ等级
     *
     * @return
     */
    /*public static JSZProvinceBean getJSZGrade() {
        return new Gson().fromJson(KVHelper.getString(UserHelper.JSZ_PROFESSION), JSZProvinceBean.class);
    }*/

    /**
     * 缓存JSZ等级
     *
     * @return
     */
    /*public static void cacheJSZGrade(JSZProvinceBean jszProfessionBean) {
        KVHelper.putValue(UserHelper.JSZ_PROFESSION, new Gson().toJson(jszProfessionBean));
    }*/

    /**
     * 缓存当前用户所在地址
     *
     * @param addresss 经度
     */
    public static void cacheAddress(String addresss) {
        KVHelper.putValue(ADDRESS, addresss);
    }

    /**
     * 获取当前用户所在地址
     *
     * @return
     */
    public static String getAddress() {
        return KVHelper.getString(ADDRESS);
    }

    /**
     * 缓存经度
     *
     * @param longitude 经度
     */
    public static void cacheLongitude(String longitude) {
        KVHelper.putValue(LONGITUDE, longitude);
    }

    /**
     * 获取经度
     *
     * @return
     */
    public static String getLongitude() {
        return KVHelper.getString(LONGITUDE);
    }
    /**
     * 缓存纬度
     *
     * @param latitude 纬度
     */
    public static void cacheLatitude(String latitude) {
        KVHelper.putValue(LATITUDE, latitude);
    }

    /**
     * 获取纬度
     *
     * @return
     */
    public static String getLatitude() {
        return KVHelper.getString(LATITUDE);
    }



    /**
     * 缓存课程
     *
     * @param course
     */
    public static void cacheCourse(CourseGroupBean.CourseBean course) {
        if (TextUtils.equals(getAppType(), Constant.CK))
            KVHelper.putValue(CK_COURSE, new Gson().toJson(course));
        else if (TextUtils.equals(getAppType(), Constant.JSZ)) {
            KVHelper.putValue(JSZ_COURSE, new Gson().toJson(course));
        } else if (TextUtils.equals(getAppType(), Constant.JZS)) {
            KVHelper.putValue(JZS_COURSE, new Gson().toJson(course));
        } else if (TextUtils.equals(getAppType(), Constant.KJ)) {
            KVHelper.putValue(KJ_COURSE, new Gson().toJson(course));
        } else if (TextUtils.equals(getAppType(), Constant.RLZY)) {
            KVHelper.putValue(RLZY_COURSE, new Gson().toJson(course));
        } else if (TextUtils.equals(getAppType(), Constant.YYDJ)) {
            KVHelper.putValue(YYDJ_COURSE, new Gson().toJson(course));
        } else
            KVHelper.putValue(COURSE, new Gson().toJson(course));
    }

    /**
     * 获取课程
     *
     * @return
     */
    public static CourseGroupBean.CourseBean getCourse() {
        if (TextUtils.equals(getAppType(), Constant.CK))
            return GsonUtils.fromJson(KVHelper.getString(CK_COURSE), CourseGroupBean.CourseBean.class);
        else if (TextUtils.equals(getAppType(), Constant.JSZ))
            return GsonUtils.fromJson(KVHelper.getString(JSZ_COURSE), CourseGroupBean.CourseBean.class);
        else if (TextUtils.equals(getAppType(), Constant.JZS))
            return GsonUtils.fromJson(KVHelper.getString(JZS_COURSE), CourseGroupBean.CourseBean.class);
        else if (TextUtils.equals(getAppType(), Constant.KJ))
            return GsonUtils.fromJson(KVHelper.getString(KJ_COURSE), CourseGroupBean.CourseBean.class);
        else if (TextUtils.equals(getAppType(), Constant.RLZY))
            return GsonUtils.fromJson(KVHelper.getString(RLZY_COURSE), CourseGroupBean.CourseBean.class);
        else if (TextUtils.equals(getAppType(), Constant.YYDJ))
            return GsonUtils.fromJson(KVHelper.getString(YYDJ_COURSE), CourseGroupBean.CourseBean.class);

        return GsonUtils.fromJson(KVHelper.getString(COURSE), CourseGroupBean.CourseBean.class);
    }

    public static void cacheCity(CityBean.City city){
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            KVHelper.putValue(CITY, GsonUtils.toJson(city));

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            KVHelper.putValue(CK_CITY, GsonUtils.toJson(city));
        }
    }

    public static CityBean.City getCity(){
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            return GsonUtils.fromJson(KVHelper.getString(CITY), CityBean.City.class);

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            return GsonUtils.fromJson(KVHelper.getString(CK_CITY), CityBean.City.class);
        }

        return null;
    }


    public static void cacheCity(CityBean.City city, String appType){
        if (TextUtils.equals(appType, Constant.ZK)){
            KVHelper.putValue(CITY, GsonUtils.toJson(city));

        }else if(TextUtils.equals(appType, Constant.CK)){
            KVHelper.putValue(CK_CITY, GsonUtils.toJson(city));
        }
    }

    public static CityBean.City getCity(String appType){
        if (TextUtils.equals(appType, Constant.ZK)){
            return GsonUtils.fromJson(KVHelper.getString(CITY), CityBean.City.class);

        }else if(TextUtils.equals(appType, Constant.CK)){
            return GsonUtils.fromJson(KVHelper.getString(CK_CITY), CityBean.City.class);
        }

        return null;
    }
    /**
     * 缓存学校
     *
     * @return
     */
    public static void cacheSchool(SchoolBean schoolBean) {
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            KVHelper.putValue(UserCache.SCHOOL, new Gson().toJson(schoolBean));

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            KVHelper.putValue(CK_SHOOL, GsonUtils.toJson(schoolBean));
        }
    }

    /**获取学校
     * @return
     */
    public static SchoolBean getSchool(){
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            return GsonUtils.fromJson(KVHelper.getString(SCHOOL), SchoolBean.class);

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            return GsonUtils.fromJson(KVHelper.getString(CK_SHOOL), SchoolBean.class);
        }

        return null;
    }
    /**
     * 缓存学校
     *
     * @return
     */
    public static void cacheSchool(SchoolBean schoolBean, String appType) {
        if (TextUtils.equals(appType, Constant.ZK)){
            KVHelper.putValue(UserCache.SCHOOL, new Gson().toJson(schoolBean));

        }else if(TextUtils.equals(appType, Constant.CK)){
            KVHelper.putValue(CK_SHOOL, GsonUtils.toJson(schoolBean));
        }
    }

    /**获取学校
     * @return
     */
    public static SchoolBean getSchool(String appType){
        if (TextUtils.equals(appType, Constant.ZK)){
            return GsonUtils.fromJson(KVHelper.getString(SCHOOL), SchoolBean.class);

        }else if(TextUtils.equals(appType, Constant.CK)){
            return GsonUtils.fromJson(KVHelper.getString(CK_SHOOL), SchoolBean.class);
        }

        return null;
    }
    /**
     * 缓存指定证书类型的专业
     *
     * @param profession
     */
    public static void cacheProfession(ProfessionBean profession, String appType) {
        if (TextUtils.equals(appType, Constant.ZK)){
            KVHelper.putValue(PROFESSION, GsonUtils.toJson(profession));

        }else if(TextUtils.equals(appType, Constant.CK)){
            KVHelper.putValue(CK_PROFESSION, GsonUtils.toJson(profession));
        }
    }

    /**
     * 获取专业
     *
     * @return
     */
    public static ProfessionBean getProfession(String appType) {
        if (TextUtils.equals(appType, Constant.ZK)){
            ProfessionBean professionBean = GsonUtils.fromJson(KVHelper.getString(PROFESSION), ProfessionBean.class);
            return professionBean;

        }else if(TextUtils.equals(appType, Constant.CK)){
            ProfessionBean professionBean = GsonUtils.fromJson(KVHelper.getString(CK_PROFESSION), ProfessionBean.class);
            return professionBean;
        }

        return null;
    }
    /**
     * 缓存专业
     *
     * @param profession
     */
    public static void cacheProfession(ProfessionBean profession) {
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            KVHelper.putValue(PROFESSION, GsonUtils.toJson(profession));

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            KVHelper.putValue(CK_PROFESSION, GsonUtils.toJson(profession));
        }
    }

    /**
     * 获取专业
     *
     * @return
     */
    public static ProfessionBean getProfession() {
        if (TextUtils.equals(getAppType(), Constant.ZK)){
            ProfessionBean professionBean = GsonUtils.fromJson(KVHelper.getString(PROFESSION), ProfessionBean.class);
            return professionBean;

        }else if(TextUtils.equals(getAppType(), Constant.CK)){
            ProfessionBean professionBean = GsonUtils.fromJson(KVHelper.getString(CK_PROFESSION), ProfessionBean.class);
            return professionBean;
        }

        return null;
    }


    /**
     * 删除课程缓存
     */
    public static void removeCourse() {
        if (TextUtils.equals(getAppType(), Constant.CK))
            KVHelper.removeValue(CK_COURSE);
        else if (TextUtils.equals(getAppType(), Constant.JSZ)) {
            KVHelper.removeValue(JSZ_COURSE);
        } else if (TextUtils.equals(getAppType(), Constant.JZS)) {
            KVHelper.removeValue(JZS_COURSE);
        } else if (TextUtils.equals(getAppType(), Constant.KJ)) {
            KVHelper.removeValue(KJ_COURSE);
        }  else if (TextUtils.equals(getAppType(), Constant.RLZY)) {
            KVHelper.removeValue(RLZY_COURSE);
        } else if (TextUtils.equals(getAppType(), Constant.YYDJ)) {
            KVHelper.removeValue(YYDJ_COURSE);
        } else
            KVHelper.removeValue(COURSE);
    }

    /**
     * 缓存搜题关键字
     *
     * @param key
     * @param isClear 是否清除之前的数据
     */
    public static void cacheSearchKey(String key, boolean isClear) {
        if (isClear) {
            KVHelper.putValue(SEARCHKEY, key);
            return;
        }

        String history = KVHelper.getString(SEARCHKEY);
        if (TextUtils.isEmpty(history)) {
            history = key;
        } else {
            if (history.contains(key)) return;
            history = history + "," + key;
        }
        KVHelper.putValue(SEARCHKEY, history);
    }

    /**
     * 获取搜题关键字
     *
     * @return
     */
    public static List<String> getSearchkey() {
        String history = KVHelper.getString(SEARCHKEY);
        if (TextUtils.isEmpty(history))
            return new ArrayList<>();

        String[] keys = history.split(",");

        //只有一条记录的时候
        if (!TextUtils.isEmpty(history) && keys.length == 0) {
            keys = new String[1];
            keys[0] = history;
        }

        List<String> list = new ArrayList<>(keys.length);
        Collections.addAll(list, keys);
        return list;
    }

    /**
     * 缓存做题字体模式
     *
     * @param sizeMode
     */
    public static void cacheSizeMode(int sizeMode) {
        KVHelper.putValue(SIZEMODE, sizeMode);
    }

    /**
     * 获取做题字体模式
     *
     * @return
     */
    public static int getSizeMode() {
        return KVHelper.containsKey(SIZEMODE) ? KVHelper.getInt(SIZEMODE) : 1;
    }

    /**
     * 缓存押密广告弹出时间
     *
     */
    public static void cacheAdYaMiTime() {
        KVHelper.putValue(DAY_AD_YAMI_DIALOG, System.currentTimeMillis());
    }

    /**
     * 获取押密广告弹出时间
     *
     * @return
     */
    public static long getAdYaMiTime() {
        return KVHelper.getLong(DAY_AD_YAMI_DIALOG);
    }

    /**
     * 缓存弹出答题分享对话框时间
     *
     */
    public static void cacheShareTime() {
        KVHelper.putValue(DAY_AD_SHARE_DIALOG, System.currentTimeMillis());
    }

    /**
     * 获取答题分享对话框弹出时间
     *
     * @return
     */
    public static long getShareTime() {
        return KVHelper.getLong(DAY_AD_SHARE_DIALOG);
    }

    /**
     * 缓存答题设置 - 自动跳转下一题
     *
     * @param auto
     */
    public static void cachePracticeAuto(boolean auto) {
        KVHelper.putValue(PRACTICE_AUTO, auto);
    }

    public static boolean getPracticeAuto() {
        return KVHelper.getBoolean(PRACTICE_AUTO);
    }

    public static void cachePracticeCorrectAuto(boolean auto) {
        KVHelper.putValue(PRACTICE_CORRECT_AUTO, auto);
    }

    public static boolean getPracticeCorrectAuto() {
        return KVHelper.getBoolean(PRACTICE_CORRECT_AUTO, true);
    }

    public static void cachePracticeErrorRemove(boolean auto) {
        KVHelper.putValue(PRACTICE_ERROR_REMOVE, auto);
    }

    public static boolean getPracticeErrorRemove() {
        //默认开启
        if(!KVHelper.containsKey(PRACTICE_ERROR_REMOVE)){
            return true;
        }
        return KVHelper.getBoolean(PRACTICE_ERROR_REMOVE);
    }

    public static void cachePracticeErrorRemoveCount(int count) {
        KVHelper.putValue(PRACTICE_ERROR_REMOVE_COUNT, count);
    }

    public static int getPracticeErrorRemoveCount() {
        //默认开启
        if(!KVHelper.containsKey(PRACTICE_ERROR_REMOVE_COUNT)){
            return 1;
        }
        return KVHelper.getInt(PRACTICE_ERROR_REMOVE_COUNT);
    }
    public static void cachePracticeMode(int mode) {
        KVHelper.putValue(PRACTICE_MODE, mode);
    }

    public static int getPracticeMode() {
        int mode = KVHelper.getInt(PRACTICE_MODE);
        return mode == 0 ? PracticeHelper.MODE_DEFAULT : mode;
    }

    public static void cacheDayNight(boolean night) {
        KVHelper.putValue(DAY_NIGHT, night);
    }

    public static boolean isDayNight() {
        return KVHelper.getBoolean(DAY_NIGHT);
    }

    /**
     * 缓存是否已填写调查问卷
     *
     */
    public static void cacheIsInputTable() {
        KVHelper.putValue(IS_INPUT_TABLE, true);
    }

    public static boolean getIsInputTable() {
        return KVHelper.getBoolean(IS_INPUT_TABLE, false);
    }

    /**
     * 缓存调查问卷的身份信息
     *
     * @param lable
     */
    public static void cacheLabel(String lable) {
        KVHelper.putValue(LABEL, lable);
    }

    public static String getLabel() {
        return KVHelper.getString(LABEL);
    }

    /**
     * 缓存调查问卷的学历
     *
     * @param qualification
     */
    public static void cacheEducation(String qualification) {
        KVHelper.putValue(QUALIFICATIONS, qualification);
    }

    public static String getEducation() {
        return KVHelper.getString(QUALIFICATIONS);
    }

    /**
     * 缓存调查问卷的备考准备
     *
     * @param exam
     */
    public static void cacheExamReference(String exam) {
        KVHelper.putValue(EXAM_PREPARATION, exam);
    }

    public static String getExamReference() {
        return KVHelper.getString(EXAM_PREPARATION);
    }

    /**
     * 缓存调查问卷考取学历的理由列表
     *
     * @param beans
     */
    public static void cacheReason(HashSet<String> beans) {
        KVHelper.putValue(REASONS_FOR_DEGREE, new Gson().toJson(beans));
    }

    /**
     * 获取考取学历的理由
     *
     * @return
     */
    public static HashSet<String> getReason() {
        return new Gson().fromJson(KVHelper.getString(REASONS_FOR_DEGREE), HashSet.class);
    }

    /**
     * 用于判断是否把问卷答完
     *
     * @return
     */
    public static boolean isCacheSurvey() {
        return (!TextUtils.isEmpty(UserCache.getLabel()) &&
                !TextUtils.isEmpty(UserCache.getEducation()) &&
                !TextUtils.isEmpty(UserCache.getExamReference()) &&
                (UserCache.getReason() != null && UserCache.getReason().size() > 0));
    }

    public static void removeSurvey(){
        KVHelper.removeValue(LABEL);
        KVHelper.removeValue(QUALIFICATIONS);
        KVHelper.removeValue(EXAM_PREPARATION);
        KVHelper.removeValue(REASONS_FOR_DEGREE);
    }
    /**
     * 删除第一代引导缓存
     */
    public static void removeGuide() {
        KVHelper.removeValue(GUIDE);
    }

    /**
     * 缓存弹出题库更新话框时间
     *
     */
    public static void cacheQuestionBanUpdateTime(String date) {
        KVHelper.putValue(UPDATE_QUESTION_BAN_DIALOG, date);
    }

    /**
     * 获取弹出题库更新话框时间
     *
     * @return
     */
    public static String getQuestionBanUpdateTime() {
        return KVHelper.getString(UPDATE_QUESTION_BAN_DIALOG);
    }

    /**判断是否已开通笔果折扣卡
     * @return
     */
    public static boolean isMemberShip(){
        return getUserCache() != null && getUserCache().getMembership() == 1;//1会员，0不是会员
    }

    /**缓存报考课程
     * @param courseBean
     */
    public static void setEnrollCourse(CourseBean courseBean){
        KVHelper.putValue(getProfession().getId()+"", new Gson().toJson(courseBean));
    }

    /**获取报考课程
     * @return
     */
    public static CourseBean getEnrollCourse(){
        String json = KVHelper.getString(getProfession().getId()+"");
        CourseBean courseBean = GsonUtils.fromJson(json, CourseBean.class);
        if(courseBean == null) {
            courseBean = new CourseBean();
            courseBean.setCourses_joined(new ArrayList<>());
        }
        return courseBean;
    }

    /**返回是否主动展示过果币须知
     * @return
     */
    public static boolean isShowRechargeRule(){
        return KVHelper.containsKey(RECHARGE_IS_SHOW);
    }

    /**设置是否主动展示过果币须知
     * @return
     */
    public static void setShowRechargeRule(){
        KVHelper.putValue(RECHARGE_IS_SHOW, true);
    }

    /**返回是否关闭了自考首页的优惠券按钮
     * @return
     */
    public static boolean isDayCloseZkHomeDiscount(){
        return TimeFormatUtils.isToday(KVHelper.getLong(IS_CLOSE_ZK_HOME_DISCOUNT));
    }

    /**设置关闭自考首页的优惠券按钮
     * @return
     */
    public static void setDayCloseZkHomeDiscount(){
        KVHelper.putValue(IS_CLOSE_ZK_HOME_DISCOUNT, System.currentTimeMillis());
    }

    /**缓存首页的加入微信群按钮
     * @return
     */
    public static void cacheZkHomeJoinWechatGroup(JoinWechatGroupBean bean){
        KVHelper.putValue(JOIN_WECHAT_GROUP, GsonUtils.toJson(bean));
    }

    /**返回首页的加入微信群按钮
     * @return
     */
    public static JoinWechatGroupBean getZkHomeJoinWechatGroup(){
        return GsonUtils.fromJson(KVHelper.getString(JOIN_WECHAT_GROUP), JoinWechatGroupBean.class);
    }

    /**缓存首页的是否显示过加入微信群弹窗
     * @return
     */
    public static void cacheIsShowZkHomeJoinWechatGroupDialog(){
        KVHelper.putValue(IS_SHOW_JOIN_WECHAT_GROUP, true);
    }

    /**返回首页的是否显示过加入微信群弹窗
     * @return
     */
    public static boolean isShowZkHomeJoinWechatGroupDialog(){
        return KVHelper.getBoolean(IS_SHOW_JOIN_WECHAT_GROUP, false);
    }

    /**
     * 缓存客服常见问题的更新时间
     *
     */
    public static void cacheQuestionTime() {
        KVHelper.putValue(DAY_QUESTION_GET, System.currentTimeMillis());
    }

    /**
     * 获取客服常见问题的更新时间
     *
     * @return
     */
    public static long getQuestionTime() {
        return KVHelper.getLong(DAY_QUESTION_GET);
    }


    /**
     * 缓存首次安装的参数
     *
     */
    public static void cacheLinkParams(String params) {
        if(getUserCache() != null) {
            LogUtil.d("dddd", "清空");
            KVHelper.removeValue(INSTALL_PARAMS);

        }else {
            HashMap<String, String> paramsMap = GsonUtils.fromJson(UserCache.getLinkParams(), new TypeToken<HashMap<String, String>>() {}.getType());
            HashMap<String, String> insertParamsMap = GsonUtils.fromJson(params, new TypeToken<HashMap<String, String>>() {}.getType());
            if (paramsMap != null && insertParamsMap != null && !insertParamsMap.isEmpty()) {
                paramsMap.putAll(insertParamsMap);
                KVHelper.putValue(INSTALL_PARAMS, GsonUtils.toJson(paramsMap));
                LogUtil.d("dddd", getLinkParams());
            }else if(paramsMap == null && insertParamsMap != null && !insertParamsMap.isEmpty()) {
                KVHelper.putValue(INSTALL_PARAMS, GsonUtils.toJson(insertParamsMap));
                LogUtil.d("dddd", getLinkParams());
            }
        }
    }

    /**
     * 获取首次安装的参数
     *
     * @return
     */
    public static String getLinkParams() {
        return KVHelper.getString(INSTALL_PARAMS);
    }


    /**
     * 缓存课件的阅读位置
     *
     */
    public static void cacheCourseFileCurrent(String filePath, int current) {
        KVHelper.putValue(filePath, current);
    }

    /**
     * 获取课件的阅读位置
     *
     * @return
     */
    public static int getCourseFileCurrent(String filePath) {
        return KVHelper.getInt(filePath);
    }

    /**
     * 缓存显示过指定计划的弹窗
     *
     */
    public static void cacheShowMakePlanDialog() {
        KVHelper.putValue(HAS_SHOW_MAKE_PLAN_DIALOG, true);
    }

    /**
     * 获取是否显示过指定计划的弹窗
     *
     * @return
     */
    public static boolean getShowMakePlanDialog() {
        return KVHelper.getBoolean(HAS_SHOW_MAKE_PLAN_DIALOG, false);
    }

    /**设置APP版本是否过审
     * @param isReview
     */
    public static void setVersionReview(boolean isReview){
        KVHelper.putValue(MAIN_AGREEMENT_IS_REVIEW, isReview);
    }

    /**查询APP版本是否过审
     * @return
     */
    public static boolean isVersionReview(){
        return KVHelper.getBoolean(MAIN_AGREEMENT_IS_REVIEW, false);
    }


    /**设置当前专业的课程改革弹窗不再弹窗
     */
    public static void setProfessionChangeDialog(){
        if(getCity() == null) return;
        if(getSchool() == null) return;
        if(getProfession() == null) return;

        int cityId = getCity().getCity_id();
        int schoolId = getSchool().getId();
        int professionId = getProfession().getId();
        KVHelper.putValue(String.format("%s_%s_%s_%s", PROFESSION_CHANGE_DIALOG, cityId, schoolId, professionId), true);
    }

    /**查询APP版本是否过审
     * @return
     */
    public static boolean isProfessionChangeDialog(){
        if(getCity() == null) return false;
        if(getSchool() == null) return false;
        if(getProfession() == null) return false;

        int cityId = getCity().getCity_id();
        int schoolId = getSchool().getId();
        int professionId = getProfession().getId();
        return KVHelper.getBoolean(String.format("%s_%s_%s_%s", PROFESSION_CHANGE_DIALOG, cityId, schoolId, professionId), false);
    }

    /**
     * 缓存今日是否显示过去好评的弹窗
     */
    public static void cacheShowGoodApp(boolean isAlways){
        KVHelper.putValue(IS_SHOW_GOOD_APP, isAlways ? Long.MAX_VALUE : System.currentTimeMillis());
    }

    /**
     * 查询今日是否显示过去好评的弹窗
     */
    public static boolean isShowGoodApp(){
        return System.currentTimeMillis() - KVHelper.getLong(IS_SHOW_GOOD_APP) > (43000 * 1000);
    }

    public static void cacheOpenMembership(){
        OpenMembershipBean bean = getOpenMembership();
        if(bean == null){
            bean = new OpenMembershipBean();
        }
        bean.setLastTime(System.currentTimeMillis());
        bean.setCount(bean.getCount()+1);

        KVHelper.putValue(IS_SHOW_OPEN_MEMBERSHIP, GsonUtils.toJson(bean));
    }

    public static OpenMembershipBean getOpenMembership(){
        String json = KVHelper.getString(IS_SHOW_OPEN_MEMBERSHIP);
        if(AppUtil.isEmpty(json)){
            return null;
        }
        return GsonUtils.fromJson(json, OpenMembershipBean.class);
    }

    public static void cacheHomeShowEnrollCourse(){
        KVHelper.putValue(HOME_SHOW_ENROLL_COURSE, true);
    }

    public static boolean getHomeShowEnrollCourse(){
        return KVHelper.getBoolean(HOME_SHOW_ENROLL_COURSE);
    }

    public static void cacheHomeCode(String code){
        KVHelper.putValue(HOME_CODE, code);
    }

    public static String getHomeCode(){
        return AppUtil.isEmpty(KVHelper.getString(HOME_CODE), "");
    }
}
