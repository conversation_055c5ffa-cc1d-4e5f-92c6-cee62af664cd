package com.dep.biguo.utils.database;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class StudyRealmBean extends RealmObject {
    @PrimaryKey private int courses_id;//课程id
    private int is_super_vip; //是否已购买超V
    private int is_yami; //是否已购买押密
    private int is_vip; //是否已购买VIP题库
    private int is_high;//是否已购买高频考点
    private int has_yami; //是否上架押密, -1预定押密未上架，0押密未上架，1押密已上架
    private int has_video; //是否上架视频
    private int has_chapter; //是否上架章节训练
    private int has_real; //是否上架历年真题
    private int question_cal_nums; //总可答题数量
    private int answered_nums; //总已答题数
    private int time_remains; //距考试剩余时长
    private int take_time; //总答题时间
    private int today_answered;//今日答题数量
    private String correct_rate;//正确率
    private String completion_rate;//完成率
    private RealmList<QuestionBankRealmBean> tikus;

    public int getCourses_id() {
        return courses_id;
    }

    public void setCourses_id(int courses_id) {
        this.courses_id = courses_id;
    }

    public int getIs_super_vip() {
        return is_super_vip;
    }

    public void setIs_super_vip(int is_super_vip) {
        this.is_super_vip = is_super_vip;
    }

    public int getIs_yami() {
        return is_yami;
    }

    public void setIs_yami(int is_yami) {
        this.is_yami = is_yami;
    }

    public int getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(int is_vip) {
        this.is_vip = is_vip;
    }

    public int getIs_high() {
        return is_high;
    }

    public void setIs_high(int is_high) {
        this.is_high = is_high;
    }

    public int getHas_yami() {
        return has_yami;
    }

    public void setHas_yami(int has_yami) {
        this.has_yami = has_yami;
    }

    public int getHas_video() {
        return has_video;
    }

    public void setHas_video(int has_video) {
        this.has_video = has_video;
    }

    public int getHas_chapter() {
        return has_chapter;
    }

    public void setHas_chapter(int has_chapter) {
        this.has_chapter = has_chapter;
    }

    public int getHas_real() {
        return has_real;
    }

    public void setHas_real(int has_real) {
        this.has_real = has_real;
    }

    public int getQuestion_cal_nums() {
        return question_cal_nums;
    }

    public void setQuestion_cal_nums(int question_cal_nums) {
        this.question_cal_nums = question_cal_nums;
    }

    public int getAnswered_nums() {
        return answered_nums;
    }

    public void setAnswered_nums(int answered_nums) {
        this.answered_nums = answered_nums;
    }

    public int getTime_remains() {
        return time_remains;
    }

    public void setTime_remains(int time_remains) {
        this.time_remains = time_remains;
    }

    public int getTake_time() {
        return take_time;
    }

    public void setTake_time(int take_time) {
        this.take_time = take_time;
    }

    public int getToday_answered() {
        return today_answered;
    }

    public void setToday_answered(int today_answered) {
        this.today_answered = today_answered;
    }

    public RealmList<QuestionBankRealmBean> getTikus() {
        return tikus;
    }

    public void setTikus(RealmList<QuestionBankRealmBean> tikus) {
        this.tikus = tikus;
    }

    public String getCorrect_rate() {
        return correct_rate;
    }

    public void setCorrect_rate(String correct_rate) {
        this.correct_rate = correct_rate;
    }

    public String getCompletion_rate() {
        return completion_rate;
    }

    public void setCompletion_rate(String completion_rate) {
        this.completion_rate = completion_rate;
    }
}
