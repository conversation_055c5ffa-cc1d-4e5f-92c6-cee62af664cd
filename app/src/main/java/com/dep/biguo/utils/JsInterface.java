package com.dep.biguo.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.webkit.JavascriptInterface;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.wxapi.WxMinApplication;

/**JS交互类
 * 1、写一个响应JS事件的方法
 * 2、注册事件，如：webView.addJavascriptInterface(new JsInterface(this), "jumpFileStorage");第二个参数是取个别名，js把这个别名当做对象名
 */
public class JsInterface {
    private Context mContext;
    private String pdf;

    public JsInterface(Context mContext) {
        this.mContext = mContext;
    }

    public JsInterface setPdf(String pdf) {
        this.pdf = pdf;
        return this;
    }

    @JavascriptInterface
    public void showShareDialog(String url) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(() -> {
            new ShareDialog.Builder(mContext)
                    .setShareTitle(mContext.getString(R.string.invite_share_content))
                    .setShareUrl(url)
                    .setOnShareListener((type) -> {
                        String value = url.contains(Constant.FACE_INVITATION) ? "注册：面对面邀请" : ("APP：h5->"+url);
                        new UmengEventUtils(mContext)
                                .addParams("path", value)
                                .addParams("platform", type)
                                .pushEvent(UmengEventUtils.CLICK_SHARE_APP);
                    })
                    .builder()
                    .show();
        });//调用方法发送消息（原来想要执行的地方）
    }


    /**对表单的提交埋点，友盟
     *
     */
    @JavascriptInterface
    public void buriedPoint() {
        //对H5页面在点击提交时的一个超链接进行拦截，对提交表单的按钮进行埋点
        new UmengEventUtils(mContext).pushEvent(UmengEventUtils.URL_COMMIT);
    }

    @JavascriptInterface
    public void clickButton(String url){
        new UmengEventUtils(mContext)
                .addParams("urL", url)
                .pushEvent(UmengEventUtils.URL_COMMIT_URL);
    }

    /**档案管理
     * @param url 小程序路径和H5链接
     */
    @JavascriptInterface
    public void jumpFileStorage(String url){
        //对H5页面在点击提交时的一个超链接进行拦截，对提交按钮进行埋点
        new UmengEventUtils(mContext).pushEvent(UmengEventUtils.CLICK_USER_INFO_MANAGE_SAVE);

        String[] ages = url.split(",");
        WxMinApplication.StartWechat(mContext, ages[1], ages[0]);
    }

    /**
     * 本地有个html文件，需要该方法传递一个PDF的链接
     */
    @JavascriptInterface
    public String getPdfParameter(){
        return pdf;
    }
}
