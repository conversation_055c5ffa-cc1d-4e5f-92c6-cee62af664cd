package com.dep.biguo.utils.image;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.os.Handler;
import android.os.Message;
import android.provider.MediaStore;
import android.widget.ImageView;

import com.biguo.utils.util.LogUtil;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.EncodeStrategy;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dep.biguo.R;

import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/27
 * @Description:
 */
public class ImageLoader {
    /**加载头像
     * @param iv 控件
     * @param path 图片路径，可以是字符串，也可以是资源ID
     */
    public static void loadAvatar(ImageView iv, Object path) {
        loadAvatar(iv, path, R.drawable.default_avatar);
    }

    /**加载头像
     * @param iv 控件
     * @param path 图片路径，可以是字符串，也可以是资源ID
     * @param placeholder 占位图
     */
    public static void loadAvatar(ImageView iv, Object path, int placeholder) {
        Glide.with(iv.getContext())
                .load(path)
                .transition(new DrawableTransitionOptions().crossFade())//淡入淡出
                .apply(new RequestOptions()
                        .placeholder(placeholder)//占位图
                        .transform(new CenterCrop(), new CropCircleTransformation()))//居中，裁剪成圆形
                .into(iv);
    }

    /**
     * 加载带边框的头像
     *
     * @param iv 控件
     * @param path 图片路径，可以是字符串，也可以是资源ID
     * @param color 边框颜色
     * @param borderSize 边框大小
     */
    public static void loadAvatarWithBorder(ImageView iv, Object path, int color, int borderSize) {
        Glide.with(iv.getContext())
                .load(path)
                .transition(new DrawableTransitionOptions().crossFade())//淡入淡出
                .apply(new RequestOptions()
                        .placeholder(R.drawable.default_avatar)//占位图
                        .transform(new CropCircleWithBorderTransformation(borderSize, color)))//绘制边框
                .into(iv);
    }

    /**加载图片，使用默认的占位图
     * @param iv
     * @param path
     */
    public static void loadImage(ImageView iv, Object path) {
        loadImage(iv, path, R.color.bgc);
    }

    /**加载图片，使用自定义的占位图
     * @param iv
     * @param path
     */
    public static void loadImage(ImageView iv, Object path, int placeholder) {
        if(iv.getLayoutParams().height <= 0){
            Glide.with(iv.getContext())
                    .load(path)
                    .placeholder(placeholder)
                    .transition(new DrawableTransitionOptions().crossFade())
                    .into(iv);
        }else {
            Glide.with(iv.getContext())
                    .load(path)
                    .placeholder(placeholder)
                    .centerCrop()//ImageView的layout_height设置了wrap_content，使用centerCrop()处理会出现图片拉伸的问题
                    .transition(new DrawableTransitionOptions().crossFade())
                    .into(iv);
        }
    }

    public static void loadImageSize(ImageView iv, Object path){
        Glide.with(iv.getContext())
                .load(path)
                .into(iv);
    }

    /**加载图片
     * @param iv
     * @param path
     */
    public static void loadImageNoPlaceholder(ImageView iv, Object path) {
        loadImageNoPlaceholder(iv, path, -1);
    }

    public static void loadImageNoPlaceholder(ImageView iv, Object path, int errorRes){
        if(iv.getLayoutParams().height <= 0){
            Glide.with(iv.getContext())
                    .load(path)
                    .error(errorRes)
                    .transition(new DrawableTransitionOptions().crossFade())
                    .into(iv);
        }else {
            //1、ImageView的layout_height设置了wrap_content、match_parent
            //2、在ConstraintLayout的布局中，ImageView的layout_height设置为0dp,
            //使用centerCrop()处理，会出现图片拉伸的问题，不排除还有其它因素会导致这个问题
            Glide.with(iv.getContext())
                    .load(path)
                    .centerCrop()
                    .error(errorRes)
                    .transition(new DrawableTransitionOptions().crossFade())
                    .into(iv);
        }
    }

    public static void loadImageCache(ImageView iv, Object path) {
        Glide.with(iv.getContext())
                .load(path)
                .transition(new DrawableTransitionOptions().crossFade())
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .into(iv);
    }
    /**加载的图片的四个角都设置指定数值的圆角
     * @param iv 控件
     * @param path 图片路径，可以是字符串，也可以是资源ID
     * @param radius 圆角大小，PX单位
     */
    public static void loadRadiusImage(ImageView iv, Object path, int radius) {
        loadRadiusImage(iv, path, radius, RoundedCornersTransformation.CornerType.ALL);
    }

    /**加载的图片的四个角都设置指定数值的圆角
     * @param iv 控件
     * @param path 图片路径，支持范围与Glide的load()方法保持一致
     * @param radius 圆角大小，PX单位
     * @param cornerType 圆角的位置，参考{@link RoundedCornersTransformation.CornerType#ALL}
     */
    public static void loadRadiusImage(ImageView iv, Object path, int radius, RoundedCornersTransformation.CornerType cornerType) {
        Glide.with(iv.getContext())
                .load(path)
                .transition(new DrawableTransitionOptions().crossFade())
                .apply(new RequestOptions().transform(new CenterCrop(), new RoundedCornersTransformation(radius, 0, cornerType)))
                .into(iv);
    }

    /**加载的图片尽量展示完整，四个角都设置指定数值的圆角
     * @param iv 控件
     * @param path 图片路径，支持范围与Glide的load()方法保持一致
     * @param radius 圆角大小，PX单位
     * @param transformation 平铺方式，CenterCrop、FitCenter等，参考{@link android.widget.ImageView.ScaleType}
     * @param cornerType 圆角的位置，参考{@link RoundedCornersTransformation.CornerType}
     */
    public static void loadRadiusImage(ImageView iv, Object path, int radius, BitmapTransformation transformation, RoundedCornersTransformation.CornerType cornerType) {
        Glide.with(iv.getContext())
                .load(path)
                .transition(new DrawableTransitionOptions().crossFade())
                .apply(new RequestOptions().transform(transformation, new RoundedCornersTransformation(radius, 0, cornerType)))
                .into(iv);
    }

    /**给定一个设置过最大宽度的控件，按比例拉伸图片，使得 图片的宽度等于控件宽度 或 图片的高度等于控件高度，
     *      控件的最终尺寸为图片拉伸后的尺寸
     * @param imageView 控件
     * @param obj 图片路径或drawable对象
     * @param maxWidth  最大显示宽度
     * @param radius 圆角大小
     */
    public static void loadMaxWidthDrawableImage(ImageView imageView, Object obj, int maxWidth, int radius){
        Glide.with(imageView.getContext())
                .asBitmap()
                .load(obj)
                .into(new CustomTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                        RoundDrawable drawable = new RoundDrawable(resource);
                        //计算图片宽的比值
                        float resourceScale = (float)drawable.getIntrinsicWidth() / drawable.getIntrinsicHeight();
                        imageView.getLayoutParams().height = (int) (maxWidth / resourceScale);
                        imageView.setImageDrawable(drawable);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {

                    }
                });
    }
}
