package com.dep.biguo.utils.database.util;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.CardBean;
import com.dep.biguo.bean.ChapterBean;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.PracticeRecordBean;
import com.dep.biguo.bean.QuestionBean;
import com.dep.biguo.bean.QuestionCacheBean;
import com.dep.biguo.bean.StudyBean;
import com.dep.biguo.bean.TestPaperListBean;
import com.dep.biguo.bean.TruePaperNewItemBean;
import com.dep.biguo.bean.UpdateQuestionBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.MD5Util;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.database.AnswerRealmBean;
import com.dep.biguo.utils.database.ChapterRealmBean;
import com.dep.biguo.utils.database.ChapterSectionsRealBean;
import com.dep.biguo.utils.database.CollRealmBean;
import com.dep.biguo.utils.database.CourseItemRealmBean;
import com.dep.biguo.utils.database.CourseRealmBean;
import com.dep.biguo.utils.database.ErrorRealmBean;
import com.dep.biguo.utils.database.MainRealmBean;
import com.dep.biguo.utils.database.Question2RealmBean;
import com.dep.biguo.utils.database.StudyRealmBean;
import com.dep.biguo.utils.database.SumiRealmBean;
import com.dep.biguo.utils.database.TruePaperRealmBean;
import com.dep.biguo.utils.database.listener.QueryListener;
import com.dep.biguo.utils.database.listener.QueryTowListener;
import com.dep.biguo.utils.database.listener.RealmQueryListener;
import com.dep.biguo.utils.database.listener.RealmTowQueryListener;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.UserCache;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.integration.AppManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.realm.FieldAttribute;
import io.realm.Realm;
import io.realm.RealmConfiguration;
import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.RealmObjectSchema;
import io.realm.RealmResults;
import io.realm.RealmSchema;
import io.realm.Sort;
import io.realm.exceptions.RealmFileException;

/**
 *
 */
public class RealQuery {
    public static final String DATABASE_NAME = "new_question";
    public static final int DATABASE_VERSION = 7;

    public static void init(Context context){
        Realm.init(context);

        Realm.setDefaultConfiguration(getConfiguration(context));
    }

    public static RealmConfiguration getConfiguration(Context context){
        return new RealmConfiguration.Builder()
                .name(DATABASE_NAME)
                .encryptionKey(getDataPassword())//设置数据库密码,必须是64bytes
                .deleteRealmIfMigrationNeeded()
                .directory(getRealFileDir(context))
                .schemaVersion(DATABASE_VERSION)
                .migration((realm, oldVersion, newVersion) -> {
                    //从版本6升级到7，添加了一个答题记录是否提交到服务器的字段
                    if(oldVersion <= 6){
                        RealmSchema schema = realm.getSchema();

                        RealmObjectSchema answerSchema = schema.get("AnswerRealmBean");
                        if(answerSchema != null) {
                            answerSchema.addField("isCommit", boolean.class, FieldAttribute.REQUIRED)
                                    .transform(obj -> obj.set("isCommit", true));
                        }

                        RealmObjectSchema courseSchema = schema.get("CourseRealmBean");
                        if(courseSchema != null) {
                            courseSchema.addField("icon_url", String.class)
                                    .transform(obj -> obj.set("icon_url", ""));
                        }
                    }
                })
                .build();
    }

    public static File getRealFileDir(Context context){
        File file = new File(context.getFilesDir().getParent()+"/realm");
        if(!file.exists()){
            file.mkdir();
        }
        return file;
    }


    public static byte[] getDataPassword(){
        String mobile = AppUtil.isEmpty(DeviceCache.getSystemModel(), Build.MODEL);
        String md5First = MD5Util.md5(mobile);
        String md5Second = MD5Util.md5(md5First);
        String key = md5First+md5Second;
        LogUtil.d("dddd", key);
        return key.getBytes();
    }

    private static Realm open(){
        try {
            return Realm.getDefaultInstance();
        }catch (RealmFileException e){
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
            return null;
        }
    }

    private static void close(Realm realm){
        realm.close();
    }

    /**插入课程
     * @param courseBean 课程
     */
    public static void insertCourse(CourseBean courseBean){
        CourseRealmBean course = RealmGson.fromJson(RealmGson.toJson(courseBean), CourseRealmBean.class);
        course.setProfessionId(UserCache.getProfession().getId());

        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        realm.copyToRealmOrUpdate(course);
        realm.commitTransaction();
        close(realm);
    }

    /**查询课程
     * @return
     */
    public static CourseBean queryCourse(){
        Realm realm = open();
        if(realm == null) return null;
        //查询课程表
        CourseRealmBean result = realm.where(CourseRealmBean.class)
                .equalTo("professionId", UserCache.getProfession().getId())
                .findFirst();
        if(result == null) return null;

        //将课程表复制一份，用于序列化
        CourseRealmBean copyResult = realm.copyFromRealm(result);
        //序列化课程表
        String serializeJson = RealmGson.toJson(copyResult);
        //返回通过反序列化初始化的CourseBean对象
        CourseBean courseBean =  RealmGson.fromJson(serializeJson, CourseBean.class);
        //关闭数据库
        close(realm);
        return courseBean;
    }

    /**插入学习
     * @param studyBean 学习对象
     * @param courses_id 课程ID
     */
    public static void insertStudy(StudyBean studyBean, int courses_id){
        StudyRealmBean study = RealmGson.fromJson(RealmGson.toJson(studyBean), StudyRealmBean.class);
        study.setCourses_id(courses_id);

        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        realm.copyToRealmOrUpdate(study);
        realm.commitTransaction();
        close(realm);
    }

    /**查询学习
     * @param courses_id 课程ID
     * @return
     * */
    public static StudyBean queryStudy(int courses_id){
        Realm realm = open();
        if(realm == null) return null;
        StudyRealmBean course = realm.where(StudyRealmBean.class)
                .equalTo("courses_id", courses_id)
                .findFirst();

        if(course == null) return null;

        StudyBean studyBean = RealmGson.fromJson(RealmGson.toJson(realm.copyFromRealm(course)), StudyBean.class);

        close(realm);
        return studyBean;
    }

    /**插入题库
     * @param code 课程代码
     * @param mainType 题库类型
     * @param mainTypeName 题库名称
     * @param otherId 真题ID/章节ID/试卷ID
     */
    public static void insertMain(String code, int mainType, String mainTypeName, String otherId){
        Realm realm = open();
        if(realm == null) return;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);

        if(main != null) return;

        realm.beginTransaction();
        main = new MainRealmBean();
        main.setId(String.format("%s%s%s", code, mainType, otherId));
        main.setCode(code);
        main.setMainType(mainType);
        main.setMainTypeName(mainTypeName);
        main.setReal_id(mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "");
        main.setChapter_id(mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
        main.setPaper_id(mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "");
        main.setUpdate_time(-1);
        realm.copyToRealmOrUpdate(main);
        realm.commitTransaction();
        close(realm);
    }

    /**查询题库
     * @param realm 数据库实例对象
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @return 题库
     */
    private static MainRealmBean queryMain(Realm realm, String code, int mainType, String otherId){
        return realm.where(MainRealmBean.class)
                .equalTo("code", code)
                .equalTo("mainType", mainType)
                .equalTo("real_id", mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .findFirst();
    }

    /**查询题库的最后更新时间
     * @return 时间戳
     */
    public static long queryMainUpdateTime(String code, int mainType, String otherId){
        Realm realm = open();
        if(realm == null) return -1;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);
        if(main == null) {
            LogUtil.d("dddd", "main题库查询更新日期不存在");
        }
        LogUtil.d("dddd", code+" "+mainType+" "+otherId);
        if(main == null) return -1;

        LogUtil.d("dddd", RealmGson.toJson(realm.copyFromRealm(main)));

        long update_time = main.getUpdate_time();
        close(realm);
        return update_time;
    }

    /**更新题库的最后一次更新时间
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     */
    public static void updateMainUpdateTime(String code, int mainType, String otherId, long updateTime){
        Realm realm = open();
        if(realm == null) return;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);

        if(main == null) {
            LogUtil.d("dddd", "更新main题库的更新日期不存在");
        }
        LogUtil.d("dddd", code+" "+mainType+" "+otherId);
        if(main == null) return;

        //本方法内没有开启事务，就需要先开启事务
        boolean isInTransaction = false;
        if(!realm.isInTransaction()) {
            realm.beginTransaction();
            isInTransaction = true;
        }
        main.setUpdate_time(updateTime);
        realm.copyToRealmOrUpdate(main);
        //本方法内开启了事务，就需要关闭事务
        if(isInTransaction){
            realm.commitTransaction();
        }
        close(realm);
    }

    /**查询答题卡
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param queryListener
     */
    public static void queryCard(String code, int mainType, String otherId, QueryTowListener<List<CardBean.Topic>, List<QuestionBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        LogUtil.d("dddd", code+" "+mainType+" "+otherId);
        RealmResults<Question2RealmBean> results = realm.where(Question2RealmBean.class)
                .equalTo("code", code)
                .equalTo("mainType", mainType)
                .equalTo("real_id", mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .sort("topic_type", Sort.ASCENDING)
                .findAllAsync();
        results.addChangeListener(new RealmTowQueryListener<Question2RealmBean, List<CardBean.Topic>, List<QuestionBean>>(queryListener, realm) {
                    @Override
                    public void onAsyncChange(List<Question2RealmBean> question2RealmBeans) {
                        LogUtil.d("dddd", System.currentTimeMillis());
                        Realm realm = open();
                        if(realm == null) {
                            callBack(new ArrayList<>(), new ArrayList<>());
                            return;
                        }
                        MainRealmBean main = queryMain(realm, code, mainType, otherId);
                        long clear_time = main == null ? 0 : main.getClear_time();

                        //复制一份，用于序列化
                        List<CardBean.Topic> resultCard = new ArrayList<>();
                        List<QuestionBean> resultQuestion = new ArrayList<>();
                        //遍历题目，查询答题记录和收藏状态
                        for(Question2RealmBean question : question2RealmBeans){
                            //创建答题卡和题目对象
                            CardBean.Topic topic = RealmGson.fromJson(RealmGson.toJson(question), CardBean.Topic.class);
                            QuestionBean questionBean = RealmGson.fromJson(RealmGson.toJson(question), QuestionBean.class);

                            //不是模拟试卷，就可以查询答题记录
                            if(mainType != PracticeHelper.PRACTICE_SIMU) {
                                AnswerRealmBean answer = realm.where(AnswerRealmBean.class)
                                        .equalTo("key", String.format("%s%s%s%s", code, mainType, otherId, topic.getId()))
                                        .greaterThan("update_time", clear_time)
                                        .findFirst();
                                if (answer == null) {
                                    topic.setSelect_answer("");
                                } else {
                                    if(TextUtils.equals(answer.getSelect_answer(), "#") && answer.getIs_correct() == Constant.ANSWER_NONE){
                                        topic.setSelect_answer("");
                                        topic.setIs_correct(Constant.ANSWER_WRITE_SHOW);
                                    }else {
                                        topic.setSelect_answer(AppUtil.isEmpty(answer.getSelect_answer(), ""));
                                        topic.setIs_correct(answer.getIs_correct());
                                    }
                                }
                            }

                            //查询收藏状态
                            CollRealmBean coll = realm.where(CollRealmBean.class)
                                    .equalTo("key", String.format("%s%s%s%s", question.getCode(), question.getMainType(), otherId, question.getId()))
                                    .findFirst();
                            if(coll != null){
                                questionBean.setIsCollection(coll.getIsCollection());
                            }

                            //将章节ID/真题ID/模拟试卷ID添加到value中，方便后续使用value值
                            if(!AppUtil.isEmpty(question.getChapter_id())){
                                topic.setValue(question.getChapter_id());
                                questionBean.setValue(question.getChapter_id());

                            }else if(!AppUtil.isEmpty(question.getReal_id())){
                                topic.setValue(question.getReal_id());
                                questionBean.setValue(question.getReal_id());

                            }else if(!AppUtil.isEmpty(question.getPaper_id())){
                                topic.setValue(question.getPaper_id());
                                questionBean.setValue(question.getPaper_id());
                            }else {
                                topic.setValue("");
                                questionBean.setValue("");
                            }

                            //添加到对应的集合中
                            resultCard.add(topic);
                            resultQuestion.add(questionBean);
                        }
                        close(realm);
                        callBack(resultCard, resultQuestion);
                    }
                });
    }

    /**插入题目
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param list 题目集合
     * @param isCover  是否是覆盖更新
     */
    public static void insertQuestion(String code, int mainType, String otherId, List<QuestionBean> list, boolean isCover){
        List<Question2RealmBean> insertList = RealmGson.fromJson(RealmGson.toJson(list), new TypeToken<List<Question2RealmBean>>(){}.getType());
        for(Question2RealmBean question : insertList){
            if(question.getMainType() > 0){
                mainType = question.getMainType();
            }
            question.setKey(String.format("%s%s%s%s", code, mainType, otherId, question.getId()));
            question.setCode(code);
            question.setMainType(mainType);
            question.setReal_id(mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "");
            question.setChapter_id(mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
            question.setPaper_id(mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "");
        }

        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        //是否是覆盖更新
        if(isCover) {
            //查出与课程相关的题目，将题目全部删除
            deleteTopicQuestion(realm, code, mainType, otherId, Question2RealmBean.class);
            //查出与课程相关的答题记录，将答题记录全部删除
            deleteTopicQuestion(realm, code, mainType, otherId, AnswerRealmBean.class);
            //查出与课程相关的错题记录，将错题记录全部删除
            deleteTopicQuestion(realm, code, mainType, otherId, ErrorRealmBean.class);
            //查出与课程相关的收藏，将收藏全部删除
            deleteTopicQuestion(realm, code, mainType, otherId, CollRealmBean.class);

            //因为删除了某个题库的数据，导致收藏对应的题目不见了，需要设置题库版本号，在下一次进入错题或收藏时，触发获取服务器题目的机制
            updateMainUpdateTime(code, PracticeHelper.PRACTICE_ERROR, otherId, -1);
            updateMainUpdateTime(code, PracticeHelper.PRACTICE_COLL, otherId, -1);
        }

        realm.copyToRealmOrUpdate(insertList);
        realm.commitTransaction();
        close(realm);
    }

    public static void deleteQuestion(String code, List<UpdateQuestionBean.DeleteTopics> list){
        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        for(UpdateQuestionBean.DeleteTopics delete : list) {
            //查出与课程相关的题目，将题目全部删除
            deleteSingleQuestion(realm, code, delete, Question2RealmBean.class);
            //查出与课程相关的答题记录，将答题记录全部删除
            deleteSingleQuestion(realm, code, delete, AnswerRealmBean.class);
            //查出与课程相关的错题记录，将错题记录全部删除
            deleteSingleQuestion(realm, code, delete, ErrorRealmBean.class);
            //查出与课程相关的收藏，将收藏全部删除
            deleteSingleQuestion(realm, code, delete, CollRealmBean.class);

        }
        realm.commitTransaction();
        close(realm);
    }

    public static <E extends RealmObject> void deleteSingleQuestion(Realm realm, String code, UpdateQuestionBean.DeleteTopics delete, Class<E> cClass){
        String otherId = "";
        if(delete.getMainType() == PracticeHelper.PRACTICE_TRUE){
            otherId = delete.getValue();
        }else if(delete.getMainType() == PracticeHelper.PRACTICE_CHAPTER){
            otherId = delete.getValue();
        }else if(delete.getMainType() == PracticeHelper.PRACTICE_SIMU){
            otherId = delete.getValue();
        }

        RealmObject object = realm.where(cClass)
                .equalTo("code", code)
                .equalTo("mainType", delete.getMainType())
                .equalTo("real_id", delete.getMainType() == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", delete.getMainType() == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", delete.getMainType() == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .equalTo("key", String.format("%s%s%s%s", code, delete.getMainType(), otherId, delete.getId()))
                .findFirst();
        if(object != null) {
            object.deleteFromRealm();
        }
    }

    public static <E extends RealmObject> void deleteTopicQuestion(Realm realm, String code, int mainType, String otherId, Class<E> cClass){
        RealmResults<E> results = realm.where(cClass)
                .equalTo("code", code)
                .equalTo("mainType", mainType)
                .equalTo("real_id", mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .findAll();

        results.deleteAllFromRealm();
    }
    
    public static void deleteErrorOrCollQuestion(String code, boolean isColl){
        //插入数据之前，先删除所有已缓存的数据，方便同步后台数据 TODO 崩溃
        if(!isColl) {
            Realm realm = open();
            if(realm == null) return;
            RealmResults<ErrorRealmBean> results = realm.where(ErrorRealmBean.class)
                    .equalTo("code", code)
                    .findAll();
            realm.beginTransaction();
            if(results != null) {
                results.deleteAllFromRealm();
            }
            realm.commitTransaction();
            close(realm);

        }else{
            Realm realm = open();
            if(realm == null) return;
            RealmResults<CollRealmBean> results = realm.where(CollRealmBean.class)
                    .equalTo("code", code)
                    .findAll();
            realm.beginTransaction();
            if(results != null) {
                results.deleteAllFromRealm();
            }
            realm.commitTransaction();
            close(realm);

        }
    }

    /**插入错题或收藏题目
     * @param list 题目集合
     */
    public static void insertErrorOrCollQuestion(String code, List<QuestionBean> list, boolean isColl){
        for(QuestionBean question : list){
            Question2RealmBean insert = RealmGson.fromJson(RealmGson.toJson(question), Question2RealmBean.class);

            String otherId = "";
            if(question.getMainType() == PracticeHelper.PRACTICE_TRUE){
                otherId = question.getValue();
            }else if(question.getMainType() == PracticeHelper.PRACTICE_CHAPTER){
                otherId = question.getValue();
            }else if(question.getMainType() == PracticeHelper.PRACTICE_SIMU){
                otherId = question.getValue();
            }

            insert.setCode(code);

            insert.setKey(String.format("%s%s%s%s", insert.getCode(), question.getMainType(), otherId, question.getId()));
            insert.setReal_id(question.getMainType() == PracticeHelper.PRACTICE_TRUE ? otherId : "");
            insert.setChapter_id(question.getMainType() == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
            insert.setPaper_id(question.getMainType() == PracticeHelper.PRACTICE_SIMU ? otherId : "");

            Realm realm = open();
            if(realm == null) return;
            realm.beginTransaction();

            //不是收藏题库，则插入错题记录
            if(!isColl) {
                RealQuery.insertError(insert.getCode(), insert.getMainType(), otherId, insert.getTopic_type(), insert.getId());
            }
            //如果还有收藏状态，也要插入收藏记录
            if(question.getIsCollection() == 1){
                RealQuery.insertColl(insert.getCode(), insert.getMainType(), otherId, insert.getTopic_type(), insert.getId());
            }
            //插入题目
            realm.copyToRealmOrUpdate(insert);
            realm.commitTransaction();
            close(realm);
        }

    }

    /**查询某个题目
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param questionId 题目ID
     * @return 某个题目
     */
    public static QuestionBean querySingleQuestion(String code, int mainType, String otherId, int questionId){
        Realm realm = open();
        if(realm == null) return null;
        Question2RealmBean results = realm.where(Question2RealmBean.class)
                .equalTo("code", code)
                .equalTo("mainType", mainType)
                .equalTo("id", questionId)
                .equalTo("real_id", mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .findFirst();

        if(results == null) return null;

        Question2RealmBean copyResults = realm.copyFromRealm(results);
        QuestionBean question = RealmGson.fromJson(RealmGson.toJson(copyResults), QuestionBean.class);
        close(realm);
        return question;
    }

    /**查询章节训练列表
     * @param code 课程代码
     * @param list 章节列表
     */
    public static void insertChapterList(String code, List<ChapterBean.Chapter> list){
        Realm realm = open();
        if(realm == null) return;
        List<ChapterRealmBean> insertList = RealmGson.fromJson(RealmGson.toJson(list), new TypeToken<List<ChapterRealmBean>>(){}.getType());
        for(ChapterRealmBean chapter : insertList){
            chapter.setKey(String.format("%s%s", code, chapter.getId()));
            
            if(AppUtil.isEmpty(chapter.getSections())) continue;
            
            for(ChapterSectionsRealBean sections : chapter.getSections()){
                //查询是否缓存过这个题库，如果不查，那么将可能把已缓存的标志从1变成0，导致无网络状态下查不到这个已缓存的题库
                ChapterSectionsRealBean chapterSections = realm.where(ChapterSectionsRealBean.class)
                        .equalTo("key", String.format("%s%s", code, sections.getSection_id()))
                        .equalTo("isCache", 1)
                        .findFirst();

                sections.setKey(String.format("%s%s", code, sections.getSection_id()));
                sections.setIsCache(chapterSections == null ? 0 : 1);
            }
        }
        realm.beginTransaction();
        realm.copyToRealmOrUpdate(insertList);
        realm.commitTransaction();
        close(realm);
    }

    /**查询章节训练列表
     * @param code 课程代码
     * @param queryListener 异步查询回调
     */
    public static void queryChapterList(String code, QueryListener<List<ChapterBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<ChapterRealmBean> results = realm.where(ChapterRealmBean.class)
                        .equalTo("code", code)
                        .equalTo("sections.isCache", 1)
                        .findAllAsync();
        results.addChangeListener(new RealmQueryListener<ChapterRealmBean, List<ChapterBean>>(queryListener, realm) {
                            @Override
                            public void onAsyncChange(List<ChapterRealmBean> chapterRealBeans) {
                                Realm realm = open();
                                if(realm == null) {
                                    callBack(new ArrayList<>());
                                    return;
                                }

                                //复制一份，用于序列化
                                List<ChapterRealmBean> copyChapter = chapterRealBeans;
                                List<ChapterBean> result = RealmGson.fromJson(RealmGson.toJson(copyChapter), new TypeToken<List<ChapterBean>>(){}.getType());

                                close(realm);
                                callBack(result);
                            }
                        });

    }

    /**更新章节训练某个小节是否已缓存
     * @param code 课程代码
     * @param otherId 章节ID
     */
    public static void updateChapterCache(String code, String otherId){
        Realm realm = open();
        if(realm == null) return;
        ChapterSectionsRealBean chapterSections = realm.where(ChapterSectionsRealBean.class)
                .equalTo("code", code)
                .equalTo("section_id", otherId)
                .findFirst();

        if(chapterSections == null) return;

        realm.beginTransaction();
        chapterSections.setIsCache(1);
        realm.copyToRealmOrUpdate(chapterSections);
        realm.commitTransaction();
        close(realm);
    }

    /**缓存章节列表
     * @param code 课程代码
     * @param list 章节列表
     */
    public static void insertTruePaper(String code, List<TruePaperNewItemBean> list){
        Realm realm = open();
        if(realm == null) return;
        List<TruePaperRealmBean> insertList = new ArrayList<>();
        for(TruePaperNewItemBean truePaper : list){
            TruePaperRealmBean truePaperRealm = RealmGson.fromJson(RealmGson.toJson(truePaper), TruePaperRealmBean.class);
            insertList.add(truePaperRealm);

            //查询是否缓存过这个题库，如果不查，那么将可能把已缓存的标志从1变成0，导致无网络状态下查不到这个已缓存的题库
            TruePaperRealmBean truePaperHas = realm.where(TruePaperRealmBean.class)
                    .equalTo("key", String.format("%s%s", code, truePaper.getId()))
                    .equalTo("isCache", 1)
                    .findFirst();

            truePaperRealm.setKey(String.format("%s%s", code, truePaper.getId()));
            truePaperRealm.setPaper_id(truePaper.getCode());
            truePaperRealm.setCode(code);
            truePaperRealm.setIsCache(truePaperHas == null ? 0 : 1);
        }
        realm.beginTransaction();
        realm.copyToRealmOrUpdate(insertList);
        realm.commitTransaction();
        close(realm);
    }

    /**查询真题列表
     * @param code 课程代码
     * @param queryListener 查询回调
     */
    public static void queryTruePaper(String code, QueryListener<List<TruePaperNewItemBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<TruePaperRealmBean> results = realm.where(TruePaperRealmBean.class)
                .equalTo("code", code)
                .equalTo("isCache", 1)
                .findAllAsync();

        results.addChangeListener(new RealmQueryListener<TruePaperRealmBean, List<TruePaperNewItemBean>>(queryListener, realm) {
                    @Override
                    public void onAsyncChange(List<TruePaperRealmBean> truePaperRealmBeans) {
                        Realm realm = open();
                        if(realm == null) {
                            callBack(new ArrayList<>());
                            return;
                        }

                        //复制一份，用于序列化
                        List<TruePaperNewItemBean> result = RealmGson.fromJson(RealmGson.toJson(truePaperRealmBeans), new TypeToken<List<TruePaperNewItemBean>>(){}.getType());

                        close(realm);
                        callBack(result);
                    }
                });
    }

    /**更新某套真题是否已缓存
     * @param code 课程代码
     * @param otherId 真题ID
     */
    public static void updateTruePaperCache(String code, String otherId){
        Realm realm = open();
        if(realm == null) return;
        TruePaperRealmBean truePaper = realm.where(TruePaperRealmBean.class)
                .equalTo("key", String.format("%s%s", code, otherId))
                .findFirst();

        if(truePaper == null) return;

        realm.beginTransaction();
        truePaper.setIsCache(1);
        realm.copyToRealmOrUpdate(truePaper);
        realm.commitTransaction();
        close(realm);
    }

    /**缓存模拟试卷列表
     * @param code 课程代码
     * @param list 章节列表
     */
    public static void insertSimu(String code, List<TestPaperListBean> list){
        Realm realm = open();
        if(realm == null) return;
        List<SumiRealmBean> insertList = RealmGson.fromJson(RealmGson.toJson(list), new TypeToken<List<SumiRealmBean>>(){}.getType());
        for(SumiRealmBean sumi : insertList){
            //查询是否缓存过这个题库，如果不查，那么将可能把已缓存的标志从1变成0，导致无网络状态下查不到这个已缓存的题库
            SumiRealmBean sumiHas = realm.where(SumiRealmBean.class)
                    .equalTo("key", String.format("%s%s", code, sumi.getCode()))
                    .equalTo("isCache", 1)
                    .findFirst();

            sumi.setKey(String.format("%s%s", code, sumi.getCode()));
            sumi.setCourse_code(code);
            sumi.setIsCache(sumiHas == null ? 0 : 1);
        }
        realm.beginTransaction();
        realm.copyToRealmOrUpdate(insertList);
        realm.commitTransaction();
        close(realm);
    }

    /**更新某套模拟试卷是否已缓存
     * @param code 课程代码
     * @param otherId 真题ID
     */
    public static void updateSimuCache(String code, String otherId){
        Realm realm = open();
        if(realm == null) return;
        SumiRealmBean result = realm.where(SumiRealmBean.class)
                .equalTo("key", String.format("%s%s", code, otherId))
                .findFirst();

        if(result == null) return;

        realm.beginTransaction();
        result.setIsCache(1);
        realm.insertOrUpdate(result);
        realm.commitTransaction();

        close(realm);
    }

    /**查询模拟试卷列表
     * @param code 课程代码
     * @param queryListener 查询回调
     */
    public static void querySumi(String code, QueryListener<List<TestPaperListBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<SumiRealmBean> results = realm.where(SumiRealmBean.class)
                .equalTo("course_code", code)
                .equalTo("isCache", 1)
                .findAllAsync();

        results.addChangeListener(new RealmQueryListener<SumiRealmBean, List<TestPaperListBean>>(queryListener, realm) {
            @Override
            public void onAsyncChange(List<SumiRealmBean> sumiRealmBeans) {
                Realm realm = open();
                if(realm == null) {
                    callBack(new ArrayList<>());
                    return;
                }
                //复制一份，用于序列化
                List<TestPaperListBean> result = RealmGson.fromJson(RealmGson.toJson(sumiRealmBeans), new TypeToken<List<TestPaperListBean>>(){}.getType());

                close(realm);
                callBack(result);
            }
        });
    }


    /**插入收藏
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param questionId 收藏ID
     */
    public static void insertColl(String code, int mainType, String otherId, int topic_type, int questionId){
        Realm realm = open();
        if(realm == null) return;
        //本方法内没有开启事务，就需要先开启事务
        boolean isInTransaction = false;
        if(!realm.isInTransaction()) {
            realm.beginTransaction();
            isInTransaction = true;
        }
        CollRealmBean coll = new CollRealmBean();
        coll.setKey(String.format("%s%s%s%s", code, mainType, otherId, questionId));
        coll.setCode(code);
        coll.setMainType(mainType);
        coll.setTopic_type(topic_type);
        coll.setReal_id(mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "");
        coll.setChapter_id(mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
        coll.setPaper_id(mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "");
        coll.setQuestionId(questionId);
        coll.setIsCollection(1);
        coll.setUpdate_time(System.currentTimeMillis() / 1000);
        realm.copyToRealmOrUpdate(coll);
        //本方法内开启了事务，就需要关闭事务
        if(isInTransaction){
            realm.commitTransaction();
        }
        close(realm);
    }

    /**查询收藏数量
     * @param code 课程代码
     * @param queryListener 回调对象
     */
    public static void queryCollCount(String code, QueryListener<Integer> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<CollRealmBean> results = realm.where(CollRealmBean.class)
                .equalTo("code", code)
                .sort("topic_type", Sort.ASCENDING)
                .findAllAsync();
        results.addChangeListener(new RealmQueryListener<CollRealmBean, Integer>(queryListener, realm) {
            @Override
            public void onAsyncChange(List<CollRealmBean> form) {
                if(!AppUtil.isEmpty(form)) {
                    callBack(form.size());
                }else {
                    callBack(0);
                }
            }
        });
    }
    /**移除收藏
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param questionId 收藏ID
     */
    public static void removeColl(String code, int mainType, String otherId, int questionId){
        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        CollRealmBean coll = realm.where(CollRealmBean.class)
                .equalTo("key", String.format("%s%s%s%s", code, mainType, otherId, questionId))
                .findFirst();
        if(coll != null) {
            coll.deleteFromRealm();
        }
        realm.commitTransaction();
        close(realm);
    }

    /**查询所有收藏
     * @param code 课程代码
     * @param queryListener 查询回调监听
     */
    public static void queryCollCard(String code, QueryTowListener<List<CardBean.Topic>, List<QuestionBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<CollRealmBean> results = realm.where(CollRealmBean.class)
                .equalTo("code", code)
                .sort("topic_type", Sort.ASCENDING)
                .findAllAsync();
        results.addChangeListener(new RealmTowQueryListener<CollRealmBean, List<CardBean.Topic>, List<QuestionBean>>(queryListener, realm) {
            @Override
            public void onAsyncChange(List<CollRealmBean> collRealmBeans) {
                Realm realm = open();
                if(realm == null) {
                    callBack(new ArrayList<>(), new ArrayList<>());
                    return;
                }

                List<CardBean.Topic> resultCard = new ArrayList<>();
                List<QuestionBean> resultQuestion = new ArrayList<>();
                for(CollRealmBean coll : collRealmBeans) {

                    String otherId = "";
                    if(!AppUtil.isEmpty(coll.getReal_id())){
                        otherId = coll.getReal_id();
                    }else if(!AppUtil.isEmpty(coll.getChapter_id())){
                        otherId = coll.getChapter_id();
                    }else if(!AppUtil.isEmpty(coll.getPaper_id())){
                        otherId = coll.getPaper_id();
                    }

                    Question2RealmBean question = realm.where(Question2RealmBean.class)
                            .equalTo("key", String.format("%s%s%s%s", coll.getCode(), coll.getMainType(), otherId, coll.getQuestionId()))
                            .findFirst();

                    if(question != null){
                        Question2RealmBean copyQuestion = realm.copyFromRealm(question);

                        CardBean.Topic topic = RealmGson.fromJson(RealmGson.toJson(copyQuestion), CardBean.Topic.class);
                        topic.setValue(otherId);
                        resultCard.add(topic);

                        QuestionBean questionBean = RealmGson.fromJson(RealmGson.toJson(copyQuestion), QuestionBean.class);
                        questionBean.setValue(otherId);
                        questionBean.setIsCollection(1);
                        resultQuestion.add(questionBean);
                    }else {
                        CollRealmBean deleteColl = realm.where(CollRealmBean.class)
                                .equalTo("key", coll.getKey())
                                .findFirst();

                        if(deleteColl == null) continue;

                        realm.beginTransaction();
                        //收藏找不到对应的题目，则从收藏表删除
                        deleteColl.deleteFromRealm();
                        realm.commitTransaction();
                    }
                }

                close(realm);
                callBack(resultCard, resultQuestion);
            }
        });
    }

    /**插入错题
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param questionId 错题ID
     */
    public static void insertError(String code, int mainType, String otherId, int topic_type, int questionId){
        Realm realm = open();
        if(realm == null) return;
        //本方法内没有开启事务，就需要先开启事务
        boolean isInTransaction = false;
        if(!realm.isInTransaction()){
            realm.beginTransaction();
            isInTransaction = true;
        }
        ErrorRealmBean error = new ErrorRealmBean();
        error.setKey(String.format("%s%s%s%s", code, mainType, otherId, questionId));
        error.setCode(code);
        error.setMainType(mainType);
        error.setTopic_type(topic_type);
        error.setReal_id(mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "");
        error.setChapter_id(mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
        error.setPaper_id(mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "");
        error.setQuestionId(questionId);
        error.setIsError(1);
        error.setUpdate_time(System.currentTimeMillis() / 1000);
        realm.copyToRealmOrUpdate(error);
        //本方法内开启了事务，就需要关闭事务
        if(isInTransaction){
            realm.commitTransaction();
        }
        close(realm);
    }

    /**查询科目下的所有错题
     * @param code 课程代码
     * @param queryListener 异步查询回调
     */
    public static void queryErrorCard(String code, QueryTowListener<List<CardBean.Topic>, List<QuestionBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<ErrorRealmBean> results = realm.where(ErrorRealmBean.class)
                .equalTo("code", code)
                .sort("topic_type", Sort.ASCENDING)
                .findAllAsync();
        results.addChangeListener(new RealmTowQueryListener<ErrorRealmBean,  List<CardBean.Topic>, List<QuestionBean>>(queryListener, realm) {
                    @Override
                    public void onAsyncChange(List<ErrorRealmBean> errorRealmBeans) {
                        Realm realm = open();
                        if(realm == null) return;

                        List<CardBean.Topic> resultCard = new ArrayList<>();
                        List<QuestionBean> resultQuestion = new ArrayList<>();
                        for(ErrorRealmBean error : errorRealmBeans) {
                            String otherId = "";
                            if(!AppUtil.isEmpty(error.getReal_id())){
                                otherId = error.getReal_id();
                            }else if(!AppUtil.isEmpty(error.getChapter_id())){
                                otherId = error.getChapter_id();
                            }else if(!AppUtil.isEmpty(error.getPaper_id())){
                                otherId = error.getPaper_id();
                            }

                            Question2RealmBean realmQuestion = realm.where(Question2RealmBean.class)
                                    .equalTo("key", String.format("%s%s%s%s", error.getCode(), error.getMainType(), otherId, error.getQuestionId()))
                                    .findFirst();

                            if(realmQuestion != null){
                                Question2RealmBean copyQuestion = realm.copyFromRealm(realmQuestion);

                                CardBean.Topic topic = RealmGson.fromJson(RealmGson.toJson(copyQuestion), CardBean.Topic.class);
                                topic.setValue(otherId);
                                resultCard.add(topic);

                                QuestionBean question = RealmGson.fromJson(RealmGson.toJson(copyQuestion), QuestionBean.class);
                                question.setValue(otherId);
                                resultQuestion.add(question);
                            }else {
                                ErrorRealmBean deleteError = realm.where(ErrorRealmBean.class)
                                        .equalTo("key", error.getKey())
                                        .findFirst();

                                if(deleteError == null) continue;

                                realm.beginTransaction();
                                //错题找不到对应的题目，则从错题表删除
                                deleteError.deleteFromRealm();
                                realm.commitTransaction();
                            }
                        }

                        close(realm);
                        callBack(resultCard, resultQuestion);
                    }
                });
    }


    /**查询错题数量
     * @param code 课程代码
     * @param queryListener 回调对象
     */
    public static void queryErrorCount(String code, QueryListener<Integer> queryListener){
        Realm realm = open();
        if(realm == null) return;
        RealmResults<ErrorRealmBean> results = realm.where(ErrorRealmBean.class)
                .equalTo("code", code)
                .sort("topic_type", Sort.ASCENDING)
                .findAllAsync();

        results.addChangeListener(new RealmQueryListener<ErrorRealmBean, Integer>(queryListener, realm) {

            @Override
            public void onAsyncChange(List<ErrorRealmBean> form) {
                if(!AppUtil.isEmpty(form)) {
                    callBack(form.size());
                }else {
                    callBack(0);
                }
            }
        });
    }

    /**移除错题
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param questionId 错题ID
     */
    public static void removeError(String code, int mainType, String otherId, int questionId){
        Realm realm = open();
        if(realm == null) return;
        LogUtil.d("dddd", code+"  "+mainType+"  "+otherId+"  "+questionId);
        realm.beginTransaction();
        ErrorRealmBean error = realm.where(ErrorRealmBean.class)
                .equalTo("key", String.format("%s%s%s%s", code, mainType, otherId, questionId))
                .findFirst();
        if(error != null) {
            error.deleteFromRealm();
        }
        realm.commitTransaction();
        close(realm);
    }

    /**插入答题记录
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @param uploadData 已答题目
     */
    public static void insertQuestionHistory(String code, int mainType, String otherId, int last_id, boolean isCommit, List<PracticeRecordBean.RecordBean> uploadData){
        LogUtil.d("dddd", "插入题库");
        LogUtil.d("dddd", uploadData);

        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();

        //保存最后一次的答题ID
        insertLastId(code, mainType, otherId, last_id);

        List<AnswerRealmBean> insertList = new ArrayList<>();
        for(PracticeRecordBean.RecordBean record : uploadData){
            //查出某题的答题记录
            AnswerRealmBean answer = realm.where(AnswerRealmBean.class)
                    .equalTo("key", String.format("%s%s%s%s", code, mainType, otherId, record.getTopic_id()))
                    .findFirst();

            //如果答题记录不存在，则创建一条
            if(answer == null){
                answer = new AnswerRealmBean();
                answer.setKey(String.format("%s%s%s%s", code, mainType, otherId, record.getTopic_id()));
                answer.setCode(code);
                answer.setMainType(mainType);
                answer.setTopic_type(record.getTopic_type());
                answer.setReal_id(mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "");
                answer.setChapter_id(mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "");
                answer.setPaper_id(mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "");
                answer.setQuestionId(record.getTopic_id());
            }
            //是否已提交到服务器
            answer.setCommit(isCommit);
            //设置标准答案
            answer.setCorrectOption(record.getCorrect_answer());
            //设置用户所答答案
            answer.setSelect_answer(record.getSelect_answer());
            //答题状态
            answer.setIs_correct(record.getIs_correct());
            //更新时间
            answer.setUpdate_time(System.currentTimeMillis() / 1000 + (insertList.indexOf(answer)));//加上下标，防止同一毫秒内更新多条值
            //插入待插入集合
            insertList.add(answer);

            //错题插入错题表
            if(record.getIs_correct() == 2){
                insertError(code, mainType, otherId, record.getTopic_type(), record.getTopic_id());
            }
            //插入收藏表
            if(record.getIsCollection() == 1){
                insertColl(code, mainType, otherId, record.getTopic_type(), record.getTopic_id());
            }
        }
        realm.copyToRealmOrUpdate(insertList);
        realm.commitTransaction();
        close(realm);
    }

    /**获取未上传到服务器的答题记录
     *
     */
    public static void queryUnCommitHistory(String code, int mainType, String otherId, QueryListener<List<PracticeRecordBean.RecordBean>> queryListener){
        Realm realm = open();
        if(realm == null) return;

        RealmResults<AnswerRealmBean> results = realm.where(AnswerRealmBean.class)
                .equalTo("code", code)
                .equalTo("mainType", mainType)
                .equalTo("real_id", mainType == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", mainType == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", mainType == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .equalTo("isCommit", false)
                .findAllAsync();
        results.addChangeListener(new RealmQueryListener<AnswerRealmBean, List<PracticeRecordBean.RecordBean>>(queryListener, realm) {
            @Override
            public void onAsyncChange(List<AnswerRealmBean> form) {
                List<PracticeRecordBean.RecordBean> list = new ArrayList<>();
                for(AnswerRealmBean answer : form){
                    String otherId = "";
                    if(answer.getMainType() == PracticeHelper.PRACTICE_TRUE){
                        otherId = answer.getReal_id();
                    }else if(answer.getMainType() == PracticeHelper.PRACTICE_CHAPTER){
                        otherId = answer.getChapter_id();
                    }else if(answer.getMainType() == PracticeHelper.PRACTICE_SIMU){
                        otherId = answer.getPaper_id();
                    }
                    QuestionBean questionBean = querySingleQuestion(answer.getCode(), answer.getMainType(), otherId, answer.getQuestionId());
                    if(questionBean == null) continue;

                    PracticeRecordBean.RecordBean recordBean = new PracticeRecordBean.RecordBean(answer.getQuestionId(), answer.getTopic_type(), answer.getIs_correct(), answer.getSelect_answer(),questionBean.getCorrectOption(),  answer.getMainType());
                    list.add(recordBean);
                }
                callBack(list);
            }
        });

    }

    /**清除答题记录
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     */
    public static void clearQuestionHistory(String code, int mainType, String otherId){
        LogUtil.d("dddd", code+" "+mainType+" "+otherId);
        Realm realm = open();
        if(realm == null) return;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);
        if(main != null) {
            realm.beginTransaction();
            main.setClear_time(System.currentTimeMillis() / 1000);
            realm.copyToRealmOrUpdate(main);
            realm.commitTransaction();
            close(realm);
        }
    }

    /**查询最后一次答题的题目ID
     * @param code 课程代码
     * @param mainType 题库类型
     * @param otherId 真题ID/章节ID/试卷ID
     * @return 题目ID
     */
    public static int queryLastId(String code, int mainType, String otherId){
        //收藏的mainType为-1，错题的mainType为-2
        if(mainType < 0) return 0;

        Realm realm = open();
        if(realm == null) return 0;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);

        if(main == null) return 0;

        int questionId = main.getLast_id();
        close(realm);
        return questionId;
    }

    public static void insertLastId(String code, int mainType, String otherId, int last_id){
        //收藏的mainType为-1，错题的mainType为-2
        if(mainType < 0) return;

        Realm realm = open();
        if(realm == null) return;
        MainRealmBean main = queryMain(realm, code, mainType, otherId);

        if(main == null) return;

        //本方法内没有开启事务，就需要先开启事务
        boolean isInTransaction = false;
        if(!realm.isInTransaction()) {
            realm.beginTransaction();
            isInTransaction = true;
        }
        main.setLast_id(last_id);
        realm.insertOrUpdate(main);
        //本方法内开启了事务，就需要关闭事务
        if(isInTransaction) {
            realm.commitTransaction();
        }
    }

    /**查询有缓存的课程
     * @return 缓存的课程
     */
    public static List<QuestionCacheBean> queryCacheCourse(String code){
        Realm realm = open();
        if(realm == null) return new ArrayList<>();
        RealmResults<MainRealmBean> results;
        if(AppUtil.isEmpty(code)) {
            results = realm.where(MainRealmBean.class)
                    .greaterThan("update_time", -1)
                    .findAll();
        }else {
            results = realm.where(MainRealmBean.class)
                    .equalTo("code", code)
                    .greaterThan("update_time", -1)
                    .findAll();
        }

        List<QuestionCacheBean> list = new ArrayList<>();
        List<String> hasAddList = new ArrayList<>();
        for(MainRealmBean main : results){
            //查找题库所属的课程
            CourseItemRealmBean course = realm.where(CourseItemRealmBean.class)
                    .equalTo("code", main.getCode())
                    .findFirst();

            if(AppUtil.isEmpty(code)) {
                if (course == null)  continue;

                //去重复课程
                if (!hasAddList.contains(course.getCode())) {
                    MainRealmBean copyMain = realm.copyFromRealm(main);
                    QuestionCacheBean cacheBean = RealmGson.fromJson(RealmGson.toJson(copyMain), QuestionCacheBean.class);
                    cacheBean.setName(course.getName());
                    cacheBean.setMainType(-1000);//课程这一级没有题库类型，所以设置-1000避免冲突
                    cacheBean.setTopic(false);
                    list.add(cacheBean);
                    hasAddList.add(cacheBean.getCode());
                }
            }else {
                String mainTypeName = PracticeHelper.getPracticeType(main.getMainType());
                boolean isTopic = main.getMainType() == PracticeHelper.PRACTICE_COURSE
                        || main.getMainType() == PracticeHelper.PRACTICE_VIP
                        || main.getMainType() == PracticeHelper.PRACTICE_TYPE_HIGH
                        || main.getMainType() == PracticeHelper.PRACTICE_ERROR
                        || main.getMainType() == PracticeHelper.PRACTICE_COLL;
                if (!hasAddList.contains(mainTypeName)) {
                    MainRealmBean copyMain = realm.copyFromRealm(main);
                    QuestionCacheBean cacheBean = RealmGson.fromJson(RealmGson.toJson(copyMain), QuestionCacheBean.class);
                    cacheBean.setName(course.getName());
                    cacheBean.setMainTypeName(mainTypeName);
                    cacheBean.setTopic(isTopic);
                    list.add(cacheBean);
                    hasAddList.add(mainTypeName);
                }
            }
        }
        close(realm);
        return list;
    }

    /**
     * 查询课程下的所有真题
     */
    public static List<QuestionCacheBean> queryCacheTruePaper(String code){
        Realm realm = open();
        if(realm == null) return new ArrayList<>();
        RealmResults<TruePaperRealmBean> results = realm.where(TruePaperRealmBean.class)
                .equalTo("code", code)
                .findAll();

        //查找题库所属的课程
        CourseItemRealmBean course = realm.where(CourseItemRealmBean.class)
                .equalTo("code", code)
                .findFirst();

        List<QuestionCacheBean> list = new ArrayList<>();
        for(TruePaperRealmBean main : results){
            QuestionCacheBean cacheBean = new QuestionCacheBean();
            cacheBean.setCode(main.getCode());
            cacheBean.setReal_id(main.getId()+"");
            cacheBean.setName(course.getName());
            cacheBean.setTpoic_name(main.getName());
            cacheBean.setMainTypeName(PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_TRUE));
            cacheBean.setMainType(PracticeHelper.PRACTICE_TRUE);
            cacheBean.setTopic(true);
            list.add(cacheBean);
        }
        close(realm);
        return list;
    }


    /**
     * 查询课程下的某章节训练的小节
     */
    public static List<QuestionCacheBean> queryCacheChapter(String code){
        Realm realm = open();
        if(realm == null) return new ArrayList<>();
        RealmResults<ChapterRealmBean> results = realm.where(ChapterRealmBean.class)
                .equalTo("code", code)
                .equalTo("sections.isCache", 1)
                .findAll();

        //查找题库所属的课程
        CourseItemRealmBean course = realm.where(CourseItemRealmBean.class)
                .equalTo("code", code)
                .findFirst();

        List<QuestionCacheBean> list = new ArrayList<>();
        for(ChapterRealmBean main : results){
            QuestionCacheBean cacheBean = new QuestionCacheBean();
            cacheBean.setCode(main.getCode());
            cacheBean.setChapter_id(main.getId() + "");
            cacheBean.setName(course.getName());
            cacheBean.setTpoic_name(main.getName());
            cacheBean.setMainTypeName(PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_CHAPTER));
            cacheBean.setMainType(PracticeHelper.PRACTICE_CHAPTER);
            cacheBean.setTopic(false);

            if(!AppUtil.isEmpty(main.getSections())) {
                cacheBean.setSections(new ArrayList<>());
                for (ChapterSectionsRealBean sections : main.getSections()) {
                    QuestionCacheBean cacheSectionsBean = new QuestionCacheBean();
                    cacheSectionsBean.setCode(sections.getCode());
                    cacheSectionsBean.setName(course.getName());
                    cacheSectionsBean.setChapter_sections_id(sections.getSection_id());
                    cacheSectionsBean.setTpoic_name(sections.getName());
                    cacheSectionsBean.setMainTypeName(PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_CHAPTER));
                    cacheSectionsBean.setMainType(PracticeHelper.PRACTICE_CHAPTER);
                    cacheSectionsBean.setTopic(true);
                    cacheBean.getSections().add(cacheSectionsBean);
                }
            }
            list.add(cacheBean);
        }
        close(realm);
        return list;
    }
    
    /**
     * 查询课程下的所有真题
     */
    public static List<QuestionCacheBean> queryCacheSimu(String code){
        Realm realm = open();
        if(realm == null) return new ArrayList<>();
        RealmResults<SumiRealmBean> results = realm.where(SumiRealmBean.class)
                .equalTo("course_code", code)
                .findAll();

        //查找题库所属的课程
        CourseItemRealmBean course = realm.where(CourseItemRealmBean.class)
                .equalTo("code", code)
                .findFirst();

        List<QuestionCacheBean> list = new ArrayList<>();
        for(SumiRealmBean main : results){
            QuestionCacheBean cacheBean = new QuestionCacheBean();
            cacheBean.setCode(main.getCourse_code());
            cacheBean.setPaper_id(main.getCode());
            cacheBean.setName(course.getName());
            cacheBean.setTpoic_name(main.getName());
            cacheBean.setMainTypeName(PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_SIMU));
            cacheBean.setMainType(PracticeHelper.PRACTICE_SIMU);
            cacheBean.setTopic(true);
            list.add(cacheBean);
        }
        close(realm);
        return list;
    }

    /**删除缓存的题库
     * @param deleteCode 课程代码集合
     */
    public static void deleteCacheCourse(List<String> deleteCode){
        new Thread(){
            @Override
            public void run() {
                super.run();
                Realm realm = open();
                if(realm == null) return;
                for(String code : deleteCode) {
                    //查出与课程相关的题库，将题库的更新时间设置为-1
                    RealmResults<MainRealmBean> mainResults = realm.where(MainRealmBean.class)
                            .equalTo("code", code)
                            .greaterThanOrEqualTo("update_time", -1)
                            .findAll();
                    realm.beginTransaction();
                    for (MainRealmBean main : mainResults) {
                        main.setLast_id(0);
                        main.setUpdate_time(-1);
                    }
                    realm.commitTransaction();

                    realm.beginTransaction();
                    //查出与课程相关的题目，将题目全部删除
                    deleteSingleCourse(realm, code, MainRealmBean.class);
                    //查出与课程相关的题目，将题目全部删除
                    deleteSingleCourse(realm, code, Question2RealmBean.class);
                    //查出与课程相关的答题记录，将答题记录全部删除
                    deleteSingleCourse(realm, code, AnswerRealmBean.class);
                    //查出与课程相关的错题记录，将错题记录全部删除
                    deleteSingleCourse(realm, code, ErrorRealmBean.class);
                    //查出与课程相关的收藏，将收藏全部删除
                    deleteSingleCourse(realm, code, CollRealmBean.class);
                    //查出与课程相关的章节列表，将章节列表全部删除
                    deleteSingleCourse(realm, code, ChapterRealmBean.class);
                    //查出与课程相关的历年真题列表，将历年真题列表全部删除
                    deleteSingleCourse(realm, code, TruePaperRealmBean.class);
                    //查出与课程相关的历年真题列表，将历年真题列表全部删除
                    deleteSingleCourse(realm, code, SumiRealmBean.class);

                    realm.commitTransaction();
                    realm.refresh();
                }
                close(realm);
            }
        }.start();
    }

    /**删除单个题库
     * @param realm 数据库实例
     * @param code 课程代码
     * @param cClass 类
     * @param <E> 题库表
     */
    private static <E extends RealmObject> void deleteSingleCourse(Realm realm, String code, Class<E> cClass){
        realm.where(cClass)
                .equalTo("code", code)
                .findAll()
                .deleteAllFromRealm();
    }

    /**删除所有失效的题库
     *
     */
    public static void deleteUnExpireTime(){
        new Thread(){
            @Override
            public void run() {
                super.run();
                Realm realm = open();
                if(realm == null) return;

                //查出与课程相关的题库，将题库的更新时间设置为0
                realm.beginTransaction();
                RealmResults<MainRealmBean> mainResults = realm.where(MainRealmBean.class)
                        .lessThan("expire_time", System.currentTimeMillis() / 1000)
                        .findAll();

                for (MainRealmBean main : mainResults) {
                    main.setLast_id(0);
                    main.setUpdate_time(-1);

                    //查出与课程相关的题目，将题目全部删除
                    deleteSingleUnExpireTime(realm, main, Question2RealmBean.class);
                    //查出与课程相关的答题记录，将答题记录全部删除
                    deleteSingleUnExpireTime(realm, main, AnswerRealmBean.class);
                    //查出与课程相关的错题记录，将错题记录全部删除
                    deleteSingleUnExpireTime(realm, main, ErrorRealmBean.class);
                    //查出与课程相关的收藏，将收藏全部删除
                    deleteSingleUnExpireTime(realm, main, CollRealmBean.class);
                }

                realm.commitTransaction();
                close(realm);
            }
        }.start();
    }

    /**删除单个题库
     * @param realm 数据库实例
     * @param main 题库
     * @param cClass 类
     * @param <E> 题库表
     */
    private static <E extends RealmObject> void deleteSingleUnExpireTime(Realm realm, MainRealmBean main, Class<E> cClass){
        String otherId = "";
        if(!AppUtil.isEmpty(main.getReal_id())){
            otherId = main.getReal_id();
        }else if(!AppUtil.isEmpty(main.getChapter_id())){
            otherId = main.getChapter_id();
        }else if(!AppUtil.isEmpty(main.getPaper_id())){
            otherId = main.getPaper_id();
        }

        realm.where(cClass)
                .equalTo("code", main.getCode())
                .equalTo("mainType", main.getMainType())
                .equalTo("real_id", main.getMainType() == PracticeHelper.PRACTICE_TRUE ? otherId : "")
                .equalTo("chapter_id", main.getMainType() == PracticeHelper.PRACTICE_CHAPTER ? otherId : "")
                .equalTo("paper_id", main.getMainType() == PracticeHelper.PRACTICE_SIMU ? otherId : "")
                .findAll()
                .deleteAllFromRealm();
    }

    /**清除数据库的所有表
     *
     */
    public static void clear(){
        LogUtil.d("dddd", "清空数据库");
        Realm realm = open();
        if(realm == null) return;
        realm.beginTransaction();
        realm.deleteAll();
        realm.commitTransaction();
        realm.refresh();
        close(realm);
        Realm.compactRealm(getConfiguration(AppManager.getAppManager().getTopActivity()));
    }
}
