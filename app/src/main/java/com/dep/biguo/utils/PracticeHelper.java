package com.dep.biguo.utils;

import android.text.TextUtils;
import android.util.TypedValue;
import android.widget.TextView;

import com.dep.biguo.bean.CardBean;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/6
 * @Description:
 */
public class PracticeHelper {

    public static final int TYPE_NONE = 0; //不分题型
    public static final int TYPE_SINGLE = 1; //单选题
    public static final int TYPE_MULTI = 2; //多选题
    public static final int TYPE_JUDGE = 3; //判断题
    public static final int TYPE_ASK = 4; //问答题
    public static final int TYPE_FILL = 5; //填空题
    public static final int TYPE_COMPLETE_FILL = 6; //完形填空
    public static final int TYPE_READ_COMPREHENSION = 7; //阅读理解
    public static final int TYPE_NOUN_EXPLAIN = 8; //名词解释
    public static final int TYPE_WORD_PRACTICE = 9; //单词练习
    public static final int TYPE_SIMPLE_ANSWER = 10; //简答题
    public static final int TYPE_COMPUTER = 11; //计算题
    public static final int TYPE_DISCUSS = 12; //论述题
    public static final int TYPE_EXAMPLE_ANALYSE = 13; //案例分析题
    public static final int TYPE_OVERALL_APPLICATION_QUESTIONS = 14; //综合应用题
    public static final int TYPE_COMPUTER_ANALYSE = 15; //计算分析题
    public static final int TYPE_MATERIAL = 16; //材料题
    public static final int TYPE_PROVE = 17; //证明题
    public static final int TYPE_WORDS_EXPLAIN = 18; //词语解释题
    public static final int TYPE_APPLICATION_QUESTIONS = 19; //应用题
    public static final int TYPE_OVERALL = 20; //综合题
    public static final int TYPE_CHINESE_TO_ENGLISH = 21; //英译汉
    public static final int TYPE_ENGLISH_TO_CHINESE = 22; //汉译英
    public static final int TYPE_COMPOSITION = 23; //作文题
    public static final int TYPE_ACCOUNTING = 24; //会计分录

    //题目类型
    public static final int PRACTICE_COURSE = 1; //课程训练
    public static final int PRACTICE_TRUE = 2; //真题
    public static final int PRACTICE_SIMU = 3; //模拟
    public static final int PRACTICE_VIP = 4; //VIP题库
    public static final int PRACTICE_CHAPTER = 5; //章节
    public static final int PRACTICE_DAYCARD = 6; //每日打卡
    public static final int PRACTICE_SECRET = 7; //考前押密
    public static final int PRACTICE_VIDEO = 8; //视频课程
    public static final int PRACTICE_TYPE_HIGH = 11; // 高频考点
    public static final int PRACTICE_INTERNET = 12; // 网络助学
    public static final int PRACTICE_ERROR = -1; //错题
    public static final int PRACTICE_COLL = -2; //收藏
    public static final int PRACTICE_SEARCH = -3; //搜题   只有背题模式

    public static final int MODE_DEFAULT = 1; //默认模式 答题模式
    public static final int MODE_SHOW = 2; //背题模式 答题模式
    public static final int MODE_SIMU = 3; //模拟测试 答题模式
    public static final int MODE_ALL = 4; //查看错题解析 答题模式

    public static final int RECORD_OPTION = 1; //可作答题型 每30题保存一次答题记录
    public static final int RECORD_UNDO = 2; //不可作答题型 保存当前题目位置的答题记录
    public static final int RECORD_COMMIT = 3; //模拟模式 只在交卷时保存答题记录

    //能作答的题型
    private static int[] TOPIC_TYPE_DO = {TYPE_SINGLE, TYPE_MULTI, TYPE_JUDGE, TYPE_NONE};

    public static String getPracticeType(int practiceType) {
        switch (practiceType) {
            case PRACTICE_COURSE:
                return "免费题库";
            case PRACTICE_TRUE:
                return "历年真题";
            case PRACTICE_SIMU:
                return "模拟试题";
            case PRACTICE_VIP:
                return "VIP题库";
            case PRACTICE_CHAPTER:
                return "章节训练";
            case PRACTICE_ERROR:
                return "我的错题";
            case PRACTICE_COLL:
                return "我的收藏";
            case PRACTICE_SEARCH:
                return "搜题练习";
            case PRACTICE_DAYCARD:
                return "每日打卡";
            case PRACTICE_SECRET:
                return "考前押密";
            case PRACTICE_TYPE_HIGH:
                return "高频考点";
            default:
                return "";
        }
    }

    public static String getTopicType(int type) {
        //因为题型有顺序，所以可以用数组循环，虽然这样很浪费内存，但是代码能简短很多
        //第0个元素是为了TYPE_NONE类型准备的
        String[] topicType = {"","单选题", "多选题", "判断题", "问答题", "填空题", "完形填空", "阅读理解", "名词解释","单词练习", "简答题",
                "计算题", "论述题", "案例分析题", "综合应用题", "计算分析题", "材料题", "证明题", "词语解释题", "应用题", "综合题",
                "英译汉", "汉译英", "作文题", "会计分录"};
        if(type < 0 || type >= topicType.length){
            return "";
        }else {
            return topicType[type];
        }
    }

    /**
     * 是否支持选择模式
     * <p>
     * 除了每日打卡，搜题之外的做题类型
     * 除了单选、多选、判断、高频，其他都不支持
     *
     * @return
     */
    public static boolean isSupportChoiceMode(int practiceType, int topicType) {
        if (practiceType == PRACTICE_DAYCARD || practiceType == PRACTICE_SEARCH)
            return false;
        boolean flag = false;
        if (Arrays.asList(TOPIC_TYPE_DO).contains(topicType)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 是否支持显示计时
     * 除了模拟模式都不支持
     *
     * @param practiceMode
     * @return
     */
    public static boolean isSupportShowTimer(int practiceMode) {
        if (practiceMode == MODE_SIMU)
            return true;
        return false;
    }

    /**
     * 是否显示交卷按钮
     *
     * @param practiceMode
     * @return
     */
    public static boolean isSupportShowCommit(int practiceMode) {
        if (practiceMode == MODE_SHOW || practiceMode == MODE_ALL)
            return false;

        return true;
    }

    /**
     * 是否支持交卷
     * 模拟模式
     * 默认模式(可作答的题型)
     * 收藏和错题不支持
     *
     * @param practiceMode
     * @param topicType
     * @return
     */
    public static boolean isSupportCommit(int practiceMode, int practiceType, int topicType) {
        if (practiceType == PRACTICE_COLL || practiceType == PRACTICE_ERROR)
            return false;

        if (practiceMode == MODE_SIMU)
            return true;

        if (isDefaultTopicType(practiceMode, topicType))
            return true;

        return false;
    }

    /**
     * 默认模式 而且 题型为可作答
     *
     * @param practiceMode
     * @param topicType
     * @return
     */
    private static boolean isDefaultTopicType(int practiceMode, int topicType) {
        if (practiceMode == MODE_DEFAULT) {
            return true;
        }
        return false;
    }

    /**
     * 是否支持显示对与错的数字
     *
     * @param practiceMode
     * @param practiceType
     * @param topicType
     * @return
     */
    public static boolean isSupportShowNumber(int practiceMode, int practiceType, int topicType) {
        if (practiceMode == MODE_SHOW
                || practiceMode == MODE_SIMU
                || practiceType == PRACTICE_SEARCH)
            return false;

        boolean flag = false;

        for (int type : TOPIC_TYPE_DO) {
            if (type == topicType) {
                flag = true;
                break;
            }
        }

        return flag;
    }

    public static String getErrorOrCollTypeName(PracticeManager practiceManager, CardBean.Topic topic){
        String practiceType = PracticeHelper.getPracticeType(practiceManager.mPracticeType);
        String practiceItemType = PracticeHelper.getPracticeType(topic.getMainType());
        String typeName;
        if(practiceManager.mPracticeType == PracticeHelper.PRACTICE_ERROR
                || practiceManager.mPracticeType == PracticeHelper.PRACTICE_COLL){
            //错题和收藏的题目来自该科目下的所有题库，因此需要知道当前是从哪里进入的，还要展示题目来自哪个题库
            if(topic.getMainType() == PracticeHelper.PRACTICE_CHAPTER){
                //章节训练
                typeName = String.format("%s•%s•%s",practiceType , practiceItemType, practiceManager.mChapterSectionId);
            }else if(topic.getMainType() == PracticeHelper.PRACTICE_TRUE){
                //历年真题
                typeName = String.format("%s•%s•%s",practiceType , practiceItemType, practiceManager.mPaperId);
            }else if(topic.getMainType() == PracticeHelper.PRACTICE_SIMU){
                //模拟试卷
                typeName = String.format("%s•%s•%s",practiceType , practiceItemType, practiceManager.mTestPaperId);
            }else {
                //VIP题库、高频考点、免费题库
                typeName = String.format("%s•%s",practiceType , practiceItemType);
            }
        }else if(practiceManager.mPracticeType == PracticeHelper.PRACTICE_DAYCARD){
            //每日打卡
            typeName = PracticeHelper.getPracticeType(practiceManager.mMainType);

        }else {
            typeName = practiceItemType;
            if(!TextUtils.isEmpty(practiceManager.mSubTitle)){
                //历年真题、章节训练、模拟试卷的副标题才不为空
                typeName += "•"+practiceManager.mSubTitle;
            }
        }
        while (typeName.endsWith("•") || typeName.endsWith("null")){
            typeName = typeName.substring(0, typeName.length() - 1);
        }
        return typeName;
    }

    /**
     * 是否支持作答，不支持作答直接显示答案
     *
     * @return
     */
    public static boolean isSupportPractice(int practiceMode, int topicType) {
        if (practiceMode == MODE_SIMU)
            return true;

        if (isDefaultTopicType(practiceMode, topicType))
            return true;

        return false;
    }

    /**
     * 是否只标记为选中
     *
     * @param practiceMode
     * @return
     */
    public static boolean isOptionSelect(int practiceMode) {
        if (practiceMode == MODE_SIMU)
            return true;

        return false;
    }

    /**
     * 回答完所有题才可交卷
     *
     * @param practiceType
     * @return
     */
    public static boolean isCheckFinish(int practiceType) {
        if (practiceType == PRACTICE_DAYCARD)
            return true;
        return false;
    }

    /**
     * 题型是否支持选择选项
     * 显示无法作答标记
     *
     * @param topicType 题型
     * @return
     */
    public static boolean isSupportSelect(int topicType) {
        if (topicType == TYPE_SINGLE //单选
                || topicType == TYPE_MULTI //多选
                || topicType == TYPE_JUDGE) //判断
            return true;
        return false;
    }

    /**
     * 获取保存答题记录的方式
     * 只有默认模式和模拟模式支持保存答题记录
     * 默认模式的不可作答题型保存当前位置
     * 可作答题型每30题保存一次
     * <p>
     * 错题和收藏题不支持保存
     *
     * @param practiceMode
     * @param topicType
     * @return
     */
    public static int getRecordType(int practiceMode, int practiceType, int topicType) {
        //Log.d("dddd",practiceMode+" "+practiceType+" "+topicType);
        if (practiceType == PRACTICE_COLL || practiceType == PRACTICE_ERROR) return 0;
        if (practiceMode != MODE_SIMU && practiceMode != MODE_DEFAULT) return 0;

        if (practiceMode == MODE_SIMU)
            return RECORD_COMMIT;

        for (int type : TOPIC_TYPE_DO) {
            if (practiceMode == MODE_DEFAULT && type == topicType) {
                return RECORD_OPTION;
            }
        }
        return RECORD_UNDO;
    }

    /**
     * 是否显示恢复答题(是否获取答题记录)
     *
     * @param practiceMode
     * @return
     */
    public static boolean getIsContinue(int practiceMode) {
        if (practiceMode == MODE_DEFAULT)
            return true;
        return false;
    }

    /**
     * 是否显示加载下一页
     *
     * @param practiceMode
     * @param practiceType
     * @return
     */
    public static boolean isShowLoadMore(int practiceMode, int practiceType) {
        if (
//                practiceType == PRACTICE_TRUE
//                || practiceType == PRACTICE_SIMU
//                ||
                practiceType == PRACTICE_SEARCH
                        || practiceType == PRACTICE_DAYCARD)
            return false;
        return true;
    }

    // 设置字体大小
    public static void changeSize(TextView tv, int defSize) {
        int size = getTextSize(defSize);
        tv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, size);
    }

    // 字体变化的变化模式 -1小号(-2) 0正常(+2) 1中号(+4) 2大号(+6)
    public static int getTextSize(int defSize) {
        switch (UserCache.getSizeMode()) {
            case -1:
                return defSize - 2;
            case 0:
                return defSize;
            case 1:
                return defSize + 2;
            case 2:
                return defSize + 4;
            case 3:
                return defSize + 6;
            default:
                return defSize;
        }
    }

    /**
     * 是否显示加载下一页
     *
     * @param practiceMode
     * @return
     */
    public static boolean isShowTrueOrFalse(int practiceMode) {
        if (practiceMode == MODE_SIMU || practiceMode == MODE_SHOW)
            return false;
        return true;
    }

    /**替换掉html中的<p>标签和空格（&nbsp;）
     * @param content
     * @return
     */
    public static String filterLabel(String content) {
        if (TextUtils.isEmpty(content)) return content;
        String head = content.substring(0, Math.min(content.length(), 10));
        Pattern p_enter = Pattern.compile("<p>", Pattern.CASE_INSENSITIVE);
        Matcher m_enter = p_enter.matcher(head);
        head = m_enter.replaceAll("");
        content = head + content.substring(Math.min(content.length(), 10));
        content = content.replace("&nbsp;", "");
        if(content.endsWith("<br>")){
            content = content.substring(0, content.lastIndexOf("<br>"));
        }else if(content.endsWith("<br/>")){
            content = content.substring(0, content.lastIndexOf("<br/>"));
        }
        return content;
    }

    public static String replaceLabel(String content) {
        if (TextUtils.isEmpty(content)) return content;
//        return content.replace("<", "&lt;").replace(">", "&gt;").replace("br/","\n");
        return content;
    }
}
