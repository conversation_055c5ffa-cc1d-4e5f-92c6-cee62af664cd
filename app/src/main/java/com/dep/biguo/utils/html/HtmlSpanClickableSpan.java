package com.dep.biguo.utils.html;

import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

public class HtmlSpanClickableSpan extends ClickableSpan {
    private String url;
    private int index;
    private OnSpanClickListener onImageClickListener;

    public HtmlSpanClickableSpan(String url, int index, OnSpanClickListener onImageClickListener) {
        this.url = url;
        this.index = index;
        this.onImageClickListener = onImageClickListener;
    }

    @Override
    public void onClick(@NonNull View widget) {
        if(onImageClickListener != null){
            onImageClickListener.onSpanClick(index, url);
        }
    }
}