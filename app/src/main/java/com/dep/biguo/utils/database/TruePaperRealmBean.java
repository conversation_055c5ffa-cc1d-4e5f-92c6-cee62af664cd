package com.dep.biguo.utils.database;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class TruePaperRealmBean extends RealmObject {
    @PrimaryKey  private String key;
    private int id;//题库id
    private String paper_id;//试卷ID
    private String code;//课程代码
    private String course_name;//课程名称
    private int total_nums;//总题数
    private int cal_nums;//可答题数
    private int person_count_answered;//已答题人数
    private String price;//价格（字符串）
    private int user_answered_num;//当前用户答题数
    private String name;//题库名称
    private int is_show;//是否可使用
    private int unlock;//是否已解锁
    private int status;//
    private long version;
    private int isCache;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPaper_id() {
        return paper_id;
    }

    public void setPaper_id(String paper_id) {
        this.paper_id = paper_id;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public int getCal_nums() {
        return cal_nums;
    }

    public void setCal_nums(int cal_nums) {
        this.cal_nums = cal_nums;
    }

    public int getPerson_count_answered() {
        return person_count_answered;
    }

    public void setPerson_count_answered(int person_count_answered) {
        this.person_count_answered = person_count_answered;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getUser_answered_num() {
        return user_answered_num;
    }

    public void setUser_answered_num(int user_answered_num) {
        this.user_answered_num = user_answered_num;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIs_show() {
        return is_show;
    }

    public void setIs_show(int is_show) {
        this.is_show = is_show;
    }

    public int getUnlock() {
        return unlock;
    }

    public void setUnlock(int unlock) {
        this.unlock = unlock;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getIsCache() {
        return isCache;
    }

    public void setIsCache(int isCache) {
        this.isCache = isCache;
    }
}
