package com.dep.biguo.utils.database;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class TopicRealmBean extends RealmObject {
    @PrimaryKey String primaryKey;//主键，code + "_" + mainType + "_" + type
    @Required private String code;//课程代码
    private int mainType;//免费、真题、VIP、章节
    private int type;//题目类型1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
    private String type_name;//题目类型名称 1 单选 2多选 3判断 4问答  6完形填空 7阅读理解 8名词解释
    private int last_id;//最后一次作答的题目的ID
    private long version;//题库的版本号

    public TopicRealmBean() {

    }



    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getType_name() {
        return type_name;
    }

    public void setType_name(String type_name) {
        this.type_name = type_name;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public int getLast_id() {
        return last_id;
    }

    public void setLast_id(int last_id) {
        this.last_id = last_id;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"primaryKey\":\"")
                .append(primaryKey).append('\"');
        sb.append(",\"code\":\"")
                .append(code).append('\"');
        sb.append(",\"mainType\":")
                .append(mainType);
        sb.append(",\"type\":")
                .append(type);
        sb.append(",\"type_name\":")
                .append(type_name);
        sb.append(",\"last_id\":")
                .append(last_id);
        sb.append(",\"version\":")
                .append(version);
        sb.append('}');
        return sb.toString();
    }
}
