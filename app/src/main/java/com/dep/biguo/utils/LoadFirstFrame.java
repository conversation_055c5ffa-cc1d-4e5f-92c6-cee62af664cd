package com.dep.biguo.utils;

import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.os.Handler;
import android.os.Message;
import android.provider.MediaStore;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class LoadFirstFrame {
    private Map<String, Bean> map;
    private ExecutorService executorService;

    private Handler handler;

    public LoadFirstFrame() {
        map = new HashMap<>();
        executorService = Executors.newFixedThreadPool(5);
        handler = new Handler(msg -> {
            Bean bean = (Bean) msg.obj;
            bean.imageView.setImageBitmap(bean.bitmap);
            return false;
        });
    }

    public Bitmap getFirstFrame(String videoUrl, ImageView imageView) {
        if(map.containsKey(videoUrl) && map.get(videoUrl) != null){
            return map.get(videoUrl).bitmap;
        }else {
            map.put(videoUrl, new Bean(imageView, null));
            Runnable task = new MyTask(videoUrl);
            executorService.submit(task);
        }
        /*new Thread(){
            @Override
            public void run() {
                Map<String, Object> map = new HashMap<>();
                map.put("view", imageView);
                map.put("bitmap", createVideoThumbnail(videoUrl, MediaStore.Images.Thumbnails.MINI_KIND));
                Message message = new Message();
                message.obj = map;
                handler.sendMessage(message);
            }
        }.start();*/
        return null;
    }

    class MyTask implements Runnable {
        private String url;

        public MyTask(String url) {
            this.url = url;
        }

        @Override
        public void run() {
            createVideoThumbnail(url, MediaStore.Images.Thumbnails.MINI_KIND);
        }

        public Bitmap createVideoThumbnail(String url, int kind) {
            Bitmap bitmap = null;
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            try {
                if (url.startsWith("http://")
                        || url.startsWith("https://")
                        || url.startsWith("widevine://")) {
                    retriever.setDataSource(url, new Hashtable<>());
                } else {
                    retriever.setDataSource(url);
                }
                bitmap = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC); //retriever.getFrameAtTime(-1);
            } catch (IllegalArgumentException ex) {
                // Assume this is a corrupt video file
                ex.printStackTrace();
            } catch (RuntimeException ex) {
                // Assume this is a corrupt video file.
                ex.printStackTrace();
            } finally {
                try {
                    retriever.release();
                } catch (RuntimeException ex) {
                    // Ignore failures while cleaning up.
                    ex.printStackTrace();
                }
            }

            if (bitmap == null) {
                return null;
            }

            if (kind == MediaStore.Images.Thumbnails.MINI_KIND) {//压缩图片 开始处
                // Scale down the bitmap if it's too large.
                int width = bitmap.getWidth();
                int height = bitmap.getHeight();
                int max = Math.max(width, height);
                if (max > 512) {
                    float scale = 512f / max;
                    int w = Math.round(scale * width);
                    int h = Math.round(scale * height);
                    bitmap = Bitmap.createScaledBitmap(bitmap, w, h, true);
                }//压缩图片 结束处
            } else if (kind == MediaStore.Images.Thumbnails.MICRO_KIND) {
                bitmap = ThumbnailUtils.extractThumbnail(bitmap,
                        96,
                        96,
                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
            }
            return bitmap;
        }
    }

    public static class Bean{
        public Bean(ImageView imageView, Bitmap bitmap) {
            this.imageView = imageView;
            this.bitmap = bitmap;
        }

        private ImageView imageView;
        private Bitmap bitmap;
    }
}
