package com.dep.biguo.utils.mmkv;

import android.content.Context;
import android.os.Build;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.IntRange;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.MD5Util;
import com.dep.biguo.utils.RequestPermissions;

public class DeviceCache {
    public static final String USER_AGENT = "userAgent";
    public static final String OAID = "OAID";
    public static final String ANDROID_ID = "android_id";
    public static final String IMEI = "imei";

    public static final String UMENG_DEVICE_TOKEN = "umengDeviceToken";//缓存友盟的token，用于获取应用推送消息的未读数
    public static final String IS_TODAY_REMIND_UPDATE_APP = "isTodayRemindUpdateApp";//今日是否已提醒更新APP

    public static final String PERSONALIZED_STATE = "personalizedState";//首页开屏广告的个性化推荐是否开启

    public static final String ONE_KEY_LOGIN_PRE_CODE = "oneKeyLoginPreCode";//一键登录的预取号是否成功

    public static final String IS_SHOW_COURSE_CHANGE_TABLE = "isShowCourseChangeTable";//是否显示过课程对照表

    public static final String DEVICE_MOBILE = "deviceModel";//设备型号
    public static final String SYSTEM_VERSION = "deviceVersion";//系统版本

    public static final String RECOMMEND = "recommend";//个性化推送

    /**缓存userAgent
     * @param userAgent
     */
    public static void cacheUserAgent(String userAgent) {
        KVHelper.putValue(USER_AGENT, userAgent);
    }

    /**获取userAgent
     * @return
     */
    public static String getUserAgent() {
        return KVHelper.getString(USER_AGENT);
    }


    /**缓存OAID
     * @param oaid
     */
    public static void cacheOAID(String oaid) {
        KVHelper.putValue(OAID, oaid);
    }

    /**获取OAID
     * @return
     */
    public static String getOAID() {
        return KVHelper.getString(OAID);
    }

    /**获取androidId
     * @return
     */
    public static String getAndroidId(Context context) {
        String androidID = KVHelper.getString(ANDROID_ID);
        if(TextUtils.isEmpty(androidID)) {
            androidID = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            KVHelper.putValue(ANDROID_ID, androidID);
        }
        return androidID;
    }

    /**缓存imei
     * @param imei
     */
    public static void cacheIMEI(String imei) {
        KVHelper.putValue(IMEI, imei);
    }

    /**获取imei
     * @return
     */
    public static String getIMEI() {
        return KVHelper.getString(IMEI);
    }

    /**缓存指定权限的申请状态
     * @param permissionName RequestPermissions.PERMISSION_GRANTED,
     *                       RequestPermissions.PERMISSION_DENIED,
     *                       RequestPermissions.PERMISSION_DENIED_APP_OP
     *                       RequestPermissions.PERMISSION_DENIED_NOT
     * @param status
     */
    public static void cachePermissionStatus(String permissionName, @IntRange(from = RequestPermissions.PERMISSION_NULL, to = RequestPermissions.PERMISSION_GRANTED) int status){
        KVHelper.putValue(permissionName, status);
    }

    /**获取指定权限的申请状态
     * @param permissionName
     * @return
     */
    public static int getPermissionStatus(String permissionName){
        return !KVHelper.containsKey(permissionName) ? RequestPermissions.PERMISSION_NULL : KVHelper.getInt(permissionName);
    }

    /**缓存个性化广告开关状态
     * @param personalizedState 1表示开启，0表示关闭
     */
    public static void cachePersonalizedState(int personalizedState){
        KVHelper.putValue(PERSONALIZED_STATE, personalizedState);
    }

    /**获取个性化广告开关状态
     * @return
     */
    public static int getPersonalizedState(){
        if(!KVHelper.containsKey(PERSONALIZED_STATE)){
            return 1;
        }
        return KVHelper.getInt(PERSONALIZED_STATE);
    }

    /**缓存友盟的deviceToken
     * @param deivceToken 友盟的deviceToken
     */
    public static void cacheUmengDeviceToken(String deivceToken){
        KVHelper.putValue(UMENG_DEVICE_TOKEN, deivceToken);
    }

    /**获取友盟的deviceToken
     * @return
     */
    public static String getUmengDeviceToken(){
        return AppUtil.isEmpty(KVHelper.getString(UMENG_DEVICE_TOKEN), "");
    }

    /**缓存友盟一键登录的预取号是否成功
     * @param statusCode 预取号状态，7000为成功
     */
    public static void cacheOneKeyLoginPreCode(int statusCode){
        KVHelper.putValue(ONE_KEY_LOGIN_PRE_CODE, statusCode);
    }

    /**获取友盟一键登录的预取号是否成功
     * @return
     */
    public static int getOneKeyLoginPreCode(){
        return KVHelper.getInt(ONE_KEY_LOGIN_PRE_CODE);
    }

    /**是否显示过课程对照表
     * @return
     */
    public static boolean getShowCourseChangeTable(){
        return KVHelper.getBoolean(IS_SHOW_COURSE_CHANGE_TABLE);
    }

    /**缓存是否显示过课程对照表
     *
     */
    public static void cacheShowCourseChangeTable(){
        KVHelper.putValue(IS_SHOW_COURSE_CHANGE_TABLE, true);
    }

    /**缓存上一次提醒更新的时间
     * @param time 毫秒值
     */
    public static void cacheIsTodayRemindUpdateApp(long time){
        KVHelper.putValue(IS_TODAY_REMIND_UPDATE_APP, time);
    }

    /**获取上一次提醒更新的时间
     * @return
     */
    public static long getIsTodayRemindUpdateApp(){
        return KVHelper.getLong(IS_TODAY_REMIND_UPDATE_APP);
    }

    /**
     * 获取手机型号
     *
     * @return 手机型号
     */
    public static String getSystemModel() {
        return KVHelper.getString(DEVICE_MOBILE);
    }

    /**
     * 缓存手机型号
     *
     * @return 手机型号
     */
    public static void cacheSystemModel(String model) {
        KVHelper.putValue(DEVICE_MOBILE, model);
    }

    /**
     * 获取当前手机系统版本号
     *
     * @return 系统版本号
     */
    public static String getSystemVersion() {
        return KVHelper.getString(SYSTEM_VERSION);
    }
    /**
     * 缓存当前手机系统版本号
     *
     * @return 系统版本号
     */
    public static void cacheSystemVersion(String version) {
        KVHelper.putValue(SYSTEM_VERSION, version);
    }

    /**获取硬件信息
     * @return
     */
    public static String getHardwareInfo(){
        StringBuilder builder = new StringBuilder()
                .append(Build.MANUFACTURER).append("\\")//厂商
                .append(Build.BRAND).append("\\")//品牌
                .append(Build.MODEL).append("\\")//型号
                .append(Build.PRODUCT).append("\\")//名称
                .append(Build.HARDWARE).append("\\")//CPU
                .append(Build.ID).append("\\")//ID
                .append(Build.VERSION.SDK_INT)//系统
                .append(DeviceCache.getOAID()).append("\\")
                .append(DeviceCache.getIMEI()).append("\\")
                .append(DeviceCache.getUserAgent()).append("\\")
                .append((System.currentTimeMillis() - SystemClock.elapsedRealtime()) / 1000);//开机时间

        LogUtil.d("dddd", builder.toString());

        return MD5Util.md5(builder.toString());
    }

    /**缓存是否开启个性化推送
     * @param isRecommend
     */
    public static void cacheRecommend(boolean isRecommend){
        KVHelper.putValue(RECOMMEND, isRecommend);
    }

    /**判断缓存的是否开启了个性化推送
     * @return
     */
    public static boolean isRecommend(){
        return KVHelper.getBoolean(RECOMMEND, true);
    }
}
