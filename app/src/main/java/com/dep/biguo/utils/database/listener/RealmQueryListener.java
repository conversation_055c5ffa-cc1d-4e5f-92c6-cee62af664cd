package com.dep.biguo.utils.database.listener;

import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.RealmModel;
import io.realm.RealmResults;

public abstract class RealmQueryListener<FORM extends RealmModel, TO> implements RealmChangeListener<RealmResults<FORM>> {
    private QueryListener<TO> queryListener;
    private Realm realm;

    private final Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            queryListener.query((TO) msg.obj);
            return false;
        }
    });

    public RealmQueryListener( QueryListener<TO> queryListener, Realm realm) {
        this.queryListener = queryListener;
        this.realm = realm;
    }

    @Override
    public void onChange(RealmResults<FORM> results) {
        results.removeChangeListener(this);
        List<FORM> copyResult = realm.copyFromRealm(results);
        realm.close();
        //可能需要遍历结果再查其他表，耗时较长，因此选择开线程
        new Thread(() -> onAsyncChange(copyResult)).start();
    }

    /**此方法运行在一个线程内，不要在此方法中操作UI，应用{@link #callBack(Object)} 方法回调，在外部处理UI
     * @param form
     */
    public abstract void onAsyncChange(List<FORM> form);

    public void callBack(TO to){
        Message message = new Message();
        message.obj = to;
        handler.sendMessage(message);
    }

}
