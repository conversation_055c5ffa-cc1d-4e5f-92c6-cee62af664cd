package com.dep.biguo.utils.database;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class ChapterSectionsRealBean extends RealmObject {
    @PrimaryKey private String key;
    private String section_id;//小节id
    private String name;//章节名称
    private String code;//课程编码
    private long version;//题库版本
    private int total_nums;//总题数
    private int isCache;//是否已缓存
    private String disable_cache;//是否暂不使用数据库缓存

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSection_id() {
        return section_id;
    }

    public void setSection_id(String section_id) {
        this.section_id = section_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public int getIsCache() {
        return isCache;
    }

    public void setIsCache(int isCache) {
        this.isCache = isCache;
    }

    public String getDisable_cache() {
        return disable_cache;
    }

    public void setDisable_cache(String disable_cache) {
        this.disable_cache = disable_cache;
    }
}
