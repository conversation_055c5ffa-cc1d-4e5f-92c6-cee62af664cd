package com.dep.biguo.utils.database;

import com.dep.biguo.utils.mmkv.UserCache;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class CourseRealmBean extends RealmObject {
    @PrimaryKey int id;
    private int professionId;//专业ID
    private String icon_url;//考试计划图片
    private double pass_score;//及格分数线
    private int is_formulate_study_plan;//是否已制定计划
    private RealmList<CourseItemRealmBean> courses_joined;//报名课程
    private RealmList<CourseItemRealmBean> courses_not_joined;//未报名课程
    private RealmList<CourseItemRealmBean> courses_passed;//已通过课程

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getIcon_url() {
        return icon_url;
    }

    public void setIcon_url(String icon_url) {
        this.icon_url = icon_url;
    }

    public int getProfessionId() {
        return professionId;
    }

    public void setProfessionId(int professionId) {
        this.professionId = professionId;
    }

    public void setPass_score(double pass_score) {
        this.pass_score = pass_score;
    }

    public double getPass_score() {
        return pass_score;
    }

    public void setPass_score(float pass_score) {
        this.pass_score = pass_score;
    }

    public int getIs_formulate_study_plan() {
        return is_formulate_study_plan;
    }

    public void setIs_formulate_study_plan(int is_formulate_study_plan) {
        this.is_formulate_study_plan = is_formulate_study_plan;
    }

    public RealmList<CourseItemRealmBean> getCourses_joined() {
        return courses_joined;
    }

    public void setCourses_joined(RealmList<CourseItemRealmBean> courses_joined) {
        this.courses_joined = courses_joined;
    }

    public RealmList<CourseItemRealmBean> getCourses_not_joined() {
        return courses_not_joined;
    }

    public void setCourses_not_joined(RealmList<CourseItemRealmBean> courses_not_joined) {
        this.courses_not_joined = courses_not_joined;
    }

    public RealmList<CourseItemRealmBean> getCourses_passed() {
        return courses_passed;
    }

    public void setCourses_passed(RealmList<CourseItemRealmBean> courses_passed) {
        this.courses_passed = courses_passed;
    }
}
