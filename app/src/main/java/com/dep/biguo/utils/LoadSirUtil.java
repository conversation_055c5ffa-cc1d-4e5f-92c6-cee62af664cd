package com.dep.biguo.utils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.widget.loadsir.EmptyContentCallBack;
import com.dep.biguo.widget.loadsir.ErrorCallBack;
import com.dep.biguo.widget.loadsir.NetworkCallBack;
import com.kingja.loadsir.callback.Callback;
import com.kingja.loadsir.callback.SuccessCallback;
import com.kingja.loadsir.core.Convertor;
import com.kingja.loadsir.core.LoadService;
import com.kingja.loadsir.core.LoadSir;

import java.net.UnknownHostException;
import java.util.Collection;
import java.util.List;

public class LoadSirUtil {
    public static <T> LoadService<T> register(Object target, Callback.OnReloadListener onReloadListener) {
        return LoadSir.getDefault().register(target, onReloadListener, obj -> {
            if(obj == null){
                return EmptyContentCallBack.class;

            } else if (obj instanceof BaseResponse) {
                BaseResponse response = (BaseResponse) obj;
                if (Api.REQUEST_SUCCESS.equals(response.getResult_code())) {//成功回调
                    //如果返回的数据是一个数组，则判断数组是否为空
                    if (response.getData() instanceof Collection) {
                        Collection collection = (Collection) ((BaseResponse<?>) obj).getData();
                        if(collection == null || collection.isEmpty()) {
                            return EmptyContentCallBack.class;
                        }
                    }
                }else {
                    return ErrorCallBack.class;
                }

            } else if (obj instanceof Collection) {
                Collection collection = (Collection) obj;
                if(collection.isEmpty()) {
                    return EmptyContentCallBack.class;
                }

            } else if (obj instanceof Throwable) {
                if (obj instanceof UnknownHostException) {
                    return NetworkCallBack.class;
                } else {
                    return ErrorCallBack.class;
                }
            }
            return SuccessCallback.class;
        });
    }
}
