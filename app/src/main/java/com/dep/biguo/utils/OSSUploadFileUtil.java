package com.dep.biguo.utils;

import android.content.Context;

import com.alibaba.sdk.android.oss.ClientConfiguration;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider;
import com.alibaba.sdk.android.oss.internal.OSSAsyncTask;
import com.alibaba.sdk.android.oss.model.DeleteObjectRequest;
import com.alibaba.sdk.android.oss.model.DeleteObjectResult;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.OssSignatureBean;
import com.dep.biguo.utils.mmkv.UserCache;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OSSUploadFileUtil{
    private static final String bucketName = "bgtk";
    private static final String endpoint = "cdn.biguotk.com";
    private static final String scheme = "https://"+endpoint;
    private static final String dir = "app/Android/circle/";

    private Context context;
    private Map<String, String> uploadMap;
    private Map<String, OSSAsyncTask<PutObjectResult>> putAsyncTaskMap;
    private Map<String, OSSAsyncTask<DeleteObjectResult>> delAsyncTaskMap;

    private OnUploadListener onUploadListener;
    private OSSClient ossClient;

    public static OSSUploadFileUtil getInstance(Context context, OssSignatureBean signatureBean){
        return new OSSUploadFileUtil(context, signatureBean);
    }

    public OSSUploadFileUtil(Context context, OssSignatureBean signatureBean) {
        this.context = context;
        this.uploadMap = new HashMap<>();
        this.putAsyncTaskMap = new HashMap<>();
        this.delAsyncTaskMap = new HashMap<>();
        init(signatureBean);
    }

    public OSSUploadFileUtil setOnUploadListener(OnUploadListener onUploadListener) {
        this.onUploadListener = onUploadListener;
        return this;
    }

    private void init(OssSignatureBean signatureBean){
        //该配置类如果不设置，会有默认配置，具体可看该类
        ClientConfiguration conf = new ClientConfiguration();
        conf.setConnectionTimeout(15 * 1000); // 连接超时，默认15秒
        conf.setSocketTimeout(15 * 1000); // socket超时，默认15秒
        conf.setMaxConcurrentRequest(9); // 最大并发请求数，默认5个
        conf.setMaxErrorRetry(2); // 失败后最大重试次数，默认2次
        OSSLog.enableLog(); //这个开启会支持写入手机sd卡中的一份日志文件位置在SDCard_path\OSSLog\logs.csv

        String ossAccessKeyId = signatureBean.getAccessKeyId();
        String secretKeyId = signatureBean.getAccessKeySecret();
        String securityToken = signatureBean.getSecurityToken();
        OSSStsTokenCredentialProvider credentialProvider = new OSSStsTokenCredentialProvider(ossAccessKeyId, secretKeyId, securityToken);

        ossClient = new OSSClient(context.getApplicationContext(), endpoint, credentialProvider, conf);
    }

    /**上传单张图片
     * @param filePath 本地文件路径
     */
    private void upload(String filePath){
        try {
            int userId = UserCache.getUserCache().getUser_id();
            //利用 用户ID+时间戳+编码文件名 作为上传到服务器的文件名
            String filename = String.format("%s_%s_%s", userId, System.currentTimeMillis(), encode(new File(filePath).getName()));
            String uploadPath = String.format("%s%s", dir, filename);
            PutObjectRequest put = new PutObjectRequest(bucketName, uploadPath, filePath);

            OSSAsyncTask<PutObjectResult> task = ossClient.asyncPutObject(put, new UploadCallback(uploadPath));
            putAsyncTaskMap.put(filePath, task);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**批量上传图片
     * 注：多次调用单张上传图片实现的，但因为上传单张图片是异步调用，所以看做有多少张图片就有几个异步任务
     * @param list 存有本地文件路径的数组
     */
    public OSSUploadFileUtil uploadList(List<String> list){
        for(String path : list){
            //判断任务是否已存在，避免任务被覆盖，导致任务执行完后，任务总数不等于上传文件的总数
            if(!putAsyncTaskMap.containsKey(path)) {
                upload(path);
            }
        }
        return this;
    }

    /**取消所有进行中的上传异步任务
     *
     */
    private void cancelAllPutAsyncTask(){
        for(OSSAsyncTask<PutObjectResult> asyncTask : putAsyncTaskMap.values()){
            if(!asyncTask.isCanceled()) {
                asyncTask.cancel();
            }
        }
        putAsyncTaskMap.clear();
        putAsyncTaskMap = null;
    }

    /**取消所有进行中的删除文件异步任务
     *
     */
    private void cancelAllDelAsyncTask(){
        for(OSSAsyncTask<DeleteObjectResult> asyncTask : delAsyncTaskMap.values()){
            if(!asyncTask.isCanceled()) {
                asyncTask.cancel();
            }
        }
        delAsyncTaskMap.clear();
        delAsyncTaskMap = null;
    }

    /**上传任务回滚（删除本次已上传的文件）
     * <p>批量上传必须保证所有文件都上传成功，当取消任务时，已上传的文件就变成废数据，需要删除本次任务中所有已上传的文件</p>
     */
    private void uploadBack(){
        for(String url : uploadMap.values()){
            delete(url);
        }
    }

    /**删除已上传的图片
     * @param fileUrl 服务器的图片路径
     */
    private void delete(String fileUrl){
        String deletePath = fileUrl.replace(scheme, "");
        DeleteObjectRequest del = new DeleteObjectRequest(bucketName, deletePath);

        OSSAsyncTask<DeleteObjectResult> task = ossClient.asyncDeleteObject(del, new DeleteCallback());
        delAsyncTaskMap.put(fileUrl, task);
    }

    /**编码，主要是本地文件名可能包含中文
     * @param value 被编码的文本
     * @return 编码后的文本
     */
    private static String encode(String value){
        try {
            return URLEncoder.encode(value, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**文件上传的回调
     *
     */
    private class UploadCallback implements OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
        private String uploadPath;

        public UploadCallback(String uploadPath) {
            this.uploadPath = uploadPath;
        }

        @Override
        public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            try {
                uploadMap.put(request.getUploadFilePath(), String.format("%s/%s", scheme, uploadPath));
                //上传完成的数量等于异步任务数量，就认为全部都上传成功了，可以回调
                if(uploadMap.size() == putAsyncTaskMap.size()) {
                    onUploadListener.success(uploadMap);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        @Override
        public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            //取消所有进行中的上传任务
            cancelAllPutAsyncTask();
            //删除已上传的文件
            uploadBack();

            String message = "上传失败";
            // 请求异常
            if (clientException != null) {
                // 本地异常如网络异常等
                clientException.printStackTrace();
                message = "网络异常";
            }
            if (serviceException != null) {
                // 服务异常
                serviceException.printStackTrace();
            }

            try {
                onUploadListener.fail(message);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    /**删除文件的回调
     *
     */
    private static class DeleteCallback implements OSSCompletedCallback<DeleteObjectRequest, DeleteObjectResult> {

        @Override
        public void onSuccess(DeleteObjectRequest deleteObjectRequest, DeleteObjectResult deleteObjectResult) {
            LogUtil.d("dddd", "删除成功："+deleteObjectRequest.getObjectKey());
        }

        @Override
        public void onFailure(DeleteObjectRequest deleteObjectRequest, ClientException e, ServiceException e1) {
            LogUtil.d("dddd", "删除失败："+deleteObjectRequest.getObjectKey());
        }
    }


    /**
     * 当手动取消任务，或销毁页面时，需要清空一下缓存，或取消异步任务
     */
    public void onDestroy(){
        onUploadListener = null;
        cancelAllPutAsyncTask();
        cancelAllDelAsyncTask();
    }

    public interface OnUploadListener{
        void success(Map<String, String> successMap);

        void fail(String error);
    }
}
