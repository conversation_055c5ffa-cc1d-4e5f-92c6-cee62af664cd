package com.dep.biguo.utils.database.rxjava;

import io.reactivex.Observer;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.realm.Realm;

public abstract class RealmSubscriber<T> implements Observer<T> {
    private boolean beginTransaction;

    public RealmSubscriber() {

    }

    public RealmSubscriber(boolean beginTransaction) {
        this.beginTransaction = beginTransaction;
    }

    @Override
    public void onSubscribe(Disposable d) {

    }

    @Override
    public void onNext(T t){
        Realm realm = Realm.getDefaultInstance();
        if(beginTransaction){
            realm.beginTransaction();
        }
        onNext(t, realm);
        if(realm.isInTransaction()){
            realm.commitTransaction();
        }
        realm.close();
    }

    public abstract void onNext(T t, @NonNull Realm realm);

    @Override
    public void onError(Throwable e) {

    }

    @Override
    public void onComplete() {

    }
}
