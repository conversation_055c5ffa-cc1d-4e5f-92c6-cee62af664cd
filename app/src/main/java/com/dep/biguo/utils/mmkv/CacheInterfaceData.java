package com.dep.biguo.utils.mmkv;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.ZkHomeBean;
import com.dep.biguo.bean.ZkNewHomeBean;
import com.google.gson.reflect.TypeToken;

import java.util.List;

//缓存策略是优先用缓存数据刷新UI，同时请求接口，当接口请求回来之后，再次刷新UI，并用请求的数据覆盖上次的缓存
public class CacheInterfaceData {
    public static final String CITY_DATA = "city_data"; //首页数据
    public static final String HOME_DATA = "home_data"; //首页数据

    /**缓存城市列表数据
     * @param list
     */
    public static void cacheZkCityBean(List<CityBean> list){
        KVHelper.putValue(CITY_DATA, GsonUtils.toJson(list));
    }

    /**获取城市列表数据
     * @return
     */
    public static List<CityBean> getZkCityBean(){
        return GsonUtils.fromJson(KVHelper.getString(CITY_DATA), new TypeToken<List<CityBean>>(){}.getType());
    }

    /**缓存首页数据
     * @param zkHomeBean
     */
    public static void cacheZkHomeBean(ZkNewHomeBean zkHomeBean){
        if(UserCache.getCity() == null) return;
        if(UserCache.getSchool() == null) return;
        if(UserCache.getProfession() == null) return;

        int cityId = UserCache.getCity().getCity_id();
        int schoolId = UserCache.getSchool().getId();
        int professionId = UserCache.getProfession().getId();
        String code = UserCache.getHomeCode();
        KVHelper.putValue(String.format("%s_%s_%s_%s_%s", HOME_DATA, cityId, schoolId, professionId, code), GsonUtils.toJson(zkHomeBean));
    }

    /**获取首页数据
     * @return
     */
    public static ZkNewHomeBean getZkHomeBean(){
        if(UserCache.getCity() == null) return null;
        if(UserCache.getSchool() == null) return null;
        if(UserCache.getProfession() == null) return null;

        int cityId = UserCache.getCity().getCity_id();
        int schoolId = UserCache.getSchool().getId();
        int professionId = UserCache.getProfession().getId();
        String code = UserCache.getHomeCode();
        return GsonUtils.fromJson(KVHelper.getString(String.format("%s_%s_%s_%s_%s", HOME_DATA, cityId, schoolId, professionId, code)), ZkNewHomeBean.class);
    }
}
