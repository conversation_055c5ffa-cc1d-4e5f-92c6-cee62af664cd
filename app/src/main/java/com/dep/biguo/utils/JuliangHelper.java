package com.dep.biguo.utils;

import static com.umeng.socialize.utils.ContextUtil.getPackageName;

import android.app.Application;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

import com.biguo.utils.util.LogUtil;
import com.bytedance.ads.convert.BDConvert;
import com.bytedance.applog.AppLog;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.game.GameReportHelper;
import com.bytedance.applog.util.UriConstants;
import com.dep.biguo.BuildConfig;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class JuliangHelper {
    public static final String PSW_REGISTER = "app";//账号密码注册/登录
    public static final String CODE_REGISTER = "code";//验证码注册/登录
    public static final String WECHAT_REGISTER = "wechat";//微信注册/登录
    public static final String ONE_KEY_REGISTER = "auth";//一键注册/登录
    public static final String QQ_REGISTER = "qq";//QQ注册/登录

    /**
     * 初始化巨量引擎统计
     *
     * @param application
     */
    public static void initJuliang(Application application) {
        try {
            final InitConfig config = new InitConfig("176612", getMetadataChannel(application));
            // 设置数据上送地址
            config.setUriConfig(UriConstants.DEFAULT);
            config.setImeiEnable(false);//建议关停获取IMEI（出于合规考虑）
            config.setAutoTrackEnabled(false); // 全埋点开关，true开启，false关闭
            config.setLogEnable(false); // true:开启日志，参考4.3节设置logger，false:关闭日志
            AppLog.setEncryptAndCompress(true); // 加密开关，true开启，false关闭
            config.setEnablePlay(true); // 配置心跳事件（时长统计）

            //SDK会采集OAID、ANDROID_ID和其他的设备特征字段，请遵循相关合规要求在隐私弹窗后采集
            BDConvert.getInstance().init(application, AppLog.getInstance());
            // 如果在 onCreate 阶段初始化拿不到 XXXActivity 则不需要传递第三个参数
            AppLog.init(application, config);
            /* 初始化SDK结束 */

            Map<String, Object> headerMap = new HashMap<>();
            if (UserCache.getUserCache() != null) {
                AppLog.setUserUniqueID(String.valueOf(UserCache.getUserCache().getUser_id()));

                UserBean bean = UserCache.getUserCache();
                headerMap.put("users_id", String.valueOf(bean.getUser_id()));
                headerMap.put("username", bean.getMobile());
            }
            if (UserCache.getProvince() != null)
                headerMap.put("province", UserCache.getProvince().getName());
            if (UserCache.getProfession() != null) {
                ProfessionBean bean = UserCache.getProfession();
                headerMap.put("profession", bean.getName());
                headerMap.put("layer", bean.getLayer() == 1 ? "本科" : "专科");
            }
            AppLog.setHeaderInfo((HashMap<String, Object>) headerMap);

        } catch (Exception e) {
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
            e.printStackTrace();
        }
    }

    /**
     * 获取渠道
     *
     * @param application
     * @return
     */
    private static String getMetadataChannel(Application application) {
        String value = "biguo";
        try {
            ApplicationInfo appInfo = application.getPackageManager().getApplicationInfo(getPackageName(),
                    PackageManager.GET_META_DATA);
            value = appInfo.metaData.getString("UMENG_CHANNEL");
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 巨量广告投放被下载后，上报注册实践
     * @param type     注册/登录类型，值： {@link JuliangHelper#PSW_REGISTER},{@link JuliangHelper#CODE_REGISTER},{@link JuliangHelper#WECHAT_REGISTER},{@link JuliangHelper#QQ_REGISTER},{@link JuliangHelper#ONE_KEY_REGISTER}
     * @param isCreate 是否是第一次登录
     */
    public static void reportRegister(String type, int isCreate) {
        try {
            if(isCreate == 1){
                GameReportHelper.onEventRegister(String.format("%s_register", type), true);

            }else {
                GameReportHelper.onEventRegister(String.format("%s_login", type), true);
            }
        } catch (Exception e) {

        }
    }

    public static void reportPay(String type, String name, String channel, int price) {
        try {
            GameReportHelper.onEventPurchase(type, name, "0", 1, channel, "¥", true, price);
        } catch (Exception e) {

        }
    }

}
