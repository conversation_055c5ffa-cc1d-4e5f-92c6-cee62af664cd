package com.dep.biguo.utils.imp;

import android.content.Context;
import android.net.Uri;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.umeng.umlink.MobclickLink;
import com.umeng.umlink.UMLinkListener;

import java.util.HashMap;

public abstract class UMLinkImp implements UMLinkListener {
    /**对跳转App的处理，唤起已安装App会走这个回调
     * 使用{@link MobclickLink#handleUMLinkURI(Context, Uri, UMLinkListener)}方法才会走这个回调
     * @param s
     * @param hashMap
     */
    @Override
    public void onLink(String s, HashMap<String, String> hashMap) {
        LogUtil.d("dddd", "deeplink open");
        LogUtil.d("dddd", hashMap);

    }

    /**为获取新装参数的处理，App首次安装启动时
     * 使用{@link MobclickLink#getInstallParams(Context, UMLinkListener)}方法才会走这个回调
     * @param hashMap
     * @param uri
     */
    @Override
    public void onInstall(HashMap<String, String> hashMap, Uri uri) {
        LogUtil.d("dddd", "deeplink install");
        LogUtil.d("dddd", GsonUtils.toJson(hashMap));
    }

    /**错误信息的回调
     * @param s
     */
    @Override
    public void onError(String s) {

    }
}
