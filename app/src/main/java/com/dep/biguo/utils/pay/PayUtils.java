package com.dep.biguo.utils.pay;

import android.app.Activity;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import com.alipay.sdk.app.PayTask;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.StartFinal;
import com.hjq.toast.ToastUtils;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.util.Map;

public class PayUtils {
    /**
     *订单类型
     */
    public static final String VIP = "vip";//VIP题库
    public static final String YAMI = "yami";//押密
    public static final String YAMI_RESERVE = "yami_reserve";//预订押密
    public static final String VIDEO = "video";//视频
    public static final String VIDEO1 = "video1";//视频
    public static final String VIDEO2 = "video2";//视频
    public static final String VIDEO3 = "video3";//视频
    public static final String REAL_PAPER = "real_paper";//历年真题
    public static final String CLASS_ROOM = "class_room";//VIP课堂
    public static final String FRUIT_COIN = "fruitcoin";//果币充值
    public static final String TUITION = "tuition";//学费缴纳
    public static final String CHAPTER = "chapter";//章节训练
    public static final String HOME_ENGLISH_TWO = "yy2";//英语2
    public static final String BOOK = "book";//购买图书
    public static final String SUPER_VIP = "super_vip";//超级VIP
    public static final String MEMBERSHIP = "membership";//笔果折扣卡
    public static final String SKILL_VIDEO = "skill_video";//技能证视频
    public static final String VOCATION_VIDEO = "vocation_video";//职场提升
    public static final String HIGH_FREQUENCY = "high_frequency";//高频考点
    public static final String DOWNLOAD_TRUE_PAPER = "real_paper_download";//历年真题下载权限
    public static final String INTERNET_STUDY = "online_assistance";//网络助学
    public static final String DEDUCTION_CARD = "study_room_card";//自习室抵扣卡
    public static final String COUNSELLING_DAZIKAO = "tutorial_class_self_exam";//线下辅导班之大自考
    public static final String COUNSELLING_ZIXUAN = "tutorial_class_single_subject";//线下辅导班之自选
    public static final String INS_TUTORIAL_CLASS = "ins_tutorial_class";//机构助学班
    public static final String GRADUATION_AGENCY = "graduation_agency";//机构助学班

    /**
     * 订单所属列表
     */
    public static final int BOOK_LIST = 1;//图书订单
    public static final int CONSUMPTION_LIST = 2;//消费订单
    public static final int SERVICE_LIST = 3;//服务订单

    /**5.9.3版本开始使用下方四种字符串标志支付类型
     * 支付类型
     */
    public static final String PAY_TYPE_WEXIN = "weixin";//微信支付
    public static final String PAY_TYPE_ALIPAY = "alipay";//支付宝支付
    public static final String PAY_TYPE_COIN = "fruitcoin";//果币支付
    public static final String PAY_TYPE_INTEGRAL = "integral";//积分支付
    public static final String PAY_TYPE_TUITION = "tuition";//分期支付

    /**
     * 支付类型
     */
    public static final int PAY_TYPE_WX = 1;
    public static final int PAY_TYPE_ALI = 2;
    /**
     * 支付宝返回参数
     */
    final static int SDK_PAY_FLAG = 1001;

    private Activity mContext;

    private IWXAPI sApi;

    public void pay(Activity activity, int payType, WXPayBean bean, String result) {
        this.mContext = activity;
        sApi = WXAPIFactory.createWXAPI(activity, Constant.WX_KEY);
        sApi.registerApp(Constant.WX_KEY);

        switch (payType) {
            case PAY_TYPE_WX:
                toWXPay(bean);
                break;
            case PAY_TYPE_ALI:
                toAliPay(result);
                break;
        }
    }

    public void pay(Activity activity, String payType, WXPayBean bean, String result) {
        this.mContext = activity;
        sApi = WXAPIFactory.createWXAPI(activity, Constant.WX_KEY);
        sApi.registerApp(Constant.WX_KEY);

        if (PayUtils.PAY_TYPE_WEXIN.equals(payType)) {
            toWXPay(bean);
        }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType) || PayUtils.PAY_TYPE_TUITION.equals(payType)){
            toAliPay(result);
        }
    }


    /**
     * 微信支付
     */
    private void toWXPay(WXPayBean bean) {
        PayReq request = new PayReq();
        request.appId = bean.getAppid();
        request.partnerId = bean.getPartnerid();
        request.prepayId = bean.getPrepayid();
        request.packageValue = bean.getPack();
        request.nonceStr = bean.getNoncestr();
        request.timeStamp = bean.getTimestamp()+"";
        request.sign = bean.getSign();
        if (sApi.isWXAppInstalled()) {
            boolean send = sApi.sendReq(request);
        } else {
            showMessage("未安装微信");
        }
    }

    /**
     * 支付宝
     */
    private void toAliPay(String result) {
        final String orderInfo = result;   // 订单信息
        Runnable payRunnable = () -> {
            PayTask alipay = new PayTask(mContext);
            Map<String, String> result1 = alipay.payV2(orderInfo, true);
            Message msg = new Message();
            msg.what = SDK_PAY_FLAG;
            msg.obj = result1;
            mHandler.sendMessage(msg);
        };
        // 必须异步调用
        Thread payThread = new Thread(payRunnable);
        payThread.start();
    }


    /**
     * 支付宝状态
     * 9000 订单支付成功
     * 8000 正在处理中，支付结果未知（有可能已经支付成功），请查询商户订单列表中订单的支付状态
     * 4000 订单支付失败
     * 5000 重复请求
     * 6001 用户中途取消
     * 6002 网络连接出错
     * 6004 支付结果未知（有可能已经支付成功），请查询商户订单列表中订单的支付状态
     * 其它   其它支付错误
     */
    private Handler mHandler = new Handler(msg -> {
        switch (msg.what) {
            case SDK_PAY_FLAG: {
                PayResult payResult = new PayResult((Map<String, String>) msg.obj);
                /**
                 对于支付结果，请商户依赖服务端的异步通知结果。同步通知结果，仅作为支付结束的通知。
                 */
                String resultInfo = payResult.getResult();// 同步返回需要验证的信息
                String resultStatus = payResult.getResultStatus();
                // 判断resultStatus 为9000则代表支付成功
                if (TextUtils.equals(resultStatus, "9000")) {
                    // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
                    showMessage("支付成功");
                    PayListenerUtils.getInstance().addSuccess();
                } else if (TextUtils.equals(resultStatus, "6001")) {
                    // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
                    showMessage("取消支付");
                    PayListenerUtils.getInstance().addCancel();
                } else {
                    // 该笔订单真实的支付结果，需要依赖服务端的异步通知。
                    showMessage("支付失败");
                    PayListenerUtils.getInstance().addError();
                }
                break;
            }
        }
        return false;
    });


    private void showMessage(String str) {
        ToastUtils.show(str);
    }

    /**根据订单类型判断所属大类
     * @param type
     * @return
     */
    public static int getBigType(String type){
        if(BOOK.equals(type)){
            return BOOK_LIST;
        }else if(MEMBERSHIP.equals(type) || SUPER_VIP.equals(type)){
            return CONSUMPTION_LIST;
        }else {
            return SERVICE_LIST;
        }
    }
}
