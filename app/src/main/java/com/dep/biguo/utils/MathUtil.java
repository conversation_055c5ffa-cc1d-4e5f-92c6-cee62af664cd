package com.dep.biguo.utils;

import com.biguo.utils.util.LogUtil;

import java.math.BigDecimal;

public class MathUtil {
    /**字符串转整型加法
     */
    public static int StringAddInt(String arg1, String arg2){
        return parseInt(arg1) + parseInt(arg2);
    }

    /**字符串转整型减法
     */
    public static int StringSubInt(String arg1, String arg2){
        return parseInt(arg1) - parseInt(arg2);
    }

    /**字符串转整型乘法
     */
    public static int StringMulInt(String arg1, String arg2){
        return parseInt(arg1) * parseInt(arg2);
    }

    /**字符串转整型除法
     */
    public static int StringDivInt(String arg1, String arg2){
        return parseInt(arg1) / parseInt(arg2);
    }

    /**字符串转浮点型加法
     */
    public static float StringAddFloat(String arg1, String arg2){
        return parseFloat(arg1).add(parseFloat(arg2)).floatValue();
    }

    /**字符串转浮点型减法
     */
    public static float StringSubFloat(String arg1, String arg2){
        return parseFloat(arg1).subtract(parseFloat(arg2)).floatValue();
    }

    /**字符串转浮点型乘法
     */
    public static float StringMulFloat(String arg1, String arg2){
        return parseFloat(arg1).multiply(parseFloat(arg2)).floatValue();
    }

    /**字符串转浮点型除法
     */
    public static float StringDivFloat(String arg1, String arg2){
        return parseFloat(arg1).divide(parseFloat(arg2)).floatValue();
    }

    public static float getParseFloat(String arg){
        try {
            return parseFloat(arg).floatValue();
        }catch (Exception e){
            return 0.0f;
        }
    }

    public static float getParseInt(String arg){
        try {
            return parseInt(arg);
        }catch (Exception e){
            return 0;
        }
    }

    /**字符串转浮点型
     */
    private static BigDecimal parseFloat(String arg){
        try {
            return new BigDecimal(arg);
        }catch (Exception e){
            return new BigDecimal("0.0");
        }
    }

    /**字符串转整型
     */
    private static int parseInt(String arg){
        try {
            return Integer.parseInt(arg);
        }catch (Exception e){
            return 0;
        }
    }
}
