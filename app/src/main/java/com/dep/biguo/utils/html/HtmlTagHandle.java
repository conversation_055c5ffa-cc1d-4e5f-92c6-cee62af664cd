package com.dep.biguo.utils.html;

import android.content.Context;
import android.text.Editable;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StyleSpan;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;

import org.xml.sax.XMLReader;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;

public class HtmlTagHandle implements Html.TagHandler {
    private Context context;
    // 存放标签所有已添加属性键值对
    private HashMap<String, TagBean> tagMap = new HashMap<>();

    public HtmlTagHandle(Context context) {
        this.context = context;
    }

    @Override
    public void handleTag(boolean opening, String tag, Editable output, XMLReader xmlReader) {
        if(!tag.equals(TagBean.TAG_SIZE) && !tag.equals(TagBean.TAG_TYPEFACE)) return;
        //读取所有标签的属性
        parseAttributes(xmlReader);
        //获取当前标签
        TagBean tagBean = tagMap.get(tag);
        if(tagBean == null) return;

        //把自定义标签出现位置和结束位置记录下来
        if(opening){
            tagBean.start = output.length();
            return;
        }else {
            tagBean.end = output.length();
        }

        //为自定义标签设置样式
        if (TagBean.TAG_SIZE.equals(tag)) {
            String sizeValue = (String) tagBean.attrMap.get(TagBean.TAG_ATTR_FONT_SIZE);
            int size = TextUtils.isEmpty(sizeValue) ? 14 : Integer.parseInt(sizeValue.replace("dp", ""));
            output.setSpan(new AbsoluteSizeSpan(DisplayHelper.dp2px(context, size)), tagBean.start, tagBean.end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        } else if (TagBean.TAG_TYPEFACE.equals(tag)) {
            String styleValue = (String) tagBean.attrMap.get(TagBean.TAG_ATTR_FONT_TYPEFACE);
            int style = TextUtils.isEmpty(styleValue) ? 0 : Integer.parseInt(styleValue);
            StyleSpan styleSpan = new StyleSpan(style);
            output.setSpan(styleSpan, tagBean.start, tagBean.end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    /**
     * 解析所有属性值
     *
     * @param xmlReader
     */
    private void parseAttributes(XMLReader xmlReader) {
        String tagName = parseTag(xmlReader);
        if(tagName == null) return;
        TagBean tag = tagMap.get(tagName);
        if(tag == null) {
            tag = new TagBean(tagName);
            tag.attrMap = new HashMap<>();
            tagMap.put(tagName, tag);
        }

        try {
            Object element = getDeclaredField(xmlReader, "theNewElement");
            if(element == null) return;

            Object atts = getDeclaredField(element, "theAtts");
            if(atts == null) return;

            Object dataObj = getDeclaredField(atts, "data");
            String[] data = dataObj == null ? null : (String[]) dataObj;//null强转String[]会崩溃，因此要先判断再强转
            if(data == null) return;

            Object obj = getDeclaredField(atts, "length");
            if(obj == null) return;
            int len = (Integer) obj;

            for (int i = 0; i < len; i++) {
                int keyIndex = i * 5 + 1;
                int valueIndex = i * 5 + 4;
                if(keyIndex < data.length && valueIndex < data.length) {
                    tag.attrMap.put(data[keyIndex], data[valueIndex]);
                }
            }
        } catch (Exception e) {
            LogUtil.e("dddd", "解析标签属性失败");
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
    }

    public String parseTag(final XMLReader xmlReader) {
        try {
            Object element = getDeclaredField(xmlReader, "theNewElement");
            if(element == null) return null;

            Object attsType = getDeclaredField(element, "theType");
            if(attsType == null) return null;

            Object tagName = getDeclaredField(attsType, "theName");
            return tagName instanceof String ? (String)tagName : null;//null强转String会崩溃，因此要先判断再强转
        } catch (Exception e) {
            LogUtil.e("dddd", "解析标签名失败");
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
        return null;
    }

    //反射获取指定字段的值
    public Object getDeclaredField(Object obj, String name) throws NoSuchFieldException, IllegalAccessException {
        Field field = obj.getClass().getDeclaredField(name);
        field.setAccessible(true);
        return field.get(obj);
    }
}