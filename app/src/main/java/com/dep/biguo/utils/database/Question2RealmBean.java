package com.dep.biguo.utils.database;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class Question2RealmBean extends RealmObject {
    @PrimaryKey private String key;//主键
    private String code;//课程代码
    private int mainType;//题库
    private int topic_type;//题型
    private String topic_type_name;//题型名称
    private String real_id;//真题ID
    private String chapter_id;//章节(小节)ID
    private String paper_id;//模拟试卷ID
    private long update_time;//最后一次更新时间戳

    private int id;//题目ID
    private String questionAsk;//问题
    private String A;//选项A
    private String B;//选项B
    private String C;//选项C
    private String D;//选项D
    private String E;//选项E
    private String F;//选项F
    private String G;//选项G
    private String correctOption;//参考答案
    private String explanation;//题目详解
    private int mark;//分数
    private String video_parse_cover;//封面图
    private String audio_url;//音频

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getQuestionAsk() {
        return questionAsk;
    }

    public void setQuestionAsk(String questionAsk) {
        this.questionAsk = questionAsk;
    }

    public String getA() {
        return A;
    }

    public void setA(String a) {
        A = a;
    }

    public String getB() {
        return B;
    }

    public void setB(String b) {
        B = b;
    }

    public String getC() {
        return C;
    }

    public void setC(String c) {
        C = c;
    }

    public String getD() {
        return D;
    }

    public void setD(String d) {
        D = d;
    }

    public String getE() {
        return E;
    }

    public void setE(String e) {
        E = e;
    }

    public String getF() {
        return F;
    }

    public void setF(String f) {
        F = f;
    }

    public String getG() {
        return G;
    }

    public void setG(String g) {
        G = g;
    }

    public String getCorrectOption() {
        return correctOption;
    }

    public void setCorrectOption(String correctOption) {
        this.correctOption = correctOption;
    }

    public int getMark() {
        return mark;
    }

    public void setMark(int mark) {
        this.mark = mark;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getTopic_type_name() {
        return topic_type_name;
    }

    public void setTopic_type_name(String topic_type_name) {
        this.topic_type_name = topic_type_name;
    }

    public String getReal_id() {
        return real_id;
    }

    public void setReal_id(String real_id) {
        this.real_id = real_id;
    }

    public String getChapter_id() {
        return chapter_id;
    }

    public void setChapter_id(String chapter_id) {
        this.chapter_id = chapter_id;
    }

    public String getPaper_id() {
        return paper_id;
    }

    public void setPaper_id(String paper_id) {
        this.paper_id = paper_id;
    }

    public long getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(long update_time) {
        this.update_time = update_time;
    }

    public String getVideo_parse_cover() {
        return video_parse_cover;
    }

    public void setVideo_parse_cover(String video_parse_cover) {
        this.video_parse_cover = video_parse_cover;
    }

    public String getAudio_url() {
        return audio_url;
    }

    public void setAudio_url(String audio_url) {
        this.audio_url = audio_url;
    }
}
