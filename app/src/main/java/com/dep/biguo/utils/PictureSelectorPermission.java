package com.dep.biguo.utils;

import android.Manifest;
import android.view.View;

import androidx.core.content.PermissionChecker;
import androidx.fragment.app.Fragment;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.LogUtil;
import com.hjq.toast.ToastUtils;
import com.luck.picture.lib.interfaces.OnPermissionsInterceptListener;
import com.luck.picture.lib.interfaces.OnRequestPermissionListener;

import java.util.ArrayList;
import java.util.List;

public class PictureSelectorPermission {
    public static OnPermissionsInterceptListener create(){
        return new OnPermissionsInterceptListener() {
            @Override
            public void requestPermission(Fragment fragment, String[] permissionArray, OnRequestPermissionListener call) {
                List<String> unRequestList = new ArrayList<>();
                for (String s : permissionArray) {
                    if (PermissionChecker.checkSelfPermission(fragment.getContext(), s) != PermissionChecker.PERMISSION_GRANTED) {
                        unRequestList.add(s);
                    }
                }

                if(unRequestList.size() > 0){
                    LogUtil.d("dddd", "申请");
                    requestPermissions(fragment, unRequestList.get(0), call);
                }else {
                    LogUtil.d("dddd", "onCll");
                    call.onCall(permissionArray, true);
                }
            }

            @Override
            public boolean hasPermissions(Fragment fragment, String[] permissionArray) {
                return false;
            }
        };
    }

    public static void requestPermissions(Fragment fragment, String permission, OnRequestPermissionListener call){
        new RequestPermissions.Builder(fragment)
                .setPermissions(permission, false)
                .setRequestContentText(getUnPermissionContent(permission))
                .setOnRequestResultListener(new RequestPermissions.OnRequestResultListener() {
                    @Override
                    public void onSuccess() {
                        call.onCall(new String[]{permission}, true);
                    }

                    @Override
                    public void onFailure(boolean isShowRequestContentDialog) {
                        if(!isShowRequestContentDialog) {
                            getUnPermission(fragment, permission, call);
                        }else {
                            ToastUtils.show(String.format("%s被拒绝", getUnPermissionContent(permission)));
                        }
                    }

                    @Override
                    public void onNotAsk(boolean isShowRequestContentDialog) {
                        if(!isShowRequestContentDialog) {
                            getUnPermission(fragment, permission, call);
                        }else {
                            ToastUtils.show(String.format("%s被拒绝", getUnPermissionContent(permission)));
                        }
                    }
                })
                .build();
    }

    public static String getUnPermissionContent(String permission){
        String permissionContent = null;
        if(Manifest.permission.CAMERA.equals(permission)) {
            permissionContent = "拍照需要申请相机权限";
        }else if(Manifest.permission.READ_EXTERNAL_STORAGE.equals(permission)){
            permissionContent = "选取图片需要申请存储权限";
        }
        return permissionContent;
    }

    public static void getUnPermission(Fragment fragment, String permission, OnRequestPermissionListener call){
        String permissionContent = null;
        if(Manifest.permission.CAMERA.equals(permission)) {
            permissionContent = "拍照需要相机权限，是否前往设置授予？";
        }else if(Manifest.permission.READ_EXTERNAL_STORAGE.equals(permission)){
            permissionContent = "选取图片需要存储权限，是否前往设置授予？";
        }
        new MessageDialog.Builder(fragment.getChildFragmentManager())
                .setTitle("权限被拒绝")
                .setContent(permissionContent)
                .setNegativeText("拒绝")
                .setPositiveText("前往")
                .setPositiveClickListener(v -> call.onCall(new String[]{permission}, false))
                .builder()
                .show();
    }
}
