package com.dep.biguo.utils.html;

import android.os.CountDownTimer;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ImageSpan;
import android.text.style.URLSpan;
import android.widget.TextView;

import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.SpannableUtil;

import java.lang.ref.WeakReference;

public class HtmlUtil {
    public static final String AUDIO_PLAYING = "question_audio_playing";
    public static final String AUDIO_PREPARE = "question_audio_prepare";

    public static Builder form(String html, int viewWidth){
        //若包含少量图片，要延迟一段时间毫秒，否则即使图片已获取到，但也无法显示出来
        return new Builder(TextUtils.isEmpty(html) ? "" : html, viewWidth, 50);
    }

    public static Builder form(String html, int viewWidth, int delay){
        //若包含大量图片，要设置刷新间隔，否则每获取成功一张图片，则会刷新一次，导致卡顿
        return new Builder(html, viewWidth, delay);
    }


    /**改变字体大小
     * @param str 需要改变字体大小的文字
     * @param size 字体大小，单位dp
     * @return 一段富文本
     */
    public static String addHtmlFontSizeTag(String str, int size){
        String startTag = String.format("<%s %s=\"%sdp\">", TagBean.TAG_SIZE, TagBean.TAG_ATTR_FONT_SIZE, size);
        String endTag = String.format("</%s>", TagBean.TAG_SIZE);
        return String.format("%s%s%s", startTag, str, endTag);
    }


    /**<p>添加设置字体粗体或斜体的自定义标签</p>
     * <p>注：需要设置TextView的Style为细体才有效果如在xml中设置：style="@style/lightText",
     * 在@style/lightText设置了android:fontFamily=“sans-serif”和android:textStyle=“normal”属性</p>
     *
     * @param str 需要改变字体粗细或斜体的文字
     * @param typeface 粗细、斜体
     * @return 一段富文本
     */
    public static String addHtmlFontStroke(String str, @TagBean.Style int typeface){
        String startTag = String.format("<span><%s %s=\"%s\">", TagBean.TAG_TYPEFACE, TagBean.TAG_ATTR_FONT_TYPEFACE, typeface);
        String endTag = String.format("</%s></span>", TagBean.TAG_TYPEFACE);
        return String.format("%s%s%s", startTag, str, endTag);
    }

    public static class Builder{
        private String html;
        private WeakReference<TextView> targetReference;
        private int viewWidth;
        private OnSpanClickListener onImageClickListener;
        private OnSpanClickListener onUrlClickListener;
        private CountDownTimer timer;

        private GetterImage getterImage;
        private HtmlTagHandle htmlTagHandle;

        public Builder(String html, int viewWidth, int delay) {
            this.html = html;
            this.viewWidth = viewWidth;
            timer = new CountDownTimer(delay, delay) {
                @Override
                public void onTick(long millisUntilFinished) {

                }

                @Override
                public void onFinish() {
                    refresh();
                }
            };
        }

        public Builder setOnImageClickListener(OnSpanClickListener onImageClickListener) {
            this.onImageClickListener = onImageClickListener;
            return this;
        }

        public Builder setOnUrlClickListener(OnSpanClickListener onUrlClickListener) {
            this.onUrlClickListener = onUrlClickListener;
            return this;
        }

        public void setTargetView(TextView targetView) {
            if(targetReference == null) {
                //弱引用保存控件
                targetReference = new WeakReference<>(targetView);
                //创建解析Html的图片加载器
                getterImage = new GetterImage(targetView.getContext(), viewWidth, targetView.getLineHeight(), (url, drawable, isRefresh) -> {
                    if(isRefresh) {
                        refresh();

                    }else {
                        //利用定时器刷新，防止打开押密电子文档加载很多图片时导致卡顿
                        timer.cancel();
                        timer.start();
                    }
                });
                //创建解析Html自定义标签的解析器
                htmlTagHandle = new HtmlTagHandle(targetView.getContext());
            }

            refresh();
        }

        //加载图片后刷新一下
        private void refresh(){
            Spanned spanned = Html.fromHtml(html, getterImage, htmlTagHandle);
            SpannableStringBuilder ssb = new SpannableStringBuilder(spanned);

            URLSpan[] urlSpans = ssb.getSpans(0, ssb.length(), URLSpan.class);
            for (int index = 0; index < urlSpans.length; index++) {
                URLSpan urlSpan = urlSpans[index];
                int start = ssb.getSpanStart(urlSpan);
                int end = ssb.getSpanEnd(urlSpan);
                int flag = ssb.getSpanFlags(urlSpan);

                //添加点击事件
                String url = urlSpan.getURL();
                HtmlSpanClickableSpan clickableSpan = new HtmlSpanClickableSpan(url, index, onUrlClickListener);
                ssb.setSpan(clickableSpan, start, end, flag);
            }

            ImageSpan[] imageSpans = ssb.getSpans(0, ssb.length(), ImageSpan.class);
            for (int index = 0; index < imageSpans.length; index++) {
                ImageSpan imageSpan = imageSpans[index];
                int start = ssb.getSpanStart(imageSpan);
                int end = ssb.getSpanEnd(imageSpan);
                int flag = ssb.getSpanFlags(imageSpan);

                //使用自定义的ImageSpan替换，使得文字和图片中心对齐
                AlignCenterImageSpan centerImageSpan = new AlignCenterImageSpan(imageSpan.getDrawable());
                ssb.setSpan(centerImageSpan, start, end, flag);

                //添加点击事件
                if(onImageClickListener != null){
                    String imageUrl = imageSpan.getSource();
                    HtmlSpanClickableSpan clickableSpan = new HtmlSpanClickableSpan(imageUrl, index, onImageClickListener);
                    ssb.setSpan(clickableSpan, start, end, flag);
                }
            }

            TextView textView = targetReference.get();
            if(textView != null) {
                textView.setText(ssb);
                if(imageSpans.length > 0 && onImageClickListener != null || urlSpans.length > 0 && onUrlClickListener != null) {
                    textView.setMovementMethod(LinkMovementMethod.getInstance());
                }else {
                    textView.setMovementMethod(null);
                }
            }
        }
    }
}
