package com.dep.biguo.utils.image;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.DrawableRes;
import androidx.appcompat.content.res.AppCompatResources;

import android.widget.TextView;

import com.biguo.utils.util.TintDrawableUtil;

public class TextDrawableLoader {

    /**加载本地图片到TextView的左边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadLeft(Context context, TextView textView, @DrawableRes int resId){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(drawable,null,null,null);
    }

    /**加载本地图片到TextView的左边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadLeft(Context context, TextView textView, @DrawableRes int resId, @ColorInt int tint){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(TintDrawableUtil.TintDrawable(drawable, tint),null,null,null);
    }


    /**加载本地图片到TextView的右边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadRight(Context context, TextView textView, @DrawableRes int resId){
        Drawable drawable = context.getDrawable(resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,null, drawable,null);
    }

    /**加载本地图片到TextView的右边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadRight(Context context, TextView textView, @DrawableRes int resId, @ColorInt int tint){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,null, TintDrawableUtil.TintDrawable(drawable, tint),null);
    }

    /**加载本地图片到TextView的上边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadTop(Context context, TextView textView, @DrawableRes int resId){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,drawable,null,null);
    }

    /**加载本地图片到TextView的上边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadTop(Context context, TextView textView, @DrawableRes int resId, @ColorInt int tint){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,TintDrawableUtil.TintDrawable(drawable, tint),null,null);
    }

    /**加载本地图片到TextView的下边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadBottom(Context context, TextView textView, @DrawableRes int resId){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,null,null,drawable);
    }

    /**加载本地图片到TextView的下边
     * @param context 上下文
     * @param textView textView
     * @param resId 本地图片的资源ID
     */
    public static void loadBottom(Context context, TextView textView, @DrawableRes int resId, @ColorInt int tint){
        Drawable drawable = AppCompatResources.getDrawable(context, resId);
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        textView.setCompoundDrawablesRelative(null,null,null,TintDrawableUtil.TintDrawable(drawable, tint));
    }

    public static void load(Context context, TextView textView, @DrawableRes int[] resId){
        Drawable[] drawables = new Drawable[4];
        for(int i=0;i<resId.length;i++) {
            if(resId[i] > 0){
                drawables[i] = AppCompatResources.getDrawable(context, resId[i]);
                drawables[i].setBounds(0, 0, drawables[i].getMinimumWidth(), drawables[i].getMinimumHeight());
            }
        }
        textView.setCompoundDrawablesRelative(drawables[0], drawables[1], drawables[2], drawables[3]);
    }
}
