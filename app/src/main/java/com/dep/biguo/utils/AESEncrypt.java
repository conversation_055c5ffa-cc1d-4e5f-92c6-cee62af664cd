package com.dep.biguo.utils;

import android.util.Base64;

import com.biguo.utils.util.LogUtil;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.SocketException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Random;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESEncrypt {

    /**
     * 加密算法
     */
    private static final String KEY_ALGORITHM = "AES";

    /**
     * AES 的 密钥长度，32 字节，范围：16 - 32 字节
     */
    public static final int SECRET_KEY_LENGTH = 16;

    /**
     * 字符编码
     */
    private static final Charset CHARSET_UTF8 = StandardCharsets.UTF_8;

    /**
     * 秘钥长度不足 16 个字节时，默认填充位数
     */
    private static final String DEFAULT_VALUE = "0";
    /**
     * 加解密算法/工作模式/填充方式
     */
    private static final String CIPHER_ALGORITHM_ECB = "AES/ECB/PKCS7Padding";
    private static final String CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS7Padding";

    /**随机生成一个key
     * @return key
     */
    public static String randomKey(){
        StringBuilder builder = new StringBuilder();
        Random random = new Random(System.currentTimeMillis());
        while (builder.length() < 16){
            int randomValue = random.nextInt(26);
            if(random.nextInt(2) % 2 == 0) {
                builder.append((char) (randomValue + 65));
            }else {
                builder.append((char) (randomValue + 97));
            }
        }
        return builder.toString();
    }

    /**
     * AES 加密
     *
     * @param data      待加密内容
     * @param secretKey 加密密码，长度：16 或 32 个字符
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String data, String secretKey) {
        try {
            //创建密码器
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_ECB);
            //初始化为加密密码器
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(secretKey));
            byte[] encryptByte = cipher.doFinal(data.getBytes(CHARSET_UTF8));
            // 将加密以后的数据进行 Base64 编码
            String encrypt = Base64.encodeToString(encryptByte, Base64.DEFAULT);
            return encrypt;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
        return null;
    }

    /**
     * AES 解密
     *
     * @param base64Data 加密的密文 Base64 字符串
     */
    public static String decrypt(String base64Data, String secretKey) {
        try {
            byte[] data = Base64.decode(base64Data, Base64.NO_WRAP);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_ECB);
            //设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(secretKey));
            //执行解密操作
            byte[] result = cipher.doFinal(data);
            return new String(result, CHARSET_UTF8);
        } catch (Exception e) {
            e.printStackTrace();
            //LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
        return null;
    }

    /**
     * 对文件进行AES加密
     * @param path       待加密文件路径
     * @param dir        加密后的文件存储路径
     * @param toFileName 加密后的文件名称
     * @param secretKey  密钥
     * @return 加密后的文件
     */
    public static File encryptFile(String path, String dir, String toFileName, String secretKey) {
        try {
            //打开流
            FileInputStream fileInputStream = new FileInputStream(path);
            //初始化 Cipher
            Cipher cipher = initFileAESCipher(secretKey, Cipher.ENCRYPT_MODE);
            //创建一个加密输入流
            CipherInputStream cipherInputStream = new CipherInputStream(fileInputStream, cipher);
            //创建加密后的文件
            File encryptFile = new File(dir, toFileName);
            //根据文件创建输出流
            FileOutputStream outputStream = new FileOutputStream(encryptFile);
            //创建缓存字节数组
            byte[] buffer = new byte[8*1024];
            //读取长度
            int len;
            //写入文件
            while ((len = cipherInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
                outputStream.flush();
            }
            //关闭流
            cipherInputStream.close();
            closeStream(outputStream);
            return encryptFile;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
        return null;
    }

    /**
     * 对网络图片进行AES解密
     * @param url        链接
     * @param dir        加密后的文件存储路径
     * @param toFileName 加密后的文件名称
     * @param secretKey  密钥
     * @return 加密后的文件
     */
    public static File decryptImage(String url, String dir, String toFileName, String secretKey) throws Exception {
        try {
            //打开流
            InputStream encryptedStream = new URL(url).openStream();
            //初始化 Cipher
            Cipher cipher = initFileAESCipher(secretKey, Cipher.DECRYPT_MODE);
            //创建一个加密输入流
            CipherInputStream cipherInputStream = new CipherInputStream(encryptedStream, cipher);
            //创建解密后的文件
            File file = new File(dir, toFileName);
            //根据文件创建输出流
            FileOutputStream outputStream = new FileOutputStream(file);
            //创建缓存字节数组
            byte[] buffer = new byte[8*1024];
            //读取长度
            int len;
            //写入文件
            while ((len = cipherInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
                outputStream.flush();
            }
            //关闭流
            outputStream.close();
            cipherInputStream.close();

            return file;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
            throw new SocketException();
        }
    }


    /**
     * 初始化 AES Cipher
     *
     * @param secretKey  密钥
     * @param cipherMode 加密模式
     * @return 密钥
     */
    private static Cipher initFileAESCipher(String secretKey, int cipherMode) {
        try {
            // 创建密钥规格
            SecretKeySpec secretKeySpec = getSecretKey(secretKey);
            // 获取密钥
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
            // 初始化
            cipher.init(cipherMode, secretKeySpec, new IvParameterSpec(new byte[cipher.getBlockSize()]));
            return cipher;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
        return null;
    }

    /**
     * 关闭流
     *
     * @param closeable 实现Closeable接口
     */
    private static void closeStream(Closeable closeable) {
        try {
            if (closeable != null) closeable.close();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
    }

    /**
     * 使用密码获取 AES 秘钥
     */
    public static SecretKeySpec getSecretKey(String secretKey) {
        secretKey = toMakeKey(secretKey);
        return new SecretKeySpec(secretKey.getBytes(CHARSET_UTF8), KEY_ALGORITHM);
    }

    /**
     * 如果 AES 的密钥小于 {@link AESEncrypt#SECRET_KEY_LENGTH } 的长度，就对秘钥进行补位，保证秘钥安全。
     *
     * @param secretKey 密钥 key
     * @return 密钥
     */
    private static String toMakeKey(String secretKey) {
        // 获取密钥长度
        int strLen = secretKey.length();
        // 判断长度是否小于应有的长度
        if (strLen < AESEncrypt.SECRET_KEY_LENGTH) {
            // 补全位数
            StringBuilder builder = new StringBuilder();
            // 将key添加至builder中
            builder.append(secretKey);
            // 遍历添加默认文本
            for (int i = 0; i < AESEncrypt.SECRET_KEY_LENGTH - strLen; i++) {
                builder.append(AESEncrypt.DEFAULT_VALUE);
            }
            // 赋值
            secretKey = builder.toString();
        }
        return secretKey;
    }

}
