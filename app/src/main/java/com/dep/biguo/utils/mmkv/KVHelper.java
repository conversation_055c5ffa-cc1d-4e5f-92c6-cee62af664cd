package com.dep.biguo.utils.mmkv;

import android.content.Context;

import com.tencent.mmkv.MMKV;

public class KVHelper {

    public static String init(Context context) {
        return MMKV.initialize(context);
    }

    /**
     * 新增一条键值对
     *
     * @param key   键
     * @param value 值
     */
    public static void putValue(String key, Object value) {
        if (value instanceof Boolean) {
            MMKV.defaultMMKV().encode(key, (Boolean) value);
        } else if (value instanceof Integer) {
            MMKV.defaultMMKV().encode(key, (Integer) value);
        } else if (value instanceof Long) {
            MMKV.defaultMMKV().encode(key, (Long) value);
        } else if (value instanceof Float) {
            MMKV.defaultMMKV().encode(key, (Float) value);
        } else if (value instanceof Double) {
            MMKV.defaultMMKV().encode(key, (Double) value);
        } else if (value instanceof String) {
            MMKV.defaultMMKV().encode(key, (String) value);
        }
    }

    public static String[] getAllKey(){
        return MMKV.defaultMMKV().allKeys();
    }

    /**
     * 根据键去删除值
     *
     * @param key 键
     */
    public static void removeValue(String key) {
        MMKV.defaultMMKV().removeValueForKey(key);
    }

    /**
     * 根据多个键去删除多个值
     *
     * @param keys
     */
    public static void removeValues(String... keys) {
        MMKV.defaultMMKV().removeValuesForKeys(keys);
    }

    public static boolean getBoolean(String key) {
        return MMKV.defaultMMKV().decodeBool(key, false);
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        return MMKV.defaultMMKV().decodeBool(key, defaultValue);
    }

    public static int getInt(String key) {
        return MMKV.defaultMMKV().decodeInt(key);
    }

    public static boolean containsKey(String key){
        return MMKV.defaultMMKV().containsKey(key);
    }

    public static float getFloat(String key) {
        return MMKV.defaultMMKV().decodeFloat(key);
    }

    public static double getDouble(String key) {
        return MMKV.defaultMMKV().decodeDouble(key);
    }

    public static long getLong(String key) {
        return MMKV.defaultMMKV().decodeLong(key);
    }

    public static String getString(String key) {
        return MMKV.defaultMMKV().decodeString(key);
    }

}
