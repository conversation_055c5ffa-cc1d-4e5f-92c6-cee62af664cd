package com.dep.biguo.utils.database.rxjava;

import io.reactivex.annotations.NonNull;
import io.reactivex.functions.Function;
import io.realm.Realm;

public abstract class RealmFunction<T, R> implements Function<T, R> {
    private boolean beginTransaction;

    public RealmFunction() {

    }

    public RealmFunction(boolean beginTransaction) {
        this.beginTransaction = beginTransaction;
    }

    @Override
    public R apply(T t) throws Exception {
        Realm realm = Realm.getDefaultInstance();
        if(beginTransaction){
            realm.beginTransaction();
        }
        R r = apply(t, realm);
        if(realm.isInTransaction()){
            realm.commitTransaction();
        }
        realm.close();
        return r;
    }

    public abstract R apply(T t, @NonNull Realm realm) throws Exception;
}
