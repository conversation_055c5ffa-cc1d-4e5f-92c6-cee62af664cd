package com.dep.biguo.utils;

import android.util.Base64;

import com.biguo.utils.util.LogUtil;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Map;

import javax.crypto.Cipher;

public class RSAEncrypt {
    /**
     * RSA算法
     */
    private static final String RSA = "RSA";
    /**加密方式，android的*/
    public static final String TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    private static String KEY = "MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgGTEwG8/" +
            "vNTAcf/HaJMvLRgTqgcYd82tgu7AU+O6tw6dHPCC4tM5B7dKRQ2ATZy8oYFBzv4tNkyIT1xp3sVbMC5vd" +
            "+4BCmf2MkpbqEPxROQZYorv4fCeNTFd87kBO5OH839otnkDhm5dmRAO835gmBJ3Y7ir65RTjY/BRGhqlrXTAgMBAAE=";


    private final static String HEX = "0123456789ABCDEF";

    /**
     * 二进制转(16进制)字符串
     */
    private static String ByteToHex(byte[] bytesKey) {
        if (bytesKey == null)
            return "";
        StringBuilder sb = new StringBuilder();
        for (byte b : bytesKey) {
            sb.append(HEX.charAt((b >> 4) & 0x0f)).append(HEX.charAt(b & 0x0f));
        }
        return sb.toString();
    }

    /**
     * 公钥加密
     */
    public static String encryptByPublicKey(String data) throws GeneralSecurityException {
        byte[] keyBytes = Base64.decode(KEY, Base64.DEFAULT);

        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);

        byte[] mi = cipher.doFinal(data.getBytes());
        return Base64.encodeToString(mi, Base64.DEFAULT);
    }

    /**私钥解密
     */
    public static String decryptByPrivateKey(String encryptedData, String privateKey) throws Exception {
        byte[] encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT);
        byte[] privateKeyBytes = Base64.decode(privateKey, Base64.DEFAULT);

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA);
        PrivateKey privateKeyObj = keyFactory.generatePrivate(keySpec);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, privateKeyObj);

        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes);
    }

    /**公钥解密
     * @param encryptedData 已加密的数据
     * @return 解密的数据
     * @throws Exception
     */
    public static String decryptByPublicKey(String encryptedData) throws Exception{
        // 假设您已经从文件中读取了私钥并将其存储在privateKeyBytes变量中
        byte[] publicKeyBytes = Base64.decode(KEY, Base64.DEFAULT);

        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA);
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, publicKey);

        byte[] decryptedData = cipher.doFinal(Base64.decode(encryptedData, Base64.DEFAULT));
        return new String(decryptedData);
    }

    /**
     * 随机生成密钥对
     */
    public static String[] genKeyPair() throws NoSuchAlgorithmException {
        // 将公钥和私钥保存到Map
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA);
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 获取私钥和公钥
        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();

        // 将PKCS#8格式的私钥和公钥进行Base64编码
        String base64EncodedPrivateKey = Base64.encodeToString(privateKey.getEncoded(), Base64.DEFAULT);
        String base64EncodedPublicKey = Base64.encodeToString(publicKey.getEncoded(), Base64.DEFAULT);

        return new String[]{base64EncodedPublicKey, base64EncodedPrivateKey};
    }
}