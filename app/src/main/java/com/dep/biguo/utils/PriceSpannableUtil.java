package com.dep.biguo.utils;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import androidx.core.content.ContextCompat;
import com.dep.biguo.R;

/**
 * 价格SpannableString工具类
 * 用于统一处理商品价格的样式显示
 */
public class PriceSpannableUtil {

    /**
     * 创建价格SpannableString
     * @param context 上下文
     * @param currentPrice 当前价格
     * @param originalPrice 原价（可为null）
     * @return SpannableString
     */
    public static SpannableString createPriceSpannable(Context context, String currentPrice, String originalPrice) {
        String currentPriceText = "¥" + currentPrice;
        
        if (originalPrice != null && !originalPrice.isEmpty()) {
            try {
                float original = Float.parseFloat(originalPrice);
                float current = Float.parseFloat(currentPrice);
                
                if (original > current) {
                    // 有优惠价格，显示优惠价和原价
                    String originalPriceText = "¥" + originalPrice;
                    String fullPriceText = currentPriceText + " " + originalPriceText;
                    
                    SpannableString spannableString = new SpannableString(fullPriceText);
                    
                    // 设置优惠价颜色为红色
                    int redColor = ContextCompat.getColor(context, R.color.price_red);
                    spannableString.setSpan(new ForegroundColorSpan(redColor), 0, currentPriceText.length(), 0);
                    
                    // 设置原价颜色为灰色并添加删除线
                    int grayColor = ContextCompat.getColor(context, R.color.tblack3);
                    int originalPriceStart = currentPriceText.length() + 1;
                    spannableString.setSpan(new ForegroundColorSpan(grayColor), originalPriceStart, fullPriceText.length(), 0);
                    spannableString.setSpan(new StrikethroughSpan(), originalPriceStart, fullPriceText.length(), 0);
                    
                    return spannableString;
                }
            } catch (NumberFormatException e) {
                // 解析失败，只显示当前价格
            }
        }
        
        // 没有优惠或解析失败，只显示当前价格
        SpannableString spannableString = new SpannableString(currentPriceText);
        int redColor = ContextCompat.getColor(context, R.color.price_red);
        spannableString.setSpan(new ForegroundColorSpan(redColor), 0, currentPriceText.length(), 0);
        
        return spannableString;
    }
    
    /**
     * 创建简单的价格SpannableString（仅用于演示）
     * @param salePrice 优惠价
     * @param originalPrice 原价
     * @return SpannableString
     */
    public static SpannableString createSimplePriceSpannable(String salePrice, String originalPrice) {
        SpannableString spannableString = new SpannableString(salePrice + originalPrice);

        // 设置优惠价颜色为红色
        spannableString.setSpan(new ForegroundColorSpan(Color.RED), 0, salePrice.length(), 0);

        // 设置原价颜色为灰色并添加删除线
        spannableString.setSpan(new ForegroundColorSpan(Color.GRAY), salePrice.length(), spannableString.length(), 0);
        spannableString.setSpan(new StrikethroughSpan(), salePrice.length(), spannableString.length(), 0);

        return spannableString;
    }
}