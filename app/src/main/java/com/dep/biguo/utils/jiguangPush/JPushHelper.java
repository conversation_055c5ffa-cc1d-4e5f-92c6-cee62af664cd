package com.dep.biguo.utils.jiguangPush;

import android.content.Context;
import android.text.TextUtils;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;

import org.simple.eventbus.EventBus;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.JPushMessage;
import cn.jpush.android.service.JPushMessageReceiver;

public class JPushHelper{
    public static final String USER_ALIAS_TYPE = "USER";//别名类别
    public static final String USER_ALIAS = "user=";//用户别名

    public static final String PROVINCE_TAG = "province=";//省份标签
    public static final String SCHOOL_TAG = "school=";//学校标签
    public static final String PROFESSIONS_TAG = "professions=";//专业标签
    public static final String GRADE_TAG = "grade=";//证书标签
    public static final String USER_TAG = "users=";//购买商品类型标签
    public static final String COURSE_TAG = "courses=";//课程记录
    public static final String REFUND_TAG = "refund=";//申请退款时间的标签
    public static final String REGISTER_TAG = "register=";//注册时间标签

    public static final int SUCCESS = 0;//别名、标签操作成功

    public static int sequence = 0;
    public static Map<Integer, JPushMessageReceiver> sequenceMap = new HashMap<>();
    public static JPushMessageReceiver  getSequenceAction(int sequence){
        return sequenceMap.remove(sequence);
    }
    public static int getSequence(JPushMessageReceiver  action){
        sequence = sequence + 1;
        sequenceMap.put(sequence, action);
        return sequence;
    }

    public static int getListType(String orderType) {
        if(PayUtils.BOOK.equals(orderType)){
            return PayUtils.BOOK_LIST;
        }else if(PayUtils.MEMBERSHIP.equals(orderType) || PayUtils.SUPER_VIP.equals(orderType)){
            return PayUtils.SERVICE_LIST;
        }else {
            return PayUtils.CONSUMPTION_LIST;
        }
    }

    public static String getRegistrationID(Context context){
        return JPushInterface.getRegistrationID(context);
    }

    public static void setLoginAlias(Context context) {
        if(UserCache.getUserCache() == null) return;

        JPushInterface.setAlias(context, getSequence(new JPushMessageReceiver () {
            @Override
            public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS) {
                    LogUtil.d("dddd", "添加设备别名成功："+jPushMessage.getAlias());
                }
            }
        }), USER_ALIAS + UserCache.getUserCache().getUser_id());
    }

    public static void setLoginTag(Context context, List<String> tags) {
        if(UserCache.getUserCache() == null) return;
        String date = UserCache.getUserCache().getCreated_at();
        if(TextUtils.isEmpty(date) || tags == null) return;

        Set<String> set = new HashSet<>();
        set.add(REGISTER_TAG + date.substring(0, date.indexOf("-")));
        set.add(REGISTER_TAG + date.substring(0, date.lastIndexOf("-")));
        set.add(REGISTER_TAG + date);

        JPushInterface.addTags(context, getSequence(new JPushMessageReceiver () {
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS) {
                    LogUtil.d("dddd", "添加登录标签成功："+jPushMessage.getTags());
                }
            }
        }), set);
    }

    public static void deleteLoginAlias(Context context, int userId) {
        JPushInterface.deleteAlias(context,getSequence(new JPushMessageReceiver () {
            @Override
            public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS){
                    LogUtil.d("dddd", "删除设备别名成功："+jPushMessage.getAlias());
                }
            }
        }));
        deleteLoginTag(context);
    }

    public static void deleteLoginTag(Context context) {
        JPushInterface.getAllTags(context, getSequence(new JPushMessageReceiver (){
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                Set<String> deleteTags = new HashSet<>();
                for(String tag : jPushMessage.getTags()){
                    if(!tag.startsWith(JPushHelper.PROVINCE_TAG) && !tag.startsWith(JPushHelper.SCHOOL_TAG) && !tag.startsWith(JPushHelper.PROFESSIONS_TAG) && !tag.startsWith(JPushHelper.GRADE_TAG)){
                        deleteTags.add(tag);
                    }
                }
                deleteTag(context, new JPushMessageReceiver () {
                    @Override
                    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                        if(jPushMessage.getErrorCode() == SUCCESS) {
                            LogUtil.d("dddd", "删除登录标签成功："+jPushMessage.getTags());
                        }
                    }
                }, deleteTags.toArray(new String[0]));
            }
        }));
    }

    public static void deleteTag(Context context, JPushMessageReceiver  receiver, String... startWithArray) {
        JPushInterface.getAllTags(context, getSequence(new JPushMessageReceiver (){
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                //获取到的标签数量是0，则不需要剔除标签
                if(jPushMessage.getTags().size() == 0){
                    if(receiver != null) {
                        receiver.onTagOperatorResult(context, jPushMessage);
                    }
                    return;
                }

                //记录删除的数量
                int deleteCount = 0;
                //获取到的标签存在要删除的标签集合里，则取出来
                Set<String> deleteSet = new HashSet<>();
                for(String tag : jPushMessage.getTags()){
                    for (String startWidth : startWithArray){
                        if(tag.startsWith(startWidth)) {
                            deleteSet.add(tag);
                            deleteCount++;
                            break;
                        }
                    }
                    //当删除的数量等于匹配集合的数量，则认为全部删除了，结束循环
                    if(deleteCount == startWithArray.length) break;
                }
                JPushInterface.deleteTags(context, getSequence(new JPushMessageReceiver () {
                    @Override
                    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                        if(jPushMessage.getErrorCode() == SUCCESS) {
                            LogUtil.d("dddd", "删除标签成功："+jPushMessage.getTags());
                        }
                    }
                }), deleteSet);
            }
        }));
    }

    public static void getTags(Context context) {
        JPushInterface.getAllTags(context, getSequence(new JPushMessageReceiver () {
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS) {
                    LogUtil.d("dddd", "获取所有标签成功：" + GsonUtils.toJson(jPushMessage.getTags()));
                }
            }
        }));
    }

    public static void addExamInfoTag(Context context) {
        if(UserCache.getProvince() == null || UserCache.getSchool() == null || UserCache.getProfession() == null){
            return;
        }

        deleteTag(context, new JPushMessageReceiver (){
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                //追加新的标签
                Set<String> tags = new HashSet<>();
                tags.add(PROVINCE_TAG + UserCache.getProvince().getId());
                tags.add(SCHOOL_TAG + UserCache.getSchool().getId());
                tags.add(PROFESSIONS_TAG + UserCache.getProfession().getId());
                JPushInterface.addTags(context, getSequence(new JPushMessageReceiver (){
                    @Override
                    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                        if(jPushMessage.getErrorCode() == SUCCESS) {
                            LogUtil.d("dddd", "专业信息标签添加成功:"+jPushMessage.getTags());
                            EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
                        }
                    }
                }), tags);
            }
        });
    }

    public static void addGradeTag(Context context, String type) {
        if(TextUtils.isEmpty(type)) return;

        Set<String> tags = new HashSet<>();
        tags.add(GRADE_TAG + type);
        //追加新的标签
        JPushInterface.addTags(context, getSequence(new JPushMessageReceiver (){
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS) {
                    LogUtil.d("dddd", "添加证书类型标签成功："+jPushMessage.getTags());
                    EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
                }
            }
        }), tags);
    }

    public static void addBuyGoodTag(Context context, String type, String code) {
        Set<String> tags = new HashSet<>();
        tags.add(USER_TAG+type);
        //具有课程代码的商品才需要注册COURSE_TAG标签
        if(PayUtils.VIP.equals(type) || PayUtils.YAMI.equals(type) || PayUtils.VIDEO.equals(type) ||
                PayUtils.YAMI_RESERVE.equals(type) || PayUtils.REAL_PAPER.equals(type) ||
                PayUtils.CHAPTER.equals(type)) {
            tags.add(COURSE_TAG+code+"_"+type);
        }
        JPushInterface.addTags(context, getSequence(new JPushMessageReceiver (){
            @Override
            public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
                if(jPushMessage.getErrorCode() == SUCCESS) {
                    LogUtil.d("dddd", "添加购买的商品的标签成功："+jPushMessage.getTags());
                    EventBus.getDefault().post(tags, EventBusTags.ADD_PUSH_TAG);
                }else {
                    LogUtil.d("dddd", "打标签失败："+jPushMessage.getErrorCode());
                }
            }
        }), tags);
    }
}
