package com.dep.biguo.utils.jiguangPush;

import android.content.Context;
import android.content.Intent;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.PushBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.CKMainActivity;
import com.dep.biguo.mvp.ui.activity.FeedbackListActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.JSZMainActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.MessageListActivity;
import com.dep.biguo.mvp.ui.activity.MyActivity;
import com.dep.biguo.mvp.ui.activity.MyCouponActivity;
import com.dep.biguo.mvp.ui.activity.MyVideoActivity;
import com.dep.biguo.mvp.ui.activity.OrderDetailActivity;
import com.dep.biguo.mvp.ui.activity.PayTuitionHistoryActivity;
import com.dep.biguo.mvp.ui.activity.RechargeDetailActivity;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.integration.AppManager;

import org.json.JSONObject;

import cn.jpush.android.api.JPushMessage;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.service.JPushMessageReceiver;

public class JiguangPushMessageReceiver extends JPushMessageReceiver {
    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage notificationMessage) {
        super.onNotifyMessageArrived(context, notificationMessage);
        String title = notificationMessage.notificationTitle;
        LogUtil.d("dddd", " title : " + title);
        String message = notificationMessage.notificationContent;
        LogUtil.d("dddd", "message : " + message);
        String extras = notificationMessage.notificationExtras;
        LogUtil.d("dddd", "extras : " + extras);
    }

    @Override
    public void onNotifyMessageOpened(Context context, NotificationMessage notificationMessage) {
        super.onNotifyMessageOpened(context, notificationMessage);
        LogUtil.d("dddd", notificationMessage);
        Intent mainIntent = null;
        if(AppManager.getAppManager().getCurrentActivity() == null){
            if(Constant.CK.equals(UserCache.getAppType())){
                mainIntent = new Intent(context, CKMainActivity.class);
            }else if(Constant.JSZ.equals(UserCache.getAppType())){
                mainIntent = new Intent(context, JSZMainActivity.class);
            }else{
                mainIntent = new Intent(context, MainActivity.class);
            }
            //无activity时，需要添加一个新的栈，否则热启动方式无法启动应用
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }

        String extras = notificationMessage.notificationExtras;
        PushBean extraBean;
        try {
            JSONObject extrasJson = new JSONObject(extras);
            extraBean = GsonUtils.fromJson(extrasJson.optString("extra"), PushBean.class);
        } catch (Exception e) {
            LogUtil.e("dddd", "Unexpected: extras is not a valid json", e);
            return;
        }
        if(PushBean.ORDER_PAGE.equals(extraBean.page)){
            //跳转到订单详情
            OrderDetailActivity.start(context,mainIntent, extraBean.order_id, getListType(extraBean.type));

        }else if(PushBean.INFO_CENTER.equals(extraBean.page)){
            //跳转到消息列表，根据type获取对应的消息
            MessageListActivity.start(context, mainIntent, Integer.parseInt(extraBean.type));

        }else if(PushBean.COURSE.equals(extraBean.page)){
            //跳转到我的题库，根据type选择题库的列表展示
            if(extraBean.type.equals("vip")) {
                MyActivity.start(context, mainIntent, 0);

            }else if(extraBean.type.equals("yami")){
                MyActivity.start(context, mainIntent, 1);

            }else if(extraBean.type.equals("video")){
                MyVideoActivity.start(context);

            }

        }else if(PushBean.FRUITCOIN.equals(extraBean.page)){
            //跳转到果币明细
            RechargeDetailActivity.start(context, mainIntent, RechargeDetailActivity.RECHARGE);

        }else if(PushBean.INTEGRAL.equals(extraBean.page)){
            //积分变动不需要打标签，后台会通过别名推送，因此此处也要做相应的跳转
            RechargeDetailActivity.start(context, mainIntent, RechargeDetailActivity.INTEGRAL);

        }else if(PushBean.COUPONS.equals(extraBean.page)){
            //跳转到优惠券页面
            MyCouponActivity.start(context, mainIntent);

        }else if(PushBean.URL.equals(extraBean.page)){
            //根据链接打开h5页面
            HtmlActivity.start(context, mainIntent, extraBean.url);

        }else if(PushBean.FEED_BACK.equals(extraBean.page)){
            //跳转到反馈列表
            FeedbackListActivity.start(context, mainIntent);

        }else if(PushBean.TUITION.equals(extraBean.page)){
            //跳转到缴费历史页面
            PayTuitionHistoryActivity.start(context, mainIntent);

        }
    }

    public int getListType(String orderType) {
        if(PayUtils.BOOK.equals(orderType)){
            return PayUtils.BOOK_LIST;
        }else if(PayUtils.MEMBERSHIP.equals(orderType) || PayUtils.SUPER_VIP.equals(orderType)){
            return PayUtils.SERVICE_LIST;
        }else {
            return PayUtils.CONSUMPTION_LIST;
        }
    }

    @Override
    public void onCheckTagOperatorResult(Context context, JPushMessage jPushMessage) {
        super.onCheckTagOperatorResult(context, jPushMessage);
    }

    @Override
    public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
        super.onAliasOperatorResult(context, jPushMessage);
        JPushMessageReceiver  receiver = JPushHelper.getSequenceAction(jPushMessage.getSequence());
        if(receiver != null){
            receiver.onAliasOperatorResult(context, jPushMessage);
        }
    }

    @Override
    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
        super.onTagOperatorResult(context, jPushMessage);
        JPushMessageReceiver  receiver = JPushHelper.getSequenceAction(jPushMessage.getSequence());
        if(receiver != null){
            receiver.onTagOperatorResult(context, jPushMessage);
        }
    }
}
