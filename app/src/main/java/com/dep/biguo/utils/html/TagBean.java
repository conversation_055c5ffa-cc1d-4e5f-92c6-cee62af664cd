package com.dep.biguo.utils.html;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Map;

public class TagBean {
    protected static final String TAG_SIZE = "size";//字体大小标签
    protected static final String TAG_ATTR_FONT_SIZE = "font-size";//字体大小属性名

    protected static final String TAG_TYPEFACE = "typeface";//字体粗细、斜体标签
    protected static final String TAG_ATTR_FONT_TYPEFACE = "font-typeface";//字体粗细、斜体标签属性名

    protected String tag;
    protected int start;
    protected int end;
    protected Map<String, Object> attrMap;

    protected TagBean(String tag) {
        this.tag = tag;
    }


    public static final int NORMAL = 0;
    public static final int BOLD = 1;
    public static final int ITALIC = 2;
    public static final int BOLD_ITALIC = 3;
    @IntDef(value = {NORMAL, BOLD, ITALIC, BOLD_ITALIC})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Style {}
}
