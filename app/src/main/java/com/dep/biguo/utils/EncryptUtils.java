package com.dep.biguo.utils;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

import timber.log.Timber;

public class EncryptUtils {

    private static final String encryptStr = "22279eb4a505455a2a54892e34b2fc10";

    public static String encrypt(Map<String, Object> params) {
        Set<String> keySet = params.keySet();
        Object[] keyArray = keySet.toArray();

        Arrays.sort(keyArray);

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < keyArray.length; i++) {
            Timber.d(keyArray[i] + " = " + params.get(keyArray[i]));
            sb.append(keyArray[i])
                    .append("=")
                    .append(params.get(keyArray[i]));
            if (i != keyArray.length - 1)
                sb.append("&");
        }

        sb.append(encryptStr);

        String encrypt = sb.toString();
        Timber.d("sb = " + encrypt);

        String md51 = MD5Util.md5(encrypt).toUpperCase();
        String md52 = MD5Util.md5(md51).toUpperCase();
        String md53 = MD5Util.md5(md52).toUpperCase();

        Timber.d("encrypt = " + md53);

        return md53;
    }

}
