package com.dep.biguo.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.webkit.WebView;

import com.biguo.utils.util.LogUtil;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.commonsdk.listener.OnGetOaidListener;

import java.util.Arrays;


/**获取推广广告的回传设备信息
 *参考 <a href="https://bytedance.feishu.cn/docs/doccnD51fTBCHJgKaFXcRYQcshd#">...</a> 文档
 */
public class ApiCallBack {
    /**获取UserAgent
     * @param context
     * @return
     */
    public static String getUserAgent(Context context){
        //return WebSettings.getDefaultUserAgent(context);
        return new WebView(context).getSettings().getUserAgentString();
    }

    public static void getOAID(Context context, OnGetOaidListener onGetOaidListener){
        UMConfigure.getOaid(context, onGetOaidListener);
    }

    @SuppressLint("HardwareIds")
    public static String getIMEI(Context context){
        try {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                return telephonyManager.getDeviceId();
            }else if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                telephonyManager.getImei();
            }
        }catch (Exception ignored) {

        }
        return "";
    }

    @SuppressLint("HardwareIds")
    public static String getAndroidId(Context context){
        try {
            return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        }catch (Exception ignored) {
            LogUtil.e("dddd", Arrays.toString(ignored.getStackTrace()), ignored);
        }
        return "";
    }

}
