package com.dep.biguo.utils;

import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;

/**加密流程
 * 1、随机一个字符串作为AES秘钥 {@link CipherUtil#randomEncryptAesKey()}
 * 2、用随机的AES秘钥加密数据 {@link CipherUtil#encryptWithParams(String)}
 * 3、用RSA公钥（公钥由后台提供，移动端写死在代码中）加密AES秘钥 {@link CipherUtil#getRandomEncryptAesKeyByRsa()}
 * 4、提交2、3步骤的加密数据
 *
 * 解密流程
 * 1、生成一对RSA秘钥 {@link CipherUtil#genKeyPair()}
 * 2、获取数据时，将生成的RSA公钥传递给服务器
 * 3、用生成的RSA私钥解密返回的数据中AES秘钥的密文 {@link CipherUtil#setServiceDecryptAesKey(String)}
 * 4、用解密后的AES秘钥解密数据 {@link CipherUtil#decryptWithParams(String)}
 *
 * */

public class CipherUtil {
    private String randomEncryptAesKey;//本地生成，用于加密数据
    private String serviceDecryptAesKey;//服务器返回，用于解密数据

    private String sendToServiceRsaPublicKey;//发送到服务器的AES公钥
    private String saveToLocalRsaPrivateKey;//留存在本地的AES私钥

    public CipherUtil(){
        randomEncryptAesKey();
        genKeyPair();
    }

    //随机一个AES的秘钥，用于加密提交的数据
    private void randomEncryptAesKey(){
        randomEncryptAesKey = AESEncrypt.randomKey();
    }

    //生成一对RSA秘钥，公钥传递给服务器，服务器利用这个公钥加密返回数据中的
    public void genKeyPair(){
        try {
            String[] keyPair = RSAEncrypt.genKeyPair();
            if(keyPair[0].startsWith("\n")){
                keyPair[0] = keyPair[0].substring(1);
            }
            if(keyPair[0].endsWith("\n")){
                keyPair[0] = keyPair[0].substring(0, keyPair[0].length()-1);
            }

            sendToServiceRsaPublicKey = URLEncoder.encode(keyPair[0], "utf-8");//一般用于get请求方式，需要编码一下，将=转换成编码符号
            saveToLocalRsaPrivateKey = keyPair[1];
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    //加密数据
    public String encryptWithParams(String params){
        if(TextUtils.isEmpty(params)){
            return "";
        }
        return AppUtil.isEmpty(AESEncrypt.encrypt(params, randomEncryptAesKey), "");
    }

    //解密数据
    public String decryptWithParams(String params){
        if(TextUtils.isEmpty(serviceDecryptAesKey)){
            return "";
        }
        return AppUtil.isEmpty(AESEncrypt.decrypt(params, serviceDecryptAesKey), "");
    }

    //保存服务器返回的AES秘钥
    public void setServiceDecryptAesKey(String decryptAesKey){
        if(TextUtils.isEmpty(decryptAesKey)){
            return;
        }
        try {
            serviceDecryptAesKey = RSAEncrypt.decryptByPrivateKey(decryptAesKey, saveToLocalRsaPrivateKey);
            LogUtil.d("dddd", serviceDecryptAesKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //获取本地随机的AES秘钥
    public String getRandomEncryptAesKeyByRsa(){
        try {
            return URLEncoder.encode(RSAEncrypt.encryptByPublicKey(randomEncryptAesKey), "utf-8");
        } catch (UnsupportedEncodingException | GeneralSecurityException e) {
            return "";
        }
    }

    //获取发送给服务器的RSA公钥
    public String getSendToServiceRsaPublicKey() {
        return sendToServiceRsaPublicKey;
    }
}
