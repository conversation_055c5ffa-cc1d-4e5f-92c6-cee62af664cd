package com.dep.biguo.utils;

import android.content.Context;

import com.dep.biguo.utils.mmkv.UserCache;
import com.umeng.analytics.MobclickAgent;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 友盟统计埋点
 * 退出APP后，间隔15-30秒再启动，否则“启动触发上传”策略将不会被触发
 */
public class UmengEventUtils {
    public static final String URL_IN = "url_in";//进入h5页面
    public static final String URL_COMMIT = "url_commit";//h5提交
    public static final String URL_COMMIT_URL = "url_commit_url";//h5提交，携带一个当前页面的H5路径
    public static final String URL_OUT = "url_out";//退出h5
    public static final String GOODS_AGAIN_PAY_DIALOG_DISMISS = "goods_stay_dialog_dismiss";//挽留弹窗消失
    public static final String PAY_IN = "pay_in";//进入结算页
    public static final String PAY_DIALOG_SHOW = "pay_dialog_show";//显示支付方式弹窗
    public static final String PAY_SUCCESS_LEAVE = "pay_success_in";//退出支付成功页
    public static final String TOPIC_ITEM_CLICK = "topic_item_click";//选择题型
    public static final String DO_PROBLEM_AD_CLICK = "do_problem_ad_click";//点击做题界面顶部的广告

    public static final String CLICK_COURSE_STUDY = "click_course_study";//选择课程开始学习
    public static final String INPUT_SCORE = "input_score";//填写成绩
    public static final String ENTER_PAGE_FOR_LAST_PAGE = "enter_page_for_last_page";//上一个页面的名称
    public static final String CLICK_GO_OPEN_BIGUO_VIP = "click_go_open_biguo_vip";//商品详情页面的笔果vip按钮是否被点击过
    public static final String CLICK_BUY = "click_buy";//商品详情点击购买
    public static final String PAY_STATUS = "pay_status";//支付状态
    public static final String CLICK_BANNER = "click_banner";//点击首页banner图
    public static final String CLICK_ICON = "click_icon";//点击首页icon
    public static final String CLICK_VIP_OR_YAMI = "click_vip_or_yami";//点击首页考试神器
    public static final String CLICK_BOOK = "click_book";//点击首页教材
    public static final String CLICK_VIDEO = "click_video";//点击首页视频
    public static final String CLICK_CUSTOMER_SERVICE = "click_customer_service";//点击首页联系客服
    public static final String CLICK_RECEIVE_INFORMATION = "click_receive_information";//点击首页领取资料
    public static final String CLICK_BOTTOM_NAVIGATION = "click_bottom_navigation";//点击主页底部导航栏
    public static final String SHOW_SHARE_APP = "show_share_app";//显示分享APP弹窗
    public static final String CLICK_SHARE_APP = "click_share_app";//分享APP
    public static final String CLICK_GO_TO_LOGIN = "click_login";//调起登录的次数
    public static final String CLICK_ONE_KEY_LOGIN_OPEN = "click_one_key_login";//调起一键登录的次数
    public static final String CLICK_ONE_KEY_LOGIN_AUTHORIZE = "click_one_key_login_authorize";//一键登录授权成功的次数
    public static final String CLICK_ONE_KEY_LOGIN_CODE = "click_one_key_login_error";//一键登录所有打开失败的统计
    public static final String ENTER_SKILL_PAGE = "enter_skill_page";//进入技能证的主页（与首页不是同一个东西）
    public static final String ENTER_SKILL_FREE_VIDEO = "enter_free_video";//播放技能证的免费视频
    public static final String ENTER_SKILL_MORE_VIDEO = "enter_more_video";//进入技能证的更多免费视频
    public static final String ENTER_VIDEO_DETAIL = "enter_video_detail";//从视频课程进入商品详情次数
    public static final String CLICK_INVITE_FRIEND = "click_invite_friend";//点击邀请好友次数
    public static final String CLICK_INVITE_FRIEND_RECEIVE_REWARD = "share_invite_friend_receive_reward";//邀请好友领取押密或VIP题库的奖励次数
    public static final String ENTER_USER_INFO_MANAGE = "enter_user_info_manage";//进入档案管理的次数
    public static final String CLICK_USER_INFO_MANAGE_SAVE = "enter_user_info_manage_save";//提交保存档案管理的次数
    public static final String CLICK_INTERNET_STUDY = "click_internet_study";//网络助学次数
    public static final String ENTER_INVITE_TO_INTRODUCE = "enter_invite_to_introduce";//进入邀请转介绍页面次数
    public static final String ENTER_INVITE_TO_INTRODUCE_SHARE = "click_invite_to_introduce_share";//点击邀请转介绍页面的分享按钮次数
    public static final String CLICK_ANSWER_MODE = "click_answer_mode";//上次切换答题模式至本次切换答题模式的时间间隔
    public static final String CLICK_TOPIC_SELECT = "click_topic_select";//点击题型选择次数
    public static final String CLICK_REGISTER_ORGANIZATION = "click_register_organization";//点击首次安装时的机构推荐的咨询和关闭次数
    public static final String ENTER_REGISTER_RECEIVE_PAGE = "enter_register_receive_page";//点击首次安装时的资料领取次数
    public static final String CLICK_REGISTER_RECEIVE_BUTTON = "click_register_receive_button";//点击首次安装时的资料领取次数
    public static final String ENTER_SELF_STUDY_TEST_CLASS = "enter_self_study_test_class";//进入自考辅导班页面的次数
    public static final String CLICK_SELF_STUDY_TEST_CLASS_BUY = "enter_self_study_test_class";//点击自考辅导班页面的购买按钮次数
    public static final String CLICK_TRUE_PAPER_DOWNLOAD = "click_true_paper_download";//历年真题的下载次数
    public static final String CLICK_GROUP_SINGLE_BUY = "click_group_single_buy";//拼团特惠的单独购买按钮点击次数
    public static final String CLICK_GROUP_SINGLE_BUY_COUNT = "click_group_single_buy_count";//拼团特惠的单独购买成功次数

    public static final String SHOW_HOME_ORGANIZATION = "show_home_organization";//首页机构的曝光次数
    public static final String CLICK_HOME_ORGANIZATION_MORE = "click_home_organization_more";//首页机构的更多按钮的点击次数

    public static final String CLICK_ORGANIZATION_BANNER = "click_organization_banner";//助学班轮播图的点击次数
    public static final String CLICK_ORGANIZATION_FLOAT_BUTTON = "click_organization_float_button";//助学班的浮动按钮点击次数

    public static final String ENTER_ORGANIZATION_DETAIL = "enter_organization_detail";//进入机构的详情页面
    public static final String CLICK_ORGANIZATION_DETAIL_STUDY_PAGE = "click_organization_detail_study_page";//点击机构助学班的次数
    public static final String CLICK_ORGANIZATION_DETAIL_ALL_STUDY_PAGE = "click_organization_detail_all_study_page";//点击全部机构助学班按钮的次数

    public static final String ENTER_ORGANIZATION_APPLY = "enter_organization_apply";//进入机构的报名页面

    public static final String CLICK_ORGANIZATION_STUDY_DETAIL_APPLY = "click_organization_study_detail_apply";//助学班详情页的报名按钮点击次数
    public static final String CLICK_ORGANIZATION_STUDY_DETAIL_COMMIT = "click_organization_study_detail_commit";//助学班的报名信息的提交次数
    public static final String SHOW_ORGANIZATION_STUDY_DETAIL_AGREEMENT_AGREE = "show_organization_study_detail_agreement_agree";//助学班的报名信息的协议同意次数
    public static final String SHOW_ORGANIZATION_STUDY_DETAIL_AGREEMENT_CLOSE= "show_organization_study_detail_agreement_close";//助学班的报名信息的协议关闭次数
    public static final String SHOW_ORGANIZATION_STUDY_DETAIL_PAY= "show_organization_study_detail_pay";//助学班的报名信息的支付弹窗显示次数
    public static final String SHOW_ORGANIZATION_STUDY_DETAIL_PAY_SUCCESS= "show_organization_study_detail_pay_success";//助学班的报名信息的支付成功次数

    public static final String REGISTER_TYPE= "register_type";//注册类型

    private Context mContext;
    private Map<String, Object> mParams;

    /**
     * @param context 上下文
     */
    public UmengEventUtils(Context context) {
        this.mContext = context;
        mParams = new HashMap<>();

        //公共参数
        this.addParams("u_id","")
                .addParams("school", UserCache.getSchool() == null ? "" : UserCache.getSchool().getName())
                .addParams("profession", UserCache.getProfession() == null ? "" : UserCache.getProfession().getName())
                .addParams("user_id", UserCache.getUserCache() == null ? "" : (UserCache.getUserCache().getUser_id()+""))
                .addParams("user_lever", UserCache.getUserCache() == null ? "" : UserCache.isMemberShip() ? "笔果折扣卡" : "普通用户")
                .addParams("app_version", DeviceHelper.getVersionName(context))
                .addParams("os", "Android")
                .addParams("page_name", context.getClass().getName())
                .addParams("os_version", DeviceHelper.getSystemVersion())
                .addParams("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",Locale.CANADA).format(System.currentTimeMillis()));
    }


    /**添加事件的属性
     * @param key 属性名
     * @param value 属性值
     * @return
     */
    public UmengEventUtils addParams(String key, Object value) {
        mParams.put(key, value);
        return this;
    }

    /**上报埋点事件
     * @param key 事件名称
     */
    public void pushEvent(String key) {
        try {
            MobclickAgent.onEventObject(mContext, key, mParams);
        }catch(Exception e) {
        }
        mContext = null;
    }
}
