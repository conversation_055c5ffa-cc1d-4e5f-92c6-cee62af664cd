package com.dep.biguo.utils.pay;

import com.biguo.utils.util.LogUtil;

public class PayListenerUtils {

    private static PayListenerUtils instance;

    //private final static ArrayList<PayResultListener> resultList = new ArrayList<>();
    private PayResultListener payResultListener;

    private PayListenerUtils() {
    }

    public synchronized static PayListenerUtils getInstance() {
        if (instance == null) {
            instance = new PayListenerUtils();
        }
        return instance;
    }

    public void setListener(PayResultListener listener) {
        LogUtil.d("dddd","调起付款");
        payResultListener = listener;
    }

    public void addSuccess() {
        if(payResultListener != null) {
            LogUtil.d("dddd","已成功付款");
            payResultListener.onPaySuccess();
        }else {
            LogUtil.d("dddd","已成功付款,但回调对象不存在");
        }
        payResultListener = null;
    }

    public void addCancel() {
        if(payResultListener != null) {
            LogUtil.d("dddd","已取消付款");
            payResultListener.onPayCancel();
        }else {
            LogUtil.d("dddd","已取消付款,但回调对象不存在");
        }
        payResultListener = null;
    }

    public void addError() {
        if(payResultListener != null) {
            LogUtil.d("dddd","付款出现错误");
            payResultListener.onPayError();
        }else {
            LogUtil.d("dddd","付款出现错误,但回调对象不存在");
        }
        payResultListener = null;
    }

}
