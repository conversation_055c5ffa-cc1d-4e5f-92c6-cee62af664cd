package com.dep.biguo.utils.database.listener;

import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.RealmModel;
import io.realm.RealmResults;

public abstract class RealmTowQueryListener<FORM extends RealmModel, TOPIC, QUESTION> implements RealmChangeListener<RealmResults<FORM>> {
    private QueryTowListener<TOPIC, QUESTION> queryListener;
    private Realm realm;

    private final Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            List<Object> list = (List<Object>) msg.obj;
            queryListener.query((TOPIC)list.get(0), (QUESTION)list.get(1));
            return false;
        }
    });

    public RealmTowQueryListener(QueryTowListener<TOPIC, QUESTION> queryListener, Realm realm) {
        this.queryListener = queryListener;
        this.realm = realm;
    }

    @Override
    public void onChange(RealmResults<FORM> results) {
        results.removeChangeListener(this);
        List<FORM> copyResult = realm.copyFromRealm(results);
        realm.close();
        //可能需要遍历结果再查其他表，耗时较长，因此选择开线程
        new Thread(() -> onAsyncChange(copyResult)).start();
    }

    /**此方法运行在一个线程内，不要在此方法中操作UI，应用{@link #callBack(Object, Object)} 方法回调，在外部处理UI
     * @param form
     */
    public abstract void onAsyncChange(List<FORM> form);

    public void callBack(TOPIC topic, QUESTION question){
        List<Object> list = new ArrayList<>();
        list.add(topic);
        list.add(question);
        Message message = new Message();
        message.obj = list;
        handler.sendMessage(message);
    }

}
