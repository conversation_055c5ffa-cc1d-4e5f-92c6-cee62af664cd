package com.dep.biguo.utils.imp;

import android.text.Editable;
import android.text.TextWatcher;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/29
 * @Description:
 */
public abstract class TextWatcherImp implements TextWatcher {

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

    }
}
