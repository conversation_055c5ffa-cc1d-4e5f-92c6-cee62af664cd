package com.dep.biguo.utils;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;

import androidx.core.content.res.ResourcesCompat;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class TimeFormatUtils {

    /**
     * 秒转换时、分、秒、毫秒
     *
     * @param sec
     * @return
     */
    public static String formatMillisecond(long sec) {
        StringBuilder stringBuilder = new StringBuilder();

        long hour = sec / 1000 / 3600;
        long min = sec / 1000 % 3600 / 60;
        long seconds = sec / 1000 % 3600 % 60;
        long millisecond = sec % 3600000 % 6000 % 6000 % 1000 / 100;

        if (hour > 0) {
            stringBuilder.append(hour < 10 ? "0" + hour + ":" : hour + ":");
        } else {
            stringBuilder.append("00:");
        }

        if (min > 0) {
            stringBuilder.append(min < 10 ? "0" + min + ":" : min + ":");
        } else {
            stringBuilder.append("00:");
        }

        if (seconds > 0) {
            stringBuilder.append(seconds < 10 ? "0" + seconds + "." : seconds + "." );
        } else {
            stringBuilder.append("00.");
        }

        stringBuilder.append(millisecond);


        return stringBuilder.toString();
    }
    /**
     * 秒转换时、分、秒
     *
     * @param sec
     * @return
     */
    public static String formatSeconds(long sec) {
        StringBuilder stringBuilder = new StringBuilder();

        long hour = sec / 3600;
        long min = sec % 3600 / 60;
        long seconds = sec % 3600 % 60;

        if (hour > 0) {
            stringBuilder.append(hour < 10 ? "0" + hour + " : " : hour + " : ");
        } else {
            stringBuilder.append("00 : ");
        }

        if (min > 0) {
            stringBuilder.append(min < 10 ? "0" + min + " : " : min + " : ");
        } else {
            stringBuilder.append("00 : ");
        }

        if (seconds > 0) {
            stringBuilder.append(seconds < 10 ? "0" + seconds : seconds);
        } else {
            stringBuilder.append("00");
        }

        return stringBuilder.toString();
    }

    /**
     * 判断缓存的时间是否是今天
     *
     * @param day
     * @return
     */
    public static boolean isToday(long day) {
        Calendar pre = Calendar.getInstance();
        Date predate = new Date(System.currentTimeMillis());
        pre.setTime(predate);

        Calendar cal = Calendar.getInstance();
        Date date = new Date(day);
        cal.setTime(date);

        if (cal.get(Calendar.YEAR) == pre.get(Calendar.YEAR)) {
            int diffDay = cal.get(Calendar.DAY_OF_YEAR) - pre.get(Calendar.DAY_OF_YEAR);
            return diffDay == 0;
        }
        return false;
    }

    public static String formatChinese(long sec){
        StringBuilder stringBuilder = new StringBuilder();

        long hour = sec / 3600;
        long min = sec % 3600 / 60;
        long seconds = sec % 3600 % 60;

        if (hour > 0) {
            stringBuilder.append(hour).append("时");
        }

        if (min > 0) {
            stringBuilder.append(min).append("分");
        }

        if (seconds > 0) {
            stringBuilder.append(seconds).append("秒");
        }

        return stringBuilder.toString();
    }

    /**
     * 日期转换成秒数
     * */
    public static long getSecondsFromDate(String format, String expireDate){
        if(expireDate==null||expireDate.trim().equals(""))
            return 0;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date=null;
        try{
            date=sdf.parse(expireDate);
            return (long)(date.getTime()/1000);
        }
        catch(ParseException e)
        {
            e.printStackTrace();
            return 0L;
        }
    }

    /**对学习时间进行自定义格式化
     * @param studyTime 已学时间
     * @return 最大单位：时（小时），最小单位：分（分钟）
     */
    public static SpannableString getStudyTimeFormat(Context context, int studyTime){
        int color = ResourcesCompat.getColor(context.getResources(), R.color.tblack3, context.getTheme());
        int size = DisplayHelper.dp2px(context, 14);

        int hours = studyTime/60/60;
        int minute = studyTime/60%60 + (studyTime%60 > 0 ? 1:0);

        String text = hours > 0 ? String.format("%s时%s分", hours, minute) : String.format("%s分", minute);
        SpannableString spannableString = new SpannableString(text);
        if(text.contains("时")) {
            int start = text.indexOf("时");
            spannableString.setSpan(new AbsoluteSizeSpan(size), start, start+1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }
        int start = text.indexOf("分");
        spannableString.setSpan(new AbsoluteSizeSpan(size), start, text.length() , Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new AbsoluteSizeSpan(size), start+1, text.length() , Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(color), start+1, text.length() , Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return spannableString;
    }

    public static String inMorningOrAfternoon(String format){
        if(TextUtils.isEmpty(format)){
            return "";
        }
        // 定义输入的日期时间格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        // 定义输出的日期格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd a");

        try {
            // 分割输入字符串为开始时间和结束时间
            String[] times = format.split("--");
            String startTimeStr = times[0].trim();

            // 解析开始时间
            Date startDate = inputFormat.parse(startTimeStr);

            // 格式化开始时间为所需格式
            String formattedStartDate = outputFormat.format(startDate);

            return formattedStartDate;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**返回分钟
     * @param time 秒
     * @return
     */
    public static int getMinute(long time){
        return (int) (time / 60);
    }
}
