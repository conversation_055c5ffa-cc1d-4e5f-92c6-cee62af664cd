package com.dep.biguo.utils.database.util;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonSyntaxException;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.Primitives;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.Type;

import io.realm.RealmModel;
import io.realm.RealmObject;

public class RealmGson {
    public static Gson gson;

    private static void create(){
        if(gson != null) return;

        gson = new GsonBuilder()
                .registerTypeAdapter(String.class, new TypeAdapter<String>() {
                    public String read(JsonReader reader) throws IOException {
                        if (reader.peek() == JsonToken.NULL) {
                            reader.nextNull();
                            return "";
                        }
                        return reader.nextString();
                    }

                    public void write(JsonWriter writer, String value) throws IOException {
                        if (value == null) {
                            // 在这里处理null改为空字符串
                            writer.value("");
                            return;
                        }
                        writer.value(value);
                    }
                })
                .setExclusionStrategies(new ExclusionStrategy() {
                    @Override
                    public boolean shouldSkipField(FieldAttributes f) {
                        return f.getDeclaredClass() == RealmObject.class;
                    }

                    @Override
                    public boolean shouldSkipClass(Class<?> clazz) {
                        return false;
                    }
                })
                .disableHtmlEscaping()
                .create();
    }

    public static String toJson(Object object){
        create();
        return gson.toJson(object);
    }

    public static <T> T fromJson(String json, Type typeOfT) throws JsonSyntaxException {
        if (json == null) {
            return null;
        }
        create();

        StringReader reader = new StringReader(json);
        return gson.fromJson(reader, typeOfT);
    }

    public static <T> T fromJson(JsonElement json, Class<T> classOfT) throws JsonSyntaxException {
        create();

        Object object = gson.fromJson(json, (Type) classOfT);
        return Primitives.wrap(classOfT).cast(object);
    }
}
