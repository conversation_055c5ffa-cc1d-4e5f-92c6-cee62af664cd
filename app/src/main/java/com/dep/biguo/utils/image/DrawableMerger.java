package com.dep.biguo.utils.image;

import android.graphics.Bitmap;
import android.graphics.Canvas;

public class DrawableMerger {
    //将第二个bitmap叠加在第一个bitmap的中间，并返回叠加后的bitmap
    public static Bitmap mergeIntoCenter(Bitmap bitmapBack, Bitmap bitmapForge){
        // 获取第一张图片的宽度和高度
        int width1 = bitmapBack.getWidth();
        int height1 = bitmapBack.getHeight();

        // 获取第二张图片的宽度和高度
        int width2 = bitmapForge.getWidth();
        int height2 = bitmapForge.getHeight();

        // 创建一个新的 Bitmap 对象，用于存储合并后的图片
        Bitmap mergedBitmap = Bitmap.createBitmap(Math.max(width1, width2), Math.max(height1, height2), Bitmap.Config.ARGB_8888);

        // 创建一个 Canvas 对象，用于绘制合并后的图片
        Canvas canvas = new Canvas(mergedBitmap);

        // 在 Canvas 上绘制第一张图片
        canvas.drawBitmap(bitmapBack, 0, 0, null);

        // 在 Canvas 上绘制第二张图片，并将其叠加在第一张图片的中间
        float x = (width1 - width2) / 2f;
        float y = (height1 - height2) / 2f;
        canvas.drawBitmap(bitmapForge, x, y, null);

        return mergedBitmap;
    }

}
