package com.dep.biguo.utils.database;

import com.biguo.utils.util.GsonUtils;

import io.realm.RealmModel;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.RealmClass;
import io.realm.annotations.Required;

@RealmClass
public class QuestionRealmBean implements RealmModel {
    @PrimaryKey private String primaryKey;//主键，构成：mainType + topic_type
    private int id;//题目ID
    @Required private String code;//题型代码
    private int mainType;//题库类型（VIP、免费、真题、章节、收藏、错题、打卡）
    private int topic_type;//题库类型（单选、多选、判断、问答、填空、完形填空、阅读理解、名词解析）
    private String topic_type_name;//题库类型名称（单选、多选、判断、问答、填空、完形填空、阅读理解、名词解析）
    @Required private String questionAsk;//问题
    @Required private String A;//选项A
    @Required private String B;//选项B
    @Required private String C;//选项C
    @Required private String D;//选项D
    @Required private String E;//选项E
    @Required private String F;//选项F
    @Required private String correctOption;//参考答案
    @Required private String explanation;//题目详解
    private int isCollection;//是否已收藏
    private String video_parse_cover;//封面图
    private String audio_url;//音频

    private int sort;//从服务器返回时的数据位置，用于排序

    public QuestionRealmBean() {
        code = "";
        questionAsk = "";
        A = "";
        B = "";
        C = "";
        D = "";
        E = "";
        F = "";
        correctOption = "";
        explanation = "";
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getTopic_type_name() {
        return topic_type_name;
    }

    public void setTopic_type_name(String topic_type_name) {
        this.topic_type_name = topic_type_name;
    }

    public String getQuestionAsk() {
        return questionAsk;
    }

    public void setQuestionAsk(String questionAsk) {
        this.questionAsk = questionAsk;
    }

    public String getA() {
        return A;
    }

    public void setA(String a) {
        A = a;
    }

    public String getB() {
        return B;
    }

    public void setB(String b) {
        B = b;
    }

    public String getC() {
        return C;
    }

    public void setC(String c) {
        C = c;
    }

    public String getD() {
        return D;
    }

    public void setD(String d) {
        D = d;
    }

    public String getE() {
        return E;
    }

    public void setE(String e) {
        E = e;
    }

    public String getF() {
        return F;
    }

    public void setF(String f) {
        F = f;
    }

    public String getCorrectOption() {
        return correctOption;
    }

    public void setCorrectOption(String correctOption) {
        this.correctOption = correctOption;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public int getIsCollection() {
        return isCollection;
    }

    public void setIsCollection(int isCollection) {
        this.isCollection = isCollection;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getVideo_parse_cover() {
        return video_parse_cover;
    }

    public void setVideo_parse_cover(String video_parse_cover) {
        this.video_parse_cover = video_parse_cover;
    }

    public String getAudio_url() {
        return audio_url;
    }

    public void setAudio_url(String audio_url) {
        this.audio_url = audio_url;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"primaryKey\":\"")
                .append(primaryKey).append('\"');
        sb.append(",\"id\":")
                .append(id);
        sb.append(",\"code\":\"")
                .append(code).append('\"');
        sb.append(",\"mainType\":")
                .append(mainType);
        sb.append(",\"topic_type\":")
                .append(topic_type);
        sb.append(",\"topic_type_name\":")
                .append(topic_type_name);
        sb.append(",\"questionAsk\":")
                .append(replaceQuotationMarks(questionAsk));
        sb.append(",\"A\":")
                .append(replaceQuotationMarks(A));
        sb.append(",\"B\":")
                .append(replaceQuotationMarks(B));
        sb.append(",\"C\":")
                .append(replaceQuotationMarks(C));
        sb.append(",\"D\":")
                .append(replaceQuotationMarks(D));
        sb.append(",\"E\":")
                .append(replaceQuotationMarks(E));
        sb.append(",\"F\":")
                .append(replaceQuotationMarks(F));
        sb.append(",\"correctOption\":")
                .append(replaceQuotationMarks(correctOption));
        sb.append(",\"explanation\":")
                .append(replaceQuotationMarks(explanation));
        sb.append(",\"isCollection\":")
                .append(isCollection);
        sb.append(",\"sort\":")
                .append(sort);
        sb.append(",\"video_parse_cover\":")
                .append(video_parse_cover);
        sb.append(",\"audio_url\":")
                .append(audio_url);
        sb.append('}');
        return sb.toString();
    }

    public String replaceQuotationMarks(String string){
        return GsonUtils.toJson(string);
    }
}
