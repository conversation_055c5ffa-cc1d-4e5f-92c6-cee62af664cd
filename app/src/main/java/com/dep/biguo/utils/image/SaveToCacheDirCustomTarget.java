package com.dep.biguo.utils.image;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Environment;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;

import java.io.File;
import java.io.FileOutputStream;

public class SaveToCacheDirCustomTarget extends CustomTarget<Bitmap> {
    private Context context;
    private String contentPath;
    private OnKeyValueResultCallbackListener onKeyValueResultCallbackListener;

    public SaveToCacheDirCustomTarget(Context context, String contentPath, OnKeyValueResultCallbackListener onKeyValueResultCallbackListener) {
        this.context = context;
        this.contentPath = contentPath;
        this.onKeyValueResultCallbackListener = onKeyValueResultCallbackListener;
    }

    @Override
    public void onResourceReady(@NonNull Bitmap bitmap, @Nullable Transition<? super Bitmap> transition) {
        try {
            String path = saveToCacheDir(bitmap);
            onKeyValueResultCallbackListener.onCallback(contentPath, path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onLoadCleared(@Nullable Drawable drawable) {

    }

    public String saveToCacheDir(Bitmap bitmap) throws Exception{
        // 保存图片至指定路径
        File appDir = context.getCacheDir();
        //检查是否有这个文件夹
        if (!appDir.exists()) appDir.mkdirs();
        //检查文件是否已创建
        File file = new File(appDir, System.currentTimeMillis()+".jpg");
        if(!file.exists()) file.createNewFile();
        //创建一个文件输出流
        FileOutputStream fos = new FileOutputStream(file);
        //通过io流的方式来压缩保存图片(100表示不压缩，80代表压缩20%)
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
        fos.flush();
        fos.close();
        return file.getPath();
    }
}
