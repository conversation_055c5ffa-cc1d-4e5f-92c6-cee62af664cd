package com.dep.biguo.utils.database;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class ChapterRealmBean extends RealmObject {
    @PrimaryKey private String key;
    private int id;
    private String name;
    private String code;
    private String price;
    private int is_show;
    private int total_nums;
    private RealmList<ChapterSectionsRealBean> sections;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getIs_show() {
        return is_show;
    }

    public void setIs_show(int is_show) {
        this.is_show = is_show;
    }

    public int getTotal_nums() {
        return total_nums;
    }

    public void setTotal_nums(int total_nums) {
        this.total_nums = total_nums;
    }

    public RealmList<ChapterSectionsRealBean> getSections() {
        return sections;
    }

    public void setSections(RealmList<ChapterSectionsRealBean> sections) {
        this.sections = sections;
    }
}
