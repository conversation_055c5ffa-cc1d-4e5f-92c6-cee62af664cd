package com.dep.biguo.utils.html;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.CountDownTimer;
import android.text.Html;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.TintDrawableUtil;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dep.biguo.R;

import java.util.HashMap;
import java.util.Map;

public class GetterImage implements Html.ImageGetter{
    private Context context;
    private int maxWidth;

    private LoadImageListener loadImageListener;
    private ColorDrawable placeholder;

    protected Map<String, Drawable> imgMap;
    public GetterImage(Context context, int maxWidth, int lineHeight, LoadImageListener loadImageListener) {
        this.context = context;
        this.maxWidth = maxWidth;
        this.loadImageListener = loadImageListener;
        this.imgMap = new HashMap<>();
        placeholder = new ColorDrawable();
        placeholder.setBounds(0, 0, lineHeight, lineHeight);
    }

    @Override
    public Drawable getDrawable(String source) {
        if(TextUtils.equals(source, HtmlUtil.AUDIO_PREPARE)){
            Drawable drawable = AppUtil.getDrawableRes(context, R.drawable.question_audio_prepare);
            return drawable;

        }else if(TextUtils.equals(source, HtmlUtil.AUDIO_PLAYING)){
            Drawable drawable = AppUtil.getDrawableRes(context, R.drawable.question_audio_playing);
            return drawable;

        }else {
            if(imgMap.containsKey(source) && imgMap.get(source) != null){
                return imgMap.get(source);
            }

            Glide.with(context)
                    .load(source)
                    .into(new CustomTargetDrawable(source));
            return placeholder;
        }
    }

    private class CustomTargetDrawable extends CustomTarget<Drawable>{
        private String url;

        public CustomTargetDrawable(String url) {
            this.url = url;
        }

        @Override
        public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
            //int maxWidth = builder.viewWidth;//TODO 计算宽度
            imgMap.put(url, scale(context, resource, maxWidth));
            loadImageListener.load(url, resource, false);
        }

        //当图片的宽度大于控件的宽度，将图片缩小到与控件宽度相同，高度也按同比缩小
        public Drawable scale(Context context, Drawable resource, int maxWidth){
            if(TextUtils.equals(url, HtmlUtil.AUDIO_PREPARE) || TextUtils.equals(url, HtmlUtil.AUDIO_PLAYING)){
                resource.setBounds(0, 0, resource.getIntrinsicWidth(), resource.getIntrinsicHeight());
            }else {
                int drawableWidth = DisplayHelper.dp2px(context, resource.getIntrinsicWidth());
                int drawableHeight = DisplayHelper.dp2px(context, resource.getIntrinsicHeight());
                float scale = maxWidth > drawableWidth ? 1f : (maxWidth * 1f / drawableWidth);
                resource.setBounds(0, 0, Math.min(maxWidth, drawableWidth), (int) (drawableHeight * scale));
            }
            return resource;
        }

        @Override
        public void onLoadCleared(@Nullable Drawable placeholder) {

        }

    }

    public interface LoadImageListener{
        void load(String url, Drawable drawable, boolean isRefresh);
    }
}