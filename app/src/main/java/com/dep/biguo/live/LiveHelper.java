package com.dep.biguo.live;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.fragment.app.FragmentActivity;

/*import com.bokecc.dwlivedemo.activity.LivePlayActivity;
import com.bokecc.dwlivedemo.activity.ReplayPlayActivity;
import com.bokecc.sdk.mobile.live.DWLive;
import com.bokecc.sdk.mobile.live.DWLiveLoginListener;
import com.bokecc.sdk.mobile.live.Exception.DWLiveException;
import com.bokecc.sdk.mobile.live.pojo.LoginInfo;
import com.bokecc.sdk.mobile.live.pojo.Marquee;
import com.bokecc.sdk.mobile.live.pojo.PublishInfo;
import com.bokecc.sdk.mobile.live.pojo.RoomInfo;
import com.bokecc.sdk.mobile.live.pojo.TemplateInfo;
import com.bokecc.sdk.mobile.live.pojo.Viewer;
import com.bokecc.sdk.mobile.live.replay.DWLiveReplay;
import com.bokecc.sdk.mobile.live.replay.DWLiveReplayLoginListener;
import com.bokecc.sdk.mobile.live.replay.pojo.ReplayLoginInfo;
import com.bokecc.dwlivedemo.activity.LiveLoginActivity;
import com.bokecc.dwlivedemo.activity.LivePlayActivity;
import com.bokecc.dwlivedemo.activity.ReplayPlayActivity;
import com.bokecc.sdk.mobile.live.DWLive;
import com.bokecc.sdk.mobile.live.DWLiveLoginListener;
import com.bokecc.sdk.mobile.live.Exception.DWLiveException;
import com.bokecc.sdk.mobile.live.pojo.LoginInfo;
import com.bokecc.sdk.mobile.live.pojo.Marquee;
import com.bokecc.sdk.mobile.live.pojo.PublishInfo;
import com.bokecc.sdk.mobile.live.pojo.RoomInfo;
import com.bokecc.sdk.mobile.live.pojo.TemplateInfo;
import com.bokecc.sdk.mobile.live.pojo.Viewer;
import com.bokecc.sdk.mobile.live.replay.DWLiveReplay;
import com.bokecc.sdk.mobile.live.replay.DWLiveReplayLoginListener;
import com.bokecc.sdk.mobile.live.replay.pojo.ReplayLoginInfo;*/
import com.aliyun.player.IPlayer;
import com.aliyun.player.alivcplayerexpand.constants.GlobalPlayerConfig;
import com.aliyun.vodplayerview.activity.AliyunPlayerActivity;
import com.aliyun.vodplayerview.activity.AliyunPlayerSettingActivity;
import com.aliyun.vodplayerview.activity.AliyunPlayerSkinActivity;
import com.dep.biguo.R;
import com.dep.biguo.bean.AliyunAuthBean;
import com.dep.biguo.bean.VipCourseClassDetailBean;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.mvp.ui.activity.AppInfoActivity;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.widget.LoadingDialog;
import com.easefun.polyv.livecloudclass.scenes.PLVLCCloudClassActivity;
import com.easefun.polyv.livecommon.module.config.PLVLiveChannelConfigFiller;
import com.easefun.polyv.livecommon.module.config.PLVLiveScene;
import com.easefun.polyv.livecommon.module.modules.player.floating.PLVFloatingPlayerManager;
import com.easefun.polyv.livecommon.module.utils.result.PLVLaunchResult;
import com.plv.livescenes.config.PLVLiveChannelType;
import com.plv.livescenes.feature.login.IPLVSceneLoginManager;
import com.plv.livescenes.feature.login.PLVLiveLoginResult;
import com.plv.livescenes.feature.login.PLVSceneLoginManager;
import com.plv.thirdpart.blankj.utilcode.util.ToastUtils;

/**
 * Created by JX on 2018/9/18.
 */
public class LiveHelper {

    //登录https://console.bokecc.com/live/room-info/4514DDECA7A63F179C33DC5901307461/replay网站的账号ID
    private static final String USER_ID = "3C816BCE2B209DBD";

    /**
     * 打开CC直播间
     * @param activity
     * @param roomId
     */
    public static void openLiveRoom(final Activity activity, String roomId) {
        /*final LoadingDialog dialog = new LoadingDialog(activity);
        dialog.show();

        LoginInfo loginInfo = new LoginInfo();
        loginInfo.setUserId(USER_ID);
        loginInfo.setRoomId(roomId);
        loginInfo.setViewerName(UserHelper.getUserCache() != null ? UserHelper.getUserCache().getNickname() : "观众");
        loginInfo.setViewerToken("");

        DWLive.getInstance().startLogin(loginInfo, new DWLiveLoginListener() {
            @Override
            public void onLogin(TemplateInfo templateInfo, Viewer viewer, RoomInfo roomInfo, PublishInfo publishInfo) {
                dialog.dismiss();
                LivePlayActivity.openActivity(activity, false,loginInfo.getUserId(),loginInfo.getViewerName(),loginInfo.getRoomId());
            }

            @Override
            public void onException(final DWLiveException e) {
                activity.runOnUiThread(() -> {
                    dialog.dismiss();
                    ToastUtils.show(e.getLocalizedMessage());
                });
            }
        });*/
    }

    /**
     * 打开阿里的回放页面
     * @param activity
     * @param roomId
     * @param liveId
     * @param recordId
     */
    public static void openReplayRoom(final Activity activity, String roomId, String liveId, String recordId) {
        /*final LoadingDialog dialog = new LoadingDialog(activity);
        dialog.show();
        ReplayLoginInfo replayLoginInfo = new ReplayLoginInfo();   // 创建登录信息对象
        replayLoginInfo.setUserId(USER_ID);   // 设置用户Id
        replayLoginInfo.setRoomId(roomId);  // 设置直播间Id
        replayLoginInfo.setLiveId(liveId);   // 设置直播Id
        replayLoginInfo.setRecordId(recordId);
        replayLoginInfo.setViewerName(UserHelper.getUserCache() != null ? UserHelper.getUserCache().getNickname() : "观众");    // 设置观众名称
        replayLoginInfo.setViewerToken("");  // 设置观众密码

        DWLiveReplay.getInstance().startLogin(replayLoginInfo, new DWLiveReplayLoginListener() {
            @Override
            public void onLogin(final TemplateInfo templateInfo, Marquee marquee) {
                dialog.dismiss();
                ReplayPlayActivity.openActivity(activity, false, replayLoginInfo.getRecordId());
            }
            @Override
            public void onException(DWLiveException e) {
                activity.runOnUiThread(() -> {
                    dialog.dismiss();
                    ToastUtils.show("观看失败");
                });
            }
        });*/
    }

    /**打开阿里直播间
     * @param context
     * @param mAliyunAuthBean
     * @param chapter_name
     * @param desc
     * @param teacher_name
     * @param userId
     * @param code
     */
    public static void openAliYunReplayRoom(final Context context, AliyunAuthBean mAliyunAuthBean, String chapter_name, String desc,String teacher_name, int userId, String code) {
        GlobalPlayerConfig.mEnableHardDecodeType = true;
        GlobalPlayerConfig.PlayConfig.mAutoSwitchOpen = true;
        GlobalPlayerConfig.PlayConfig.mEnablePlayBackground = false;
        GlobalPlayerConfig.PlayConfig.mEnableAccurateSeekModule = true;
        GlobalPlayerConfig.mRotateMode = IPlayer.RotateMode.ROTATE_0;
        GlobalPlayerConfig.mMirrorMode = IPlayer.MirrorMode.MIRROR_MODE_NONE;
        GlobalPlayerConfig.mCurrentPlayType = GlobalPlayerConfig.PLAYTYPE.AUTH;

        GlobalPlayerConfig.mPlayAuth = mAliyunAuthBean.getPlayAuth();
        GlobalPlayerConfig.mVid = mAliyunAuthBean.getVideoMeta().getVideoId();
        GlobalPlayerConfig.mRegion = mAliyunAuthBean.getRegion();

        /*Intent intent = new Intent(context, AliyunPlayerSettingActivity.class);
        context.startActivity(intent);*/
        Intent intent = new Intent(context, AliyunPlayerActivity.class);
        intent.putExtra(AliyunPlayerActivity.CHAPTER_NAME, chapter_name);
        intent.putExtra(AliyunPlayerActivity.DESC, desc);
        intent.putExtra(AliyunPlayerActivity.TEACHER_NAME, teacher_name);
        intent.putExtra(AliyunPlayerActivity.VIDEO_TIEM, mAliyunAuthBean.getVideoMeta().getDuration());
        intent.putExtra(AliyunPlayerActivity.USER_ID, userId);
        intent.putExtra(AliyunPlayerActivity.CODE, code);
        context.startActivity(intent);
    }

    public static void openAliYunReplayRoom(final Context context, AliyunAuthBean mAliyunAuthBean, final VipCourseClassDetailBean.VideoBean videoBean) {
        GlobalPlayerConfig.mEnableHardDecodeType = true;
        GlobalPlayerConfig.PlayConfig.mAutoSwitchOpen = true;
        GlobalPlayerConfig.PlayConfig.mEnablePlayBackground = false;
        GlobalPlayerConfig.PlayConfig.mEnableAccurateSeekModule = true;
        GlobalPlayerConfig.mRotateMode = IPlayer.RotateMode.ROTATE_0;
        GlobalPlayerConfig.mMirrorMode = IPlayer.MirrorMode.MIRROR_MODE_NONE;
        GlobalPlayerConfig.mCurrentPlayType = GlobalPlayerConfig.PLAYTYPE.AUTH;

        GlobalPlayerConfig.mPlayAuth = mAliyunAuthBean.getPlayAuth();
        GlobalPlayerConfig.mVid = mAliyunAuthBean.getVideoMeta().getVideoId();
        GlobalPlayerConfig.mRegion = mAliyunAuthBean.getRegion();

        Intent intent = new Intent(context, AliyunPlayerActivity.class);
        intent.putExtra(AliyunPlayerActivity.CHAPTER_NAME, videoBean.getName());
        intent.putExtra(AliyunPlayerActivity.DESC, videoBean.getCourses_name());
        intent.putExtra(AliyunPlayerActivity.TEACHER_NAME, videoBean.getTeacher_name());
        intent.putExtra(AliyunPlayerActivity.VIDEO_TIEM, mAliyunAuthBean.getVideoMeta().getDuration());
        context.startActivity(intent);
    }


    /**打开保利威的直播间
     * @param context 上下文
     * @param channelId 房间ID
     * @param viewerId 用户的ID
     * @param viewerName 用户的昵称
     * @param viewerAvatar 用户的头像
     */
    //初始化保利威登录管理器
    public static void loginLive(FragmentActivity context, String channelId, String viewerId, String viewerName, String viewerAvatar) {
        LoadingDialog.showLoadingDialog(context);

        //从这个网页可以查到下面三个字段的信息：https://console.polyv.net/live/index.html#/develop/app-id
        //正式服用的账号与测试服的账号不一样，需要区别一下
        final String appId = Api.isReleaseService() ? "gh1tj265wo" : "fsql1myxuc";
        final String userId = Api.isReleaseService() ? "4defe49911" : "317698c586";//保利威的开发者账号ID
        final String appSecret = Api.isReleaseService() ? "52867549edac488dbb613ef83dac5d46" : "f0c754b284434ea89d7977bc626839f8";


        PLVFloatingPlayerManager.getInstance().clear();
        PLVSceneLoginManager loginManager = new PLVSceneLoginManager();
        loginManager.loginLiveNew(appId, appSecret, userId, channelId, new IPLVSceneLoginManager.OnLoginListener<PLVLiveLoginResult>() {
            @Override
            public void onLoginSuccess(PLVLiveLoginResult plvLiveLoginResult) {
                LoadingDialog.hideLoadingDialog();
                PLVLiveChannelConfigFiller.setupAccount(userId, appId, appSecret);
                PLVLiveChannelType channelType = plvLiveLoginResult.getChannelTypeNew();

                if (PLVLiveScene.isCloudClassSceneSupportType(channelType)) {
                    PLVLaunchResult launchResult = PLVLCCloudClassActivity.launchLive(context, channelId, channelType, viewerId, viewerName, viewerAvatar);
                    if (!launchResult.isSuccess()) {
                        ToastUtils.showShort(launchResult.getErrorMessage());
                    }
                } else {
                    ToastUtils.showShort(R.string.plv_scene_login_toast_cloudclass_no_support_type);
                }
                loginManager.destroy();
            }

            @Override
            public void onLoginFailed(String msg, Throwable throwable) {
                LoadingDialog.hideLoadingDialog();
                ToastUtils.showShort(msg);
                LogUtil.e("dddd", "loginLive onLoginFailed:" + throwable.getMessage());
                loginManager.destroy();
            }
        });
    }

}