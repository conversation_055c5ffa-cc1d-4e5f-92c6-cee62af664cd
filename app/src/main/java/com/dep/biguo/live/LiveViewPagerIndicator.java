package com.dep.biguo.live;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;
import android.view.View;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.imp.OnPageChangeImp;

/**
 * 直播界面ViewPagerIndicator
 * Created by JX on 2018/5/25.
 */

public class LiveViewPagerIndicator extends View {

    /**
     * 默认的Tab个数
     */
    private static final int COUNT_DEFAULT_TAB = 3;
    /**
     * 与之绑定的ViewPager
     */
    public ViewPager mViewPager;
    private Paint mPaint;
    private int mLineWidth;
    private int mLineHeight = 2;
    /**
     * tab数量
     */
    private int mTabVisibleCount = COUNT_DEFAULT_TAB;
    private float mScrollX;

    private Context mContext;

    public LiveViewPagerIndicator(Context context) {
        this(context, null);
    }

    public LiveViewPagerIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LiveViewPagerIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mContext = context;

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(context.getResources().getColor(R.color.theme));
        mPaint.setStyle(Paint.Style.FILL);
    }

//    @Override
//    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
//        super.onSizeChanged(w, h, oldw, oldh);
//        mLineWidth = (int) (w / mTabVisibleCount);
//    }

    @Override
    protected void onDraw(Canvas canvas) {
        mLineWidth = (DisplayHelper.getWindowWidth(mContext) / mTabVisibleCount);
        canvas.save();
        canvas.drawRect(mScrollX, 0, mLineWidth + mScrollX, getMeasuredHeight(), mPaint);
        canvas.restore();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        int height;

        if (heightMode == MeasureSpec.EXACTLY) {
            height = heightSize;
        } else {
            height = mLineHeight;
        }

        setMeasuredDimension(getMeasuredWidth(), height);
    }

    // 设置关联的ViewPager
    public void setViewPager(ViewPager mViewPager) {
        this.mViewPager = mViewPager;
        mTabVisibleCount = mViewPager.getAdapter().getCount();

        mViewPager.addOnPageChangeListener(new OnPageChangeImp() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                // 滚动
                scroll(position, positionOffset);
            }
        });
        // 设置当前页
        mViewPager.setCurrentItem(0);
        invalidate();
    }

    /**
     * 指示器跟随手指滚动，以及容器滚动
     */
    public void scroll(int position, float offset) {
        // 不断改变偏移量，invalidate
        mScrollX = getWidth() / mTabVisibleCount * (position + offset);

        invalidate();
    }

}
