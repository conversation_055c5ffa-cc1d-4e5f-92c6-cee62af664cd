package com.dep.biguo.dialog;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.databinding.CouponDialogBinding;
import com.dep.biguo.mvp.ui.adapter.SelectCouponAdapter;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

public class CouponDialog extends BottomSheetDialog implements View.OnClickListener{
    private CouponDialogBinding binding;

    public CouponDialog(@NonNull Context context, Builder builder) {
        super(context);
        View view = getLayoutInflater().inflate(R.layout.coupon_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(view);

        SelectCouponAdapter couponAdapter = new SelectCouponAdapter();
        couponAdapter.setList(builder.list);
        couponAdapter.setPayPrice(builder.payPrice);
        couponAdapter.setDefaultDiscountBean(builder.defaultDiscountBean);
        couponAdapter.setOnSelectListener(select -> {
            dismiss();
            if(builder.onSelectListener != null){
                builder.onSelectListener.onSelect(select);
            }
        });
        binding.recyclerView.setAdapter(couponAdapter);
    }

    @Override
    public void onClick(View view) {
        if(view == binding.closeView){
            dismiss();
        }
    }

    public static class Builder{
        private Context context;
        private List<DiscountBean> list;
        private float payPrice;
        private DiscountBean defaultDiscountBean;
        private OnSelectListener onSelectListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setList(List<DiscountBean> list) {
            this.list = list;
            return this;
        }

        public Builder setPayPrice(float payPrice) {
            this.payPrice = payPrice;
            return this;
        }

        public Builder setDefaultDiscountBean(DiscountBean defaultDiscountBean) {
            this.defaultDiscountBean = defaultDiscountBean;
            return this;
        }

        public Builder setOnSelectListener(OnSelectListener onSelectListener) {
            this.onSelectListener = onSelectListener;
            return this;
        }

        public CouponDialog build(){
            return new CouponDialog(context, this);
        }
    }

    public interface OnSelectListener{
        void onSelect(DiscountBean select);
    }
}
