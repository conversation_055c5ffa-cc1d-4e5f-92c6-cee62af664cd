package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import android.view.LayoutInflater;
import android.view.View;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.RuleDialogBinding;
import com.dep.biguo.mvp.ui.adapter.GroupRuleAdapter;
import com.dep.biguo.widget.ItemDecoration;

import java.util.List;

public class RuleDialog extends Dialog implements View.OnClickListener{
    private final GroupRuleAdapter groupRuleAdapter;
    private RuleDialogBinding binding;

    public RuleDialog(@NonNull Context context) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.rule_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(5));
        groupRuleAdapter = new GroupRuleAdapter(null);
        groupRuleAdapter.bindToRecyclerView(binding.recyclerView);

        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 280);
        getWindow().setBackgroundDrawableResource(R.color.tran);
    }

    public RuleDialog setTitleText(CharSequence title){
        binding.titleView.setText(title);
        return this;
    }

    public RuleDialog setRules(List<String> rules) {
        groupRuleAdapter.setNewData(rules);
        return this;
    }

    @Override
    public void onClick(View view) {
        dismiss();
    }
}
