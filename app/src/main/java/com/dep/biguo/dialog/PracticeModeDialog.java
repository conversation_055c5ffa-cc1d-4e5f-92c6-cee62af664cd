package com.dep.biguo.dialog;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.dialog.BottomDialog;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.mmkv.UserCache;

import butterknife.BindView;
import butterknife.OnClick;

public class PracticeModeDialog extends BottomDialog {

    @BindView(R.id.closeView)
    ImageView closeView;
    @BindView(R.id.tvMode1)
    TextView tvMode1;
    @BindView(R.id.tvMode2)
    TextView tvMode2;
    @BindView(R.id.tvMode3)
    TextView tvMode3;
    @BindView(R.id.flMode3)
    FrameLayout flMode3;

    private Context mContext;
    private int mPracticeType;

    public PracticeModeDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    public PracticeModeDialog(@NonNull Context context, int practiceType) {
        super(context);
        this.mContext = context;
        this.mPracticeType = practiceType;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_practice_mode;
    }

    @Override
    public void init() {
        initMode();
    }

    private void initMode() {
        int mode = UserCache.getPracticeMode();
        if ((mPracticeType == PracticeHelper.PRACTICE_ERROR || mPracticeType == PracticeHelper.PRACTICE_COLL)) {
            if (mode == PracticeHelper.MODE_SIMU)
                mode = PracticeHelper.MODE_DEFAULT;
            flMode3.setForeground(mContext.getDrawable(R.drawable.fg_round_10_white));
            flMode3.setClickable(false);
        }

//        tvMode1.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_DEFAULT ? R.color.orange : R.color.tblack));
//        tvMode2.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_SHOW ? R.color.orange : R.color.tblack));
//        tvMode3.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_SIMU ? R.color.orange : R.color.tblack));
        tvMode1.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_DEFAULT ? R.color.theme : R.color.tblack));
        tvMode2.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_SHOW ? R.color.theme : R.color.tblack));
        tvMode3.setTextColor(ContextCompat.getColor(mContext, mode == PracticeHelper.MODE_SIMU ? R.color.theme : R.color.tblack));
    }

    @OnClick({R.id.closeView,R.id.conlMode1, R.id.conlMode2, R.id.flMode3})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.closeView:
                dismiss();
                break;
            case R.id.conlMode1:
                UserCache.cachePracticeMode(PracticeHelper.MODE_DEFAULT);
                break;
            case R.id.conlMode2:
                UserCache.cachePracticeMode(PracticeHelper.MODE_SHOW);
                break;
            case R.id.flMode3:
                UserCache.cachePracticeMode(PracticeHelper.MODE_SIMU);
                break;
        }
        initMode();
        dismiss();
        if (mOnChangeModeListener != null)
            mOnChangeModeListener.onChangeMode();
    }

    private OnChangeModeListener mOnChangeModeListener;

    public void setOnChangeModeListener(OnChangeModeListener onChangeModeListener) {
        this.mOnChangeModeListener = onChangeModeListener;
    }

    public interface OnChangeModeListener {
        void onChangeMode();
    }
}
