package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.InputEmailDialogBinding;
import com.hjq.toast.ToastUtils;

public class InputEmailDialog extends Dialog implements View.OnClickListener{
    private InputEmailDialogBinding binding;
    private OnPositiveListener onPositiveListener;

    public InputEmailDialog(@NonNull Context context) {
        super(context);
        View view = getLayoutInflater().inflate(R.layout.input_email_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(view);

    }

    @Override
    public void onClick(View view) {
        if(view == binding.tvNegative){
            dismiss();

        }else if(view == binding.tvPositive){
            if(binding.etContent.length() == 0){
                ToastUtils.show("请输入邮箱");
                return;
            }

            if(onPositiveListener != null){
                dismiss();
                onPositiveListener.onPositive(binding.etContent.getText().toString());
            }
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawableResource(R.color.tran);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 295);
    }

    public InputEmailDialog setTitle(String title){
        binding.tvTitle.setText(title);
        return this;
    }

    public InputEmailDialog setContent(String content){
        binding.etContent.setText(content);
        return this;
    }
    public InputEmailDialog setHint(String hint){
        binding.etContent.setHint(hint);
        return this;
    }

    public InputEmailDialog setPositiveText(String text){
        binding.tvPositive.setText(text);
        return this;
    }

    public InputEmailDialog setOnPositiveListener(OnPositiveListener onPositiveListener) {
        this.onPositiveListener = onPositiveListener;
        return this;
    }

    public interface OnPositiveListener {
        void onPositive(String email);
    }
}
