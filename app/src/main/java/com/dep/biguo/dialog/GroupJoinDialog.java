package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;

import androidx.annotation.NonNull;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.widget.TimeView;

public class GroupJoinDialog extends Dialog implements View.OnClickListener {
    private Builder builder;

    public GroupJoinDialog(@NonNull Context context, Builder builder) {
        super(context);
        this.builder = builder;
        setContentView(R.layout.group_join_dialog);

        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        init();
    }


    public void init() {
        TextView titleView = findViewById(R.id.titleView);
        TimeView timeView = findViewById(R.id.timeView);
        ImageView firstPeopleAvatarView = findViewById(R.id.firstPeopleAvatarView);
        TextView firstPeopleNameView = findViewById(R.id.firstPeopleNameView);
        TextView joinView = findViewById(R.id.joinView);
        ImageView closeView = findViewById(R.id.closeView);

        if(TextUtils.isEmpty(builder.courseCode)){
            titleView.setText(builder.courseName);
        }else {
            titleView.setText(String.format("【%s】%s", builder.courseCode, builder.courseName));
        }
        TextDrawableLoader.loadLeft(getContext(), titleView, builder.titleIcon);
        timeView.setText(TimeFormatUtils.formatMillisecond(builder.groupBean.getSec()));
        ImageLoader.loadAvatar(firstPeopleAvatarView, builder.groupBean.getUsers_info().get(0).getAvatar());
        firstPeopleNameView.setText(builder.groupBean.getUsers_info().get(0).getNickname());

        joinView.setOnClickListener(this);
        closeView.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if(view == findViewById(R.id.joinView)){
            builder.onJoinGroupListener.join(builder.groupBean);
            dismiss();

        }else if(view == findViewById(R.id.closeView)){
            dismiss();
        }
    }

    /**刷新时间
     *
     */
    public void notifyTime(){
        try {
            TimeView timeView = findViewById(R.id.timeView);
            timeView.setText(TimeFormatUtils.formatMillisecond(builder.groupBean.getSec()));
        }catch (NullPointerException e){
            //因为有计时器调用，当弹窗关闭时，有可能抛出空指针
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        builder.context = null;
        builder = null;
    }

    public static class Builder{
        private Context context;
        private String courseName;
        private String courseCode;
        private int titleIcon;
        private GroupBean groupBean;
        private OnJoinGroupListener onJoinGroupListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCourseName(String courseName) {
            this.courseName = courseName;
            return this;
        }

        public Builder setCourseCode(String courseCode) {
            this.courseCode = courseCode;
            return this;
        }

        public Builder setTitleIcon(int titleIcon) {
            this.titleIcon = titleIcon;
            return this;
        }

        public Builder setGroupBean(GroupBean groupBean) {
            this.groupBean = groupBean;
            return this;
        }

        public Builder setOnJoinGroupListener(OnJoinGroupListener onJoinGroupListener) {
            this.onJoinGroupListener = onJoinGroupListener;
            return this;
        }

        public GroupJoinDialog build(){
            return new GroupJoinDialog(context, this);
        }

    }
    public interface OnJoinGroupListener{
        void join(GroupBean groupBean);
    }
}
