package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.AppGoodDialogBinding;
import com.dep.biguo.utils.StartAppStoreUtil;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.integration.AppManager;

public class AppGoodDialog extends Dialog implements View.OnClickListener{
    private AppGoodDialogBinding binding;

    private OnTerribleListener onTerribleListener;
    private OnGoodAppListener onGoodAppListener;

    public AppGoodDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.app_good_dialog, null, false);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
    }


    @Override
    public void onClick(View view) {
        if(view == binding.goodView){
            if(StartAppStoreUtil.findAppStore(AppManager.getAppManager().getTopActivity())){
                if(onGoodAppListener != null) {
                    onGoodAppListener.onGoodApp();
                }
            }

        }else if(view == binding.abysmalView){
            if(onTerribleListener != null) {
                onTerribleListener.onTerrible();
            }
        }
        dismiss();
    }

    public AppGoodDialog setOnTerribleListener(OnTerribleListener onTerribleListener) {
        this.onTerribleListener = onTerribleListener;
        return this;
    }

    public AppGoodDialog setOnGoodAppListener(OnGoodAppListener onGoodAppListener) {
        this.onGoodAppListener = onGoodAppListener;
        return this;
    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        setCanceledOnTouchOutside(false);
    }
    public interface OnTerribleListener{
        void onTerrible();
    }

    public interface OnGoodAppListener{
        void onGoodApp();
    }
}
