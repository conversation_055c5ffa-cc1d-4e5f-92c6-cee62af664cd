package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.TextDrawableLoader;

public class GroupGoodsCommentDialog  extends Dialog {
    private EditText inputView;
    private TextView inputCountView;

    public GroupGoodsCommentDialog(Context context, OnSendListener onSendListener) {
        super(context, R.style.BottomInputDialog);
        setContentView(R.layout.group_goods_comment_dialog);

        inputView = findViewById(R.id.inputView);
        inputCountView = findViewById(R.id.inputCountView);
        TextView sendView = findViewById(R.id.sendView);
        inputView.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                inputCountView.setText(String.format("%s/100",inputView.length()));

                if(inputView.length() > 0){//有文字，则不显示画笔图标
                    inputView.setCompoundDrawables(null, null, null, null);
                }else {//没有文字，显示画笔图标
                    TextDrawableLoader.loadLeft(getContext(), inputView, R.drawable.practice_icon_mode1);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        sendView.setOnClickListener(v -> {
            onSendListener.onSend(inputView.getText().toString());
            dismiss();
        });
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getRealScreenSize(getContext())[0];
        getWindow().setAttributes(layoutParams);
        inputView.setFocusable(true);
        inputView.requestFocus();
        new Handler().postDelayed(() -> {
            InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        },200);
    }

    public void setComment(String comment){
        inputView.setText(comment);
    }

    public String getComment(){
        return inputView.getText().toString();
    }

    public interface OnSendListener{
        void onSend(String content);
    }
}