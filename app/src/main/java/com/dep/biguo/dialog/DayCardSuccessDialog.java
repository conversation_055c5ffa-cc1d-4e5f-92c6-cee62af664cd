package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.bean.DayCardV3Bean;
import com.dep.biguo.mvp.ui.activity.DayCardV3Activity;
import com.dep.biguo.widget.DiversificationTextView;

public class DayCardSuccessDialog extends Dialog implements View.OnClickListener {
    private DiversificationTextView prizeView;
    private ImageView closeView;
    private TextView makeView;
    private TextView shareView;

    private ImageView prizeImageView;

    private ConstraintLayout couponLayout;
    private TextView couponPriceView;
    private TextView couponDescView;
    private TextView expireDateView;
    private TextView lookCouponView;

    private DayCardV3Bean.Prize prizeBean;

    private OnShareListener onShareListener;

    public DayCardSuccessDialog(@NonNull Context context, DayCardV3Bean.Prize prizeBean, OnShareListener onShareListener) {
        super(context);
        setContentView(R.layout.day_card_success_dialog);
        this.onShareListener = onShareListener;
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);

        this.prizeBean = prizeBean;

        //积分或果币的UI
        prizeImageView = findViewById(R.id.prizeImageView);
        //优惠券的UI
        couponLayout = findViewById(R.id.couponLayout);
        couponPriceView = findViewById(R.id.couponPriceView);
        couponDescView = findViewById(R.id.couponDescView);
        expireDateView = findViewById(R.id.expireDateView);
        lookCouponView = findViewById(R.id.lookCouponView);
        //公共UI
        closeView = findViewById(R.id.closeView);
        prizeView = findViewById(R.id.prizeView);
        makeView = findViewById(R.id.makeView);
        shareView = findViewById(R.id.shareView);

        prizeView.setText(String.format("恭喜您，获得%s",getPrizeName(prizeBean)));
        if(DayCardV3Activity.FRUIT_COIN.equals(prizeBean.getPrize_type())){
            showCouponView(false);
            //mageLoader.loadImage(prizeImageView, prizeBean.getIcon());
            prizeImageView.setImageResource(R.drawable.day_card_prize_fruition_show);

        }else if(DayCardV3Activity.INTEGRAL.equals(prizeBean.getPrize_type())){
            showCouponView(false);
            prizeImageView.setImageResource(R.drawable.day_card_prize_integral_show);

        }else if(DayCardV3Activity.VIP.equals(prizeBean.getPrize_type())){
            showCouponView(false);
            prizeImageView.setImageResource(R.drawable.day_card_prize_book_show);

        }else if(DayCardV3Activity.MEMBER.equals(prizeBean.getPrize_type())){
            showCouponView(false);
            prizeImageView.setImageResource(R.drawable.day_card_prize_member_show);

        }else if(DayCardV3Activity.COUPON.equals(prizeBean.getPrize_type())){
            showCouponView(true);
            couponPriceView.setText(String.format("¥%s", prizeBean.getPrize_value()));
            couponDescView.setText(prizeBean.getPrize_desc());
            expireDateView.setText(prizeBean.getValid_date());

        }else {
            //以防万一出现不在预估内的奖品，显示一个通用的弹窗
            showCouponView(false);
            prizeImageView.setImageResource(R.drawable.day_card_success);
        }

        closeView.setOnClickListener(this);
        lookCouponView.setOnClickListener(this);
        makeView.setOnClickListener(this);
        shareView.setOnClickListener(this);
    }

    public void showCouponView(boolean isShowCouponView){
        prizeImageView.setVisibility(isShowCouponView ? View.GONE : View.VISIBLE);
        couponLayout.setVisibility(isShowCouponView ? View.VISIBLE : View.GONE);
        makeView.setVisibility(isShowCouponView ? View.VISIBLE : View.GONE);
    }

    public String getPrizeName(DayCardV3Bean.Prize prizeBean){
        if(DayCardV3Activity.FRUIT_COIN.equals(prizeBean.getPrize_type())){
            return String.format("%s果币", prizeBean.getPrize_value());
        }else if(DayCardV3Activity.INTEGRAL.equals(prizeBean.getPrize_type())){
            return String.format("%s积分", prizeBean.getPrize_value());
        }else if(DayCardV3Activity.VIP.equals(prizeBean.getPrize_type())){
            return String.format("VIP题库%s天", prizeBean.getValid_lenth());
        }else if(DayCardV3Activity.MEMBER.equals(prizeBean.getPrize_type())){
            return String.format("笔果折扣卡%s天", prizeBean.getValid_lenth());
        }else if(DayCardV3Activity.COUPON.equals(prizeBean.getPrize_type())){
            return String.format("%s元优惠券", prizeBean.getPrize_value());
        }else {
            return "";
        }
    }

    @Override
    public void onClick(View view) {
        if(view == shareView){
            onShareListener.onShare();

        }else if(view == makeView){
            //当优惠券的类型是通用优惠券或适用多种商品时，传递空字符串，表示拼团特惠页面需要展示底部的筛选按钮
            boolean isMultipleType = prizeBean.getCoupon_category().equals("all") || prizeBean.getCoupon_category().contains(",");
            onShareListener.makeCoupon(isMultipleType ? "" : prizeBean.getCoupon_category());

        }else if(view == lookCouponView){
            onShareListener.lookCoupon();
        }
        dismiss();
    }

    public interface OnShareListener{
        void onShare();

        void makeCoupon(String goodsType);

        void lookCoupon();
    }
}
