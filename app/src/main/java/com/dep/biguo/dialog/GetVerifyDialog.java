package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.dep.biguo.R;
import com.dep.biguo.utils.image.ImageLoader;

public class GetVerifyDialog extends Dialog implements View.OnClickListener {
    private ImageView imageVerifyView;
    private EditText inputVerifyView;//取消
    private TextView negativeView;//取消
    private TextView positiveView;//确定
    private OnListener onListener;

    public GetVerifyDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.get_verify_dialog);

        imageVerifyView=findViewById(R.id.imageVerifyView);
        inputVerifyView=findViewById(R.id.inputVerifyView);
        negativeView=findViewById(R.id.negativeView);
        positiveView=findViewById(R.id.positiveView);

        imageVerifyView.setOnClickListener(this);
        negativeView.setOnClickListener(this);
        positiveView.setOnClickListener(this);
    }

    public void setImageVerify(String url){
        inputVerifyView.setText("");
        imageVerifyView.setBackgroundColor(getContext().getResources().getColor(R.color.tran));
        //Glide.with(getContext()).load(url).into(imageVerifyView);
        ImageLoader.loadImageNoPlaceholder(imageVerifyView, url);

    }

    @Override
    public void onClick(View v) {
        if(onListener == null) return;

        if(v == imageVerifyView){
            onListener.refreshImageVerify();
        }else if(v == positiveView){
            onListener.positive(inputVerifyView.getText().toString());
            dismiss();
        }else if(v == negativeView){
            dismiss();
        }
    }

    @Override
    public void show() {
        super.show();
        if(onListener != null){
            onListener.refreshImageVerify();
        }

        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        final InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputVerifyView.postDelayed(() -> {
            inputVerifyView.requestFocus();
            inputMethodManager.showSoftInput(inputVerifyView, 0);
        }, 300);
    }

    public interface OnListener{
        void refreshImageVerify();//刷新图片验证码
        void positive(String verify);//确定
    }

    public void setOnListener(OnListener onListener) {
        this.onListener = onListener;
    }
}
