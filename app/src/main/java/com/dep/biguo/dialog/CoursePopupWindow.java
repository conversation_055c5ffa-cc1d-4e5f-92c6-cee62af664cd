package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.adapter.CourseAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.ArrayList;

public class CoursePopupWindow extends PopupWindow {

    private CourseAdapter mCourseAdapter;

//    private CourseAdapter mCourseAdapter;
    private TextView tvEmpty;
    private TextView tvProfession;
    private TextView tvSchool;

    public CoursePopupWindow(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.popup_course, null);

        this.setContentView(view);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(DisplayHelper.getRealScreenSize(context)[1] - DisplayHelper.getStatusBarHeight(context)-DisplayHelper.dp2px(context,44));
        this.setBackgroundDrawable(new ColorDrawable());
        this.setTouchable(true);

        tvEmpty = view.findViewById(R.id.tvEmpty);
        tvProfession = view.findViewById(R.id.tvProfession);
        tvSchool = view.findViewById(R.id.tvSchool);

        RecyclerView rvCourse = view.findViewById(R.id.rvCourse);
        rvCourse.setLayoutManager(new LinearLayoutManager(context));
        mCourseAdapter = new CourseAdapter(new ArrayList<>());
        mCourseAdapter.bindToRecyclerView(rvCourse);
        view.findViewById(R.id.flContent).setOnClickListener(v -> dismiss());

        /*view.findViewById(R.id.tvManager).setOnClickListener(v -> {
            ArmsUtils.startActivity(CourseActivity.class);
            dismiss();
        });*/
    }

    public void setProfessionName(String name){
        if(Constant.CK.equals(UserCache.getAppType())) {
            tvProfession.setText("专业：" + name);
        }else {
            tvProfession.setText("技能证：" + name);
        }
    }

    public void setSchoolName(String name) {
        if (TextUtils.isEmpty(name)) {
            tvSchool.setVisibility(View.GONE);
        } else {
            tvSchool.setText("学校：" + name);
            tvSchool.setVisibility(View.VISIBLE);
        }

    }
    public void setCourseData(CourseGroupBean data) {
//        tvEmpty.setVisibility(AppUtils.isEmpty(data) ? View.VISIBLE : View.GONE);
        mCourseAdapter.setNewData(new ArrayList<>());
        for (int i = 0; i < data.getData().size(); i++) {
            CourseGroupBean group = data.getData().get(i);
            mCourseAdapter.addData(new CourseGroupBean.CourseBean(CourseAdapter.HEAD, group.getGroup(), AppUtil.isEmpty(group.getCourses())));
            mCourseAdapter.addData(group.getCourses());
        }
    }

//    public class CourseAdapter extends CommonAdapter<CourseGroupBean.CourseBean> {
//
//        public CourseAdapter(@Nullable List<CourseGroupBean.CourseBean> data) {
//            super(R.layout.popup_course_item, data);
//        }
//
//        @Override
//        protected void convert(BaseViewHolder holder, CourseGroupBean.CourseBean item) {
//            holder.setText(R.id.tvCode, item.getCode());
//            holder.setText(R.id.tvName, item.getName());
//
//            CourseGroupBean.CourseBean bean = UserHelper.getCourse();
//            if (bean != null)
//                holder.setTextColor(R.id.tvName, ContextCompat.getColor(mContext, bean.getId() == item.getId() ? R.color.orange : R.color.tblack));
//            else
//                holder.setTextColor(R.id.tvName, ContextCompat.getColor(mContext, R.color.tblack));
//        }
//    }

    public CourseAdapter getCourseAdapter() {
        return mCourseAdapter;
    }

}