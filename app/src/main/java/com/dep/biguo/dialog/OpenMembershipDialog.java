package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.OpenMembershipDialogBean;
import com.dep.biguo.databinding.OpenMembershipDialogBinding;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.utils.MathUtil;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.utils.ArmsUtils;

public class OpenMembershipDialog extends Dialog {
    private OpenMembershipDialogBinding binding;
    public OpenMembershipDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.open_membership_dialog, null, false);
        binding.buyView.setOnClickListener(v -> {
            ArmsUtils.startActivity(BiguoVipOpenActivity.class);
            dismiss();
        });
        binding.closeView.setOnClickListener(v -> {dismiss();});

        setContentView(binding.getRoot());
        getWindow().setBackgroundDrawableResource(R.color.tran);
    }

    public OpenMembershipDialog setBean(OpenMembershipDialogBean bean){
        binding.discountView.setText(String.format("%s折", MathUtil.StringMulFloat(bean.getDiscount(), "10")));
        binding.discountDescView.setText(bean.getDescription());
        binding.buyView.setText(String.format("¥%s 立即开通", bean.getPrice()));

        if(!AppUtil.isEmpty(bean.getEquity())) {
            OpenMembershipDialogBean.EquityBean firstEquityBean = bean.getEquity().get(0);

            ImageLoader.loadImageNoPlaceholder(binding.firstIconView, firstEquityBean.getIcon());
            binding.firstTextView.setText(firstEquityBean.getTitle());
        }else {
            binding.firstLayout.setVisibility(View.GONE);
        }

        if(!AppUtil.isEmpty(bean.getEquity()) && bean.getEquity().size() > 1) {
            OpenMembershipDialogBean.EquityBean secondEquityBean = bean.getEquity().get(1);

            ImageLoader.loadImageNoPlaceholder(binding.secondIconView, secondEquityBean.getIcon());
            binding.secondTextView.setText(secondEquityBean.getTitle());
        }else {
            binding.secondLayout.setVisibility(View.GONE);
        }

        if(!AppUtil.isEmpty(bean.getEquity()) && bean.getEquity().size() > 2) {
            OpenMembershipDialogBean.EquityBean threeEquityBean = bean.getEquity().get(2);

            ImageLoader.loadImageNoPlaceholder(binding.threeIconView, threeEquityBean.getIcon());
            binding.threeTextView.setText(threeEquityBean.getTitle());
        }else {
            binding.threeLayout.setVisibility(View.GONE);
        }

        return this;
    }

    @Override
    public void dismiss() {
        super.dismiss();
        UserCache.cacheOpenMembership();
    }
}
