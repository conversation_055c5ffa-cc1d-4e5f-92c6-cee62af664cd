package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.GroupUserBean;
import com.dep.biguo.mvp.ui.adapter.GroupListAdapter;
import com.dep.biguo.utils.mmkv.UserCache;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class GroupListDialog extends DialogFragment {
    public static final String DIALOG_TAG = "GroupListDialog";

    private ImageView ivClose;
    private RecyclerView rvGroup;
    private TextView tvMaxGroupCount;
    private GroupListAdapter mGroupNewAdapter;
    private List<GroupBean> data;
    private GoJoinGroupListener mGoJoinGroupListener;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_group_list, container, false);
        ivClose = view.findViewById(R.id.ivClose);
        rvGroup = view.findViewById(R.id.rvGroup);
        tvMaxGroupCount = view.findViewById(R.id.tvMaxGroupCount);
        ivClose.setOnClickListener(v -> dismiss());

        tvMaxGroupCount.setVisibility(data.size() > 5 ? View.VISIBLE : View.INVISIBLE);

        mGroupNewAdapter = new GroupListAdapter(R.layout.group_new_rv_item, data);
        mGroupNewAdapter.bindToRecyclerView(rvGroup);
        DividerItemDecoration dec = new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL);
        dec.setDrawable(getResources().getDrawable(R.drawable.province_v2_divider));
        rvGroup.addItemDecoration(dec);

        mGroupNewAdapter.setOnItemChildClickListener((adapter, view1, position) -> {
            if (null != mGoJoinGroupListener) {
                dismiss();
                GroupBean groupBean=mGroupNewAdapter.getData().get(position);
                GroupUserBean groupUserBean=groupBean.getUsers_info().get(0);
                //检测是否是该拼团的发起者
                boolean isInvite= UserCache.getUserCache() != null
                        && groupUserBean.getUsers_id() == UserCache.getUserCache().getUser_id()
                        && groupUserBean.getIs_head() == 1;
                mGoJoinGroupListener.OnJoin(groupBean, isInvite);
            }
        });
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, 0);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOnKeyListener((dialog1, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    getActivity().finish();
                    return true;
                }
                return false;
            });
        }
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, DIALOG_TAG);

    }

    public void notifyChangeData(){
        if(mGroupNewAdapter!=null){
            mGroupNewAdapter.notifyTime();
        }
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    /**
     * setting up a new instance to data;
     *
     * @param data
     */
    public void setAdapterData(@Nullable List<GroupBean> data) {
        if (null == this.data) {
            this.data = new ArrayList<>();
            this.data.addAll(data);
        } else {
            this.data.clear();
            this.data.addAll(data);
        }
    }

    public void setGoJoinGroupListener(GoJoinGroupListener mGoJoinGroupListener) {
        this.mGoJoinGroupListener = mGoJoinGroupListener;
    }

    public interface GoJoinGroupListener {
        void OnJoin(GroupBean data,boolean isInvite);
    }
}
