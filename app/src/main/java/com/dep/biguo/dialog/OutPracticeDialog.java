package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;

public class OutPracticeDialog extends Dialog implements View.OnClickListener{
    private ConstraintLayout rootView;
    private ImageView closeView;
    private TextView doQuestionView;
    private TextView leaveView;
    private OnLeaveListener onLeaveListener;

    public OutPracticeDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.out_practice_dialog);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);

        rootView = findViewById(R.id.rootView);
        closeView = findViewById(R.id.closeView);
        doQuestionView = findViewById(R.id.doQuestionView);
        leaveView = findViewById(R.id.leaveView);

        closeView.setOnClickListener(this);
        doQuestionView.setOnClickListener(this);
        leaveView.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if(view == leaveView){
            onLeaveListener.onLeave();
        }
        dismiss();
    }

    @Override
    public void show() {
        super.show();
    }

    public OutPracticeDialog setLeaveText(CharSequence leaveText){
        leaveView.setText(leaveText);
        return this;
    }

    public OutPracticeDialog setOnLeaveListener(OnLeaveListener onLeaveListener) {
        this.onLeaveListener = onLeaveListener;
        return this;
    }

    public interface OnLeaveListener{
        void onLeave();
    }
}
