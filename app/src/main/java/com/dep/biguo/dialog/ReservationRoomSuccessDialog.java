package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrganizationReservationRoomSuccessBean;
import com.dep.biguo.databinding.ReservationRoomSuccessDialogBinding;

public class ReservationRoomSuccessDialog extends Dialog implements View.OnClickListener {
    private ReservationRoomSuccessDialogBinding binding;
    public ReservationRoomSuccessDialog(@NonNull Context context) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.reservation_room_success_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
    }

    public ReservationRoomSuccessDialog setData(OrganizationReservationRoomSuccessBean data){
        binding.resultView.setText(data.getText());
        binding.dateView.setRightText(data.getDate());
        binding.timeView.setRightText(data.getTime_period());
        binding.durationView.setRightText(data.getDuration());
        binding.numberView.setRightText(data.getSeat_number());
        binding.typeView.setRightText(data.getReserve_way());
        binding.addressView.setRightText(data.getAddress());
        binding.telView.setRightText(data.getContact_number());
        return this;
    }

    @Override
    public void onClick(View view) {
        dismiss();
    }

    @Override
    public void show() {
        super.show();
        Window dialogWindow = getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = (int) (DisplayHelper.getWindowWidth(getContext()) * 0.9);
        dialogWindow.setAttributes(lp);

        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
    }
}
