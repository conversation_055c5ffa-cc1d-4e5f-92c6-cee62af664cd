package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.databinding.UpdateAppDialogBinding;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.download.CallBackUtil;
import com.dep.biguo.utils.download.OkhttpUtil;
import com.dep.biguo.utils.download.RequestUtil;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.hjq.toast.ToastUtils;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.Call;

public class UpdateAppDialog extends Dialog {
    private UpdateAppDialogBinding binding;
    private Builder builder;

    public UpdateAppDialog(Builder builder) {
        super(builder.context);
        setCancelable(!builder.isMustUpdate);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        setOnCancelListener((OnCancelListener) dialog -> cancelShow());

        this.builder = builder;

        View view = LayoutInflater.from(builder.context).inflate(R.layout.update_app_dialog, null);
        binding = DataBindingUtil.bind(view);
        setContentView(binding.getRoot());

        showUpdateMessage();
    }

    public void showUpdateMessage(){
        binding.tvContent.setVisibility(View.VISIBLE);
        binding.progressLayout.setVisibility(View.GONE);
        binding.tvNegative.setVisibility(builder.isMustUpdate ? View.GONE : View.VISIBLE);
        binding.tvPositive.setVisibility(View.VISIBLE);
        binding.verticalLineView.setVisibility(builder.isMustUpdate ? View.INVISIBLE : View.VISIBLE);

        binding.tvContent.setText("温馨提示");
        if(TextUtils.isEmpty(builder.updateContent)){
            binding.tvTitle.setText(builder.newVersion+"版本更新");
            binding.tvContent.setText(builder.isMustUpdate ? "APP版本过低，请更新后重试":"是否跳转至应用商店更新？");
        }else {
            binding.tvTitle.setText(builder.newVersion+"版本更新内容");
            binding.tvContent.setText(builder.updateContent+"\n是否跳转至应用商店更新？");
        }
        binding.tvContent.setGravity(TextUtils.isEmpty(builder.updateContent) ? Gravity.CENTER : Gravity.START);
        binding.tvNegative.setText("稍后再说");
        binding.tvPositive.setText("立即更新");

        binding.tvNegative.setOnClickListener(v -> cancelShow());
        binding.tvPositive.setOnClickListener(v -> {
            //华为、小米、OPPO(两个)、VIVO、应用宝
            String[] appPackageName = {"com.huawei.appmarket", //华为
                    "com.hihonor.appmarket",//荣耀
                    "com.xiaomi.market", //小米
                    "com.oppo.market", //OPPO
                    "com.heytap.market", //OPPO
                    "com.bbk.appstore", //vivo
                    "com.lenovo.leos.appstore",//联想
                    "com.tencent.android.qqdownloader"//应用宝
            };
            for(String packageName : appPackageName){
                if(isInstallApp(packageName)){
                    launchAppDetail(getContext().getPackageName(), packageName);
                    if(!builder.isMustUpdate){
                        dismiss();
                    }
                    return;
                }
            }

            if(!AppUtil.isEmpty(builder.appDownloadLink)) {
                downloadApk(builder.appDownloadLink);
            }else {
                ToastUtils.show("您未安装应用商店");
            }
        });
    }

    public void showDownloadMessage(RequestUtil requestUtil){
        binding.tvTitle.setText("下载进度");
        binding.tvContent.setVisibility(View.INVISIBLE);
        binding.tvNegative.setText("取消下载");
        binding.tvNegative.setVisibility(View.VISIBLE);
        binding.tvPositive.setVisibility(View.GONE);
        binding.progressLayout.setVisibility(View.VISIBLE);

        //给取消按钮添加取消功能
        binding.tvNegative.setOnClickListener(v -> {
            requestUtil.cancel();
            deleteApk(builder.context);
            //强制更新，在点击取消下载，禁止取消弹窗
            if(builder.isMustUpdate){
                LogUtil.d("dddd", "must");
                showUpdateMessage();
            }else {
                LogUtil.d("dddd", "弄 must");
                dismiss();
            }
        });
    }

    public void showInstallMessage(File file){
        binding.tvContent.setVisibility(View.VISIBLE);
        binding.tvPositive.setVisibility(View.VISIBLE);
        binding.tvNegative.setVisibility(builder.isMustUpdate ? View.GONE : View.VISIBLE);
        binding.progressLayout.setVisibility(View.GONE);

        binding.tvTitle.setText("下载完成");
        binding.tvContent.setText("是否立即安装？");
        binding.tvContent.setGravity(Gravity.CENTER);
        binding.tvNegative.setText("稍后安装");
        binding.tvPositive.setText("安装");

        binding.tvNegative.setOnClickListener(v -> {
            //强制更新，在点击稍后安装，禁止取消弹窗
            if(builder.isMustUpdate){
                showUpdateMessage();
            }else {
                dismiss();
            }
        });

        binding.tvPositive.setOnClickListener(v -> {
            if (file != null && file.exists()) {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);//安装完成后打开新版本
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION); // 给目标应用一个临时授权
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N) {
                    //如果SDK版本>24，即：Build.VERSION.SDK_INT > 24，使用FileProvider兼容安装apk
                    Uri uri = FileProvider.getUriForFile(builder.context, builder.context.getPackageName() + ".fileprovider", file);
                    intent.setDataAndType(uri, "application/vnd.android.package-archive");
                } else {
                    intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
                }
                builder.context.startActivity(intent);
            }

            //不是强制更新，可以在点击安装之后取消
            if(!builder.isMustUpdate){
                dismiss();
            }
        });
    }

    public void cancelShow(){
        DeviceCache.cacheIsTodayRemindUpdateApp(System.currentTimeMillis());
        if(builder.onCancelListener != null){
            builder.onCancelListener.onCancel(UpdateAppDialog.this);
        }
        dismiss();
    }

    public void downloadApk(String downloadUrl){
        //如果安装包已存在，则直接开始安装
        File file = new File(getApkPath(builder.context));
        if(file.exists()){
            showInstallMessage(file);
            return;
        }
        //服务器会随机的对下发的资源做GZip操作，说明：https://blog.csdn.net/fighting_2017/article/details/93972909
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("Accept-Encoding", "identity");
        //下载
        RequestUtil requestUtil = OkhttpUtil.okHttpGet(downloadUrl, null, paramsMap, new CallBackUtil.CallBackFile(file.getParent(), file.getName()) {
            @Override
            public void onProgress(float progress, long total) {
                binding.progressView.setProgress((int) progress);
                binding.progressTextView.setText(String.format("%s%%",(int) progress));
            }

            @Override
            public void onFailure(Call call, Exception e) {
                deleteApk(builder.context);
                ToastUtils.show("下载更新包失败");
            }

            @Override
            public void onResponse(File response) {
                //强制更新的下载过程中，取消下载，不能关闭弹窗
                if(response == null && builder.isMustUpdate) return;
                //提示更新的下载过程中，取消下载，可直接关闭弹窗
                if(response == null) {
                    dismiss();
                    return;
                }
                //下载的文件原本只有读写权限，但没有运行权限，需要给文件设置运行权限，方能安装，否则提示解析异常
                try {
                    Process p = Runtime.getRuntime().exec("chmod 755 " + response);
                    p.waitFor();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //提示安装
                showInstallMessage(response);
            }
        });

        showDownloadMessage(requestUtil);
    }

    public String getApkPath(Context context){
        //下载路径
        String dir = context.getFilesDir().getPath();
        //APK文件名
        String appName = "zikaobiguotiku"+builder.newVersion.replaceAll("\\.", "");
        //APK文件路径
        return dir + "/" +appName;
    }

    /**删除APK安装包
     *
     */
    public static void deleteApk(Context context){
        //文件夹不存在，直接返回
        File file = context.getFilesDir();
        if(!file.exists()) return;

        //文件夹没有子文件，直接返回
        File[] files = file.listFiles();
        if(files == null) return;

        //删除所有与安装包
        for(File childFile : files) {
            if(childFile.getName().matches("^zikaobiguotiku.*\\.apk$")) {
                childFile.delete();
            }
        }

    }

    /**判断是否安装了应用市场
     * @param packageName 指定的应用市场
     * @return
     */
    public boolean isInstallApp(String packageName){
        final PackageManager packageManager = getContext().getPackageManager();// 获取packagemanager
        try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    /**跳转应用商店升级应用
     * @param appPkg 需要升级的App包名
     * @param marketPkg 应用商店包名，如果为”则由系统弹出应用商店列表供用户选择,否则调转到目标市场的应用详情界面
     */
    public void launchAppDetail(String appPkg, String marketPkg){
        try {
            if(TextUtils.isEmpty(appPkg)) return;

            Uri uri = Uri.parse("market://details?id=" + appPkg);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            if(TextUtils.isEmpty(marketPkg)) {
                intent.setPackage(marketPkg);
            }
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            getContext().startActivity(intent);
        }catch (Exception e){

        }
    }

    public interface OnCancelListener extends DialogInterface.OnCancelListener{

    }

    public static class Builder{
        private AppCompatActivity context;
        private String appDownloadLink;//APP下载地址
        private String newVersion;//可升级的版本号
        private String updateContent;//更新内容
        private boolean isMustUpdate;//是否强更
        private OnCancelListener onCancelListener;

        public Builder(AppCompatActivity context) {
            this.context = context;
        }

        public Builder setAppDownloadLink(String appDownloadLink) {
            this.appDownloadLink = appDownloadLink;
            return this;
        }

        public Builder setNewVersion(String newVersion) {
            this.newVersion = newVersion;
            return this;
        }

        public Builder setUpdateContent(String updateContent) {
            this.updateContent = updateContent;
            return this;
        }

        public Builder setIsMustUpdate(boolean isMustUpdate) {
            this.isMustUpdate = isMustUpdate;
            return this;
        }

        public Builder setOnCancelListener(OnCancelListener onCancelListener) {
            this.onCancelListener = onCancelListener;
            return this;
        }

        public UpdateAppDialog build(){
            return new UpdateAppDialog(this);
        }
    }
}
