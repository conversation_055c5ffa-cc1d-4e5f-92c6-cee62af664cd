package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.PrizeBean;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.ItemDecoration;

import java.util.List;

public class DayCardReceiveCourseDialog extends Dialog implements View.OnClickListener {
    private RecyclerView courseRecyclerView;//课程列表
    private TextView finishView;//确认领取
    private ImageView closeView;//关闭弹窗

    private SelectedAdapter selectedAdapter;
    private OnSelectedListener onSelectedListener;

    public DayCardReceiveCourseDialog(@NonNull Context context, List<PrizeBean.Prize> list, OnSelectedListener onSelectedListener) {
        super(context);
        this.onSelectedListener = onSelectedListener;
        setContentView(R.layout.day_card_receive_course_dialog);
        if(UserCache.isDayNight()){
            findViewById(R.id.courseLayout).getBackground().setTint(getContext().getResources().getColor(R.color.black));
        }
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);

        courseRecyclerView = findViewById(R.id.courseRecyclerView);
        finishView = findViewById(R.id.finishView);
        closeView = findViewById(R.id.closeView);

        finishView.setOnClickListener(this);
        closeView.setOnClickListener(this);

        courseRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.line_color));

        selectedAdapter = new SelectedAdapter(list);
        selectedAdapter.bindToRecyclerView(courseRecyclerView);

    }

    @Override
    public void onClick(View view) {
        if(view == finishView){
            onSelectedListener.onSelected(selectedAdapter.prize_id, selectedAdapter.value);
            dismiss();

        }else if(view == closeView){
            dismiss();
        }
    }

    public interface OnSelectedListener{
        void onSelected(int prize_id, String value);
    }

    private class SelectedAdapter extends CommonAdapter<PrizeBean.Prize>{
        private int prize_id;
        private String value;

        public SelectedAdapter(@Nullable List<PrizeBean.Prize> data) {
            super(R.layout.day_card_shop_item, data);
            if(data != null && data.size() > 0) {
                prize_id = data.get(0).getPrize_id();
                value = data.get(0).getValue();
            }
        }

        @Override
        protected BaseViewHolder createBaseViewHolder(View view) {
            TextView textView = new TextView(mContext);
            textView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.dp2px(mContext, 48)));
            textView.setTextColor(mContext.getResources().getColor(R.color.tblack));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            textView.setGravity(Gravity.CENTER_VERTICAL);
            return new BaseViewHolder(textView);
        }

        @Override
        protected void convert(BaseViewHolder holder, PrizeBean.Prize item) {
            TextView textView = (TextView) holder.itemView;
            textView.setText(item.getName());
            LogUtil.d("dddd", item.getName()+" "+item.getPrize_id() +" "+ prize_id);
            if(item.getValue().equals(value)){
                TextDrawableLoader.loadRight(mContext, textView, R.drawable.selected);
            }else {
                TextDrawableLoader.loadRight(mContext, textView, R.drawable.un_selected);
            }

            if(!textView.hasOnClickListeners()){
                textView.setOnClickListener(v -> {
                    PrizeBean.Prize prize = getItem(holder.getAdapterPosition());
                    prize_id = prize.getPrize_id();
                    value = prize.getValue();
                    notifyDataSetChanged();
                });
            }
        }
    }

}
