package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.StudyPlanMakeDialogBinding;
import com.dep.biguo.mvp.ui.activity.MakeStudyPlanActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.mmkv.UserCache;

public class StudyPlanMakeDialog extends Dialog implements View.OnClickListener{
    private StudyPlanMakeDialogBinding binding;

    public StudyPlanMakeDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.study_plan_make_dialog, null, false);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        setCanceledOnTouchOutside(false);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 320);
        getWindow().setBackgroundDrawableResource(R.color.tran);

        UserCache.cacheShowMakePlanDialog();
    }

    @Override
    public void onClick(View view) {
        dismiss();
        if(view == binding.makePlanView){
            if(!MainAppUtils.checkLogin(getContext())) return;

            MakeStudyPlanActivity.start(getContext(), 0, false, 1,null);
        }
    }
}
