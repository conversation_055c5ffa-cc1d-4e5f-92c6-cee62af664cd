package com.dep.biguo.dialog;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrganizationReservationRoomTimeBean;
import com.dep.biguo.databinding.SelectDateReservationDialogBinding;
import com.dep.biguo.mvp.ui.adapter.OrganizationSelectBaseAdapter;
import com.dep.biguo.widget.loadsir.EmptyContentCallBack;
import com.dep.biguo.widget.loadsir.ErrorCallBack;
import com.dep.biguo.widget.loadsir.LoadingCallBack;
import com.hjq.toast.ToastUtils;
import com.kingja.loadsir.callback.Callback;
import com.kingja.loadsir.core.LoadService;
import com.kingja.loadsir.core.LoadSir;

import java.util.ArrayList;
import java.util.List;


public class SelectDateReservationDialog extends BottomDialog implements View.OnClickListener{
    private SelectDateReservationDialogBinding binding;
    private Adapter timeAdapter;
    private LoadService<String> loadService;

    private OnLoadTimeListener onLoadTimeListener;
    private OnSelectListener onSelectListener;

    private List<OrganizationReservationRoomTimeBean> checkList;

    private String region;
    private String number;

    public SelectDateReservationDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.select_date_reservation_dialog;
    }

    @Override
    public void init() {
        binding = DataBindingUtil.bind(findViewById(R.id.rootView));
        binding.setOnClickListener(this);

        checkList = new ArrayList<>();

        //时段列表
        timeAdapter = new Adapter();
        timeAdapter.setOnItemClickListener((adapter, view1, position) -> {
            OrganizationReservationRoomTimeBean timeBean = timeAdapter.getItem(position);
            if(timeBean.getReserve_status() != 0) return;//不可预约状态，则不加入选中列表

            if(checkList.contains(timeBean)){
                checkList.remove(timeBean);
            }else {
                checkList.add(timeBean);
            }
            //timeAdapter.notifyItemChanged(position);会导致自动滚动到顶部
            timeAdapter.notifyDataSetChanged();
        });
        binding.timeLoopView.setAdapter(timeAdapter);

        //绑定加载状态页
        bindLoadSir(binding.loopLayout);
    }

    @Override
    public void onClick(View view) {
        if(view == binding.closeView){
            dismiss();

        }else if(view == binding.reservationView){
            if(onSelectListener != null){
                onSelectListener.onSelect(region, number, checkList);
            }
        }
    }

    private void bindLoadSir(View targetView){
        loadService = LoadSir.getDefault().register(targetView, (Callback.OnReloadListener) v -> loadData());
    }

    private void loadData(){
        showLoading();
        onLoadTimeListener.loadTime(region, number);
    }

    //添加时段数据
    public void setTimeList(List<OrganizationReservationRoomTimeBean> timeList) {
        timeAdapter.setNewData(timeList);
    }

    public SelectDateReservationDialog setRegion(String region) {
        this.region = region;
        return this;
    }

    public SelectDateReservationDialog setNumber(String number) {
        this.number = number;
        return this;
    }

    @Override
    public void show() {
        super.show();
        binding.timeTitleView.setText(String.format("%s号座位可预约时间段", number));
        checkList.clear();
        loadData();
    }

    //加载的内容为空
    public void showEmpty(){
        binding.timeTitleView.setEnabled(true);
        if(loadService != null) {
            loadService.showCallback(EmptyContentCallBack.class);
        }
    }

    //加载错误
    public void showError(){
        if(loadService != null) {
            loadService.showCallback(ErrorCallBack.class);
        }
    }

    //加载中
    public void showLoading(){
        if(loadService != null) {
            loadService.showCallback(LoadingCallBack.class);
        }
    }

    //加载成功
    public void showSuccess(){
        if(loadService != null) {
            loadService.showSuccess();
        }
    }

    public class Adapter extends OrganizationSelectBaseAdapter<OrganizationReservationRoomTimeBean> {
        public Adapter() {
            super(R.layout.select_date_reservation_dialog_item, null);
        }

        @Override
        public int spanCount() {
            return 3;
        }

        @Override
        public int itemWidth() {
            return DisplayHelper.dp2px(mContext, 100);
        }

        @Override
        protected void convert(BaseViewHolder holder, OrganizationReservationRoomTimeBean item) {
            TextView textView = holder.itemView.findViewById(R.id.timeView);
            textView.setText(item.getText());
            if(checkList.contains(item) && item.getReserve_status() == 0){
                setCheck(holder);
                setTextViewColor(holder, R.id.timeView, R.color.tblack);

            }else if(item.getReserve_status() == 0){
                setUnCheck(holder);
                setTextViewColor(holder, R.id.timeView, R.color.tblack);

            }else if(item.getReserve_status() == 1){
                setReservation(holder);
                setTextViewColor(holder, R.id.timeView, R.color.twhite);

            } else if(item.getReserve_status() == 2 || item.getReserve_status() == 3){
                setEnableCheck(holder);
                setTextViewColor(holder, R.id.timeView, R.color.tblack);
            }
        }
    }

    //需要加载时段的回调
    public SelectDateReservationDialog setOnLoadTimeListener(OnLoadTimeListener onLoadTimeListener) {
        this.onLoadTimeListener = onLoadTimeListener;
        return this;
    }

    //完成时段选择的回调
    public SelectDateReservationDialog setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
        return this;
    }

    public interface OnLoadTimeListener{
        void loadTime(String region, String number);
    }

    public interface OnSelectListener{
        void onSelect(String region, String number, List<OrganizationReservationRoomTimeBean> timeList);
    }
}
