package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.utils.MathUtil;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;

/**
 * @Author: created by biguo
 * @CreatedDate :2019年11月19日10:48:39
 * @Description: 答题报告分享弹窗
 */
public class PracticeShareDialog extends DialogFragment{

    private static final String TAG = "PracticeShareDialog";

    private String courseName;//课程名称
    private String correctRate;//正确率
    private String allCount;//题目总数量
    private String errorCount;//答错数量
    private String correctCount;//答对数量
    private String writeCount;//已答但未判断正误数量

    private View.OnClickListener mClickListener;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_practice_share, container, false);
        TextView courseNameView = view.findViewById(R.id.courseNameView);
        TextView correctRateValueView = view.findViewById(R.id.correctRateValueView);
        TextView finishCountValueView = view.findViewById(R.id.finishCountValueView);
        TextView errorCountValueView = view.findViewById(R.id.errorCountValueView);
        TextView correctCountValueView = view.findViewById(R.id.correctCountValueView);
        ImageView shareView = view.findViewById(R.id.shareView);
        View closeView = view.findViewById(R.id.closeView);

        courseNameView.setText(String.format("在「%s」学习过程中", courseName));
        correctRateValueView.setText(correctRate);
        finishCountValueView.setText(String.format("%s/%s", MathUtil.StringAddInt(MathUtil.StringAddInt(correctCount, errorCount)+"", writeCount) , allCount));
        errorCountValueView.setText( errorCount);
        correctCountValueView.setText( correctCount);

        shareView.setOnClickListener(v -> {
            dismiss();
            if (mClickListener != null)
                mClickListener.onClick(v);
        });

        closeView.setOnClickListener(v -> dismiss());

        return view;
    }

    public PracticeShareDialog setCourseName(String courseName) {
        this.courseName = courseName;
        return this;
    }

    public PracticeShareDialog setCorrectRate(String correctRate) {
        this.correctRate = correctRate;
        return this;
    }

    public PracticeShareDialog setAllCount(String allCount) {
        this.allCount = allCount;
        return this;
    }

    public PracticeShareDialog setErrorCount(String errorCount) {
        this.errorCount = errorCount;
        return this;
    }

    public PracticeShareDialog setWriteCount(String writeCount) {
        this.writeCount = writeCount;
        return this;
    }

    public PracticeShareDialog setCorrectCount(String correctCount) {
        this.correctCount = correctCount;
        return this;
    }

    public PracticeShareDialog setClickListener(View.OnClickListener clickListener) {
        this.mClickListener = clickListener;
        return this;
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        show(manager, TAG);
    }

    @Override
    public void onStart() {
        super.onStart();

        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        try {
            Class<?> clazz = Class.forName("androidx.fragment.app.DialogFragment");
            Field dismissed = clazz.getDeclaredField("mDismissed");
            Field shownByMe = clazz.getDeclaredField("mShownByMe");
            dismissed.setAccessible(false);
            shownByMe.setAccessible(true);
            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            // 这里吧原来的commit()方法换成了commitAllowingStateLoss()
            ft.commitAllowingStateLoss();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

}
