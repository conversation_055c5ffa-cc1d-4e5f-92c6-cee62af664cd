package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class PayDialog extends BottomDialog {

    @BindView(R.id.rvPay)
    RecyclerView rvPay;
    @BindView(R.id.tvPay)
    TextView tvPay;
    @BindView(R.id.tvHint)
    TextView tvHint;
    @BindView(R.id.ll_discount)
    LinearLayout llDiscount;
    @BindView(R.id.tv_discount)
    TextView tvDiscount;
    @BindView(R.id.rv_discount)
    RecyclerView rvDiscount;

    private PayAdapter mPayAdapter;
    private DiscountAdapter mDiscountAdapter;

    private Builder.Params P;
    private boolean mUseDiscount = false; //是否使用优惠券
    public PayDialog(@NonNull Context context) {
        super(context);
        mPayAdapter = new PayAdapter(new ArrayList<>());
        mDiscountAdapter = new DiscountAdapter(new ArrayList<>());
        mPayAdapter.bindToRecyclerView(rvPay);
        mDiscountAdapter.bindToRecyclerView(rvDiscount);
        ArmsUtils.configRecyclerView(rvPay, new LinearLayoutManager(context));
    }

    private void setParams(Builder.Params P) {
        this.P = P;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_pay;
    }

    @Override
    public void init() {
        tvPay.setText("确认付款 ¥" + P.mPrice);

        List<PayBean> data = new ArrayList<>();
        if (P.mShowAlipay)
            data.add(new PayBean(0, R.drawable.pay_icon_alipay, "支付宝", ""));
        if (P.mShowWechat)
            data.add(new PayBean(1, R.drawable.pay_icon_wechat, "微信支付", ""));
        if (P.mShowGuobi)
            data.add(new PayBean(2, R.drawable.pay_icon_guobi, "果币支付", ""));
        if (P.mShowPoint)
            data.add(new PayBean(3, R.drawable.pay_icon_point, "积分支付", "(1果币=10积分)"));

        if(!TextUtils.isEmpty(P.mHint)) {
            tvHint.setText(P.mHint);
        }
        tvHint.setVisibility(P.mShowHint ? View.VISIBLE : View.GONE);

        mPayAdapter.setNewData(data);
        mPayAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (position == 0 || position == 1) {
                if (mDiscountAdapter.getSelectPosition() != -1) {
                    DiscountBean bean = mDiscountAdapter.getItem(mDiscountAdapter.getSelectPosition());
                    float price = Float.parseFloat(P.mPrice) - Integer.parseInt(bean.getPrice());
                    tvPay.setText("确认付款 ¥" + price);
                } else {
                    tvPay.setText("确认付款 ¥" + P.mPrice);
                }
            } else if (position == 2) {
                tvPay.setText("确认付款 " + P.mPrice + "果币");
            } else if (position == 3) {
                tvPay.setText("确认付款 " + Double.parseDouble(P.mPrice) * 10 + "积分");
            }
            mPayAdapter.setSelectPosition(position);
        });
        if (P.beans != null && P.beans.size() > 0) {
            if (P.mOnDiscountSelectedListener == null) return;
            mDiscountAdapter.setNewData(P.beans);
            mDiscountAdapter.setOnItemClickListener((adapter, view, position) -> {
                if (mDiscountAdapter.getSelectPosition() == position) {
                    mDiscountAdapter.setSelectPosition(-1);
                    P.mOnDiscountSelectedListener.onDiscountSelected(-1);
                    tvPay.setText("确认付款 ¥" + P.mPrice);
                } else {
                    DiscountBean bean = mDiscountAdapter.getItem(position);
                    float price = Float.parseFloat(P.mPrice) - Integer.parseInt(bean.getPrice());
                    mDiscountAdapter.setSelectPosition(position);
                    P.mOnDiscountSelectedListener.onDiscountSelected(bean.getId());
                    tvPay.setText("确认付款 ¥" + price);
                }
            });
        }
        llDiscount.setVisibility(P.mShowDiscount ? View.VISIBLE : View.GONE);
        mUseDiscount = P.mUseDiscount;
        rvDiscount.setVisibility(mUseDiscount ? View.VISIBLE : View.GONE);
        Drawable drawable1 = P.mContext.getDrawable(R.drawable.opinion_normal);
        Drawable drawable2 = P.mContext.getDrawable(R.drawable.opinion_select);
        drawable1.setBounds(0, 0, drawable1.getMinimumWidth(), drawable1.getMinimumHeight());
        drawable2.setBounds(0, 0, drawable2.getMinimumWidth(), drawable2.getMinimumHeight());
        tvDiscount.setCompoundDrawables(mUseDiscount ? drawable2 : drawable1, null, null, null);
        tvDiscount.setOnClickListener(v -> {
            if (P.mDiscountClickListener != null) {
                P.mDiscountClickListener.onClick(v);
                mUseDiscount = !mUseDiscount;
                tvDiscount.setCompoundDrawables(mUseDiscount ? drawable2 : drawable1, null, null, null);
                rvDiscount.setVisibility(mUseDiscount ? View.VISIBLE : View.GONE);
            }
        });
        tvPay.setOnClickListener(v -> {
            if (AppUtil.isEmpty(mPayAdapter.getData())) return;
            if (P.mOnOldPayListener != null) {
                PayBean bean = mPayAdapter.getItem(mPayAdapter.getSelectPosition());
                if (bean.getType() == 0) {
                    P.mOnOldPayListener.onAliPay();
                }else if (bean.getType() == 1) {
                    P.mOnOldPayListener.onWechatPay();
                }else if (bean.getType() == 2) {
                    P.mOnOldPayListener.onGuobiPay();
                }else if (bean.getType() == 3) {
                    P.mOnOldPayListener.onPointPay();
                }
            }

            if (P.mOnPayListener != null) {
                PayBean bean = mPayAdapter.getItem(mPayAdapter.getSelectPosition());
                if (bean.getType() == 0) {
                    P.mOnPayListener.onPay(PayUtils.PAY_TYPE_ALIPAY);
                }else if (bean.getType() == 1) {
                    P.mOnPayListener.onPay(PayUtils.PAY_TYPE_WEXIN);
                }else if (bean.getType() == 2) {
                    P.mOnPayListener.onPay(PayUtils.FRUIT_COIN);
                }else if (bean.getType() == 3) {
                    P.mOnPayListener.onPay(PayUtils.PAY_TYPE_INTEGRAL);
                }
            }

            dismiss();
        });
    }

    public PayDialog addOnDismissListener(@Nullable OnDismissListener listener) {
        setOnDismissListener(listener);
        return this;
    }

    public void setPrice(String price) {
        P.mPrice = price;
        tvPay.setText("确认付款 ¥" + P.mPrice);
    }

    public static class Builder {
        private Params P;

        public Builder(Context context) {
            P = new Params(context);
        }

        public Builder setPrice(String price) {
            P.mPrice = price;
            return this;
        }

        public Builder setHint(String hint) {
            P.mHint = hint;
            return this;
        }

        public Builder showAlipay(boolean show) {
            P.mShowAlipay = show;
            return this;
        }

        public Builder showWechat(boolean show) {
            P.mShowWechat = show;
            return this;
        }

        public Builder showGuobi(boolean show) {
            P.mShowGuobi = show;
            return this;
        }

        public Builder showPoint(boolean show) {
            P.mShowPoint = show;
            return this;
        }

        public Builder showHint(boolean show) {
            P.mShowHint = show;
            return this;
        }

        public Builder showDiscount(boolean showDiscount) {
            P.mShowDiscount = showDiscount;
            return this;
        }

        public Builder setDiscountData(List<DiscountBean> beans) {
            if (P.beans != null && P.beans.size() > 0) P.beans.clear();
            P.beans = beans;
            if (AppUtil.isEmpty(P.beans)) {
                P.mShowDiscount = false;
            } else {
                P.mShowDiscount = true;
            }
            return this;
        }

        public Builder useDiscount(boolean useDiscount) {
            P.mUseDiscount = useDiscount;
            return this;
        }

        public Builder setDiscountClickListener(View.OnClickListener discountClickListener) {
            P.mDiscountClickListener = discountClickListener;
            return this;
        }

        public Builder setDiscountSelectListener(OnDiscountSelectedListener mOnDiscountSelectedListener) {
            P.mOnDiscountSelectedListener = mOnDiscountSelectedListener;
            return this;
        }
        public Builder setOldPayListener(OnOldPayListener mOnOldPayListener) {
            P.mOnOldPayListener = mOnOldPayListener;
            return this;
        }

        public Builder setPayListener(OnPayListener mOnPayListener) {
            P.mOnPayListener = mOnPayListener;
            return this;
        }

        public PayDialog builder() {
            PayDialog dialog = new PayDialog(P.mContext);
            dialog.setParams(P);
            P.mPayDialog = dialog;
            return dialog;
        }

        private static class Params {

            private Context mContext;
            private String mPrice;
            private String mHint;
            private boolean mShowAlipay = true;
            private boolean mShowWechat = true;
            private boolean mShowGuobi = true;
            private boolean mShowPoint;
            private boolean mShowHint = true;
            private boolean mShowDiscount;
            private boolean mUseDiscount;
            private List<DiscountBean> beans;
            private OnOldPayListener mOnOldPayListener;
            private OnPayListener mOnPayListener;
            private View.OnClickListener mDiscountClickListener;
            private OnDiscountSelectedListener mOnDiscountSelectedListener;
            private PayDialog mPayDialog;

            public Params(Context context) {
                this.mContext = context;
            }
        }

    }

    class PayAdapter extends CommonAdapter<PayBean> {

        private int selectPosition = 0;

        public PayAdapter(@Nullable List<PayBean> data) {
            super(R.layout.dialog_pay_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, PayBean item) {
            holder.setImageResource(R.id.ivIcon, item.getIcon());
            holder.setText(R.id.tvName, item.getName());

            holder.setGone(R.id.tvDetail, !TextUtils.isEmpty(item.getDetail()));
            holder.setText(R.id.tvDetail, item.getDetail());

            holder.setImageResource(R.id.ivSelect, selectPosition == holder.getLayoutPosition() ? R.drawable.pay_icon_s : R.drawable.pay_icon_n);
        }

        private void setSelectPosition(int position) {
            this.selectPosition = position;
            notifyDataSetChanged();
        }

        private int getSelectPosition() {
            return selectPosition;
        }
    }

    class DiscountAdapter extends CommonAdapter<DiscountBean> {

        private int selectPosition = -1;

        public DiscountAdapter(@Nullable List<DiscountBean> data) {
            super(R.layout.item_discount, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, DiscountBean item) {
            holder.setText(R.id.tvPrice, "优惠券 ¥" + item.getPrice());
            holder.setText(R.id.tvTime, "(有效期至 " + item.getReceive_time() + ")");

            holder.setImageResource(R.id.ivSelect, selectPosition == holder.getLayoutPosition() ? R.drawable.pay_icon_s : R.drawable.pay_icon_n);
        }

        private void setSelectPosition(int position) {
            this.selectPosition = position;
            notifyDataSetChanged();
        }

        private int getSelectPosition() {
            return selectPosition;
        }
    }
    class PayBean {
        private int type;
        private int icon;
        private String name;
        private String detail;

        public PayBean(int type, int icon, String name, String detail) {
            this.type = type;
            this.icon = icon;
            this.name = name;
            this.detail = detail;
        }

        public int getType() {
            return type;
        }

        public int getIcon() {
            return icon;
        }

        public String getName() {
            return name;
        }

        public String getDetail() {
            return detail;
        }
    }

    public interface OnOldPayListener {
        default void onAliPay() {
        }

        default void onWechatPay() {
        }

        default void onGuobiPay() {
        }

        default void onPointPay() {
        }
    }

    public interface OnPayListener{
        void onPay(String payType);
    }

    public interface OnDiscountSelectedListener {
        void onDiscountSelected(int mDiscountId);
    }
}
