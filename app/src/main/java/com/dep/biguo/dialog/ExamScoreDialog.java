package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.ImageLoader;

public class ExamScoreDialog extends Dialog {
    private TextView codeView;
    private TextView nameView;
    private TextView inputScoreView;
    private TextView commitView;

    public ExamScoreDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.exam_score_dialog);
        getWindow().setBackgroundDrawableResource(R.color.tran);
        init();
    }

    private void init(){
        ImageView closeView = findViewById(R.id.closeView);
        ImageView headBgView = findViewById(R.id.headBgView);
        codeView = findViewById(R.id.bgCodeView);
        nameView = findViewById(R.id.nameView);
        inputScoreView = findViewById(R.id.inputScoreView);
        commitView = findViewById(R.id.commitView);

        //设置图片为10dp的圆角，防止图片在不同分辨率的设备上，圆角不一致的问题
        ImageLoader.loadImageNoPlaceholder(headBgView, R.drawable.exam_score_head_bg);

        closeView.setOnClickListener(v -> dismiss());

    }

    public ExamScoreDialog setCode(String code) {
        codeView.setText(code);
        return this;
    }

    public ExamScoreDialog setName(String name) {
        nameView.setText(name);
        return this;
    }

    public ExamScoreDialog setOnCommitListener(OnCommitListener onCommitListener) {
        commitView.setOnClickListener(v -> {
            onCommitListener.onCommit(inputScoreView.getText().toString());
            dismiss();
        });
        return this;
    }

    public interface OnCommitListener{
        void onCommit(String score);
    }
}
