package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.annotation.LayoutRes;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.content.res.ResourcesCompat;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.widget.ArrowLayout;
import com.dep.biguo.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PopupDialog<T> extends PopupWindow {
    public static final int ARROW_START = 0;
    public static final int ARROW_CENTER = 1;
    public static final int ARROW_END = 2;

    private Builder<T> builder;

    private ArrowLayout arrowLayout;

    public PopupDialog(Builder<T> builder) {
        super(builder.context);
        this.setBackgroundDrawable(new ColorDrawable());
        //https://www.jianshu.com/p/6a65107b19a1?utm_campaign=maleskine&utm_content=note&utm_medium=seo_notes&utm_source=recommendation
        this.setFocusable(true);
        this.builder = builder;

        init(builder);
    }

    private void init(Builder<T> builder){
        ArrowLayout arrowLayout = createArrowLayout();
        ScrollView scrollView = createScrollView();
        LinearLayout linearLayout = createLinearLayout();
        //创建每一项
        for(int i = 0; i < builder.list.size(); i++){
            linearLayout.addView(createItem(i, builder.minItemHeight));
        }
        //嵌套布局
        scrollView.addView(linearLayout);
        arrowLayout.addView(scrollView);
        setContentView(arrowLayout);
    }

    private ArrowLayout createArrowLayout(){
        arrowLayout = new ArrowLayout(builder.context);
        arrowLayout.setMaxHeight(builder.popupMaxHeight);
        arrowLayout.setOrientation(LinearLayout.VERTICAL);
        arrowLayout.setArrowOffsetX(builder.arrowOffsetX, builder.arrowXAnchor);
        return arrowLayout;
    }

    private ScrollView createScrollView(){
        ScrollView scrollView = new ScrollView(builder.context);
        scrollView.setVerticalScrollBarEnabled(false);
        int scrollPadding = DisplayHelper.dp2px(builder.context, 6);
        scrollView.setPadding(scrollPadding, scrollPadding, scrollPadding, scrollPadding);
        return scrollView;
    }

    private LinearLayout createLinearLayout(){
        LinearLayout linearLayout = new LinearLayout(builder.context);
        linearLayout.setOrientation(builder.orientation);
        return linearLayout;
    }

    private View createItem(int position, int itemViewHeight){
        View view;
        if(builder.itemViewId != 0){
            view = LayoutInflater.from(builder.context).inflate(builder.itemViewId, null);
            view.setMinimumHeight(itemViewHeight);

        }else {
            TextView textView = new TextView(builder.context);
            textView.setTextColor(ResourcesCompat.getColor(builder.context.getResources(), R.color.tblack, builder.context.getTheme()));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            textView.setGravity(Gravity.CENTER_VERTICAL);
            textView.setPadding(DisplayHelper.dp2px(builder.context, 8), 0, DisplayHelper.dp2px(builder.context, 8), 0);
            textView.setMinHeight(itemViewHeight);
            textView.setMinWidth(DisplayHelper.dp2px(builder.context, isItemVertical() ? 80 : 0));
            int width = isItemVertical() ? LinearLayout.LayoutParams.MATCH_PARENT : LinearLayout.LayoutParams.WRAP_CONTENT;
            textView.setLayoutParams(new LinearLayout.LayoutParams(width, LinearLayout.LayoutParams.WRAP_CONTENT));
            view = textView;
        }
        //添加数据交给外部实现
        builder.foreachData.foreach(view, builder.list.get(position), position);
        //点击事件不存在则直接返回
        if (builder.onItemClickListener == null)  return view;
        //添加点击事件
        view.setOnClickListener(v -> {
            builder.onItemClickListener.onItemClick(v, builder.list.get(position), position);
            if (!builder.isAlwaysShow) dismiss();
        });
        return view;
    }


    //item是否是纵向排列
    private boolean isItemVertical(){
        return builder.orientation == LinearLayout.VERTICAL;
    }

    //返回额外的占用高度，箭头的高度+阴影的高度(上方有阴影，下方有阴影，所以是双倍阴影高度)
    public int getAdditionalHeight(){
        return arrowLayout.getAdditionalHeight();
    }

    //返回额外的占用宽度，阴影的宽度(左边有阴影，右边有阴影，所以是双倍阴影宽度)
    public int getAdditionalWidth(){
        return arrowLayout.getAdditionalWidth();
    }

    @Override
    public void showAsDropDown(View anchor, int xoff, int yoff, int gravity) {
        //偏移弹窗阴影占用宽度，视觉上弹窗的左边与锚点控件左边对齐
        int offsetXShadow = - DisplayHelper.dp2px(builder.context, 6);
        super.showAsDropDown(anchor, xoff + offsetXShadow, yoff, gravity);
        //等弹窗出现，再获取弹窗是出现在锚点控件顶部还是底部，之后设置箭头的方向
        arrowLayout.setArrowToward(isAboveAnchor() ? ArrowLayout.ARROW_TOWARD_BOTTOM : ArrowLayout.ARROW_TOWARD_TOP);

    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        //偏移弹窗阴影占用宽度，视觉上弹窗的左边与锚点控件左边对齐
        int offsetXShadow = - arrowLayout.getAdditionalWidth() / 2;
        super.showAtLocation(parent, gravity, x + offsetXShadow, y);
        //等弹窗出现，再获取弹窗是出现在锚点控件顶部还是底部，之后设置箭头的方向
        arrowLayout.setArrowToward(isAboveAnchor() ? ArrowLayout.ARROW_TOWARD_BOTTOM : ArrowLayout.ARROW_TOWARD_TOP);
    }

    @Override
    public void setWidth(int width) {
        //因为阴影需要空间绘制，所以视觉上的宽度是小于设置的宽度的，加入额外占用宽度，就能保证视觉上与设置宽度一致了
        super.setWidth(width + arrowLayout.getAdditionalWidth());
    }

    public static class Builder<T>{
        private Context context;//上下文
        private List<T> list;//数据集
        private int itemViewId;//自定义item的控件ID
        private int arrowOffsetX;//箭头偏移距离
        private int arrowXAnchor;//箭头的锚点位置
        private int minItemHeight;//每一项的高度
        private int popupMaxHeight;//弹窗的最大高度
        private int orientation;//item的排列方向
        private boolean isAlwaysShow;//在点击某一项之后，弹窗是否一直显示
        private ForeachData<T> foreachData;//数据迭代器
        private OnItemClickListener<T> onItemClickListener;//item的监听事件

        public Builder(Context context, List<T> list) {
            this.context = context;
            this.list = list;
            this.minItemHeight = DisplayHelper.dp2px(context, 32);
            this.arrowXAnchor = ARROW_START;
            this.orientation = LinearLayout.VERTICAL;
        }

        public Builder(Context context, T...t) {
            this.context = context;
            this.list = new ArrayList<>(Arrays.asList(t));
            this.minItemHeight = DisplayHelper.dp2px(context, 32);
            this.arrowXAnchor = ARROW_START;
            this.orientation = LinearLayout.VERTICAL;
        }

        public Builder<T> setItemView(@LayoutRes int itemViewId) {
            this.itemViewId = itemViewId;
            return this;
        }

        public Builder<T> setOrientation(@LinearLayoutCompat.OrientationMode int orientation) {
            this.orientation = orientation;
            return this;
        }

        //设置箭头偏移距离，负数往左移，正数往右移
        public Builder<T> setArrowOffsetX(int arrowOffsetX) {
            this.arrowOffsetX = arrowOffsetX;
            return this;
        }

        public Builder<T> setMinItemHeight(int minItemHeight) {
            this.minItemHeight = minItemHeight;
            return this;
        }

        //设置箭头在水平方向的起始位置
        public Builder<T> setArrowXAnchor(@ArrowRelative int arrowXAnchor) {
            this.arrowXAnchor = arrowXAnchor;
            return this;
        }

        //设置弹窗的最大高度
        public Builder<T> setPopupMaxHeight(int popupMaxHeight) {
            this.popupMaxHeight = popupMaxHeight;
            return this;
        }

        //是否在点击选项之后，弹窗不消失
        public Builder<T> setAlwaysShow(boolean alwaysShow) {
            isAlwaysShow = alwaysShow;
            return this;
        }

        //设置数据解析器
        public Builder<T> setForeachData(ForeachData<T> foreachData) {
            this.foreachData = foreachData;
            return this;
        }

        //点击监听
        public Builder<T> setOnItemClickListener(OnItemClickListener<T> onItemClickListener) {
            this.onItemClickListener = onItemClickListener;
            return this;
        }

        public PopupDialog<T> build(){
            if(foreachData == null){
                throw new NullPointerException("请添加数据迭代器：ForeachData类");
            }else if(list == null){
                throw new NullPointerException("请添加数据");
            }
            return new PopupDialog<>(this);
        }
    }

    public interface ForeachData<T>{
        void foreach(View itemView, T data, int position);

        //返回一个默认实现String的实例
        static ForeachData<String> getForeachDataInstance() {
            return (itemView, data, position) -> ((TextView)itemView).setText(data);
        }
    }

    public interface OnItemClickListener<T>{
        void onItemClick(View itemView, T data, int position);
    }

    @IntDef(value = {ARROW_START, ARROW_CENTER, ARROW_END})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ArrowRelative{

    }

}
