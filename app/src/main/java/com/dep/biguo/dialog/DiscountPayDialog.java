package com.dep.biguo.dialog;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.GroupUserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.DiscountPayPager1Binding;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.mvp.ui.adapter.ViewPagerAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.widget.DiversificationTextView;
import com.dep.biguo.widget.NoScrollViewPager;
import com.dep.biguo.wxapi.WxMinApplication;
import com.hjq.toast.ToastUtils;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class DiscountPayDialog extends Dialog implements View.OnClickListener {
    private ImageView backView;
    private ImageView closeView;
    private NoScrollViewPager viewPager;

    /*第一个pager的UI*/
    private DiscountPayPager1Binding payPagerBinding;


    /*第二个pager的UI*/
    private TextView titleView;
    private RecyclerView discountRecyclerView;


    public static int DISCOUNT = 1;//选择优惠券
    public static int PAY_TYPE = 2;//选择支付类型
    private SelectAdapter payTypeAdapter;
    private SelectAdapter discountAdapter;

    private Builder builder;


    public DiscountPayDialog(@NonNull Context context, Builder builder) {
        super(context);
        this.builder = builder;
        setContentView(R.layout.discount_pay_dialog);
        closeView = findViewById(R.id.closeView);
        viewPager = findViewById(R.id.viewPager);
        init();
    }

    public void init() {
        closeView.setOnClickListener(this);

        List<View> list = new ArrayList<>();
        list.add(getPayPagerView());
        list.add(getSelectPagerView());
        ViewPagerAdapter viewPagerAdapter = new ViewPagerAdapter(list);
        viewPager.setAdapter(viewPagerAdapter);
    }

    private View getPayPagerView(){
        View payPagerView = LayoutInflater.from(getContext()).inflate(R.layout.discount_pay_pager_1, null);
        payPagerBinding = DataBindingUtil.bind(payPagerView);
        payPagerBinding.setOnClickListener(this);

        //初始化支付方式列表
        List<Object> list = new ArrayList<>();
        if(builder.isShowStage){
            list.add(PayUtils.PAY_TYPE_TUITION);
        }else {
            if (builder.isShowAlipay) list.add(PayUtils.PAY_TYPE_ALIPAY);
            if (builder.isShowWeChat) list.add(PayUtils.PAY_TYPE_WEXIN);
            if (builder.isShowGuobi) list.add(PayUtils.PAY_TYPE_COIN);
            if (builder.isShowIntegral) list.add(PayUtils.PAY_TYPE_INTEGRAL);
        }
        payTypeAdapter = new SelectAdapter();
        payTypeAdapter.setList(list);
        payTypeAdapter.setShowType(PAY_TYPE);
        payPagerBinding.payTypeRecyclerView.setAdapter(payTypeAdapter);

        //只有以下判断的产品可以享受折扣卡优惠
        if(Constant.ZK.equals(UserCache.getAppType())
                && (StartFinal.VIP.equals(builder.goodsType)
                    || StartFinal.YAMI.equals(builder.goodsType)
                    || StartFinal.YAMI_RESERVE.equals(builder.goodsType)
                    || StartFinal.VIDEO1.equals(builder.goodsType)
                    || StartFinal.VIDEO2.equals(builder.goodsType)
                    || StartFinal.VIDEO3.equals(builder.goodsType)
                    || StartFinal.VIDEO4.equals(builder.goodsType)
                    || StartFinal.HIGH_FREQUENCY.equals(builder.goodsType)
                    || StartFinal.SKILL_VIDEO.equals(builder.goodsType)
                    || StartFinal.VOCATION_VIDEO.equals(builder.goodsType))
                && builder.isShowMemberShipHint){
            if(UserCache.isMemberShip()) {
                payPagerBinding.memberPriceLayoutView.setVisibility(View.GONE);
                payPagerBinding.memberHintView.setVisibility(View.VISIBLE);
            }else {
                payPagerBinding.memberPriceLayoutView.setVisibility(View.VISIBLE);
                payPagerBinding.memberHintView.setVisibility(View.GONE);
            }

        }else {
            payPagerBinding.memberPriceLayoutView.setVisibility(View.GONE);
            payPagerBinding.memberHintView.setVisibility(View.GONE);
            payPagerBinding.recommendLayout.setVisibility(View.GONE);
        }

        if(Constant.ZK.equals(UserCache.getAppType())){
            //有监听推荐产品的点击事件，则显示推荐产品的控件，否则隐藏
            payPagerBinding.recommendLayout.setVisibility(builder.onStartRecommendListener == null ? View.GONE : View.VISIBLE);
        }
        //设置支付价格
        setPrice();
        //设置商品名称
        payPagerBinding.courseNameView.setText(SpannableUtil.setChineseBracketsRightDrawable(getContext(), builder.goodsName, payPagerBinding.courseNameView.getTextSize(), payPagerBinding.courseNameView.getCurrentTextColor()));
        //设置默认选中的优惠券
        setDiscount();
        //设置会员价提示语
        setMemberHint();
        //设置购买时的提示信息
        payPagerBinding.hintView.setText(setPayDialogHint(builder.goodsType));
        //当有人发起拼团，则设置发起者的信息
        if(builder.senderGroup != null && !builder.isJoinGroup) {
            GroupUserBean sender = builder.senderGroup.getUsers_info().get(0);
            ImageLoader.loadAvatar(payPagerBinding.senderAvatarView, sender.getAvatar());
            payPagerBinding.senderNicknameView.setText(sender.getNickname());
        }else {
            payPagerBinding.senderLayout.setVisibility(View.GONE);
        }

        return payPagerView;
    }

    private void setPrice(){
        payPagerBinding.originalPriceView.setText(String.format(Locale.CHINA, "¥%.2f",builder.price));
        payPagerBinding.originalPriceView.setVisibility((builder.price- builder.payPrice) > 0f || isMakeDiscount() ? View.VISIBLE : View.GONE);

        //当使用优惠券之后，优惠价格超出支付价格，应显示未0，不能显示负数
        float price = builder.payPrice - (builder.defaultDiscountBean == null ? 0f : Float.parseFloat(builder.defaultDiscountBean.getPrice()));
        //当用户使用的是积分支付，则乘以10，积分比例是1:10
        price = price * (PayUtils.PAY_TYPE_INTEGRAL.equals(builder.defaultPayType) ? 10 : 1);
        if(price >= 0.01f) {
            payPagerBinding.payView.setText(String.format(Locale.CHINA, "支付 ¥%.2f", price));
            payPagerBinding.priceView.setText(String.format(Locale.CHINA, "¥%.2f", price));
        }else {
            //小于0.01f的不填充价格
            payPagerBinding.payView.setText("支付 ");
            payPagerBinding.priceView.setText("¥");
        }
    }

    private void setDiscount(){
        //移动积分暂不开放
        payPagerBinding.chinaMobileIntegralView.setVisibility(View.GONE);

        //若没有符合要求的默认优惠券，则设置优惠券选项不可点击
        if(builder.defaultDiscountBean != null) {
            //payPagerBinding.chinaMobileIntegralView.setVisibility(View.GONE);
            payPagerBinding.discountLayout.setVisibility(View.VISIBLE);

            payPagerBinding.discountContentView.setText(builder.defaultDiscountBean.getCoupon_name());
            payPagerBinding.discountSizeView.setVisibility(View.VISIBLE);
            //选择了暂不使用优惠券，则清空面额
            if(builder.defaultDiscountBean == null){
                payPagerBinding.discountSizeView.setText("");
            }else {
                payPagerBinding.discountSizeView.setText(String.format("-¥%s", builder.defaultDiscountBean.getPrice()));
            }

        }else if(!AppUtil.isEmpty(builder.discountList)){
            //payPagerBinding.chinaMobileIntegralView.setVisibility(View.GONE);
            payPagerBinding.discountLayout.setVisibility(View.VISIBLE);
            payPagerBinding.discountContentView.setText("暂不使用优惠券");
            payPagerBinding.discountSizeView.setVisibility(View.GONE);
            payPagerBinding.discountLayout.setEnabled(true);

        }else if(builder.isShowChinaMobileIntegral){
            //payPagerBinding.chinaMobileIntegralView.setVisibility(View.VISIBLE);
            payPagerBinding.discountLayout.setVisibility(View.GONE);
            payPagerBinding.discountContentView.setText("暂无可用优惠券");
            payPagerBinding.discountSizeView.setVisibility(View.GONE);
            payPagerBinding.discountLayout.setEnabled(false);

        }else {
            //payPagerBinding.chinaMobileIntegralView.setVisibility(View.VISIBLE);
            payPagerBinding.discountLayout.setVisibility(View.GONE);
            payPagerBinding.discountContentView.setText("暂无可用优惠券");
            payPagerBinding.discountSizeView.setVisibility(View.GONE);
            payPagerBinding.discountLayout.setEnabled(false);
        }
    }

    public void setMemberHint(){
        //设置笔果折扣卡优惠的提示语
        float memberDiscount = 0;
        if(UserCache.getUserCache() != null && !TextUtils.isEmpty(UserCache.getUserCache().getMember_discount())){
            memberDiscount = Float.parseFloat(UserCache.getUserCache().getMember_discount());
        }
        if(UserCache.isMemberShip()){
            payPagerBinding.memberHintView.setText(String.format(Locale.CHINA,"已享受%.1f折", memberDiscount * 10f));
        }else {
            float memberPrice = builder.payPrice * memberDiscount - (builder.defaultDiscountBean == null ? 0f : Float.parseFloat(builder.defaultDiscountBean.getPrice()));
            //当用户使用的是积分支付，则乘以10，积分比例是1:10
            memberPrice = memberPrice * (PayUtils.PAY_TYPE_INTEGRAL.equals(builder.defaultPayType) ? 10 : 1);
            String price = memberPrice >= 0.01f ? String.format(Locale.CHINA, "%.2f", memberPrice) : "";
            String unit = "元";
            if(PayUtils.PAY_TYPE_ALIPAY.equals(builder.defaultPayType) || PayUtils.PAY_TYPE_WEXIN.equals(builder.defaultPayType)){
                unit = "元";
            }else if(PayUtils.PAY_TYPE_COIN.equals(builder.defaultPayType)){
                unit = "果币";
            }else if(PayUtils.PAY_TYPE_INTEGRAL.equals(builder.defaultPayType)){
                unit = "积分";
            }
            String text = String.format(Locale.CHINA, "笔果折扣卡下单，本单只需%s%s", price, unit);
            int start = text.indexOf("需") + 1;
            int end = start + String.format(Locale.CHINA, "%s", price).length();
            int color = getContext().getResources().getColor(R.color.theme);
            payPagerBinding.memberPriceView.setText(SpannableUtil.setColorString(text, start, end, color));
        }
    }

    /**返回支付弹窗里需要显示的提示语
     *
     */
    public String setPayDialogHint(String goodsType){
        if(goodsType == null) return "";

        if(PayUtils.BOOK.equals(goodsType)){
            return  "付款前请认真确认购买信息，避免购买错误";
        }else if(PayUtils.YAMI.equals(goodsType)){
            return "您将购买虚拟商品，购买后可享受协议保障";
        }else if(goodsType.contains(PayUtils.VIDEO)
                || PayUtils.SUPER_VIP.equals(goodsType)
                || PayUtils.VIP.equals(goodsType)
                || PayUtils.HIGH_FREQUENCY.equals(goodsType)){
            return getContext().getString(R.string.paydialog_hint);
        }else {
            return "";
        }
    }

    private View getSelectPagerView(){
        View selectPagerView = LayoutInflater.from(getContext()).inflate(R.layout.discount_pay_pager_2, null);
        backView = selectPagerView.findViewById(R.id.backView);
        titleView = selectPagerView.findViewById(R.id.titleView);
        backView.setOnClickListener(this);
        discountRecyclerView = selectPagerView.findViewById(R.id.discountRecyclerView);
        discountAdapter = new SelectAdapter();
        discountRecyclerView.setAdapter(discountAdapter);
        return selectPagerView;
    }

    @Override
    public void onClick(View view) {
        if(view == closeView){
            dismiss();

        }else if(view == payPagerBinding.recommendLayout){
            if(builder.onStartRecommendListener != null){
                builder.onStartRecommendListener.startRecommend(builder.goodsType);
            }

        }else if(view == payPagerBinding.memberPriceLayoutView){
            ArmsUtils.startActivity(BiguoVipOpenActivity.class);

        }else if(view == payPagerBinding.closeRecommendView){
            payPagerBinding.recommendLayout.setVisibility(View.GONE);

        }else if(view == payPagerBinding.discountLayout){
            titleView.setText("选择优惠券");
            discountAdapter.setList(builder.discountList);
            discountAdapter.setShowType(DISCOUNT);
            viewPager.setCurrentItem(1,true);
            discountAdapter.notifyDataSetChanged();

        }else if(view == payPagerBinding.joinGroupView){
            builder.isJoinGroup = true;
            ObjectAnimator animation = ObjectAnimator.ofFloat(payPagerBinding.getRoot(), "alpha", 1f, 0f, 1f);
            animation.setDuration(300);
            animation.addUpdateListener(animation1 -> {
                if((float) animation1.getAnimatedValue() < 0.1f){
                    payPagerBinding.senderLayout.setVisibility(View.GONE);
                }else if((float) animation1.getAnimatedValue() == 1f){
                    Toast toast = ToastUtils.getToast();
                    toast.setGravity(Gravity.BOTTOM, 0, DisplayHelper.dp2px(getContext(), 80));
                    toast.setText("已选择加入拼团");
                    toast.show();
                }
            });
            animation.start();

        }else if(view == payPagerBinding.chinaMobileIntegralView){
            WxMinApplication.StartChainMobileMinApp(getContext());

        }else if(view == payPagerBinding.payView){
            if(!builder.isShowAlipay && !builder.isShowWeChat && !builder.isShowGuobi && !builder.isShowIntegral){
                ToastUtils.show("无可用的支付方式");
                return;
            }

            //选中最后一个，则认为不使用优惠券，应返回null
            DiscountBean discountBean = null;
            if(isMakeDiscount()){
                discountBean = builder.defaultDiscountBean;
            }
            //有人发起拼团，并且选择直接拼成，应返回这个团
            GroupBean groupBean = builder.senderGroup != null && builder.isJoinGroup ? builder.senderGroup : null;

            builder.onPayListener.onPay(groupBean, discountBean, builder.defaultPayType);
            dismiss();

        }else if(view == backView){
            viewPager.setCurrentItem(0, true);
        }
    }

    /**选中最后一个，则认为不使用优惠券
     * @return
     */
    public boolean isMakeDiscount(){
        return !AppUtil.isEmpty(builder.discountList) && builder.defaultDiscountBean != null;
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if(builder.onCloseListener != null){
            builder.onCloseListener.close();
        }
        builder.context = null;
        builder.onPayListener = null;
        builder.onCloseListener = null;
        builder = null;
    }

    private class SelectAdapter <T> extends RecyclerView.Adapter<SelectAdapter.Holder> {
        public int showType;//DISCOUNT，PAY_TYPE
        private List<T> list = new ArrayList<>();

        public void setShowType(int showType) {
            this.showType = showType;
        }

        public void setList(List<T> list) {
            this.list = list;
        }

        @NonNull
        @Override
        public Holder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
            if(showType == DISCOUNT) {
                View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.discount_pay_item, viewGroup, false);
                return new Holder(view);
            }else {
                TextView textView = new TextView(getContext());
                int height = DisplayHelper.dp2px(getContext(), 40);
                textView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height));
                textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                textView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), R.color.tblack, getContext().getTheme()));
                textView.setCompoundDrawablePadding(DisplayHelper.dp2px(getContext(), 15));
                textView.setGravity(Gravity.CENTER_VERTICAL);
                return new Holder(textView);
            }
        }

        @Override
        public void onBindViewHolder(@NonNull SelectAdapter.Holder holder, int i) {
            if(showType == DISCOUNT) {
                TextView priceView = holder.itemView.findViewById(R.id.priceView);
                TextView fullMinusPriceView = holder.itemView.findViewById(R.id.fullMinusPriceView);
                TextView nameView = holder.itemView.findViewById(R.id.nameView);
                DiversificationTextView timeView = holder.itemView.findViewById(R.id.timeView);
                TextView checkView = holder.itemView.findViewById(R.id.checkView);

                DiscountBean bean = (DiscountBean) list.get(i);
                priceView.setText(String.format("¥%s", bean.getPrice()));
                fullMinusPriceView.setText(String.format("满%s可用", bean.getFull_minus_price()));
                nameView.setText(bean.getCoupon_name());
                if(bean.getValid().equals("今日到期")){
                    timeView.setStart(0);
                    timeView.setColor(holder.itemView.getResources().getColor(R.color.orange));
                }else if(bean.getValid().startsWith("距到期时间仅剩 ")){
                    timeView.setStart(7);
                    timeView.setColor(holder.itemView.getResources().getColor(R.color.orange));
                }else {
                    timeView.setStart(0);
                    timeView.setColor(holder.itemView.getResources().getColor(R.color.tblack3));
                }
                timeView.setEnd(bean.getValid().length());
                timeView.setText(bean.getValid());

                int rightDrawableId = bean == builder.defaultDiscountBean ? R.drawable.selected : R.drawable.un_selected;
                TextDrawableLoader.load(getContext(), checkView, new int[]{0, 0, rightDrawableId, 0});

            }else {
                TextView textView = (TextView) holder.itemView;
                String payType = (String) list.get(i);
                textView.setText(getName(payType));
                int rightDrawableId = payType.equals(builder.defaultPayType) ? R.drawable.selected : R.drawable.un_selected;
                TextDrawableLoader.load(getContext(), textView, new int[]{getLeftDrawableId(payType), 0, rightDrawableId, 0});
            }

        }

        private int getLeftDrawableId(String type){
            switch (type){
                case PayUtils.PAY_TYPE_ALIPAY : return R.drawable.ali_pay_66;
                case PayUtils.PAY_TYPE_WEXIN : return R.drawable.wechat_pay_66;
                case PayUtils.PAY_TYPE_COIN : return R.drawable.guobi_pay_66;
                case PayUtils.PAY_TYPE_INTEGRAL : return R.drawable.integral_pay_66;
                case PayUtils.PAY_TYPE_TUITION: return R.drawable.huabei_pay_66;
                default: return 0;
            }
        }


        @Override
        public int getItemCount() {
            return list.size();
        }

        private class Holder extends RecyclerView.ViewHolder {
            public Holder(@NonNull View itemView) {
                super(itemView);
                itemView.setOnClickListener(v -> {
                    if(showType == DISCOUNT){
                        DiscountBean selectedBean = (DiscountBean) list.get(getAdapterPosition());
                        if(selectedBean.getStatus() == 0){
                            showToast("优惠券暂未生效");
                            return;
                        }
                        if(Float.parseFloat(AppUtil.isEmpty(selectedBean.getFull_minus_price(), "0")) > builder.payPrice){
                            showToast("未达到满减要求");
                            return;
                        }
                        if(builder.defaultDiscountBean == selectedBean){
                            builder.defaultDiscountBean = null;
                        }else {
                            builder.defaultDiscountBean = selectedBean;
                        }
                        setPrice();//设置价格
                        setDiscount();//设置优惠券
                        setMemberHint();//设置会员价提示语

                    }else {
                        String payType = (String) list.get(getAdapterPosition());
                        builder.defaultPayType = payType;
                        setPrice();//设置价格
                        setMemberHint();//设置会员价提示语
                        notifyDataSetChanged();
                    }
                    viewPager.setCurrentItem(0, true);
                });

            }
        }
    }

    public void showToast(String message){
        Toast toast = ToastUtils.getToast();
        toast.setGravity(Gravity.BOTTOM, 0, DisplayHelper.dp2px(getContext(), 80));
        toast.setText(message);
        toast.show();
    }

    private String getName(String type){
        switch (type){
            case PayUtils.PAY_TYPE_ALIPAY: return "支付宝支付";
            case PayUtils.PAY_TYPE_WEXIN: return "微信支付";
            case PayUtils.PAY_TYPE_COIN: return "果币支付";
            case PayUtils.PAY_TYPE_INTEGRAL: return "积分支付";
            case PayUtils.PAY_TYPE_TUITION: return "周期扣款";
            default:return "";
        }
    }

    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getWindowWidth(getContext());
        layoutParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(layoutParams);
    }

    public interface OnPayListener{
        void onPay(GroupBean senderGroup, @Nullable DiscountBean discount, String payType);
    }
    public interface OnCloseListener{
        void close();
    }

    public interface OnStartRecommendListener{
        void startRecommend(String goodsType);
    }

    public static class Builder{
        private Context context;//上下文
        private String goodsName;//商品名称
        private float price;//原价（不包含优惠券）
        private float payPrice;//实际支付价格（不包含优惠券）
        private boolean isShowAlipay = true;//是否隐藏支付宝支付
        private boolean isShowWeChat = true;//是否隐藏微信支付
        private boolean isShowGuobi;//是否显示果币支付
        private boolean isShowIntegral;//是否显示积分支付
        private boolean isShowStage;//是否显示分期支付
        private boolean isShowMemberShipHint = true;//是否显示开通会员的提示
        private String defaultPayType = PayUtils.PAY_TYPE_ALIPAY;//设置的支付方式（PayUtils），不设置，则默认为支付宝(PayUtils.PAY_TYPE_ALIPAY)
        private GroupBean senderGroup;//发起拼团
        private boolean isJoinGroup;//是否默认加入这个团
        private String goodsType;
        private List<DiscountBean> discountList;//优惠券列表
        private DiscountBean defaultDiscountBean;//默认选中的优惠券
        private boolean isShowChinaMobileIntegral = true;//是否显示移动积分
        private OnPayListener onPayListener;//支付回调
        private OnCloseListener onCloseListener;//关闭回调
        private OnStartRecommendListener onStartRecommendListener;//点击推荐广告回调

        public Builder(Context context, String goodsType) {
            this.context = context;
            this.goodsType = goodsType;
        }

        public Builder setGoodsName(String goodsName) {
            this.goodsName = goodsName;
            return this;
        }
        public Builder setGoodsType(String goodsType) {
            this.goodsType = goodsType;
            return this;
        }

        public Builder setPrice(float price) {
            this.price = price;
            return this;
        }

        public Builder setPayPrice(float payPrice) {
            this.payPrice = payPrice;
            return this;
        }

        public Builder setShowAlipay(boolean showAlipay) {
            isShowAlipay = showAlipay;
            return this;
        }

        public Builder setShowWeChat(boolean showWeChat) {
            isShowWeChat = showWeChat;
            return this;
        }

        public Builder setShowGuobi(boolean showGuobi) {
            isShowGuobi = showGuobi;
            return this;
        }

        public Builder setShowIntegral(boolean showIntegral) {
            isShowIntegral = showIntegral;
            return this;
        }

        public Builder setShowStage(boolean showStage) {
            isShowStage = showStage;
            return this;
        }

        public Builder setShowChinaMobileIntegral(boolean showChinaMobileIntegral) {
            this.isShowChinaMobileIntegral = showChinaMobileIntegral;
            return this;
        }

        public Builder setShowMemberShipHint(boolean showMemberShipHint) {
            isShowMemberShipHint = showMemberShipHint;
            return this;
        }

        public Builder setDefaultPayType(String defaultPayType) {
            this.defaultPayType = defaultPayType;
            return this;
        }

        public Builder setSenderGroup(GroupBean senderGroup) {
            this.senderGroup = senderGroup;
            return this;
        }

        public Builder setJoinGroup(boolean joinGroup) {
            isJoinGroup = joinGroup;
            return this;
        }

        public Builder setDiscountList(List<DiscountBean> discountList) {
            this.discountList = discountList;
            /*if(!AppUtil.isEmpty(this.discountList) && this.discountList.get(this.discountList.size() - 1).getId() != -1){
                DiscountBean discountBean = new DiscountBean(-1, 0);
                discountBean.setCoupon_name("暂不使用优惠券");
                discountBean.setPrice("0.00");
                discountBean.setStatus(1);
                this.discountList.add(discountBean);
            }*/
            this.defaultDiscountBean = getDefaultDiscount();
            return this;
        }

        public Builder setSelectDiscount(DiscountBean discountBean){
            this.defaultDiscountBean = discountBean;
            return this;
        }

        public Builder setOnPayListener(OnPayListener onPayListener) {
            this.onPayListener = onPayListener;
            return this;
        }

        public Builder setOnCloseListener(OnCloseListener onCloseListener) {
            this.onCloseListener = onCloseListener;
            return this;
        }

        public Builder setOnStartRecommendListener(OnStartRecommendListener onStartRecommendListener) {
            this.onStartRecommendListener = onStartRecommendListener;
            return this;
        }

        public DiscountPayDialog build(){
            return new DiscountPayDialog(context, this);
        }

        /**控制台输出日志可以查看优惠券的默认选择逻辑
         *
         */
        public void print(DiscountBean discountBean){
            if(discountBean == null) {
                LogUtil.d("dddd", "没有符合要求的优惠券");
                return;
            }
            LogUtil.d("dddd",String.format("id：%s\t面额：%s\t满减：%s\t到期时间：%s\t优惠券类型：%s\t目前是否可用：%s",
                    discountBean.getId(),
                    discountBean.getPrice(),
                    discountBean.getFull_minus_price(),
                    discountBean.getLeft_time(),
                    discountBean.getType() == 1 ? "抵扣券" : "满减券",
                    discountBean.getStatus() == 1 ? "是":"否"));
        }

        /**筛选出性价比最高的优惠券作为默认优惠券
         *
         */
        public DiscountBean getDefaultDiscount(){
            //需要优惠券的,当订单信息为空或者优惠券信息为空时，暂不处理，等待两个信息都请求完成再处理
            if(discountList == null || discountList.size() == 0){
                return null;
            }

            for(DiscountBean discountBean : discountList){
                print(discountBean);
            }

            DiscountBean selectedDiscountBean = null;//选择的优惠券，VIP、押密、视频初始时默认选中最大优惠额度且不超过总计价格的优惠券

            //满减线
            float fullLinePrice = payPrice;
            LogUtil.d("dddd",String.format(Locale.CHINA,"满减线:%.2f", fullLinePrice));
            //循环筛选出总计价格之下的最大优惠额度的优惠券
            List<DiscountBean> canMakeDiscountList = new ArrayList<>();
            List<DiscountBean> removeDiscountList = new ArrayList<>();
            for(DiscountBean discountBean : discountList){
                //超过支付价格的优惠券存到另一个集合中，方便此处循环完成后，从优惠券列表中移除
                if(Float.parseFloat(discountBean.getPrice()) >= fullLinePrice) {
                    removeDiscountList.add(discountBean);
                    continue;
                }
                //目前可以使用的优惠券,剔除不使用优惠券这个选项
                if(discountBean.getStatus() == 1 && discountBean.getId() != -1){
                    //是满减券，但不满减券满足使用要求，则跳过
                    if(discountBean.getType() == 2 && Float.parseFloat(discountBean.getFull_minus_price()) > fullLinePrice) continue;

                    canMakeDiscountList.add(discountBean);
                }
            }
            //移除超过支付价格的优惠券
            discountList.removeAll(removeDiscountList);

            LogUtil.d("dddd","=====================================================");
            LogUtil.d("dddd","第一次筛选结果：");
            for(DiscountBean discountBean : canMakeDiscountList){
                print(discountBean);
            }
            LogUtil.d("dddd", "默认的优惠券ID："+ -1);

            //循环筛选出优惠力度最大的
            List<DiscountBean> discountMaxList = new ArrayList<>();
            boolean hasLessThanMemberShipPrice = false;//是否存在 满减线 > 优惠券面额 的情况
            for(DiscountBean discountBean : canMakeDiscountList){
                if(selectedDiscountBean == null) selectedDiscountBean = discountBean;
                LogUtil.d("dddd", String.format("开始筛选优惠券（ID：%s）", discountBean.getId()));
                boolean isClearDiscountMaxList = false;
                if(fullLinePrice < Float.parseFloat(discountBean.getPrice())){//满减线 < 当前循环取到的优惠券面额
                    LogUtil.d("dddd", "该券溢价");
                    //出现过 满减线 > 优惠券面额 的情况，不再考虑面额超过满减线的优惠券
                    if(hasLessThanMemberShipPrice) continue;
                    //比默认的优惠券浪费额度更大的优惠券将不再考虑
                    if(Float.parseFloat(selectedDiscountBean.getPrice()) > Float.parseFloat(discountBean.getPrice())) {
                        LogUtil.d("dddd", "默认券更浪费");
                        isClearDiscountMaxList = true;
                    }else if(Float.parseFloat(selectedDiscountBean.getPrice()) < Float.parseFloat(discountBean.getPrice())){
                        LogUtil.d("dddd", "当前券更浪费");
                        continue;
                    }else {
                        LogUtil.d("dddd", "同样浪费");
                    }
                }else {
                    LogUtil.d("dddd", "该券未溢价");
                    //首次出现未溢价的优惠券，则默认选择未溢价的
                    if(!hasLessThanMemberShipPrice) {
                        LogUtil.d("dddd","首次遇到抵扣券，重新设置默认券");
                        selectedDiscountBean = discountBean;
                        isClearDiscountMaxList = true;
                    }
                    //标志出现过 满减线 > 优惠券面额 的情况
                    hasLessThanMemberShipPrice = true;
                    //比默认优惠券的优惠额度更小的优惠券将不再考虑
                    if(Float.parseFloat(selectedDiscountBean.getPrice()) < Float.parseFloat(discountBean.getPrice())) {
                        LogUtil.d("dddd", "当前券更优惠");
                        isClearDiscountMaxList = true;
                    }else if(Float.parseFloat(selectedDiscountBean.getPrice()) > Float.parseFloat(discountBean.getPrice())){
                        LogUtil.d("dddd", "默认券更优惠");
                        continue;
                    }else {
                        LogUtil.d("dddd", "同样优惠");
                    }
                }


                if(isClearDiscountMaxList) {//默认选中的优惠券面额 == 当前循环取到的优惠券面额
                    LogUtil.d("dddd", "清除优惠券");
                    discountMaxList.clear();
                    selectedDiscountBean = discountBean;
                }
                LogUtil.d("dddd", "添加优惠券："+discountBean.getId());
                discountMaxList.add(discountBean);
            }

            LogUtil.d("dddd","=====================================================");
            LogUtil.d("dddd","第二次筛选结果：");
            for(DiscountBean discountBean : discountMaxList){
                print(discountBean);
            }
            LogUtil.d("dddd", "默认的优惠券ID："+(selectedDiscountBean != null ? selectedDiscountBean.getId() : -1));

            //筛选出最接近过期时间的优惠券
            List<DiscountBean> discountFullList = new ArrayList<>();
            for(DiscountBean discountBean : discountMaxList){
                if(selectedDiscountBean.getLeft_time() == discountBean.getLeft_time()){
                    discountFullList.add(discountBean);
                }else if(selectedDiscountBean.getLeft_time() > discountBean.getLeft_time()){
                    discountFullList.clear();
                    discountFullList.add(discountBean);
                    selectedDiscountBean = discountBean;
                }
            }

            LogUtil.d("dddd","=====================================================");
            LogUtil.d("dddd","第三次筛选结果：");
            for(DiscountBean discountBean : discountFullList){
                print(discountBean);
            }
            LogUtil.d("dddd", "默认的优惠券ID："+(selectedDiscountBean != null ? selectedDiscountBean.getId() : -1));

            //没有符合要求的优惠券，则设置默认选择未空
            boolean hasCanMakeDiscount = discountFullList.size() > 0;
            if(!hasCanMakeDiscount){
                return null;
            }
            //优先选择满减券
            for(DiscountBean discountBean : discountFullList){
                if(discountBean.getType() == 2){
                    LogUtil.d("dddd","该券是满减券");
                    if(selectedDiscountBean.getType() == 2){
                        LogUtil.d("dddd","默认券是满减券");
                        if(Float.parseFloat(selectedDiscountBean.getFull_minus_price()) < Float.parseFloat(discountBean.getFull_minus_price())){
                            LogUtil.d("dddd","默认券门槛更高");
                            selectedDiscountBean = discountBean;
                        }else {
                            LogUtil.d("dddd","该券门槛更高");
                        }
                    }else {
                        LogUtil.d("dddd","默认券不是满减券");
                        selectedDiscountBean = discountBean;
                    }
                }else {
                    LogUtil.d("dddd","该券不是满减券");
                }
            }

            LogUtil.d("dddd","=====================================================");
            LogUtil.d("dddd","第四次筛选结果：");
            print(selectedDiscountBean);

            return selectedDiscountBean;
        }
    }

}
