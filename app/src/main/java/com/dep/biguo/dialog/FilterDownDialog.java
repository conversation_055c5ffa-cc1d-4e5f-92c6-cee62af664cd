package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.LayerBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.databinding.FilterDialogBinding;
import com.dep.biguo.widget.ItemDecoration;

import java.util.ArrayList;
import java.util.List;

public class FilterDownDialog extends PopupWindow {
    private FilterDialogBinding binding;
    private FilterDialogAdapter dialogAdapter;

    private List<Object> list = new ArrayList<>();
    private OnCheckListener onCheckListener;

    private Object select;

    public FilterDownDialog(Context context) {
        super(context);

        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.filter_dialog, null, false);
        setContentView(binding.getRoot());
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setBackgroundDrawable(new ColorDrawable());
        this.setOutsideTouchable(true);
        this.setFocusable(true);

        dialogAdapter = new FilterDialogAdapter();
        dialogAdapter.setOnItemClickListener((adapter, view, position) -> {
            onCheckListener.onCheck(list.get(position), position);
            dismiss();
        });
        binding.filterRecyclerView.setAdapter(dialogAdapter);
        binding.filterRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10).setColorRes(R.color.tran));

        //触摸根布局就消失
        binding.getRoot().setOnClickListener(v -> dismiss());
    }

    public void setList(List<Object> list) {
        this.list = list;
        dialogAdapter.setNewData(list);
    }

    public void setSelect(Object select) {
        this.select = select;
    }

    @Override
    public void showAsDropDown(View anchor, int xoff, int yoff, int gravity) {
        super.showAsDropDown(anchor, xoff, yoff, gravity);
        if(list.indexOf(select) > 0){
            binding.filterRecyclerView.scrollToPosition(list.indexOf(select));
        }
    }

    public class FilterDialogAdapter extends CommonAdapter<Object>{

        public FilterDialogAdapter() {
            super(R.layout.filter_dialog_item, null);
        }

        @Override
        protected void convert(BaseViewHolder holder, Object item) {
            holder.setTextColor(R.id.textView, ResourcesCompat.getColor(mContext.getResources(), select == item ? R.color.theme : R.color.tblack, mContext.getTheme()));

            if(item instanceof ProvinceBean){
                holder.setText(R.id.textView, ((ProvinceBean) item).getName());
            }else if(item instanceof SchoolBean){
                holder.setText(R.id.textView, ((SchoolBean) item).getName());
            }else if(item instanceof LayerBean){
                holder.setText(R.id.textView, ((LayerBean) item).getName());
            }else if(item instanceof ProfessionBean){
                holder.setText(R.id.textView, ((ProfessionBean) item).getName());
            }
        }
    }

    public void setOnCheckListener(OnCheckListener onCheckListener) {
        this.onCheckListener = onCheckListener;
    }

    public interface OnCheckListener {
        void onCheck(Object obj, int position);
    }
}
