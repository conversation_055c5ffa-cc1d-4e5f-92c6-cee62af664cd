package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.biguo.utils.util.DisplayHelper;

import org.jetbrains.annotations.NotNull;

public class UserAgreementDialog extends DialogFragment {

    public static final String DIALOG_TAG = "UserAgreementDialog";

    private TextView tvOut;
    private TextView tvAgree;
    private TextView tvTitle1;
    private TextView tvTitle2;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_secret_agreement, container, false);

        WebView wbSecret = view.findViewById(R.id.wbSecret);
        /*WebView wbSecret2 = view.findViewById(R.id.wbSecret2);
        tvOut = view.findViewById(R.id.tvOut);*/
        tvAgree = view.findViewById(R.id.tvAgree);
        tvTitle1 = view.findViewById(R.id.tvTitle1);
        tvTitle2 = view.findViewById(R.id.tvTitle2);

        tvOut.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        tvAgree.setText("同意并使用");
        tvAgree.setOnClickListener(v -> {
            if (mOnAgreementListener == null) return;
            mOnAgreementListener.onAgreement();
            dismiss();
        });

        tvTitle1.setOnClickListener(v -> {
            tvTitle1.setTextColor(ContextCompat.getColor(getActivity(),R.color.auxiliary_color2 ));
            tvTitle2.setTextColor(ContextCompat.getColor(getActivity(),R.color.tblack2 ));
            wbSecret.setVisibility(View.VISIBLE);
            //wbSecret2.setVisibility(View.GONE);
        });

        tvTitle2.setOnClickListener(v -> {
            tvTitle1.setTextColor(ContextCompat.getColor(getActivity(),R.color.tblack2));
            tvTitle2.setTextColor(ContextCompat.getColor(getActivity(),R.color.auxiliary_color2 ));
            wbSecret.setVisibility(View.GONE);
            //wbSecret2.setVisibility(View.VISIBLE);
        });

        wbSecret.setWebViewClient(new WebViewClient()); //防止外部浏览器打开
        //wbSecret2.setWebViewClient(new WebViewClient()); //防止外部浏览器打开
        WebSettings settings = wbSecret.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        //WebSettings settings2 = wbSecret2.getSettings();
        //settings2.setJavaScriptEnabled(true);
        //settings2.setDomStorageEnabled(true);

        /*if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            settings2.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
*/
        wbSecret.loadUrl(Constant.AGREEMENT_USER);
        //wbSecret2.loadUrl(Constant.AGREEMENT_USER4);

        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, 0);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window dialogWindow = dialog.getWindow();
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = (int) (DisplayHelper.getWindowWidth(dialog.getContext()) * 0.9);
            lp.height = (int) (DisplayHelper.getWindowHeight(dialog.getContext()) * 0.8);
            dialogWindow.setAttributes(lp);

            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOnKeyListener((dialog1, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    getActivity().finish();
                    return true;
                }
                return false;
            });
        }
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, DIALOG_TAG);
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    private OnAgreementListener mOnAgreementListener;

    public void setOnAgreementListener(OnAgreementListener onAgreementListener) {
        this.mOnAgreementListener = onAgreementListener;
    }

    public interface OnAgreementListener {
        void onAgreement();
    }

}
