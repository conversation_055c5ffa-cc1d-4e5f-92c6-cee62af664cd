package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.GoodsUpgradeBean;
import com.dep.biguo.databinding.GoodsUpgradeDialogBinding;
import com.dep.biguo.utils.MathUtil;
import com.dep.biguo.utils.StartFinal;
import com.jess.arms.integration.AppManager;

import java.util.HashMap;
import java.util.Map;

public class GoodsUpgradeDialog extends Dialog implements View.OnClickListener{
    private GoodsUpgradeDialogBinding binding;

    private OnBuyListener onBuyListener;
    private String groupId;

    public GoodsUpgradeDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.goods_upgrade_dialog, null, false);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        setCanceledOnTouchOutside(false);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 295);
        getWindow().setBackgroundDrawableResource(R.color.tran);
    }

    @Override
    public void onClick(View view) {
        if(view == binding.singleView){
            if(onBuyListener != null){
                onBuyListener.onBuy(false, "");
            }
        }else if(view == binding.groupBuyView){
            if(onBuyListener != null){
                onBuyListener.onBuy(true, groupId);
            }
        }
        dismiss();
    }

    public GoodsUpgradeDialog setGroupId(String groupId) {
        this.groupId = groupId;
        return this;
    }

    public GoodsUpgradeDialog setUpgradeBean(GoodsUpgradeBean upgradeBean, String paySinglePrice, String payGroupPrice) {
        Map<String, String> goodsNameMap = new HashMap<>();
        goodsNameMap.put(StartFinal.VIP, "VIP题库");
        goodsNameMap.put(StartFinal.YAMI, "考前押密");
        goodsNameMap.put(StartFinal.HIGH_FREQUENCY, "高频考点");
        goodsNameMap.put(StartFinal.VIDEO0, "精讲视频");
        goodsNameMap.put(StartFinal.VIDEO1, "精讲视频");
        goodsNameMap.put(StartFinal.VIDEO2, "串讲视频");

        String topicName = "";
        String videoName = "";
        if(!AppUtil.isEmpty(upgradeBean.getGoods_infos())) {
            for (GoodsUpgradeBean.GoodsInfos goodsInfos : upgradeBean.getGoods_infos()) {
                if (!goodsInfos.getType().startsWith(StartFinal.VIDEO)) {
                    topicName += goodsNameMap.get(goodsInfos.getType()) + "、";
                } else {
                    videoName += goodsNameMap.get(goodsInfos.getType()) + "、";
                }
            }
            if (topicName.endsWith("、")) {
                topicName = topicName.substring(0, topicName.length() - 1);
            }
            if (videoName.endsWith("、")) {
                videoName = videoName.substring(0, videoName.length() - 1);
            }
        }

        binding.buyTopicNameView.setRightText(topicName);
        binding.buyVideoNameView.setRightText(videoName);
        binding.discountPriceView.setRightText("-¥"+upgradeBean.getPrice_sum());
        if(!AppUtil.isEmpty(paySinglePrice)) {
            float singlePrice = upgradeBean.getMin().getIs_apply() == 0
                    ? MathUtil.getParseFloat(paySinglePrice)
                    : MathUtil.getParseFloat(upgradeBean.getMin().getMin_price());
            binding.singleView.setText(String.format("%s %s", "单独购买", singlePrice));
            binding.singleView.setVisibility(View.VISIBLE);
        }else {
            binding.singleView.setVisibility(View.GONE);
        }
        float groupPrice = upgradeBean.getMin_group().getIs_apply() == 0
                ? MathUtil.getParseFloat(payGroupPrice)
                : MathUtil.getParseFloat(upgradeBean.getMin_group().getMin_price());
        binding.groupBuyView.setText(String.format("%s %s", TextUtils.isEmpty(groupId) ? "发起拼团" : "立即参团", groupPrice));

        binding.minPriceDescView.setText(upgradeBean.getMin_text());
        return this;
    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
    }

    public GoodsUpgradeDialog setOnBuyListener(OnBuyListener onBuyListener) {
        this.onBuyListener = onBuyListener;
        return this;
    }

    public interface OnBuyListener{
        void onBuy(boolean isGroup, String groupId);
    }
}
