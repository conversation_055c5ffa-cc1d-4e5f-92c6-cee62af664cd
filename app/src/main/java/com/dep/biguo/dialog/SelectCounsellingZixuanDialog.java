package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.SpannableUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.CounsellingZixuanCourseBean;
import com.dep.biguo.bean.InternetStudyGoodsBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.databinding.SelectCounsellingZixuanDialogBinding;
import com.dep.biguo.utils.MathUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.widget.ItemDecoration;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.hjq.toast.ToastUtils;

import java.util.ArrayList;
import java.util.List;

public class SelectCounsellingZixuanDialog extends BottomSheetDialog implements View.OnClickListener{
    private SelectCounsellingZixuanDialogBinding binding;
    private SelectCounsellingZixuanDialog.Adapter courseAdapter;

    private SelectCounsellingZixuanDialog.Builder builder;
    private List<CounsellingZixuanCourseBean> checkedCourse = new ArrayList<>();

    public SelectCounsellingZixuanDialog(@NonNull Context context, SelectCounsellingZixuanDialog.Builder builder) {
        super(context);
        this.builder = builder;
        View view = LayoutInflater.from(context).inflate(R.layout.select_counselling_zixuan_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
        init();

        Window window = getWindow();
        window.findViewById(R.id.design_bottom_sheet).setBackgroundResource(R.color.tran);
    }

    public void init() {
        ImageLoader.loadImageNoPlaceholder(binding.thumbnailView, builder.thumbnail);
        binding.titleView.setText(builder.title);
        binding.descView.setText(builder.subTitle);
        binding.priceView.setText(String.format("¥%s/科", builder.price));

        courseAdapter = new SelectCounsellingZixuanDialog.Adapter(builder.list);
        courseAdapter.setOnItemClickListener((adapter, view, position) -> {
            if(courseAdapter.getItem(position).getIs_buy() == 1){
                ToastUtils.show("该课程已购买");
                return;
            }

            CounsellingZixuanCourseBean courseBean = courseAdapter.getItem(position);
            if(checkedCourse.contains(courseBean)){
                checkedCourse.remove(courseBean);
            }else {
                checkedCourse.add(courseBean);
            }
            courseAdapter.notifyDataSetChanged();
            totalPrice();
        });
        binding.recyclerView.setAdapter(courseAdapter);
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(5));
    }

    private void totalPrice(){
        String totalPrice = "0";
        for (CounsellingZixuanCourseBean itemBean : this.checkedCourse) {
            totalPrice = MathUtil.StringAddFloat(totalPrice, itemBean.getPrice()) + "";
        }

        if(MathUtil.getParseFloat(totalPrice) > 0f) {
            binding.mergeBuyView.setText(String.format("¥%s 确认报考科目", totalPrice));
        }else {
            binding.mergeBuyView.setText("确认报考科目");
        }
    }

    @Override
    public void onClick(View view) {
        if(view == binding.mergeBuyView){
            if(checkedCourse.size() < 3){
                ToastUtils.show("请至少选择3门课程");
                return;
            }
            builder.onBuyListener.onBuy(checkedCourse);
        }
        dismiss();
    }

    public class Adapter extends CommonAdapter<CounsellingZixuanCourseBean> {
        public Adapter(@Nullable List<CounsellingZixuanCourseBean> data) {
            super(R.layout.integral_exchange_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, CounsellingZixuanCourseBean item) {
            TextView courseNameView = holder.getView(R.id.courseNameView);
            courseNameView.setText(String.format("%s%s", item.getCode(), item.getName()));
            int textColor = item.getIs_buy() == 0 ? R.color.tblack : R.color.tblack3;
            courseNameView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), textColor, getContext().getTheme()));
            if(checkedCourse.contains(item)){
                courseNameView.setBackgroundResource(R.drawable.border_round_200_theme);
            }else {
                courseNameView.setBackgroundResource(R.drawable.bg_round_200_bgc);
            }
        }
    }

    public static class Builder{
        private Context context;
        private String thumbnail;
        private String title;
        private String subTitle;
        private String price;
        private List<CounsellingZixuanCourseBean> list;
        private SelectCounsellingZixuanDialog.OnBuyListener onBuyListener;

        public Builder(Context context) {
            this.context = context;
        }

        public SelectCounsellingZixuanDialog.Builder setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
            return this;
        }

        public SelectCounsellingZixuanDialog.Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public SelectCounsellingZixuanDialog.Builder setSubTitle(String subTitle) {
            this.subTitle = subTitle;
            return this;
        }

        public SelectCounsellingZixuanDialog.Builder setPrice(String price) {
            this.price = price;
            return this;
        }

        public SelectCounsellingZixuanDialog.Builder setList(List<CounsellingZixuanCourseBean> list) {
            this.list = list;
            return this;
        }

        public SelectCounsellingZixuanDialog.Builder setOnBuyListener(SelectCounsellingZixuanDialog.OnBuyListener onBuyListener) {
            this.onBuyListener = onBuyListener;
            return this;
        }

        public SelectCounsellingZixuanDialog build(){
            return new SelectCounsellingZixuanDialog(context, this);
        }
    }

    public interface OnBuyListener{
        void onBuy(List<CounsellingZixuanCourseBean> course);
    }
}
