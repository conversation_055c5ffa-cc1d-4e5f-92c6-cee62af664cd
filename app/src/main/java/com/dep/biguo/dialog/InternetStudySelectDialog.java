package com.dep.biguo.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.SpannableUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.InternetStudyGoodsBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.databinding.InternetStudySelectDialogBinding;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.widget.ItemDecoration;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.hjq.toast.ToastUtils;

import java.util.List;

public class InternetStudySelectDialog extends BottomSheetDialog implements View.OnClickListener{
    private InternetStudySelectDialogBinding binding;
    private InternetStudySelectDialog.Adapter courseAdapter;

    private Builder builder;
    private InternetStudyGoodsBean.Course checkedCourse;

    public InternetStudySelectDialog(@NonNull Context context, Builder builder) {
        super(context);
        this.builder = builder;
        View view = LayoutInflater.from(context).inflate(R.layout.internet_study_select_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
        init();

        Window window = getWindow();
        window.findViewById(R.id.design_bottom_sheet).setBackgroundResource(R.color.tran);
    }
    public void init() {
        ImageLoader.loadImageNoPlaceholder(binding.thumbnailView, builder.thumbnail);
        binding.titleView.setText(SpannableUtil.setChineseBracketsRightDrawable(getContext(), builder.title, binding.titleView.getTextSize()));
        binding.subtitleView.setText(builder.subTitle);
        binding.priceView.setText(builder.price);

        courseAdapter = new InternetStudySelectDialog.Adapter(builder.list);
        courseAdapter.setOnItemClickListener((adapter, view, position) -> {
            if(courseAdapter.getItem(position).getIs_buy() == 1){
                ToastUtils.show("该课程已购买");
                return;
            }

            if(checkedCourse == courseAdapter.getItem(position)){
                checkedCourse = null;
            }else {
                checkedCourse = courseAdapter.getItem(position);
            }
            setBuyViewStyle();
        });
        binding.recyclerView.setAdapter(courseAdapter);
        binding.recyclerView.getItemAnimator().setChangeDuration(0);
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(5));
        //初始化兑换按钮
        setBuyViewStyle();
    }

    public void setBuyViewStyle(){
        if(checkedCourse != null && checkedCourse.getIs_bundle() == StartFinal.NO){
            binding.singleBugView.setText(String.format("立即购买¥%s", checkedCourse.getPrice()));
            binding.singleBugView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), R.color.twhite, getContext().getTheme()));
            binding.singleBugView.setBackgroundResource(R.drawable.bg_round_200_theme);

        }else if(checkedCourse != null && checkedCourse.getIs_bundle() == StartFinal.YES) {
            binding.singleBugView.setText(String.format("单独购买¥%s", checkedCourse.getPrice()));
            binding.singleBugView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), R.color.theme, getContext().getTheme()));
            binding.singleBugView.setBackgroundResource(R.drawable.border_round_200_theme);

        }else {
            binding.singleBugView.setText("立即购买");
            binding.singleBugView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), R.color.twhite, getContext().getTheme()));
            binding.singleBugView.setBackgroundResource(R.drawable.bg_round_200_gray);
        }

        if(checkedCourse != null && checkedCourse.getIs_bundle() == StartFinal.YES) {
            binding.mergeBuyView.setText(String.format("购买助学押题包¥%s", checkedCourse.getBundle_price()));
            binding.mergeBuyView.setVisibility(View.VISIBLE);
            binding.mergeDetailView.setVisibility(View.VISIBLE);

        }else {
            binding.mergeBuyView.setVisibility(View.GONE);
            binding.mergeDetailView.setVisibility(View.GONE);
        }

        courseAdapter.notifyItemRangeChanged(0, builder.list.size());
    }

    @Override
    public void onClick(View view) {
        if(view == binding.closeView){
            dismiss();

        }else if(view == binding.singleBugView){
            if(checkedCourse == null){
                ToastUtils.show("请选择一个科目");
                return;
            }

            builder.onBuyListener.onBuy(checkedCourse, StartFinal.NO,  checkedCourse.getPrice());
            dismiss();

        }else if(view == binding.mergeBuyView){
            if(checkedCourse == null){
                ToastUtils.show("请选择一个科目");
                return;
            }

            if(checkedCourse.getIs_bundle() != StartFinal.YES) {
                ToastUtils.show("该科目没有押题包");
                return;
            }

            builder.onBuyListener.onBuy(checkedCourse, StartFinal.YES, checkedCourse.getBundle_price());
            dismiss();
        }
    }

    public class Adapter extends CommonAdapter<InternetStudyGoodsBean.Course> {
        public Adapter(@Nullable List<InternetStudyGoodsBean.Course> data) {
            super(R.layout.integral_exchange_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, InternetStudyGoodsBean.Course item) {
            TextView courseNameView = holder.getView(R.id.courseNameView);
            courseNameView.setText(String.format("%s%s", item.getCode(), item.getName()));
            int textColor = item.getIs_buy() == 0 ? R.color.tblack : R.color.tblack3;
            courseNameView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), textColor, getContext().getTheme()));
            if(checkedCourse == item){
                courseNameView.setBackgroundResource(R.drawable.border_round_200_theme);
            }else {
                courseNameView.setBackgroundResource(R.drawable.bg_round_200_bgc);
            }
        }
    }


    @Override
    public void dismiss() {
        super.dismiss();
        builder.context = null;
        builder.onBuyListener = null;
        builder = null;
    }

    public static class Builder{
        private Context context;
        private String thumbnail;
        private String title;
        private String subTitle;
        private String price;
        private List<InternetStudyGoodsBean.Course> list;
        private OnBuyListener onBuyListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setSubTitle(String subTitle) {
            this.subTitle = subTitle;
            return this;
        }

        public Builder setPrice(String price) {
            this.price = price;
            return this;
        }

        public Builder setList(List<InternetStudyGoodsBean.Course> list) {
            this.list = list;
            return this;
        }

        public Builder setOnBuyListener(OnBuyListener onBuyListener) {
            this.onBuyListener = onBuyListener;
            return this;
        }

        public InternetStudySelectDialog build(){
            return new InternetStudySelectDialog(context, this);
        }
    }

    public interface OnBuyListener{
        void onBuy(InternetStudyGoodsBean.Course course, int is_bundle, String totalPrice);
    }
}
