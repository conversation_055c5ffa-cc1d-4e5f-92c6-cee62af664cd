package com.dep.biguo.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.StyleRes;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import com.dep.biguo.R;

import butterknife.ButterKnife;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/29
 * @Description:
 */
public abstract class BottomDialog extends BottomSheetDialog {

    public @LayoutRes
    int layoutId = 0;
    public BottomDialog(@NonNull Context context) {
        super(context, R.style.BottomDialog);
        setContentView(getLayoutId());
        ButterKnife.bind(this);
        getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundResource(android.R.color.transparent);
    }

    public BottomDialog(@NonNull Context context, @LayoutRes int layoutId) {
        super(context, R.style.BottomDialog);
        setLayoutId(layoutId);
        setContentView(getLayoutId());
        ButterKnife.bind(this);
        getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundResource(android.R.color.transparent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    public void setLayoutId(int layoutId) {
        this.layoutId = layoutId;
    }

    public abstract @LayoutRes
    int getLayoutId();

    public abstract void init();

}
