package com.dep.biguo.dialog;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.LoginPrivateDialogBinding;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.google.android.material.bottomsheet.BottomSheetDialog;

public class LoginPrivateDialog extends BottomSheetDialog {
    private LoginPrivateDialogBinding binding;

    public LoginPrivateDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.login_private_dialog, null, false);
        setContentView(binding.getRoot());

        setPrivateAgreementText();
    }

    private void setPrivateAgreementText(){
        String text = binding.privateView.getText().toString();
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new ForegroundColorSpan(getContext().getResources().getColor(R.color.theme)), 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(getContext().getResources().getColor(R.color.theme)), 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加用户协议事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(getContext(), Constant.AGREEMENT_USER);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加隐私政策事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(getContext(), Constant.AGREEMENT_USER4);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        //必须设置，否则无法点击
        binding.privateView.setMovementMethod(LinkMovementMethod.getInstance());
        binding.privateView.setText(spannableString);
    }

    public LoginPrivateDialog setOnAgreeListener(View.OnClickListener onClickListener){
        binding.agreeView.setOnClickListener(v -> {
            dismiss();
            onClickListener.onClick(v);
        });
        return this;
    }
}
