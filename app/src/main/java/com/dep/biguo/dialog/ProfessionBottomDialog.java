package com.dep.biguo.dialog;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.dialog.BottomDialog;
import com.dep.biguo.mvp.ui.adapter.ProfessionAdapter;
import com.jess.arms.utils.ArmsUtils;

import butterknife.BindView;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/30
 * @Description:
 */
public class ProfessionBottomDialog extends BottomDialog {

    public static final int BK_LAYER = 1;
    public static final int ZK_LAYER = 2;

    @BindView(R.id.rvProfession)
    RecyclerView rvProfession;
    @BindView(R.id.tvBk)
    TextView tvBk;
    @BindView(R.id.tvZk)
    TextView tvZk;
    @BindView(R.id.lineBk)
    View lineBk;
    @BindView(R.id.lineZk)
    View lineZk;

    private ProfessionAdapter mProfessionAdapter;

    private OnChangeLayerListener mOnChangeLayerListener;

    public ProfessionBottomDialog(@NonNull Context context, ProfessionAdapter adapter) {
        super(context);
        this.mProfessionAdapter = adapter;

        mProfessionAdapter.bindToRecyclerView(rvProfession);
        ArmsUtils.configRecyclerView(rvProfession, new LinearLayoutManager(context));
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_profession;
    }

    @Override
    public void init() {
        tvBk.setOnClickListener(v -> {
            if (mOnChangeLayerListener != null) {
                mOnChangeLayerListener.onChangeLayer(BK_LAYER);
                rvProfession.scrollToPosition(0);
                changeLayer(BK_LAYER);
            }
        });
        tvZk.setOnClickListener(v -> {
            if (mOnChangeLayerListener != null) {
                mOnChangeLayerListener.onChangeLayer(ZK_LAYER);
                rvProfession.scrollToPosition(0);
                changeLayer(ZK_LAYER);
            }
        });
    }

    private void changeLayer(int laer) {
        tvBk.setTextColor(ContextCompat.getColor(getContext(), laer == BK_LAYER ? R.color.orange : R.color.tblack2));
        tvZk.setTextColor(ContextCompat.getColor(getContext(), laer == ZK_LAYER ? R.color.orange : R.color.tblack2));
        lineBk.setVisibility(laer == BK_LAYER ? View.VISIBLE : View.INVISIBLE);
        lineZk.setVisibility(laer == ZK_LAYER ? View.VISIBLE : View.INVISIBLE);
    }

    public void setOnChangeLayerListener(OnChangeLayerListener onChangeLayerListener) {
        this.mOnChangeLayerListener = onChangeLayerListener;
    }

    public interface OnChangeLayerListener {
        void onChangeLayer(int layer);
    }
}
