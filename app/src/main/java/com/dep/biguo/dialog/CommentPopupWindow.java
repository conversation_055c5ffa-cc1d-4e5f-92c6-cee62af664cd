package com.dep.biguo.dialog;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.hjq.toast.ToastUtils;

public class CommentPopupWindow extends PopupWindow implements View.OnClickListener{
    private OnItemClickListener onItemClickListener;
    private String comment;
    private int measuredHeight;
    private final LinearLayout optionLayout;
    private final ImageView arrowView;
    private final TextView deleteView;
    private final TextView reportView;

    private TextView targetView;

    public CommentPopupWindow(Context context, OnItemClickListener onItemClickListener) {
        super(context);
        this.onItemClickListener = onItemClickListener;

        View parentView = LayoutInflater.from(context).inflate(R.layout.comment_popup_window,null);
        setContentView(parentView);
        findViewById(R.id.replayView,parentView);
        findViewById(R.id.copyView,parentView);
        findViewById(R.id.deleteView,parentView);
        findViewById(R.id.reportView,parentView);
        optionLayout = parentView.findViewById(R.id.optionLayout);
        arrowView = parentView.findViewById(R.id.arrowView);
        deleteView = parentView.findViewById(R.id.deleteView);
        reportView = parentView.findViewById(R.id.reportView);

        this.setBackgroundDrawable(new ColorDrawable());
        this.setOutsideTouchable(true);
        this.setTouchable(true);

        parentView.measure(0,0);
        measuredHeight = parentView.getMeasuredHeight();

    }

    public void findViewById(int viewId, View parentView){
        TextView view = parentView.findViewById(viewId);
        view.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if(view.getId() == R.id.replayView){
            onItemClickListener.onReplay();

        }else if(view.getId() == R.id.copyView){
            Context context = getContentView().getContext();
            //获取剪贴板管理器：
            ClipboardManager cm = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            // 创建普通字符型ClipData
            ClipData mClipData = ClipData.newPlainText("Label", comment);
            // 将ClipData内容放到系统剪贴板里。
            cm.setPrimaryClip(mClipData);
            ToastUtils.show("复制成功");

        }else if(view.getId() == R.id.deleteView){
            onItemClickListener.onDelete();

        }else if(view.getId() == R.id.reportView){
            onItemClickListener.onReport();

        }
        dismiss();
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public void setArrowX(int arrowX){
        //弹窗的宽度，42是指每个按钮的宽度，12是弹窗左右两边的边距
        int width = DisplayHelper.dp2px(optionLayout.getContext(), deleteView.getVisibility() == View.VISIBLE ? 42*4+12 : 42*3+12);
        //测量箭头的尺寸
        arrowView.measure(0,0);
        //设置箭头的位置
        ConstraintLayout.LayoutParams params = ((ConstraintLayout.LayoutParams)arrowView.getLayoutParams());
        params.setMarginStart(Math.min(arrowX, width-arrowView.getMeasuredWidth()));
        arrowView.setLayoutParams(params);
    }

    public void isShowDelete(boolean isShow){
        deleteView.setVisibility(isShow ? View.VISIBLE:View.GONE);
    }

    public void isShowReport(boolean isShow){
        reportView.setVisibility(isShow ? View.VISIBLE:View.GONE);
    }

    /**将评论或回复控件的背景颜色改变，作为选中的颜色
     *
     */
    private void setTargetViewBackground(TextView targetView){
        Drawable drawable = optionLayout.getContext().getResources().getDrawable(R.color.theme);
        drawable.setBounds(0,0, targetView.getWidth(), targetView.getMeasuredHeight());
        drawable.setAlpha(40);
        targetView.setBackground(drawable);
        this.targetView = targetView;
    }

    public void show(TextView targetView, int touchX){
        setTargetViewBackground(targetView);

        int[] location = new int[2];
        //获取评论在屏幕中坐标
        targetView.getLocationOnScreen(location);
        //弹窗的箭头距离弹窗边界的外边距,touchX是一个相对questionRecyclerView的item的坐标，此处可以看做相对于屏幕
        int commentViewWidth = targetView.getWidth();
        setArrowX(getInTheInterval(0, touchX-location[0], commentViewWidth));
        //设置弹窗的位置
        showAtLocation(targetView, Gravity.TOP|Gravity.START, location[0]-30, location[1] - measuredHeight);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if(targetView != null) {
            targetView.setBackground(null);
            targetView = null;
        }
    }

    /**返回长按评论弹出弹窗的箭头距离弹窗边界的外边距
     * @param leftBorder 弹窗的左边界
     * @param value 触摸点的横坐标
     * @param rightBorder 弹窗的右边界
     * @return 外边距
     */
    public int getInTheInterval(int leftBorder, int value, int rightBorder){
        return Math.min(Math.max(leftBorder, value),rightBorder);
    }

    public interface OnItemClickListener{
        void onReplay();

        void onDelete();

        void onReport();
    }
}
