package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.DisplayHelper;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.IntegralExchangeListBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.widget.ItemDecoration;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**继承BottomDialog类，当recyclerView快速滚动到顶部或底部，导致item点击事件需要点击两次才能生效
 * 但目前方案依然有问题，用列表保存适配器的holder，可以得知每次响应点击事件后，将会创建新的holder*/
public class TaskExchangeDialog extends Dialog {
    @BindView(R.id.closeView) ImageView closeView;
    @BindView(R.id.thumbnailView) ImageView thumbnailView;
    @BindView(R.id.titleView) TextView titleView;
    @BindView(R.id.subtitleView) TextView subtitleView;
    @BindView(R.id.recyclerView) RecyclerView recyclerView;
    @BindView(R.id.exchangeView) TextView exchangeView;

    private Builder builder;
    private Adapter courseAdapter;
    private IntegralExchangeListBean checkedCourseBean;

    public TaskExchangeDialog(@NonNull Context context, Builder builder) {
        super(context, R.style.BottomDialog);
        this.builder = builder;
        setContentView(R.layout.task_exchange_dialog);
        ButterKnife.bind(this);
        init();
        Window window = getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.getAttributes().width = DisplayHelper.getWindowWidth(getContext());

    }

    public void init() {
        ImageLoader.loadImageNoPlaceholder(thumbnailView, builder.thumbnail);
        titleView.setText(builder.title);
        subtitleView.setText(builder.subtitle);

        courseAdapter = new Adapter(builder.list);
        courseAdapter.setOnItemClickListener((adapter, view, position) -> {
            if(checkedCourseBean != builder.list.get(position)) {
                checkedCourseBean = builder.list.get(position);
            }else {
                checkedCourseBean = null;
            }
            setExchangeViewStyle();
        });
        courseAdapter.bindToRecyclerView(recyclerView);
        recyclerView.getItemAnimator().setChangeDuration(0);
        recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(5));
        //初始化兑换按钮
        setExchangeViewStyle();
    }

    public void setExchangeViewStyle(){
        exchangeView.setText("确定兑换");
        exchangeView.setBackgroundResource(checkedCourseBean != null ? R.drawable.bg_round_200_theme : R.drawable.bg_round_200_un_click);
        courseAdapter.notifyItemRangeChanged(0, builder.list.size());
    }

    @OnClick({R.id.closeView, R.id.exchangeView})
    public void onViewClicked(View view) {
        if(view == closeView){
            dismiss();

        }else if(view == exchangeView){
            if(checkedCourseBean != null){
                builder.onExchangeListener.onExchange(checkedCourseBean.getCode());
                dismiss();
            }
        }
    }

    public class Adapter extends CommonAdapter<IntegralExchangeListBean>{
        public Adapter(@Nullable List<IntegralExchangeListBean> data) {
            super(R.layout.integral_exchange_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, IntegralExchangeListBean item) {
            TextView courseNameView = holder.getView(R.id.courseNameView);
            courseNameView.setText(String.format("【%s】%s%s", builder.title, item.getCode(), item.getName()));
            if(checkedCourseBean == item){
                courseNameView.setBackgroundResource(R.drawable.border_round_200_theme);
            }else {
                courseNameView.setBackgroundResource(R.drawable.bg_round_200_bgc);
            }
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        builder.context = null;
        builder.onExchangeListener = null;
        builder = null;
    }

    public static class Builder{
        private Context context;
        private String title;
        private String subtitle;
        private String thumbnail;
        private List<IntegralExchangeListBean> list;
        private OnExchangeListener onExchangeListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setSubtitle(String subtitle) {
            this.subtitle = subtitle;
            return this;
        }

        public Builder setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
            return this;
        }


        public Builder setList(List<IntegralExchangeListBean> list) {
            this.list = list;
            return this;
        }

        public Builder setOnExchangeListener(OnExchangeListener onExchangeListener) {
            this.onExchangeListener = onExchangeListener;
            return this;
        }

        public TaskExchangeDialog build(){
            return new TaskExchangeDialog(context, this);
        }
    }

    public interface OnExchangeListener{
        void onExchange(String code);
    }
}
