package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.ExchangeCodeDialogBinding;

public class ExchangeCodeDialog extends Dialog implements View.OnClickListener{
    private ExchangeCodeDialogBinding binding;
    private OnExchangeListener onExchangeListener;

    public ExchangeCodeDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.exchange_code_dialog);
        binding = DataBindingUtil.bind(findViewById(R.id.rootView));
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        //监听软键盘的“完成”按钮，相当于点击了兑换按钮
        binding.inputCodeView.setOnEditorActionListener((v, actionId, event) -> {
            if(actionId == EditorInfo.IME_ACTION_DONE){
                onClick(binding.exchangeView);
            }
            return false;
        });
    }

    public void setProfession(CharSequence profession){
        binding.professionView.setText(String.format("当前专业：%s", profession));
    }

    @Override
    public void onClick(View view) {
        dismiss();
        if(view == binding.exchangeView){
            if(onExchangeListener != null){
                onExchangeListener.onExchange(binding.inputCodeView.getText().toString());
            }
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.dp2px(getContext(), 295);
        getWindow().setAttributes(layoutParams);
    }

    public void setOnExchangeListener(OnExchangeListener onExchangeListener) {
        this.onExchangeListener = onExchangeListener;
    }

    public interface OnExchangeListener{
        void onExchange(String code);
    }
}
