package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.biguo.utils.widget.LoadingDialog;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.ShareUtil;
import com.hjq.toast.ToastUtils;
import com.umeng.socialize.Config;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMMin;

import butterknife.OnClick;

public class ShareDownloadDialog extends BottomDialog {

    private Builder.Params P;

    private void setParams(Builder.Params P) {
        this.P = P;
    }

    private ShareDownloadDialog(@NonNull Context context) {
        super(context);
    }

    private ShareDownloadDialog(@NonNull Context context, @LayoutRes int layouId) {
        super(context, layouId);
    }

    @Override
    public int getLayoutId() {
        if (layoutId > 0)
            return layoutId;
        else
            return R.layout.dialog_download_share;
    }

    @Override
    public void init() {
        TextView tvShareHint = findViewById(R.id.tvShareHint);
        tvShareHint.setVisibility(TextUtils.isEmpty(P.mShareDialogHint) ? View.GONE : View.VISIBLE);
        tvShareHint.setText(P.mShareDialogHint);
    }

    @OnClick({R.id.tvWechat, R.id.tvCircle, R.id.tvQq, R.id.tvWechatMini, R.id.tvCancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvWechat:
                share(SHARE_MEDIA.WEIXIN);
                break;
            case R.id.tvCircle:
                share(SHARE_MEDIA.WEIXIN_CIRCLE);
                break;
            case R.id.tvCancel:
                break;
            case R.id.tvQq:
                share(SHARE_MEDIA.QQ);
                break;
            case R.id.tvWechatMini:
                P.mShareType = ShareUtil.SHARE_TYPE.MINI;
                share(SHARE_MEDIA.WEIXIN);
                break;
        }
        dismiss();
    }

    private void share(SHARE_MEDIA share_media) {
        ShareUtil.OnShareListener shareListener = media -> {
            ToastUtils.show("分享成功");
            if (P.mOnShareListener != null) {
                P.mOnShareListener.onSuccess(ShareUtil.getNameByMedia(share_media));
            }
        };

        ShareUtil.Builder builder = new ShareUtil.Builder((AppCompatActivity) P.mContext, share_media)
                .setShare_type(P.mShareType)
                .setOnShareListener(shareListener);

        if (P.mShareType == ShareUtil.SHARE_TYPE.IMAGE) {
            ToastUtils.show("正在处理图片");

        }else {
            builder.setTitle(P.mShareTitle)
                    .setContent(P.mShareContent)
                    .setMedia(share_media)
                    .setUrl(P.mShareUrl)
                    .setOnShareListener(shareListener);
            if(P.mShareBitmap != null) {
                builder.setIconBitmap(P.mShareBitmap);
            }else {
                builder.setIconRes(P.mShareResIcon);
            }
        }
        ShareUtil.Share(builder);
    }

    public static void loadImageShare(Context context, Object obj, LoadImageListener listener){
        //弄个弹窗是防止加载图片太久
        LoadingDialog.showLoadingDialog(context);
        Glide.with(context)
                .asBitmap()
                .load(obj)
                .into(new CustomTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                        LoadingDialog.hideLoadingDialog();
                        listener.loadImage(resource);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {

                    }
                });
    }

    public static interface LoadImageListener{
        void loadImage(Bitmap resource);
    }


    public static class Builder {
        private Params P;

        public Builder(Context context) {
            P = new Params(context);
        }

        public Builder setShareUrl(String mShareUrl) {
            P.mShareUrl = mShareUrl;
            return this;
        }

        public Builder setShareTitle(String mShareTitle) {
            P.mShareTitle = mShareTitle;
            return this;
        }

        public Builder setShareDialogHint(String mShareDialogHint) {
            P.mShareDialogHint = mShareDialogHint;
            return this;
        }

        public Builder setShareContent(String mShareContent) {
            P.mShareContent = mShareContent;
            return this;
        }

        public Builder setShareIcon(String mShareIcon) {
            P.mShareIcon = mShareIcon;
            return this;
        }

        public Builder setShareIcon(int mShareResIcon) {
            P.mShareResIcon = mShareResIcon;
            return this;
        }

        public Builder setShareBitmap(Bitmap bitmap, ShareUtil.SHARE_TYPE share_type) {
            P.mShareBitmap = bitmap;
            P.mShareType = share_type;
            return this;
        }

        public Builder setOnShareListener(OnShareListener mOnShareListener) {
            P.mOnShareListener = mOnShareListener;
            return this;
        }

        public ShareDownloadDialog builder() {
            ShareDownloadDialog dialog = new ShareDownloadDialog(P.mContext);
            dialog.setParams(P);
            return dialog;
        }

        public ShareDownloadDialog builder(@LayoutRes int layouId) {
            ShareDownloadDialog dialog = new ShareDownloadDialog(P.mContext, layouId);
            dialog.setParams(P);
            return dialog;
        }
        private static class Params {
            private Context mContext;

            private String mShareUrl = ShareUtil.getShareUrl();
            private String mShareTitle = "自考笔果题库-我的自考神器";
            private String mShareContent = "下载笔果，考试必过";
            private String mShareDialogHint = "";
            private String mShareIcon;
            private int mShareResIcon;
            private Bitmap mShareBitmap;
            private ShareUtil.SHARE_TYPE mShareType = ShareUtil.SHARE_TYPE.LINK;
            private OnShareListener mOnShareListener;

            public Params(Context mContext) {
                this.mContext = mContext;
            }
        }
    }

    public interface OnShareListener {
        void onSuccess(String type);
    }

}
