package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.hjq.toast.ToastUtils;

public class AssessDialog extends Dialog implements View.OnClickListener {
    private ImageView closeView;
    private EditText inputView;
    private TextView inputCountView;
    private TextView commitView;

    private OnCommitListener onCommitListener;

    public AssessDialog(@NonNull Context context) {
        super(context);
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams wmLp = window.getAttributes();
            wmLp.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            wmLp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            window.setAttributes(wmLp);
            window.setBackgroundDrawableResource(android.R.color.transparent);//去掉白色背景
        }
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.assess_dialog);

        init();
    }


    public void init(){
        closeView = findViewById(R.id.closeView);
        inputView = findViewById(R.id.inputView);
        inputCountView = findViewById(R.id.inputCountView);
        commitView = findViewById(R.id.commitView);

        inputView.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                inputCountView.setText(String.format("%s/100", inputView.length()));
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        closeView.setOnClickListener(this);
        commitView.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if(v == closeView){
            dismiss();
        }else if(v == commitView){
            String assess = inputView.getText().toString();
            if(assess.length() >= 6) {
                onCommitListener.commit(inputView.getText().toString());
                dismiss();
            }else {
                ToastUtils.show("评论不能少于6个字");
            }
        }

    }

    public void setOnCommitListener(OnCommitListener onCommitListener) {
        this.onCommitListener = onCommitListener;
    }

    public interface OnCommitListener{
        void commit(String assess);
    }
}
