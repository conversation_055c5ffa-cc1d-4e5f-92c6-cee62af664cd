package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.SelectCourseBean;
import com.dep.biguo.databinding.SelectCourseDialogBinding;
import com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity;
import com.dep.biguo.mvp.ui.activity.SelectCourseActivity;
import com.dep.biguo.mvp.ui.activity.TestPlanTableActivity;
import com.dep.biguo.mvp.ui.adapter.SelectCourseAdapter;
import com.dep.biguo.mvp.ui.adapter.SelectCourseDialogAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.ItemDecoration;
import com.hjq.toast.ToastUtils;

public class SelectCourseDialog extends PopupWindow {
    private SelectCourseDialogBinding binding;
    private SelectCourseDialogAdapter joinAdapter;
    private SelectCourseDialogAdapter unJoinAdapter;
    private SelectCourseDialogAdapter passAdapter;

    private SelectCourseDialog.OnSelectCourseListener onSelectCourseListener;

    private String courseChangeUrl;

    public SelectCourseDialog(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.select_course_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.lookPlanView.setOnClickListener(v -> {
            TestPlanTableActivity.start(context, courseChangeUrl);
            dismiss();
        });
        binding.moreCourseView.setOnClickListener(v -> {
            ProfessionSchoolActivity.Start(context);
            dismiss();
        });
        binding.flContent.setOnClickListener(v -> dismiss());
        binding.confirmView.setOnClickListener(v -> {
            if(joinAdapter.getItemCount() > 0) {
                onSelectCourseListener.onSelectCourse(joinAdapter.getItem(0));
            }else {
                ToastUtils.show("请先报考科目");
            }
        });

        this.setContentView(binding.getRoot());
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setBackgroundDrawable(new ColorDrawable());
        this.setFocusable(true);

        joinAdapter = new SelectCourseDialogAdapter(false);
        joinAdapter.setInputScore(true);
        binding.joinRecyclerView.setAdapter(joinAdapter);
        binding.joinRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10));

        unJoinAdapter = new SelectCourseDialogAdapter(true);
        unJoinAdapter.setInputScore(true);
        binding.unJoinRecyclerView.setAdapter(unJoinAdapter);
        binding.unJoinRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10));

        passAdapter = new SelectCourseDialogAdapter(true);
        binding.passRecyclerView.setAdapter(passAdapter);
        binding.passRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10));

    }

    public SelectCourseDialog manageCourseSuccess() {
        joinAdapter.notifyDataSetChanged();
        unJoinAdapter.notifyDataSetChanged();
        passAdapter.notifyDataSetChanged();

        binding.joinRecyclerView.setVisibility(joinAdapter.getItemCount() == 0 ? View.GONE : View.VISIBLE);
        binding.noJoinView.setVisibility(joinAdapter.getItemCount() == 0 ? View.VISIBLE : View.GONE);

        binding.unJoinRecyclerView.setVisibility(unJoinAdapter.getItemCount() == 0 ? View.GONE : View.VISIBLE);
        binding.noUnJoinView.setVisibility(unJoinAdapter.getItemCount() == 0 ? View.VISIBLE : View.GONE);

        binding.passRecyclerView.setVisibility(passAdapter.getItemCount() == 0 ? View.GONE : View.VISIBLE);
        binding.noPassView.setVisibility(passAdapter.getItemCount() == 0 ? View.VISIBLE : View.GONE);

        return this;
    }

    public SelectCourseDialog setCourseBean(CourseBean courseBean) {
        joinAdapter.setNewData(courseBean.getCourses_joined());
        unJoinAdapter.setNewData(courseBean.getCourses_not_joined());
        passAdapter.setNewData(courseBean.getCourses_passed());
        courseChangeUrl = courseBean.getCompare_picture();

        manageCourseSuccess();
        return this;
    }

    public SelectCourseDialog setWriteScoreListener(SelectCourseDialogAdapter.OnWriteScoreListener onWriteScoreListener){
        joinAdapter.setOnWriteScoreListener(onWriteScoreListener);
        unJoinAdapter.setOnWriteScoreListener(onWriteScoreListener);
        passAdapter.setOnWriteScoreListener(item -> ToastUtils.show("已通过的科目不可修改成绩"));
        return this;
    }

    public SelectCourseDialog setJoinOnClickListener(SelectCourseDialogAdapter.OnClickListener onClickListener){
        joinAdapter.setOnClickListener(onClickListener);
        return this;
    }
    public SelectCourseDialog setUnJoinOnClickListener(SelectCourseDialogAdapter.OnClickListener onClickListener){
        unJoinAdapter.setOnClickListener(onClickListener);
        return this;
    }
    public SelectCourseDialog setPassOnClickListener(SelectCourseDialogAdapter.OnClickListener onClickListener){
        passAdapter.setOnClickListener(onClickListener);
        return this;
    }
    public SelectCourseDialog setSelectCourseListener(OnSelectCourseListener onSelectCourseListener){
        joinAdapter.setOnSelectCourseListener(onSelectCourseListener);
        unJoinAdapter.setOnSelectCourseListener(onSelectCourseListener);
        passAdapter.setOnSelectCourseListener(onSelectCourseListener);
        this.onSelectCourseListener = onSelectCourseListener;
        return this;
    }

    public interface OnSelectCourseListener{
        void onSelectCourse(CourseBean.CourseItemBean item);
    }
}

