package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.ApplyPromoterDialogBinding;
import com.hjq.toast.ToastUtils;

public class ApplyPromoterDialog extends Dialog implements View.OnClickListener{
    private ApplyPromoterDialogBinding binding;

    private OnApplyPromoterListener onApplyPromoterListener;

    public ApplyPromoterDialog(@NonNull Context context) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.apply_promoter_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(view);

        setCanceledOnTouchOutside(false);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 320);
        getWindow().setBackgroundDrawableResource(R.color.tran);
    }

    @Override
    public void onClick(View v) {
        if(v == binding.okView){
            String name = binding.nameView.getText().toString();
            String IDCard = binding.IDCardView.getText().toString();
            if(name.length() == 0){
                ToastUtils.show(binding.nameView.getHint());
                return;
            }
            if(IDCard.length() == 0){
                ToastUtils.show(binding.IDCardView.getHint());
                return;
            }
            onApplyPromoterListener.onApplyPromoter(name, IDCard);
        }else {
            dismiss();
        }
    }

    public void setOnApplyPromoterListener(OnApplyPromoterListener onApplyPromoterListener) {
        this.onApplyPromoterListener = onApplyPromoterListener;
    }

    public interface OnApplyPromoterListener{
        void onApplyPromoter(String name, String IDCard);
    }
}
