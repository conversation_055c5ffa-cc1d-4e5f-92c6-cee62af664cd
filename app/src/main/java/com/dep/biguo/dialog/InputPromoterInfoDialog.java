package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.MatcherUtil;
import com.hjq.toast.ToastUtils;

import org.jetbrains.annotations.NotNull;

public class InputPromoterInfoDialog extends DialogFragment {

    public static final String DIALOG_TAG = "ExchangeRuleDialog";

    private TextView tvAgree;
    private ImageView ivClose;
    private EditText etReallyName;
    private EditText etIDCard;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_input_prmoter_info, container, false);
        tvAgree = view.findViewById(R.id.tvAgree);
        ivClose = view.findViewById(R.id.ivClose);
        etReallyName = view.findViewById(R.id.etReallyName);
        etIDCard = view.findViewById(R.id.etIDCard);

        tvAgree.setOnClickListener(v -> {
            if (null != mOnAgreementListener) {
                String strReallyName = etReallyName.getText().toString();
                String strIDCard = etIDCard.getText().toString();
                if (TextUtils.isEmpty(strReallyName)) {
                    ToastUtils.show("姓名不能为空");
                    return;
                }
                if (TextUtils.isEmpty(strIDCard)) {
                    ToastUtils.show("身份证号不能为空");
                    return;
                }
                if (!MatcherUtil.isIDNumber(strIDCard)) {
                    ToastUtils.show("身份证号不正确");
                    return;
                }
                mOnAgreementListener.onAgreement(strReallyName, strIDCard);
            }
        });
        ivClose.setOnClickListener(v -> dismiss());
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, 0);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window dialogWindow = dialog.getWindow();
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = (int) (DisplayHelper.getWindowWidth(dialog.getContext()) * 0.9);
            lp.height = (int) (DisplayHelper.getWindowHeight(dialog.getContext()) * 0.8);
            dialogWindow.setAttributes(lp);

            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
        }
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, DIALOG_TAG);
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    private OnAgreementListener mOnAgreementListener;

    public void setOnAgreementListener(OnAgreementListener onAgreementListener) {
        this.mOnAgreementListener = onAgreementListener;
    }

    public interface OnAgreementListener {
        void onAgreement(String name, String id);
    }
}
