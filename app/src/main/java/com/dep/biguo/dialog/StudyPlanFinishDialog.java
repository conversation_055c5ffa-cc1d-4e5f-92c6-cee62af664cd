package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.widget.StyleTextView;
import com.biguo.utils.widget.StyleViewAttr;
import com.dep.biguo.R;
import com.dep.biguo.databinding.StudyPlanFinishDialogBinding;

public class StudyPlanFinishDialog extends Dialog implements View.OnClickListener {
    private StudyPlanFinishDialogBinding binding;

    public StudyPlanFinishDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.study_plan_finish_dialog, null, false);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        setCanceledOnTouchOutside(false);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 295);
        getWindow().setBackgroundDrawableResource(R.color.tran);
        setCanceledOnTouchOutside(false);
    }

    //设置已学天数
    public StudyPlanFinishDialog setStudyDay(String text){
        String[] count = text.split("");
        binding.finishDayView.removeAllViews();
        for(String item : count) {
            StyleTextView itemView = new StyleTextView(getContext());
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(DisplayHelper.dp2px(getContext(), 18), ViewGroup.LayoutParams.WRAP_CONTENT);
            int margin = DisplayHelper.dp2px(getContext(), 1);
            layoutParams.setMargins(margin, 0 , margin, 0);
            itemView.setLayoutParams(layoutParams);
            itemView.setIncludeFontPadding(false);
            itemView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
            itemView.setTextColor(ResourcesCompat.getColor(getContext().getResources(), R.color.theme, getContext().getTheme()));
            itemView.setGravity(Gravity.CENTER);
            itemView.setText(item);

            StyleViewAttr viewAttr = itemView.getStyleViewAttr();
            viewAttr.setAllRound(DisplayHelper.dp2px(getContext(), 2));
            viewAttr.setBgGradientColor(ResourcesCompat.getColor(getContext().getResources(), R.color.twhite, getContext().getTheme()));
            itemView.setStyleViewAttr(viewAttr);

            binding.finishDayView.addView(itemView);
        }
        return this;
    }

    public StudyPlanFinishDialog setDoQuestionCount(int count){
        binding.doQuestionView.setText(String.format("总答题数\n%s", count));
        return this;
    }

    public StudyPlanFinishDialog setStudyTime(int time){
        binding.studyTimeView.setText(String.format("视频学习时长\n%s min", time));
        return this;
    }

    @Override
    public void onClick(View v) {
        dismiss();
    }
}
