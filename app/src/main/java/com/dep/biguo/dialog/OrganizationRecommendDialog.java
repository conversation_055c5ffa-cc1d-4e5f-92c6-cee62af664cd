package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrganizationRecommendDialogBean;
import com.dep.biguo.databinding.OrganizationRecommendDialogBinding;
import com.dep.biguo.utils.image.ImageLoader;

import java.text.DecimalFormat;

public class OrganizationRecommendDialog extends Dialog {
    private OrganizationRecommendDialogBinding binding;
    private OnToAskListener onToAskListener;

    private OrganizationRecommendDialogBean data;

    public OrganizationRecommendDialog(@NonNull Context context) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.organization_recommend_dialog, null);
        binding = DataBindingUtil.bind(view);
        setContentView(binding.getRoot());

        binding.closeView.setOnClickListener(v -> cancel());

        binding.toAskView.setOnClickListener(v -> {
            dismiss();
            if(onToAskListener != null){
                onToAskListener.onToAsk(data);
            }
        });
    }

    public OrganizationRecommendDialog setData(OrganizationRecommendDialogBean data){
        this.data = data;

        ImageLoader.loadImageNoPlaceholder(binding.coverView, data.getLogo());
        binding.nameView.setText(data.getName());
        binding.descView.setText(data.getIntro());

        if (AppUtil.isEmpty(data.getDistance_text())) {
            binding.distanceView.setVisibility(View.GONE);
        } else {
            binding.distanceView.setVisibility(View.VISIBLE);
            binding.distanceView.setText(data.getDistance_text());
        }

        if (AppUtil.isEmpty(data.getSign_up_text())) {
            binding.priceView.setVisibility(View.GONE);
        } else {
            binding.priceView.setVisibility(View.VISIBLE);
            binding.priceView.setText(data.getSign_up_text());
        }

        if(AppUtil.isEmpty(data.getDistance_text()) && AppUtil.isEmpty(data.getSign_up_text())) {
            binding.distanceAndPriceLayout.setVisibility(View.INVISIBLE);
        }else {
            binding.distanceAndPriceLayout.setVisibility(View.VISIBLE);
        }

        binding.priceView.setText(data.getSign_up_text());

        return this;
    }

    @Override
    public void cancel() {
        super.cancel();
        if(onToAskListener != null){
            onToAskListener.close();
        }

    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.dp2px(getContext(), 295);
        getWindow().setAttributes(layoutParams);
    }

    public OrganizationRecommendDialog setOnToAskListener(OnToAskListener onToAskListener) {
        this.onToAskListener = onToAskListener;
        return this;
    }

    public interface OnToAskListener{
        void onToAsk(OrganizationRecommendDialogBean data);

        void close();
    }
}
