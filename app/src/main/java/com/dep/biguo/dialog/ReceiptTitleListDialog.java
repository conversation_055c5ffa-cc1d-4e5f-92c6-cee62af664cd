package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dep.biguo.R;
import com.dep.biguo.bean.ReceiptTitleBean;
import com.dep.biguo.databinding.ReceiptTitleListDialogBinding;
import com.dep.biguo.mvp.ui.adapter.ReceiptTitleListAdapter;
import com.dep.biguo.widget.ItemDecoration;

import java.util.ArrayList;
import java.util.List;

public class ReceiptTitleListDialog extends Dialog {
    private ReceiptTitleListDialogBinding binding;
    private ReceiptTitleListAdapter adapter;
    private List<ReceiptTitleBean> list;
    private OnSelectListener onSelectListener;

    public ReceiptTitleListDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.receipt_title_list_dialog, null, false);
        binding.closeView.setOnClickListener(v -> dismiss());
        setContentView(binding.getRoot());

        adapter = new ReceiptTitleListAdapter();
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            if(onSelectListener != null){
                onSelectListener.onSelect(adapter.getItem(i));
            }
            dismiss();
        });
        adapter.setOnItemChildClickListener((baseQuickAdapter, view, i) -> {
            if(onSelectListener != null){
                if(view.getId() == R.id.editView){
                    onSelectListener.onEdit(adapter.getItem(i));
                    dismiss();
                }else if(view.getId() == R.id.delView){
                    onSelectListener.onDel(adapter.getItem(i));
                }
            }
        });
        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10).setColorRes(R.color.tran));

        binding.addTitleView.setOnClickListener(v -> {
            if(onSelectListener != null){
                onSelectListener.onEdit(null);
            }
        });
    }

    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getWindowWidth(getContext());
        layoutParams.height = DisplayHelper.dp2px(getContext(), 375);
        layoutParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(layoutParams);
    }

    public ReceiptTitleListDialog setList(List<ReceiptTitleBean> list) {
        this.list = list;
        adapter.setNewData(list);
        return this;
    }

    public ReceiptTitleListDialog setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
        return this;
    }

    public void removeTitle(ReceiptTitleBean bean){
        int index = this.list.indexOf(bean);
        this.adapter.remove(index);
    }

    public interface OnSelectListener{
        void onSelect(ReceiptTitleBean select);

        void onEdit(ReceiptTitleBean select);

        void onDel(ReceiptTitleBean select);
    }

}
