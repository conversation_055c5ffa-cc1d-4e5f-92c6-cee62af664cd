package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.databinding.H5DialogBinding;
import com.dep.biguo.utils.UrlAddParamUtil;

public class H5Dialog extends Dialog {
    private H5DialogBinding binding;

    public H5Dialog(@NonNull Context context, Builder builder) {
        super(context);
        View view = LayoutInflater.from(builder.context).inflate(R.layout.h5_dialog, null);
        binding = DataBindingUtil.bind(view);
        setContentView(binding.getRoot());

        binding.titleView.setText(builder.title);

        //根据是否有禁止点击时的文案，设置按钮是否可点击
        binding.agreeView.setEnabled(TextUtils.isEmpty(builder.enableText));
        binding.agreeView.setText(binding.agreeView.isEnabled() ? builder.buttonText : builder.enableText);
        binding.agreeView.setAlpha(binding.agreeView.isEnabled() ? 1f : 0.6f);

        intiWebView(builder.buttonText);
        loadUrl(UrlAddParamUtil.addPublicParams(getContext(), builder.url, builder.isShowTitle));

        binding.closeView.setOnClickListener(v -> {
            dismiss();
            if(builder.onCloseListener != null) {
                builder.onCloseListener.onClose();
            }

        });
        binding.agreeView.setOnClickListener(v -> {
            dismiss();
            if(builder.onAgreeListener != null) {
                builder.onAgreeListener.onAgree();
            }
        });
    }

    public void intiWebView(String buttonText){
        binding.webView.setWebViewClient(new WebViewClient()); //防止外部浏览器打开
        WebSettings settings = binding.webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        binding.webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                binding.progressbar.setProgress(newProgress);
                if (newProgress == 100) {
                    binding.progressbar.setVisibility(View.GONE);
                } else {
                    binding.progressbar.setVisibility(View.VISIBLE);
                }
            }
        });
        //当初始化的设置是按钮不可点击时，由H5页面控制按钮是否可以点击
        /*binding.webView.addJavascriptInterface(new Object(){
            @JavascriptInterface
            public void scrollToBottom(){

            }
        }, "$BGapp");*/
        setAgreeStyle(buttonText);
    }

    private void loadUrl(String url){
        LogUtil.d("dddd", url);
        binding.webView.loadUrl(url);
    }

    public void setAgreeStyle(String buttonText){
        binding.agreeView.setText(buttonText);
        binding.agreeView.setAlpha(1);
        binding.agreeView.setEnabled(true);
    }

    @Override
    public void show() {
        super.show();
        Window dialogWindow = getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = (int) (DisplayHelper.getWindowWidth(getContext()) * 0.9);
        lp.height = (int) (DisplayHelper.getWindowHeight(getContext()) * 0.7);
        dialogWindow.setAttributes(lp);

        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
    }

    public static class Builder{
        private Context context;
        private String title;
        private boolean isShowTitle;
        private String url;
        private String buttonText;
        private String enableText;

        private OnAgreeListener onAgreeListener;
        private OnCloseListener onCloseListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setUrl(String url, boolean isShowTitle) {
            this.url = url;
            this.isShowTitle = isShowTitle;
            return this;
        }

        public Builder setButtonText(String buttonText) {
            this.buttonText = buttonText;
            return this;
        }

        public Builder setEnableText(String enableText) {
            this.enableText = enableText;
            return this;
        }

        public Builder setOnAgreeListener(OnAgreeListener onAgreeListener) {
            this.onAgreeListener = onAgreeListener;
            return this;
        }

        public Builder setOnCloseListener(OnCloseListener onCloseListener) {
            this.onCloseListener = onCloseListener;
            return this;
        }

        public H5Dialog build(){
            return new H5Dialog(context, this);
        }
    }

    public interface OnAgreeListener{
        void onAgree();
    }

    public interface OnCloseListener{
        void onClose();
    }
}
