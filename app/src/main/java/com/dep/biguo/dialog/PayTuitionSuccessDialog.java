package com.dep.biguo.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.PayTuitionSuccessDialogBinding;
import com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.wxapi.WxMinApplication;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.integration.AppManager;

public class PayTuitionSuccessDialog extends Dialog implements View.OnClickListener {
    private PayTuitionSuccessDialogBinding binding;
    public PayTuitionSuccessDialog(@NonNull Context context, String content) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.pay_tuition_success_dialog, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
        binding.contentView.setText(content);
    }

    @Override
    public void onClick(View view) {
        if(view == binding.closeView){
            dismiss();

        }else if(view == binding.uploadInfoView){
            UploadEnrollInfoActivity.Start( getContext());
            dismiss();
            AppManager.getAppManager().getTopActivity().finish();

        }else if(view == binding.wechatView){
            if (!AppUtil.isInstallWechat(getContext())) {
                ToastUtils.show("未安装微信");
            } else {
                WxMinApplication.StartWechat(getContext(),"pages/web/web", Constant.WX_GONG_ZHONG_HAO);
            }
        }
    }

    @Override
    public void show() {
        super.show();
        Window window = getWindow();
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.dp2px(getContext(), 295);
        window.setAttributes(layoutParams);
        window.setBackgroundDrawableResource(android.R.color.transparent);//去掉白色背景
    }
}
