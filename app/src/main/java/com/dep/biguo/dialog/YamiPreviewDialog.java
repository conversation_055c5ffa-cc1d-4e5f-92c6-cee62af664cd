package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.activity.ImageActivity;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.mvp.ui.activity.SecretActivity;
import com.dep.biguo.utils.html.HtmlUtil;
import com.dep.biguo.widget.HtmlWebView;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class YamiPreviewDialog extends Dialog implements View.OnClickListener {

    private HtmlWebView contentView;
    private ImageView closeView;

    public YamiPreviewDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.yami_preview_dialog);

        init();
    }

    public void init(){
        contentView = findViewById(R.id.contentView);
        closeView = findViewById(R.id.closeView);

        closeView.setOnClickListener(this);
    }
    @Override
    public void onClick(View view) {
        if(view == closeView) dismiss();
    }

    public YamiPreviewDialog setContent(String content){
        if (!TextUtils.isEmpty(content)){
            //加载富文本
            contentView.setHtml(content);
        }
        return this;
    }

    @Override
    public void show() {
        super.show();
        Window window = getWindow();
        window.setBackgroundDrawable(getContext().getDrawable(R.drawable.bg_round_10_white));
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = (int) (DisplayHelper.getWindowWidth(getContext()) * 0.9);
        lp.height = (int) (DisplayHelper.getWindowHeight(getContext()) * 0.6);
        window.setAttributes(lp);
        setCanceledOnTouchOutside(false);
    }
}
