package com.dep.biguo.dialog;

import android.content.Context;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.databinding.DataBindingUtil;

import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.DialogPracticeSettingBinding;
import com.dep.biguo.dialog.BottomDialog;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;
import com.dep.biguo.utils.mmkv.UserCache;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import butterknife.BindView;
import butterknife.OnClick;

public class PracticeSettingDialog extends BottomSheetDialog implements View.OnClickListener {
    private DialogPracticeSettingBinding binding;

    private PracticeManager mPracticeManager;
    private OnChangeSizeListener mOnChangeSizeListener;
    private View mCurrentSelected;


    public PracticeSettingDialog(@NonNull Context context, PracticeManager practiceManager) {
        super(context);
        this.mPracticeManager = practiceManager;
        init();
    }

    public void init() {
        View view = getLayoutInflater().inflate(R.layout.dialog_practice_setting, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(view);

        if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU){
            //模拟模式下，答题自动跳转下一题默认开启，且不能被关闭
            binding.swCorrectAuto.setChecked(true);
        }else {
            binding.swCorrectAuto.setChecked(mPracticeManager.autoNext);
        }
        boolean isSimu = mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU;
        binding.tvCorrectAuto.setText(getContext().getString(isSimu ? R.string.practice_setting_auto : R.string.practice_setting_correct_auto));
        binding.tvCorrectAuto.setAlpha(binding.swCorrectAuto.getAlpha());
        binding.swCorrectAuto.setEnabled(!isSimu);
        binding.swCorrectAuto.setThumbDrawable(getContext().getDrawable(R.drawable.switch_thumb));
        binding.swCorrectAuto.setTrackDrawable(getContext().getDrawable(R.drawable.switch_track));
        binding.swCorrectAuto.setAlpha(binding.swCorrectAuto.isEnabled() ? 1f:0.4f);
        binding.swErrorRemove.setChecked(mPracticeManager.autoRemoveError);
        binding.swErrorRemove.setThumbDrawable(getContext().getDrawable(R.drawable.switch_thumb));
        binding.swErrorRemove.setTrackDrawable(getContext().getDrawable(R.drawable.switch_track));

        binding.swCorrectAuto.setOnCheckedChangeListener((buttonView, isChecked) -> {
            mPracticeManager.autoNext = isChecked;
            UserCache.cachePracticeAuto(isChecked);
        });

        binding.swErrorRemove.setOnCheckedChangeListener((buttonView, isChecked) -> {
            mPracticeManager.autoRemoveError = isChecked;
            UserCache.cachePracticeErrorRemove(isChecked);
        });
        switch (UserCache.getSizeMode()) {
            case 0:
                binding.tvSizeNormal.setSelected(true);
                mCurrentSelected = binding.tvSizeNormal;
                break;
            case 1:
                binding.tvSizeLarge.setSelected(true);
                mCurrentSelected = binding.tvSizeLarge;
                break;
            case 2:
                binding.tvSizeSuperLarge.setSelected(true);
                mCurrentSelected = binding.tvSizeSuperLarge;
                break;
            case 3:
                binding.tvSizeMaxLarge.setSelected(true);
                mCurrentSelected = binding.tvSizeMaxLarge;
                break;
        }
    }

    @Override
    public void onClick(View view) {
        if(view == binding.tvSizeNormal){
            setSize(0);
            setCurrentSelected(view);

        }else if(view == binding.tvSizeLarge){
            setSize(1);
            setCurrentSelected(view);

        }else if(view == binding.tvSizeSuperLarge){
            setSize(2);
            setCurrentSelected(view);

        }else if(view == binding.tvSizeMaxLarge){
            setSize(3);
            setCurrentSelected(view);

        }
    }

    private void setSize(@IntRange(from = 0, to = 3) int i) {
        int sizeMode = UserCache.getSizeMode();
        if (sizeMode == i) return;
        UserCache.cacheSizeMode(i);
        if (mOnChangeSizeListener != null) {
            mOnChangeSizeListener.onChangeSize();
        }

    }

    private void setCurrentSelected(View view) {
        if (mCurrentSelected == null) return;
        mCurrentSelected.setSelected(false);
        view.setSelected(true);
        mCurrentSelected = view;
    }

    public void setOnChangeSizeListener(OnChangeSizeListener onChangeSizeListener) {
        this.mOnChangeSizeListener = onChangeSizeListener;
    }

    public interface OnChangeSizeListener {
        void onChangeSize();
    }
}
