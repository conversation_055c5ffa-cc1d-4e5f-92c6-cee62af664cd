package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

public class FeedbackInputDialog extends Dialog {
    private EditText openInputView;
    private OnSendListener onSendListener;

    public FeedbackInputDialog(Context context, OnSendListener onSendListener) {
        super(context, R.style.BottomInputDialog);
        this.onSendListener = onSendListener;
        setContentView(R.layout.feedback_input_popup_window);

        openInputView = findViewById(R.id.openInputView);
        TextView sendView = findViewById(R.id.sendView);

        sendView.setOnClickListener(v -> {
            onSendListener.onSend(openInputView.getText().toString());
            dismiss();
        });
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getRealScreenSize(getContext())[0];
        getWindow().setAttributes(layoutParams);
        openInputView.setFocusable(true);
        openInputView.requestFocus();
        new Handler().postDelayed(() -> {
            InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        },200);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        onSendListener.onChangeText(openInputView.getText().toString());

    }

    /**清空保存的输入记录
     *
     */
    public void clearInputText(){
        openInputView.setText("");
    }

    public interface OnSendListener{
        void onSend(String content);
        void onChangeText(String content);
    }
}
