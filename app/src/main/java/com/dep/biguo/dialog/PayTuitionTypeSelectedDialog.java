package com.dep.biguo.dialog;

import android.content.Context;
import androidx.databinding.DataBindingUtil;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.biguo.utils.widget.ArrowLayout;
import com.dep.biguo.R;
import com.dep.biguo.bean.PayTuitionStageBean;
import com.dep.biguo.databinding.PayTuitionTypeSelectedDialogBinding;
import com.biguo.utils.util.DisplayHelper;

import java.util.ArrayList;
import java.util.List;

public class PayTuitionTypeSelectedDialog extends PopupWindow {
    private PayTuitionTypeSelectedDialogBinding binding;

    private List<PayTuitionStageBean> list = new ArrayList<>();
    private OnCheckListener onCheckListener;

    public PayTuitionTypeSelectedDialog(Context context) {
        super(context);

        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.pay_tuition_type_selected_dialog, null, false);
        binding.arrowLayout.setArrowOffsetX(DisplayHelper.dp2px(context, 10), ArrowLayout.ARROW_LEFT);
        setContentView(binding.getRoot());
        this.setWidth(DisplayHelper.dp2px(context, 200));
        this.setBackgroundDrawable(new ColorDrawable());
        this.setOutsideTouchable(true);

        //触摸根布局就消失
        binding.getRoot().setOnClickListener(v -> dismiss());
    }

    private TextView getItemView(PayTuitionStageBean selectedBean){
        Context context = getContentView().getContext();
        TextView itemView = new TextView(context);
        itemView.setText(selectedBean.getName());
        itemView.setTextColor(context.getResources().getColor(R.color.tblack));
        itemView.setGravity(Gravity.CENTER_VERTICAL);
        itemView.setPadding(DisplayHelper.dp2px(context, 10), 0, 0, 0);
        itemView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.dp2px(context, 30)));
        itemView.setOnClickListener(v -> {
            onCheckListener.onCheck(selectedBean);
            dismiss();
        });
        return itemView;
    }

    public void setList(List<PayTuitionStageBean> list) {
        this.list.addAll(list);
        for(PayTuitionStageBean selectedBean : list) {
            binding.itemLayout.addView(getItemView(selectedBean));
        }
    }

    public void show(TextView targetView){
        int[] location = new int[2];
        //获取评论在屏幕中坐标
        targetView.getLocationOnScreen(location);
        //弹窗的箭头距离弹窗边界的外边距,touchX是一个相对questionRecyclerView的item的坐标，此处可以看做相对于屏幕
        int commentViewWidth = targetView.getWidth();
        //setArrowX(getInTheInterval(0, touchX-location[0], commentViewWidth));
        //设置弹窗的位置
        showAtLocation(targetView, Gravity.TOP|Gravity.START, location[0]-30, location[1] + targetView.getLayoutParams().height);
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }
    public void setOnCheckListener(OnCheckListener onCheckListener) {
        this.onCheckListener = onCheckListener;
    }

    public interface OnCheckListener{
        void onCheck(PayTuitionStageBean selectedBean);
    }
}
