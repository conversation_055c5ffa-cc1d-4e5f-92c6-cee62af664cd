package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;

import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dep.biguo.R;
import com.dep.biguo.bean.QuestionBankInfoBean;
import com.dep.biguo.mvp.ui.adapter.QuestionBanAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.widget.ItemDecoration;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019年11月19日10:48:39
 * @Description: 题库更新弹窗
 */
public class QuestionBankUpdateDialog extends DialogFragment {
    private static final String TAG = "QuestionBankUpdateDialog";
    private static final String LIST = "list";
    private static final String TIME = "time";

    private OnClickListener mOnClickListener;
    private OnCancelListener onCancelListener;
    private RecyclerView rvPracticeUpdate;
    private QuestionBanAdapter mQuestionBanAdapter;
    private List<QuestionBankInfoBean> list;
    private String time;

    public static QuestionBankUpdateDialog newInstance(List<QuestionBankInfoBean> list, String time) {
        QuestionBankUpdateDialog mQuestionBankUpdateDialog = new QuestionBankUpdateDialog();
        Bundle bundle = new Bundle();
        bundle.putString(LIST, GsonUtils.toJson(list));
        bundle.putString(TIME, time);
        mQuestionBankUpdateDialog.setArguments(bundle);

        return mQuestionBankUpdateDialog;
    }

    public void setOnClickListener(OnClickListener mOnClickListener) {
        this.mOnClickListener = mOnClickListener;
    }

    public void setOnCancelListener(OnCancelListener onCancelListener) {
        this.onCancelListener = onCancelListener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        list = GsonUtils.fromJson(getArguments().getString(LIST), new TypeToken<List<QuestionBankInfoBean>>(){}.getType());
        time = getArguments().getString(TIME);
        View view = inflater.inflate(R.layout.dialog_practice_update, container, false);
        rvPracticeUpdate = view.findViewById(R.id.rvPracticeUpdate);
        rvPracticeUpdate.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));
        mQuestionBanAdapter = new QuestionBanAdapter(new ArrayList<>());
        mQuestionBanAdapter.bindToRecyclerView(rvPracticeUpdate);
        mQuestionBanAdapter.setNewData(list);
        view.findViewById(R.id.ivConfirm).setOnClickListener(v -> {
            dismiss();
            if (null != mOnClickListener) {
                mOnClickListener.onClick(v);
            }
            if(onCancelListener != null) {
                onCancelListener.onCancel();
            }
        });

        return view;
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        show(manager, TAG);
    }


    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.getWindow().getAttributes().height = DisplayHelper.dp2px(getContext(), 340);
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
            /*dialog.setOnKeyListener((dialog1, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                    onCancelListener.onCancel();
                    return true; // 表示事件已被处理，不再向下传递
                }
                return false;
            });*/
        }
    }

    @Override
    public void onCancel(@NonNull DialogInterface dialog) {
        super.onCancel(dialog);
        if(onCancelListener != null) {
            onCancelListener.onCancel();
        }
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        try {
            Class<?> clazz = Class.forName("androidx.fragment.app.DialogFragment");
            Field dismissed = clazz.getDeclaredField("mDismissed");
            Field shownByMe = clazz.getDeclaredField("mShownByMe");
            dismissed.setAccessible(false);
            shownByMe.setAccessible(true);
            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            // 这里吧原来的commit()方法换成了commitAllowingStateLoss()
            ft.commitAllowingStateLoss();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";
    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    public interface OnClickListener {
        void onClick(View view);
    }

    public interface OnCancelListener {
        void onCancel();
    }
}
