package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.CircleCommentDialogBinding;
import com.dep.biguo.utils.image.TextDrawableLoader;

public class CircleCommentDialog extends Dialog {
    private CircleCommentDialogBinding binding;
    private OnSendListener onSendListener;
    private boolean isSend;

    public CircleCommentDialog(Context context) {
        super(context, R.style.BottomInputDialog);
        binding = DataBindingUtil.bind(LayoutInflater.from(context).inflate(R.layout.circle_comment_dialog, null));
        setContentView(binding.getRoot());
        getWindow().setWindowAnimations(0);

        binding.sendView.setOnClickListener(v -> {
            isSend = true;
            cancel();
        });
    }

    @Override
    public void cancel() {
        super.cancel();
        if(onSendListener != null){
            onSendListener.onSend(isSend, binding.inputView.getText().toString());
            isSend = false;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getRealScreenSize(getContext())[0];
        getWindow().setAttributes(layoutParams);
        binding.inputView.setFocusable(true);
        binding.inputView.requestFocus();
        binding.inputView.setSelection(binding.inputView.getText().length());
        new Handler().postDelayed(() -> {
            InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        },200);
    }


    public CircleCommentDialog setOnSendListener(OnSendListener onSendListener) {
        this.onSendListener = onSendListener;
        return this;
    }

    public CircleCommentDialog setComment(String comment){
        binding.inputView.setText(comment);
        return this;
    }

    public CircleCommentDialog setHint(String hint){
        binding.inputView.setHint(hint);
        return this;
    }

    public String getComment(){
        return binding.inputView.getText().toString();
    }

    public interface OnSendListener{
        void onSend(boolean isSend, String content);
    }
}