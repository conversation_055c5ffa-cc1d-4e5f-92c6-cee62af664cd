package com.dep.biguo.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.mvp.ui.activity.CKMainActivity;
import com.dep.biguo.mvp.ui.activity.JSZMainActivity;
import com.dep.biguo.mvp.ui.activity.JZSMainActivity;
import com.dep.biguo.mvp.ui.activity.KJMainActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.activity.RLZYMainActivity;
import com.dep.biguo.mvp.ui.activity.YYDJMainActivity;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import com.dep.biguo.mvp.ui.activity.KJMainActivity;

public class AppTypePopupWindow extends PopupWindow {

    private Context mContext;

    private RecyclerView rvApptype;

    private AppTypeAdapter mAdapter;


    //TODO 弹窗的前三项有功能，其余暂时屏蔽，后续再做开发
    private String[] mApptypeName = {"自考",
            "成考",
            "教师资格证"};/*,
            "会计资格证","建造师","人力资源","英语等级"};*/
    private int[] mApptypeIcon = {R.drawable.apptype_icon_zk,
            R.drawable.apptype_icon_ck,
            R.drawable.apptype_icon_jz};
    /*,
            R.drawable.apptype_icon_kj,
            R.drawable.apptype_icon_jzs,
            R.drawable.apptype_icon_rlzy,
            R.drawable.apptype_icon_yydj};*/

    public AppTypePopupWindow(Context context) {
        this.mContext = context;
        View view = LayoutInflater.from(context).inflate(R.layout.popup_app_type, null);
        rvApptype = view.findViewById(R.id.rvApptype);

        this.setContentView(view);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        //this.setHeight(DisplayHelper.getRealScreenSize(context)[1] - DisplayHelper.getStatusBarHeight(context)-DisplayHelper.dp2px(context,44));

        this.setBackgroundDrawable(new ColorDrawable());
        this.setFocusable(true);

        List<Map<String, Object>> data = new ArrayList<>();
        for (int i = 0; i < mApptypeName.length; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", mApptypeName[i]);
            map.put("icon", mApptypeIcon[i]);
            data.add(map);
        }

        mAdapter = new AppTypeAdapter(data);
        mAdapter.bindToRecyclerView(rvApptype);

        mAdapter.setOnItemClickListener((adapter, view1, position) -> {
            dismiss();
            Activity activity = AppManager.getAppManager().getCurrentActivity();
            if (activity != null) {
                switch (position) {
                    case 0:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof MainActivity)
                            return;
                        ArmsUtils.startActivity(MainActivity.class);
                        break;
                    case 1:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof CKMainActivity)
                            return;
                        ArmsUtils.startActivity(CKMainActivity.class);
                        break;
                    case 2:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof JSZMainActivity)
                            return;
                        ArmsUtils.startActivity(JSZMainActivity.class);
                        break;
                    case 3:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof KJMainActivity)
                            return;
                        ArmsUtils.startActivity(KJMainActivity.class);
                        break;
                    case 4:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof JZSMainActivity)
                            return;
                        ArmsUtils.startActivity(JZSMainActivity.class);
                        break;
                    case 5:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof RLZYMainActivity)
                            return;
                        ArmsUtils.startActivity(RLZYMainActivity.class);
                        break;
                    case 6:
                        if (AppManager.getAppManager().getCurrentActivity() instanceof YYDJMainActivity)
                            return;
                        ArmsUtils.startActivity(YYDJMainActivity.class);
                        break;
                }
                activity.finish();
            }
        });

        view.findViewById(R.id.flContent).setOnClickListener(v -> dismiss());
    }

    public class AppTypeAdapter extends CommonAdapter<Map<String, Object>> {

        public AppTypeAdapter(@Nullable List<Map<String, Object>> data) {
            super(R.layout.apptype_rv_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, Map<String, Object> item) {
            TextView tvApptype = holder.getView(R.id.tvApptype);
            tvApptype.setText((String) item.get("name"));
            Drawable topIcon = mContext.getDrawable((int) item.get("icon"));
            topIcon.setBounds(0, 0, topIcon.getMinimumWidth(), topIcon.getMinimumHeight());
            tvApptype.setCompoundDrawables(null, topIcon, null, null);
        }
    }

}
