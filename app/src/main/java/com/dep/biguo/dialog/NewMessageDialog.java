package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.databinding.MessageDialogBinding;

public class NewMessageDialog extends Dialog implements View.OnClickListener{
    private MessageDialogBinding binding;
    private Builder builder;
    public NewMessageDialog(@NonNull Context context, Builder builder) {
        super(context);
        this.builder = builder;
        setCanceledOnTouchOutside(false);
        getWindow().setBackgroundDrawableResource(R.color.tran);

        View view = LayoutInflater.from(context).inflate(R.layout.message_dialog, null);
        binding = DataBindingUtil.bind(view);
        setContentView(binding.getRoot());

        setText(binding.tvTitle, builder.title);
        setText(binding.tvContent, builder.content);
        setText(binding.tvNegative, builder.negativeText);
        setText(binding.tvPositive, builder.positiveText);

        if(builder.negativeColor > 0) {
            binding.tvNegative.setTextColor(builder.negativeColor);
        }
        if(builder.positiveColor > 0) {
            binding.tvPositive.setTextColor(builder.positiveColor);
        }

        binding.tvNegative.setOnClickListener(this);
        binding.tvPositive.setOnClickListener(this);
    }

    private void setText(TextView textView, String text){
        if(AppUtil.isEmpty(text)){
            textView.setVisibility(View.GONE);
        }else {
            textView.setVisibility(View.VISIBLE);
            textView.setText(text);
        }
    }

    @Override
    public void onClick(View view) {
        dismiss();
        if(view == binding.tvNegative){
            if(builder.onNegativeClickListener == null) return;
            builder.onNegativeClickListener.onClick(view);

        }else if(view == binding.tvPositive){
            if(builder.onPositiveClickListener == null) return;
            builder.onPositiveClickListener.onClick(view);
        }
    }

    @Override
    public void setOnCancelListener(@Nullable DialogInterface.OnCancelListener listener) {
        super.setOnCancelListener(listener);
        if(builder.onCancelListener != null){
            builder.onCancelListener.onCancel();
        }
    }

    public static class Builder{
        private Context context;
        private String title;
        private String content;
        private String negativeText;
        private String positiveText;
        private @ColorInt int negativeColor;
        private @ColorInt int positiveColor;
        private View.OnClickListener onNegativeClickListener;
        private View.OnClickListener onPositiveClickListener;
        private OnCancelListener onCancelListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContent(String content) {
            this.content = content;
            return this;
        }

        public Builder setNegativeText(String negativeText) {
            this.negativeText = negativeText;
            return this;
        }

        public Builder setPositiveText(String positiveText) {
            this.positiveText = positiveText;
            return this;
        }

        public Builder setNegativeColor(int negativeColor) {
            this.negativeColor = negativeColor;
            return this;
        }

        public Builder setPositiveColor(int positiveColor) {
            this.positiveColor = positiveColor;
            return this;
        }

        public Builder setOnNegativeClickListener(View.OnClickListener onNegativeClickListener) {
            this.onNegativeClickListener = onNegativeClickListener;
            return this;
        }

        public Builder setOnPositiveClickListener(View.OnClickListener onPositiveClickListener) {
            this.onPositiveClickListener = onPositiveClickListener;
            return this;
        }

        public Builder setOnCancelListener(OnCancelListener onCancelListener) {
            this.onCancelListener = onCancelListener;
            return this;
        }

        public NewMessageDialog build(){
            return new NewMessageDialog(context, this);
        }
    }


    public interface OnCancelListener {
        void onCancel();
    }
}
