package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.ProfessionInfo;
import com.dep.biguo.databinding.ProfessionChangeDialogBinding;

public class ProfessionChangeDialog extends Dialog implements View.OnClickListener{
    private ProfessionChangeDialogBinding binding;

    private OnClickListener onNegativeClickListener;
    private OnClickListener onPositiveClickListener;

    private ProfessionInfo.Profession server;
    private ProfessionInfo.Profession local;

    public ProfessionChangeDialog(@NonNull Context context) {
        super(context);
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.profession_change_dialog, null, false);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        setCanceledOnTouchOutside(false);
        getWindow().getAttributes().width = DisplayHelper.dp2px(getContext(), 320);
        getWindow().setBackgroundDrawableResource(R.color.tran);
    }

    @Override
    public void onClick(View view) {
        dismiss();
        if(view == binding.tvNegative){
            if(onNegativeClickListener == null) return;

            onNegativeClickListener.onClick(local);

        }else if(view == binding.tvPositive){
            if(onPositiveClickListener == null) return;

            onPositiveClickListener.onClick(server);

        }
    }

    private SpannableStringBuilder addSpan(SpannableStringBuilder spannable, String info, String str){
        try {
            int start = info.indexOf(str);
            int end = start + str.length();
            int color = ResourcesCompat.getColor(getContext().getResources(), R.color.theme, getContext().getTheme());
            return SpannableUtil.setColorString(spannable, start, end, color);
        }catch (Exception e){
            return spannable;
        }
    }

    public ProfessionChangeDialog setProfession(ProfessionInfo professionInfo){
        server = professionInfo.getServer();
        local = professionInfo.getLocal();

        String serverInfo = String.format("%s-%s-%s-%s", server.getCity_name(), server.getSchool_name(), server.getLayer_name(), server.getProfessions_name());
        String localInfo = String.format("%s-%s-%s-%s",  local.getCity_name(), local.getSchool_name(), local.getLayer_name(), local.getProfessions_name());

        SpannableStringBuilder serverSpan = new SpannableStringBuilder(serverInfo);
        SpannableStringBuilder localSpan = new SpannableStringBuilder(localInfo);

        if(server.getCity_id() != local.getCity_id()){
            serverSpan = addSpan(serverSpan, serverInfo, server.getCity_name());
            localSpan = addSpan(localSpan, localInfo, local.getCity_name());
        }

        if(server.getSchool_id() != local.getSchool_id()){
            serverSpan = addSpan(serverSpan, serverInfo, server.getSchool_name());
            localSpan = addSpan(localSpan, localInfo, local.getSchool_name());
        }

        if(server.getLayer_id() != local.getLayer_id()){
            serverSpan = addSpan(serverSpan, serverInfo, server.getLayer_name());
            localSpan = addSpan(localSpan, localInfo, local.getLayer_name());
        }

        if(server.getProfessions_id() != local.getProfessions_id()){
            serverSpan = addSpan(serverSpan, serverInfo, server.getProfessions_name());
            localSpan = addSpan(localSpan, localInfo, local.getProfessions_name());
        }

        binding.currentProfessionView.setRightText(localSpan);
        binding.lastProfessionView.setRightText(serverSpan);

        return this;
    }

    public ProfessionChangeDialog setNegativeOnClickListener(OnClickListener onClickListener){
        this.onNegativeClickListener = onClickListener;
        return this;
    }

    public ProfessionChangeDialog setPositiveOnClickListener(OnClickListener onClickListener){
        this.onPositiveClickListener = onClickListener;
        return this;
    }

    public interface OnClickListener{
        void onClick(ProfessionInfo.Profession profession);
    }
}
