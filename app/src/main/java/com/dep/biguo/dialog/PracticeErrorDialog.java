package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.Rect;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.dialog.BottomDialog;

import java.util.Arrays;
import java.util.List;

import butterknife.BindArray;
import butterknife.BindView;
import butterknife.OnClick;

public class PracticeErrorDialog extends BottomDialog {

    @BindView(R.id.rvErrorType)
    RecyclerView rvErrorType;
    @BindView(R.id.etDetail)
    EditText etDetail;
    @BindView(R.id.tvSubmit)
    TextView tvSubmit;
    @BindView(R.id.tvExample)
    TextView tvExample;
    @BindView(R.id.ivClose)
    ImageView ivClose;
    @BindArray(R.array.practice_error_type)
    String[] mTypes;

    private Context mContext;
    private int position;//记录对第几题进行纠错

    private ErrorTypeAdapter mErrorTypeAdapter;

    public PracticeErrorDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_practice_error_v2;
    }

    @Override
    public void init() {
        mErrorTypeAdapter = new ErrorTypeAdapter(Arrays.asList(mTypes));
        mErrorTypeAdapter.bindToRecyclerView(rvErrorType);
//        ArmsUtils.configRecyclerView(rvErrorType, new LinearLayoutManager(mContext));
        int dp10 = DisplayHelper.dp2px(getContext(), 10);
        rvErrorType.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                if (parent.getChildLayoutPosition(view) % 3 == 0) {
                    outRect.left = 0;
                    outRect.right = dp10;
                } else {
                    outRect.right = dp10;
                }
                outRect.bottom = dp10;
            }
        });
        mErrorTypeAdapter.setOnItemClickListener((adapter, view, position) -> {
            mErrorTypeAdapter.setSelectPosition(position);
            refreshSubmit();
        });
        ivClose.setOnClickListener(view -> dismiss());
    }

    public void setPath(String path){
        if(path.lastIndexOf("-") == path.length() - 1){
            path = path.substring(0, path.length() - 1);
        }
        tvExample.setText(path);
    }

    @OnClick(R.id.tvSubmit)
    public void onViewClicked() {
        if (mOnErrorSubmitListener != null) {
            if (mErrorTypeAdapter.getSelectPosition() == -1) return;
            mOnErrorSubmitListener.onErrorSubmit(position,mErrorTypeAdapter.getItem(mErrorTypeAdapter.getSelectPosition()), etDetail.getText().toString());
            dismiss();
        }
    }

    public void setPosition(int position) {
        this.position = position;
    }

    private class ErrorTypeAdapter extends CommonAdapter<String> {

        private int mSelectPosition = -1;

        public ErrorTypeAdapter(@Nullable List<String> data) {
            super(R.layout.dialog_practice_error_item_v2, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, String item) {
            holder.setText(R.id.tvType, item);
//            holder.setTextColor(R.id.tvType, ContextCompat.getColor(mContext, mSelectPosition == holder.getLayoutPosition() ? R.color.twhite : R.color.tblack));
            holder.setBackgroundRes(R.id.tvType, mSelectPosition == holder.getLayoutPosition() ? R.drawable.practice_round_5_bg : R.drawable.practice_option_normal);
        }

        public void setSelectPosition(int position) {
            this.mSelectPosition = position;
            notifyDataSetChanged();
        }

        public int getSelectPosition() {
            return mSelectPosition;
        }
    }

    private OnErrorSubmitListener mOnErrorSubmitListener;

    public void setOnErrorSubmitListener(OnErrorSubmitListener onErrorSubmitListener) {
        this.mOnErrorSubmitListener = onErrorSubmitListener;
    }

    public interface OnErrorSubmitListener {
        void onErrorSubmit(int requestPosition, String message, String detail);
    }

    public void clear() {
        etDetail.setText("");
        mErrorTypeAdapter.setSelectPosition(-1);
        refreshSubmit();
    }

    private void refreshSubmit() {
        tvSubmit.setBackgroundResource(mErrorTypeAdapter.getSelectPosition() != -1 ? R.drawable.bg_round_200_theme : R.drawable.bg_round_200_gray);
    }

}
