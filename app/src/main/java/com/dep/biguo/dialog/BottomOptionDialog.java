package com.dep.biguo.dialog;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/29
 * @Description:
 */
public class BottomOptionDialog extends BottomDialog {

    @BindView(R.id.rvOption)
    RecyclerView rvOption;
    @BindView(R.id.tvBottom)
    TextView tvBottom;

    private List<String> mData;
    private BottomOptionAdapter mBottomOptionAdapter;

    private OnItemClickListener onItemClickListener;

    public enum Type {
        CIRCLE_REPORT, CIRCLE_ME, COURSE_MANAGER__YBK, COURSE_MANAGER__WBK,
    }

    public BottomOptionDialog(@NonNull Context context, Type type) {
        super(context);

        mData = new ArrayList<>();
        if (type == Type.CIRCLE_REPORT) {
            mData.add("举报");
        } else if (type == Type.CIRCLE_ME) {
            mData.add("删除");
        } else if (type == Type.COURSE_MANAGER__YBK) {
            mData.add("移除报考");
            mData.add("成绩管理");
            mData.add("题库反馈");
        } else if (type == Type.COURSE_MANAGER__WBK) {
            mData.add("添加报考");
            mData.add("成绩管理");
            mData.add("题库反馈");
        }
    }

    public BottomOptionDialog(@NonNull Context context, List<String> mData) {
        super(context);
        this.mData = new ArrayList<>();
        this.mData = mData;
    }
    @Override
    public int getLayoutId() {
        return R.layout.dialog_option_layout;
    }

    @Override
    public void init() {
        mBottomOptionAdapter = new BottomOptionAdapter(mData);
        mBottomOptionAdapter.bindToRecyclerView(rvOption);
        rvOption.setLayoutManager(new LinearLayoutManager(getContext()));

        mBottomOptionAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (onItemClickListener != null)
                onItemClickListener.onItemClick(adapter, view, position);
        });

        tvBottom.setOnClickListener(v -> dismiss());
    }

    public interface OnItemClickListener {
        void onItemClick(BaseQuickAdapter adapter, View view, int position);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    private class BottomOptionAdapter extends CommonAdapter<String> {

        public BottomOptionAdapter(@Nullable List<String> data) {
            super(R.layout.dialog_option_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, String item) {
            holder.setText(R.id.tvOption, item);
        }
    }

}
