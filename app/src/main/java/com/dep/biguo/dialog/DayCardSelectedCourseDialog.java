package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.DayCardV3SignCourseBean;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.widget.ItemDecoration;

import java.util.List;

public class DayCardSelectedCourseDialog extends Dialog implements View.OnClickListener {
    private TextView titleView;//标题
    private RecyclerView courseRecyclerView;//课程列表
    private TextView finishView;//我选好了
    private TextView delayView;//稍后打卡

    private SelectedAdapter selectedAdapter;

    private Builder builder;

    public DayCardSelectedCourseDialog(@NonNull Context context, Builder builder) {
        super(context);
        this.builder = builder;
        setContentView(R.layout.day_card_selected_course_dialog);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);

        titleView = findViewById(R.id.titleView);
        courseRecyclerView = findViewById(R.id.courseRecyclerView);
        finishView = findViewById(R.id.finishView);
        delayView = findViewById(R.id.delayView);

        finishView.setOnClickListener(this);
        delayView.setOnClickListener(this);

        titleView.setText(builder.isSetting ? "请选择需要切换的科目" : "请选择科目进行打卡");
        finishView.setText(builder.isSetting ? "确认切换" : "我选好啦");
        delayView.setText(builder.isSetting ? "下次再选" : "稍后打卡");

        courseRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.line_color));


        selectedAdapter = new SelectedAdapter(builder.list);
        selectedAdapter.bindToRecyclerView(courseRecyclerView);

        //设置默认选中的课程
        for(DayCardV3SignCourseBean.SelectCourseBean courseBean : builder.list){
            if(courseBean.getCode().equals(builder.selectedCode)){
                selectedAdapter.courseBean = courseBean;
            }
        }

        //没有设置默认选中的课程，则默认选中第一个
        if(selectedAdapter.courseBean == null && !AppUtil.isEmpty(builder.list)){
            selectedAdapter.courseBean = builder.list.get(0);
        }

        if(selectedAdapter.courseBean != null) {
            courseRecyclerView.scrollToPosition(builder.list.indexOf(selectedAdapter.courseBean));
        }
    }

    @Override
    public void onClick(View view) {
        if(view == finishView){
            //当没有可以打卡的课程时，有可能为null
            if(selectedAdapter.courseBean != null) {
                builder.onSelectedListener.onSelected(selectedAdapter.courseBean.getCourses_id(), selectedAdapter.courseBean.getCode(), selectedAdapter.courseBean.getName());
            }
            dismiss();

        }else if(view == delayView){
            dismiss();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        builder.context = null;
        builder.list = null;
        builder.onSelectedListener = null;
        builder = null;
    }

    public interface OnSelectedListener{
        void onSelected(int courseId, String code, String name);
    }

    private class SelectedAdapter extends CommonAdapter<DayCardV3SignCourseBean.SelectCourseBean>{
        private DayCardV3SignCourseBean.SelectCourseBean courseBean;

        public SelectedAdapter(@Nullable List<DayCardV3SignCourseBean.SelectCourseBean> data) {
            super(R.layout.day_card_shop_item, data);
        }

        @Override
        protected BaseViewHolder createBaseViewHolder(View view) {
            TextView textView = new TextView(mContext);
            textView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.dp2px(mContext, 48)));
            textView.setTextColor(mContext.getResources().getColor(R.color.tblack));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            textView.setGravity(Gravity.CENTER_VERTICAL);
            return new BaseViewHolder(textView);
        }

        @Override
        protected void convert(BaseViewHolder holder, DayCardV3SignCourseBean.SelectCourseBean item) {
            TextView textView = (TextView) holder.itemView;
            textView.setText(item.getName());
            if(item.getCode().equals(courseBean.getCode())){
                TextDrawableLoader.loadRight(mContext, textView, R.drawable.selected);
            }else {
                TextDrawableLoader.loadRight(mContext, textView, R.drawable.un_selected);
            }

            if(!textView.hasOnClickListeners()){
                textView.setOnClickListener(v -> {
                    courseBean = getItem(holder.getAdapterPosition());
                    notifyDataSetChanged();
                });
            }
        }
    }

    public static class Builder{
        private Context context;
        private List<DayCardV3SignCourseBean.SelectCourseBean> list;
        private boolean isSetting;
        private OnSelectedListener onSelectedListener;
        private String selectedCode;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setList(List<DayCardV3SignCourseBean.SelectCourseBean> list) {
            this.list = list;
            return this;
        }

        public Builder setSetting(boolean setting) {
            isSetting = setting;
            return this;
        }

        public Builder setOnSelectedListener(OnSelectedListener onSelectedListener) {
            this.onSelectedListener = onSelectedListener;
            return this;
        }

        public Builder setSelectedCode(String selectedCode) {
            this.selectedCode = selectedCode;
            return this;
        }

        public DayCardSelectedCourseDialog build(){
            return new DayCardSelectedCourseDialog(context, this);
        }
    }

}
