package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.text.InputFilter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import com.dep.biguo.R;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.utils.mmkv.UserCache;
import com.hjq.toast.ToastUtils;

import org.jetbrains.annotations.NotNull;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/4
 * @Description: 带输入框
 */
public class InputDialog extends DialogFragment {

    private static final String TAG = "InputDialog";
    private static final String TYPE = "type";
    private static final String TEXT = "text";
    public static final int TYPE_NICKNAME = 1;
    public static final int TYPE_PRACTICE_COMMENT = 2;
    public static final int TYPE_COURSE_SOCRE = 3;
    public static final int TYPE_EMAIL = 4;
    public static final int TYPE_APP_INFO = 5;

    private EditText etInput;

    private OnInputListener onInputListener;

    private int mType = TYPE_NICKNAME;
    private String mText = "";
    private String mSaveText = "";

    public static InputDialog newInstance(int type) {
        return newInstance(type, "");
    }

    public static InputDialog newInstance(int type, String text) {
        InputDialog inputDialog = new InputDialog();
        Bundle bundle = new Bundle();
        bundle.putInt(TYPE, type);
        bundle.putString(TEXT, text);
        inputDialog.setArguments(bundle);
        return inputDialog;
    }

    public void setOnInputListener(OnInputListener onInputListener) {
        this.onInputListener = onInputListener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_input, container, false);
        etInput = view.findViewById(R.id.etInput);

        mType = getArguments().getInt(TYPE, TYPE_NICKNAME);
        mText = getArguments().getString(TEXT);

        if (mType == TYPE_NICKNAME) {
            UserBean bean = UserCache.getUserCache();
            if (bean != null) {
                String nickname = bean.getNickname();
                if (!TextUtils.isEmpty(nickname)) {
                    etInput.setText(nickname);
                    etInput.setSelection(etInput.getText().length());
                }
            }
        } else if (mType == TYPE_PRACTICE_COMMENT) {
            if (!TextUtils.isEmpty(UserCache.COMMENT_STR)) {
                etInput.setText(UserCache.COMMENT_STR);
            } else {
                etInput.setHint("我对这题的见解...");
                etInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(200)});
            }
        } else if (mType == TYPE_COURSE_SOCRE) {
            etInput.setHint("我的成绩是...");
            etInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(15)});
        } else if (mType == TYPE_EMAIL) {
            etInput.setHint("请输入电子邮箱");
            etInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(30)});
            if (!TextUtils.isEmpty(mText)) {
                etInput.setText(mText);
                etInput.setSelection(etInput.getText().length());
            }
        } else if (mType == TYPE_APP_INFO) {
            etInput.setHint("填写用户ID");
            etInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(15)});
        }

        view.findViewById(R.id.tvNegative).setOnClickListener(v -> dismiss());

        view.findViewById(R.id.tvPositive).setOnClickListener(v -> {
            String input = etInput.getText().toString();

            if (onInputListener != null) {
                if (TextUtils.isEmpty(input.trim())) {
                    ToastUtils.show("请输入内容");
                    return;
                }
                onInputListener.onInput(v, input);
                dismiss();
            }
        });

        return view;
    }

    public interface OnInputListener {
        void onInput(View v, String input);
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, TAG);
    }

    @Override
    public void onStart() {
        super.onStart();

        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        final InputMethodManager inputMethodManager = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);

        etInput.postDelayed(() -> {
            etInput.requestFocus();
            inputMethodManager.showSoftInput(etInput, 0);
        }, 300);
        dialog.setOnCancelListener(dialogInterface -> {
            UserCache.COMMENT_STR = etInput.getText().toString();
        });
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    @Override
    public void dismiss() {
        super.dismiss();
        UserCache.COMMENT_STR = null;
    }
}
