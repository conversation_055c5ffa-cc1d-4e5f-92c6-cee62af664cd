package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dep.biguo.R;
import com.dep.biguo.databinding.JoinWechatGroupDialogBinding;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.utils.mmkv.UserCache;

/**
 * 图片尽可能最大化展示
 */
public class JoinWechatGroupDialog extends Dialog implements View.OnClickListener{
    private JoinWechatGroupDialogBinding binding;

    private String url;
    private View.OnClickListener onClickAdListener;
    private View.OnClickListener onCloseAdListener;

    public JoinWechatGroupDialog(@NonNull Context context) {
        super(context);

        getWindow().setBackgroundDrawableResource(R.color.tran);
        setCanceledOnTouchOutside(false);
        binding = DataBindingUtil.bind(LayoutInflater.from(context).inflate(R.layout.join_wechat_group_dialog, null));
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());
    }

    @Override
    public void onClick(View view) {
        cancel();
        if(view == binding.imageView){
            if(onClickAdListener != null){
                onClickAdListener.onClick(view);

            }else {
                HtmlActivity.start(getContext(), url);
            }
        }else if(view == binding.closeView){
            if(onCloseAdListener != null){
                onCloseAdListener.onClick(view);
            }
        }
    }

    @Override
    public void cancel() {
        super.cancel();
        if(binding.unShowCheckView.isChecked()) {
            UserCache.cacheIsShowZkHomeJoinWechatGroupDialog();
        }
    }

    public JoinWechatGroupDialog setUrl(String url){
        this.url = url;
        return this;
    }

    public JoinWechatGroupDialog setImgUrlShow(String imageUrl){
        Glide.with(getContext())
                .load(imageUrl)
                .into(new CustomTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        //计算弹窗的最大显示宽度
                        int widthMargin = DisplayHelper.dp2px(getContext(), 40);//左右两边距离屏幕边缘的边距之和
                        int maxWidth = DisplayHelper.getWindowWidth(getContext()) - widthMargin;
                        //获取弹窗的最大显示高度
                        int heightMargin = DisplayHelper.getStatusBarHeight(getContext()) * 4;//上下两边距离屏幕边缘的边距之和，自定义数值
                        int closeViewWidth = binding.closeView.getMeasuredWidth();//关闭按钮的高度
                        int maxHeight = DisplayHelper.getWindowHeight(getContext()) - heightMargin + closeViewWidth;

                        //计算图片的宽高比
                        float resourceScale = (float) resource.getMinimumWidth() / resource.getMinimumHeight();
                        //计算图片与弹窗的宽度差,当 图片宽度<弹窗宽度,值为负数
                        int widthDisparity = resource.getMinimumWidth() - maxWidth;
                        //计算图片与弹窗的高度差,当 图片高度<弹窗高度,值为负数
                        int heightDisparity = resource.getMinimumHeight() - maxHeight;
                        //筛选图片同比缩放变化最大的是宽还是高，1宽，2高
                        int scaleDirection = widthDisparity * resourceScale > heightDisparity * resourceScale ? 1 : 2;
                        if(scaleDirection == 1){
                            //图片同比缩放的宽变化最大，则设置图片的宽为弹窗的最大值，高度同比缩小
                            resource.setBounds(0,0,maxWidth, (int) (maxWidth / resourceScale));
                        }else {
                            //图片同比缩放的高变化最大，则设置图片的高为弹窗的最大值，宽度同比缩小
                            resource.setBounds(0,0,(int) (maxHeight * resourceScale), maxHeight);
                        }
                        //控件随图片变化
                        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) binding.imageView.getLayoutParams();
                        layoutParams.width = resource.getBounds().right;
                        layoutParams.height = resource.getBounds().bottom;
                        binding.imageView.setLayoutParams(layoutParams);

                        binding.imageView.setImageDrawable(resource);
                        show();
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {

                    }
                });
        return this;
    }

    @Override
    public void show() {
        super.show();
        Window dialogWindow = getWindow();
        dialogWindow.setWindowAnimations(R.style.dialogWindowScaleSAnim);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width =  DisplayHelper.getWindowWidth(getContext());
        lp.height = DisplayHelper.getWindowHeight(getContext());
        dialogWindow.setAttributes(lp);
    }

    public JoinWechatGroupDialog setOnClickAdListener(View.OnClickListener onClickAdListener) {
        this.onClickAdListener = onClickAdListener;
        return this;
    }

    public JoinWechatGroupDialog setOnCloseAdListener(View.OnClickListener onCloseAdListener){
        this.onCloseAdListener = onCloseAdListener;
        return this;
    }
}
