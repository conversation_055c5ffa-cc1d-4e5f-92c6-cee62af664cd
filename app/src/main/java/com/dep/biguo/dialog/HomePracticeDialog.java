package com.dep.biguo.dialog;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.dialog.BottomDialog;

import java.util.List;

import butterknife.BindView;

public class HomePracticeDialog extends BottomDialog {

    @BindView(R.id.rvPracticeTime)
    RecyclerView rvPracticeTime;

    private PracticeTimeAdapter mPracticeTimeAdapter;

    private List<String> mData;

    public HomePracticeDialog(@NonNull Context context, List<String> data) {
        super(context);
        mData = data;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_home_practice;
    }

    @Override
    public void init() {
        mPracticeTimeAdapter = new PracticeTimeAdapter(mData);
        mPracticeTimeAdapter.bindToRecyclerView(rvPracticeTime);

        findViewById(R.id.tvBottom).setOnClickListener(v -> dismiss());
    }

    public class PracticeTimeAdapter extends CommonAdapter<String> {

        public PracticeTimeAdapter(@Nullable List<String> data) {
            super(R.layout.dialog_option_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, String item) {
            holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.orange));
            holder.setText(R.id.tvOption, item);
        }
    }

    public PracticeTimeAdapter getAdapter() {
        return mPracticeTimeAdapter;
    }

}
