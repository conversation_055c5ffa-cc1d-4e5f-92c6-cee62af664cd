package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.ImageLoader;
import com.hjq.toast.ToastUtils;

import org.jetbrains.annotations.NotNull;

public class VerifyCodeDialog extends DialogFragment {

    private static final String IMG = "img";
    private static final String TAG = "VerifyCodeDialog";

    private EditText etCode;
    private ImageView ivCode;

    private OnVerifyListener mOnVerifyListener;
    private String mImageUrl = "";

    public static VerifyCodeDialog newInstance(String img) {
        VerifyCodeDialog dialog = new VerifyCodeDialog();
        Bundle bundle = new Bundle();
        bundle.putString(IMG, img);
        dialog.setArguments(bundle);
        return dialog;
    }

    public void setOnVerifyListener(OnVerifyListener onVerifyListener) {
        this.mOnVerifyListener = onVerifyListener;
    }

    public interface OnVerifyListener {
        void onVerify(String code);

        void onChangeImage();
    }

    public void setCodeImage(String image) {
        this.mImageUrl = image;
        loadImage(image);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_verify_code, container, false);

        etCode = view.findViewById(R.id.etCode);
        ivCode = view.findViewById(R.id.ivCode);

        mImageUrl = getArguments().getString(IMG);

        loadImage(mImageUrl);

        view.findViewById(R.id.tvPositive).setOnClickListener(v -> {
            String code = etCode.getText().toString();
            if (TextUtils.isEmpty(code)) {
                ToastUtils.show("请填写图形验证码");
                return;
            }

            if (mOnVerifyListener != null)
                mOnVerifyListener.onVerify(code);

            dismiss();
        });

        ivCode.setOnClickListener(v -> {
            if (mOnVerifyListener != null)
                mOnVerifyListener.onChangeImage();
        });

        view.findViewById(R.id.tvNegative).setOnClickListener(v -> dismiss());

        return view;
    }

    private void loadImage(String image) {
        if (ivCode == null) return;
        ImageLoader.loadImage(ivCode, image);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        dialog.setCanceledOnTouchOutside(false);
        if (dialog != null) {
            Window dialogWindow = dialog.getWindow();
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = (int) (DisplayHelper.getWindowWidth(dialog.getContext()) * 0.8);
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
//            lp.height = (int) (DisplayHelper.getScreenHeight(dialog.getContext()) * 0.7);
            dialogWindow.setAttributes(lp);

            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

            final InputMethodManager inputMethodManager = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);

            etCode.postDelayed(() -> {
                etCode.requestFocus();
                inputMethodManager.showSoftInput(etCode, 0);
            }, 300);
        }
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, TAG);
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

}
