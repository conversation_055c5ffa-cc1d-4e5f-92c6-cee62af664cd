package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.CardBean;
import com.dep.biguo.databinding.DialogPracticeCardBinding;
import com.dep.biguo.mvp.ui.adapter.practice.PracticeCardAdapter;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.google.android.flexbox.AlignItems;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import qdx.stickyheaderdecoration.NormalDecoration;

public class PracticeCardDialog extends Dialog implements View.OnClickListener {
    private DialogPracticeCardBinding binding;

    private PracticeCardAdapter cardAdapter;
    private PracticeManager mPracticeManager;
    private List<CardBean.Topic> topicList;
    private List<CardBean.Topic> mGroupTopicList;
    private HashMap<Integer, CardBean.TopicHead> typeMappingCountMap;

    private OnOperateListener mOnOperateListener;

    public PracticeCardDialog(@NonNull Context context, List<CardBean.Topic> topicList, PracticeManager mPracticeManager) {
        super(context);
        this.mPracticeManager = mPracticeManager;
        this.topicList = topicList;
        this.typeMappingCountMap = new HashMap<>();

        View view = LayoutInflater.from(context).inflate(R.layout.dialog_practice_card, null);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        this.mGroupTopicList = new ArrayList<>();
        group();
        init();
    }

    public void init() {
        cardAdapter = new PracticeCardAdapter(mGroupTopicList);
        cardAdapter.setOnItemClickListener(position -> {
            mOnOperateListener.onItemClick(position);
            dismiss();
        });
        binding.recyclerView.setAdapter(cardAdapter);

        //标签列表添加流布局管理器
        FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(getContext());
        layoutManager.setFlexDirection(FlexDirection.ROW);//设置主轴排列方式
        layoutManager.setFlexWrap(FlexWrap.WRAP);//设置是否换行
        layoutManager.setAlignItems(AlignItems.STRETCH);
        binding.recyclerView.setLayoutManager(layoutManager);

        //添加粘性头部
        NormalDecoration normalDecoration = new NormalDecoration() {
            @Override
            public String getHeaderName(int i) {
                String headerName = mGroupTopicList.get(i).getTopic_type_name();
                int count = typeMappingCountMap.get(mGroupTopicList.get(i).getTopic_type()).getCount();
                return String.format("%s(%s题)", headerName, count);
            }
        };
        normalDecoration.setOnDecorationHeadDraw(i -> {
            int dp10 = DisplayHelper.dp2px(getContext(), 10);
            int dp4 = DisplayHelper.dp2px(getContext(), 4);
            TextView textView = new TextView(getContext());
            textView.setTextColor(getContext().getResources().getColor(R.color.tblack2));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            textView.setPadding(dp10, dp4, dp10, dp4);
            textView.setBackgroundColor(ResourcesCompat.getColor(getContext().getResources(), R.color.bgc, getContext().getTheme()));
            textView.setLayoutParams(new ViewGroup.LayoutParams(DisplayHelper.getWindowWidth(getContext()), ViewGroup.LayoutParams.WRAP_CONTENT));

            textView.setText(normalDecoration.getHeaderName(i));
            return textView;
        });
        binding.recyclerView.addItemDecoration(normalDecoration);

        int drawableTint = ResourcesCompat.getColor(getContext().getResources(), R.color.tblack, getContext().getTheme());
        TextDrawableLoader.loadLeft(getContext(), binding.currentTotalView, R.drawable.practice_icon_card, drawableTint);
        TextDrawableLoader.loadLeft(getContext(), binding.commitView, R.drawable.practice_icon_hand, drawableTint);
    }

    public void setEnableCommit(int visibility){
        binding.commitView.setVisibility(visibility);
    }

    public void group(){
        typeMappingCountMap.clear();
        List<CardBean.Topic> data = new ArrayList<>();
        CardBean.TopicHead topicHead = null;

        for(CardBean.Topic topic : topicList){
            int key = topic.getTopic_type();
            if(topicHead == null || topic.getTopic_type() != topicHead.getTopic_type()){
                topicHead = new CardBean.TopicHead();
                topicHead.setTopic_type(topic.getTopic_type());
                topicHead.setTopic_type_name(topic.getTopic_type_name());
                typeMappingCountMap.put(key, topicHead);
                data.add(topicHead);
            }

            topicHead.setCount(topicHead.getCount() + 1);

            data.add(topic);
        }
        mGroupTopicList.clear();
        mGroupTopicList.addAll(data);
    }

    public void show(int position) {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getWindowWidth(getContext());
        layoutParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(layoutParams);

        boolean isShowAnswer = mPracticeManager.mPracticeMode == PracticeHelper.MODE_DEFAULT || mPracticeManager.mPracticeMode == PracticeHelper.MODE_ALL;

        binding.currentTotalView.setText(String.format("%s/%s", mPracticeManager.mCurrentPosition + 1, mPracticeManager.mTotalCount));
        //答题模式或解析模式，答题卡可以展示对或错
        binding.correctCountView.setVisibility(isShowAnswer ? View.VISIBLE : View.GONE);
        binding.errorCountView.setVisibility(isShowAnswer ? View.VISIBLE : View.GONE);
        binding.doCountView.setVisibility(isShowAnswer || mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU ? View.VISIBLE : View.GONE);
        if(isShowAnswer) {
            binding.correctCountView.setText(String.format("%s", mPracticeManager.mCorrectCount));
            binding.errorCountView.setText(String.format("%s", mPracticeManager.mErrorCount));
        }
        binding.doCountView.setText(String.format("%s", mPracticeManager.mDoCount));
        //是否显示交卷按钮
        binding.commitView.setVisibility(PracticeHelper.isSupportCommit(mPracticeManager.mPracticeMode, mPracticeManager.mPracticeType, mPracticeManager.mTopicType) ? View.VISIBLE : View.GONE);
        //答题模式或解析模式，答题卡可以展示对或错
        cardAdapter.setShowAnswer(isShowAnswer);

        //做题模式才显示同步答题记录的功能
        binding.clearLayout.setVisibility(mPracticeManager.isDefaultMode()
                ? View.VISIBLE
                : View.GONE);

        notifyDataSetChanged();

        if(!AppUtil.isEmpty(topicList)) {
            //找到要滚动的目标位置
            int scrollToPosition = mGroupTopicList.indexOf(topicList.get(position));
            //标记目标位置
            cardAdapter.setCurrentPosition(scrollToPosition);
        }
    }

    @Override
    public void onClick(View view){
        if(view == binding.synchronizeView){
            /*if(mPracticeManager.mMainType == PracticeHelper.PRACTICE_ERROR){
                mOnOperateListener.onClearErrorQuestion();
            }else {
                mOnOperateListener.onSynchronize();
            }*/
            mOnOperateListener.onSynchronize();

        }else if(view == binding.commitView){
            mOnOperateListener.onCommit();
        }
    }

    public void notifyDataSetChanged(){
        cardAdapter.notifyDataSetChanged();
    }

    public void setOnCommitListener(OnOperateListener onCommitListener) {
        this.mOnOperateListener = onCommitListener;
    }

    public interface OnOperateListener {
        void onItemClick(int position);

        void onCommit();

        void onSynchronize();

        void onClearErrorQuestion();
    }
}
