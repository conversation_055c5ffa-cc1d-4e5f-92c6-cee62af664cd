package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.dep.biguo.R;
import com.biguo.utils.util.DisplayHelper;

import java.util.HashMap;
import java.util.Map;

public class PracticeInputDialog extends Dialog {
    private EditText inputView;
    private TextView inputCountView;
    private TextView targetView;
    private int exams_id;//题目的ID
    private int parent_id;//回复目标的ID，“我要解析”传递0
    private int commentPosition;//评论或回复的下标

    private Map<String,String> historyInputTextMap = new HashMap<>();

    public PracticeInputDialog(Context context, OnSendListener onSendListener) {
        super(context,R.style.BottomInputDialog);
        setContentView(R.layout.bottom_input_popup_window);

        inputView = findViewById(R.id.inputView);
        inputCountView = findViewById(R.id.inputCountView);
        TextView sendView = findViewById(R.id.sendView);
        inputView.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                inputCountView.setText(String.format("%s/200",inputView.length()));
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        sendView.setOnClickListener(v -> {
            onSendListener.onSend(inputView.getText().toString(), commentPosition, exams_id, parent_id);
            dismiss();
        });
    }

    @Override
    public void show() {
        super.show();
        String historyInputText = historyInputTextMap.get(parent_id+"");
        inputView.setText(TextUtils.isEmpty(historyInputText) ? "" : historyInputText);
        inputView.setSelection(inputView.length());

        getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getRealScreenSize(getContext())[0];
        getWindow().setAttributes(layoutParams);
        inputView.setFocusable(true);
        inputView.requestFocus();
        new Handler().postDelayed(() -> {
            InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
        },200);
    }

    /**将评论或回复控件的背景颜色改变，作为选中的颜色
     *
     */
    public void setTargetView(TextView targetView) {
        if(targetView == null) return;

        Drawable drawable = getContext().getResources().getDrawable(R.color.theme);
        drawable.setBounds(0,0, targetView.getWidth(), targetView.getMeasuredHeight());
        drawable.setAlpha(40);
        targetView.setBackground(drawable);
        this.targetView = targetView;
    }

    /**清空保存的输入记录
     *
     */
    public void clearHistoryInputText(){
        historyInputTextMap.clear();
    }

    //根据回复的目标的ID移除指定的记录
    public void removeHistoryInputText(int parent_id){
        historyInputTextMap.remove(parent_id+"");
    }

    public void setExams_id(int exams_id) {
        this.exams_id = exams_id;
    }

    public void setParent_id(int parent_id) {
        this.parent_id = parent_id;
    }

    public void setCommentPosition(int commentPosition) {
        this.commentPosition = commentPosition;
    }

    @Override
    public void dismiss() {
        super.dismiss();
        historyInputTextMap.put(parent_id+"", inputView.getText().toString());
        if(targetView != null) {
            targetView.setBackground(null);
            targetView = null;
        }
    }

    public void setHint(String hint){
        //当输入的提示不与上一次相等时，将已编辑的评论删除
        if(!hint.equals(inputView.getHint().toString())){
            inputView.setText("");
        }
        inputView.setHint(hint);
    }

    public interface OnSendListener{
        void onSend(String content, int commentPosition, int exams_id, int parent_id);
    }
}
