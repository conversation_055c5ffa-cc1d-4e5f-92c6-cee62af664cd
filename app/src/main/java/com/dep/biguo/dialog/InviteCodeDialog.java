package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.databinding.ExchangeCodeDialogBinding;
import com.dep.biguo.databinding.InviteCodeDialogBinding;

public class InviteCodeDialog extends Dialog implements View.OnClickListener{
    private InviteCodeDialogBinding binding;
    private OnInviteListener onExchangeListener;

    public InviteCodeDialog(@NonNull Context context) {
        super(context);
        setContentView(R.layout.invite_code_dialog);
        binding = DataBindingUtil.bind(findViewById(R.id.rootView));
        binding.setOnClickListener(this);
        setContentView(binding.getRoot());

        //监听软键盘的“完成”按钮，相当于点击了兑换按钮
        binding.inputCodeView.setOnEditorActionListener((v, actionId, event) -> {
            if(actionId == EditorInfo.IME_ACTION_DONE){
                onClick(binding.confirmView);
            }
            return false;
        });
    }

    @Override
    public void onClick(View view) {
        dismiss();
        if(view == binding.confirmView){
            if(onExchangeListener != null){
                onExchangeListener.onInvite(binding.inputCodeView.getText().toString());
            }
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.dp2px(getContext(), 295);
        getWindow().setAttributes(layoutParams);
    }

    public InviteCodeDialog setOnExchangeListener(OnInviteListener onExchangeListener) {
        this.onExchangeListener = onExchangeListener;
        return this;
    }

    public interface OnInviteListener{
        void onInvite(String code);
    }
}
