package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderPayDetailBean;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.pay.PayUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class GroupAgainPayDialog extends DialogFragment {
    public static final String DIALOG_TAG = "GroupAgainPayDialog";

    private PayAdapter mPayAdapter;
    private RightAdapter mRightAdapter;
    private RecyclerView rvRights;
    private RecyclerView rvPay;
    private ImageView ivClose;
    private TextView tvTitle;
    private TextView tvClose;
    private TextView tvNowBuy;
    private Builder.Params P;

    private PayBean selectBean;
    private boolean isToBuy;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_group_again_pay, container, false);
        rvRights = view.findViewById(R.id.rvRights);
        rvPay = view.findViewById(R.id.rvPay);
        tvClose = view.findViewById(R.id.tvClose);
        ivClose = view.findViewById(R.id.ivClose);
        tvTitle = view.findViewById(R.id.tvTitle);
        tvNowBuy = view.findViewById(R.id.tvNowBuy);
        tvClose.setOnClickListener(v -> {
            dismiss();
            P.onCancelBuyListener.onCancelBuy();
        });
        ivClose.setOnClickListener(v -> {
            dismiss();
            P.onCancelBuyListener.onCancelBuy();
        });
        tvNowBuy.setOnClickListener(v -> {
            if (AppUtil.isEmpty(mPayAdapter.getData())) return;
            if (P.mOnPayListener == null) return;
            selectBean = mPayAdapter.getItem(mPayAdapter.getSelectPosition());
            if (selectBean.getType() == 0)
                P.mOnPayListener.onPay(PayUtils.PAY_TYPE_ALIPAY);
            else if (selectBean.getType() == 1)
                P.mOnPayListener.onPay(PayUtils.PAY_TYPE_WEXIN);
            else if (selectBean.getType() == 2)
                P.mOnPayListener.onPay(PayUtils.PAY_TYPE_COIN);
            else if (selectBean.getType() == 3)
                P.mOnPayListener.onPay(PayUtils.PAY_TYPE_INTEGRAL);

            isToBuy = true;
            dismiss();
        });
        tvNowBuy.setText(P.mBuyText);
        init();
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, 0);
    }

    public void setParams(Builder.Params P) {
        this.P = P;
    }

    private void init() {
        List<PayBean> data = new ArrayList<>();
        mPayAdapter = new PayAdapter(new ArrayList<>());
        mRightAdapter = new RightAdapter(new ArrayList<>());
        if (P.mShowAlipay)
            data.add(new PayBean(0, R.drawable.pay_icon_alipay, "支付宝", ""));
        if (P.mShowWechat)
            data.add(new PayBean(1, R.drawable.pay_icon_wechat, "微信支付", ""));
        if (P.mShowGuobi)
            data.add(new PayBean(2, R.drawable.pay_icon_guobi, "果币支付", ""));
        if (P.mShowPoint)
            data.add(new PayBean(3, R.drawable.pay_icon_point, "积分支付", "(1果币=10积分)"));
        selectBean = data.get(0);
        tvTitle.setText(String.format(getString(R.string.group_again_pay_dialog_title), P.mTitle));
        mPayAdapter.setNewData(data);
        DividerItemDecoration dec = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL);
        dec.setDrawable(getResources().getDrawable(R.drawable.province_v2_divider));
        rvPay.addItemDecoration(dec);
        mPayAdapter.bindToRecyclerView(rvPay);
        mPayAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mPayAdapter.setSelectPosition(position);
            }
        });
        mRightAdapter.setNewData(P.mBeans);
        mRightAdapter.bindToRecyclerView(rvRights);
        rvRights.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                int dp11 = DisplayHelper.dp2px(getContext(), 11);
                if (parent.getChildLayoutPosition(view) % 2 == 0) {
                    outRect.right = dp11;
                } else {
                    outRect.left = dp11;
                }
                if (parent.getChildLayoutPosition(view) > 1) {
                    outRect.top = DisplayHelper.dp2px(getContext(), 20);
                }

            }
        });
    }

    @Override
    public void dismiss() {
        super.dismiss();
        new UmengEventUtils(getContext())
                .addParams("pay_type", selectBean.getName())
                .addParams("is_to_buy", isToBuy ? "是" : "否")
                .pushEvent(UmengEventUtils.GOODS_AGAIN_PAY_DIALOG_DISMISS);

    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window dialogWindow = dialog.getWindow();
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = (int) (DisplayHelper.getWindowWidth(dialog.getContext()) * 0.9);
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            dialogWindow.setAttributes(lp);
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOnKeyListener((dialog1, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    getActivity().finish();
                    return true;
                }
                return false;
            });
        }
    }

    public GroupAgainPayDialog addOnDismiss(DialogInterface listener) {
        onDismiss(listener);
        return this;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        super.show(manager, DIALOG_TAG);

    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }

    public static class Builder {
        private Params P;

        public Builder(Context context) {
            P = new Params(context);
        }

        public Builder setPrice(String price) {
            P.mPrice = price;
            return this;
        }

        public Builder setTitle(String title) {
            P.mTitle = title;
            return this;
        }

        public Builder setBuyText(String buyText) {
            P.mBuyText = buyText;
            return this;
        }

        public Builder setBeans(List<OrderPayDetailBean.Bposter> beans) {
            P.mBeans = beans;
            return this;
        }

        public Builder showAlipay(boolean show) {
            P.mShowAlipay = show;
            return this;
        }

        public Builder showWechat(boolean show) {
            P.mShowWechat = show;
            return this;
        }

        public Builder showGuobi(boolean show) {
            P.mShowGuobi = show;
            return this;
        }

        public Builder showPoint(boolean show) {
            P.mShowPoint = show;
            return this;
        }


        public Builder setOnBuyListener(PayDialog.OnPayListener mOnPayListener) {
            P.mOnPayListener = mOnPayListener;
            return this;
        }

        public Builder setOnCancelBuyListener(OnCancelBuyListener onCancelBuyListener) {
            P.onCancelBuyListener = onCancelBuyListener;
            return this;
        }

        public GroupAgainPayDialog builder() {
            GroupAgainPayDialog dialog = new GroupAgainPayDialog();
            dialog.setParams(P);
            P.mPayDialog = dialog;
            return dialog;
        }

        private static class Params {
            private Context mContext;
            private boolean mShowAlipay = true;
            private boolean mShowWechat = true;
            private boolean mShowGuobi = true;
            private boolean mShowPoint=true;
            private String mPrice;
            private List<OrderPayDetailBean.Bposter> mBeans;
            private String mTitle;
            private String mBuyText;
            private PayDialog.OnPayListener mOnPayListener;
            private OnCancelBuyListener onCancelBuyListener;
            private GroupAgainPayDialog mPayDialog;

            public Params(Context context) {
                this.mContext = context;
            }
        }

    }

    public class PayBean {
        private int type;
        private int icon;
        private String name;
        private String detail;

        public PayBean(int type, int icon, String name, String detail) {
            this.type = type;
            this.icon = icon;
            this.name = name;
            this.detail = detail;
        }

        public int getType() {
            return type;
        }

        public int getIcon() {
            return icon;
        }

        public String getName() {
            return name;
        }

        public String getDetail() {
            return detail;
        }
    }


    class PayAdapter extends CommonAdapter<PayBean> {

        private int selectPosition = 0;

        public PayAdapter(@Nullable List<PayBean> data) {
            super(R.layout.dialog_pay_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, PayBean item) {
            holder.setImageResource(R.id.ivIcon, item.getIcon());
            holder.setText(R.id.tvName, item.getName());

            holder.setGone(R.id.tvDetail, !TextUtils.isEmpty(item.getDetail()));
            holder.setText(R.id.tvDetail, item.getDetail());

            holder.setImageResource(R.id.ivSelect, selectPosition == holder.getLayoutPosition() ? R.drawable.pay_icon_s : R.drawable.pay_icon_n);
        }

        private void setSelectPosition(int position) {
            this.selectPosition = position;
            notifyDataSetChanged();
        }

        private int getSelectPosition() {
            return selectPosition;
        }
    }

    class RightAdapter extends CommonAdapter<OrderPayDetailBean.Bposter> {


        public RightAdapter(@Nullable List<OrderPayDetailBean.Bposter> data) {
            super(R.layout.item_rv_group_right, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, OrderPayDetailBean.Bposter item) {
            ImageView imageView = holder.getView(R.id.ivImage);
            ImageLoader.loadImageNoPlaceholder(imageView, item.getImg());
            holder.setText(R.id.tvTitle, item.getTitle());

        }

    }

    public interface OnCancelBuyListener{
        void onCancelBuy();
    }
}
