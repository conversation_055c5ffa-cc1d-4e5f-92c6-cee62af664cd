package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.IntegralExchangeListBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.mvp.ui.activity.DayCardV3Activity;
import com.dep.biguo.mvp.ui.activity.TaskActivity;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.widget.DiversificationTextView;
import com.dep.biguo.widget.ItemDecoration;
import com.jess.arms.utils.ArmsUtils;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**继承BottomDialog类，当recyclerView快速滚动到顶部或底部，导致item点击事件需要点击两次才能生效
 * 但目前方案依然有问题，用列表保存适配器的holder，可以得知每次响应点击事件后，将会创建新的holder*/
public class IntegralExchangeDialog extends Dialog {
    @BindView(R.id.closeView) ImageView closeView;
    @BindView(R.id.thumbnailView) ImageView thumbnailView;
    @BindView(R.id.titleView) TextView titleView;
    @BindView(R.id.subtitleView) TextView subtitleView;
    @BindView(R.id.priceView) DiversificationTextView priceView;
    @BindView(R.id.recyclerView) RecyclerView recyclerView;
    @BindView(R.id.exchangeView) TextView exchangeView;

    private Builder builder;
    private Adapter courseAdapter;
    private IntegralExchangeListBean checkedCourseBean;

    public IntegralExchangeDialog(@NonNull Context context, Builder builder) {
        super(context, R.style.BottomDialog);
        this.builder = builder;
        setContentView(R.layout.integral_exchange_dialog);
        ButterKnife.bind(this);
        init();
        Window window = getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.getAttributes().width = DisplayHelper.getWindowWidth(getContext());

    }

    public void init() {
        ImageLoader.loadImageNoPlaceholder(thumbnailView, builder.thumbnail);
        titleView.setText(builder.title);
        subtitleView.setText(builder.subtitle);
        priceView.setText(String.format("%s 积分", builder.exchange_price));

        courseAdapter = new Adapter(builder.list);
        courseAdapter.setOnItemClickListener((adapter, view, position) -> {
            if(checkedCourseBean != builder.list.get(position)) {
                checkedCourseBean = builder.list.get(position);
            }else {
                checkedCourseBean = null;
            }
            setExchangeViewStyle();
        });
        courseAdapter.bindToRecyclerView(recyclerView);
        recyclerView.getItemAnimator().setChangeDuration(0);
        recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(5));
        //初始化兑换按钮
        setExchangeViewStyle();
    }

    public void setExchangeViewStyle(){
        if(builder.hasExchangeNumber){
            exchangeView.setText("立即打卡");
            exchangeView.setBackgroundResource(R.drawable.bg_round_200_theme);
            courseAdapter.notifyItemRangeChanged(0, builder.list.size());

        }else if(builder.isIntegralEnough){
            exchangeView.setText("获取积分");
            exchangeView.setBackgroundResource(R.drawable.bg_round_200_theme);
            courseAdapter.notifyItemRangeChanged(0, builder.list.size());

        }else {
            exchangeView.setText("确定兑换");
            exchangeView.setBackgroundResource(checkedCourseBean != null ? R.drawable.bg_round_200_theme : R.drawable.bg_round_200_un_click);
            courseAdapter.notifyItemRangeChanged(0, builder.list.size());
        }
    }

    @OnClick({R.id.closeView, R.id.exchangeView})
    public void onViewClicked(View view) {
        if(view == closeView){
            dismiss();

        }else if(view == exchangeView){
            if(builder.hasExchangeNumber){
                ArmsUtils.startActivity(DayCardV3Activity.class);

            }else if(builder.isIntegralEnough){
                ArmsUtils.startActivity(TaskActivity.class);

            }else if(checkedCourseBean != null){
                builder.onExchangeListener.onExchange(checkedCourseBean.getCode());
                dismiss();
            }
        }
    }

    public class Adapter extends CommonAdapter<IntegralExchangeListBean>{
        public Adapter(@Nullable List<IntegralExchangeListBean> data) {
            super(R.layout.integral_exchange_item, data);
        }

        @Override
        protected void convert(BaseViewHolder holder, IntegralExchangeListBean item) {
            TextView courseNameView = holder.getView(R.id.courseNameView);
            courseNameView.setText(String.format("【%s】%s%s", builder.title, item.getCode(), item.getName()));
            if(checkedCourseBean == item){
                courseNameView.setBackgroundResource(R.drawable.border_round_200_theme);
            }else {
                courseNameView.setBackgroundResource(R.drawable.bg_round_200_bgc);
            }
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        builder.context = null;
        builder.onExchangeListener = null;
        builder = null;
    }

    public static class Builder{
        private Context context;
        private String title;
        private String subtitle;
        private String goodsType;
        private String exchange_price;
        private String thumbnail;
        private boolean hasExchangeNumber;//是否有兑换次数
        private boolean isIntegralEnough;//积分是否充足
        private List<IntegralExchangeListBean> list;
        private OnExchangeListener onExchangeListener;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setSubtitle(String subtitle) {
            this.subtitle = subtitle;
            return this;
        }

        public Builder setGoodsType(String goodsType) {
            this.goodsType = goodsType;
            return this;
        }

        public Builder setExchange_price(String exchange_price) {
            this.exchange_price = exchange_price;
            return this;
        }

        public Builder setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
            return this;
        }


        public Builder setList(List<IntegralExchangeListBean> list) {
            this.list = list;
            return this;
        }

        public Builder setHasExchangeNumber(boolean hasExchangeNumber) {
            this.hasExchangeNumber = hasExchangeNumber;
            return this;
        }

        public Builder setIntegralEnough(boolean integralEnough) {
            isIntegralEnough = integralEnough;
            return this;
        }

        public Builder setOnExchangeListener(OnExchangeListener onExchangeListener) {
            this.onExchangeListener = onExchangeListener;
            return this;
        }

        public IntegralExchangeDialog build(){
            return new IntegralExchangeDialog(context, this);
        }
    }

    public interface OnExchangeListener{
        void onExchange(String code);
    }
}
