package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import androidx.annotation.NonNull;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.biguo.utils.util.DisplayHelper;
import com.google.android.material.bottomsheet.BottomSheetDialog;

public class PrivateDialog extends Dialog implements View.OnClickListener{
    private TextView outView;
    private TextView agreeView;
    private TextView titleView;
    private TextView contentView;

    public PrivateDialog(@NonNull Context context) {
        super(context);

        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        setOnKeyListener((dialog1, keyCode, event) -> {
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                System.exit(0);
                return true;
            }
            return false;
        });

        setContentView(R.layout.private_agreement_dialog);
        onCreateView();
    }

    public void onCreateView() {
        outView = findViewById(R.id.outView);
        agreeView = findViewById(R.id.agreeView);
        titleView = findViewById(R.id.titleView);
        contentView = findViewById(R.id.contentView);

        outView.setOnClickListener(this);
        agreeView.setOnClickListener(this);

        setPrivateAgreementText();
    }

    public void setTitle(String title){
        titleView.setText(title);
    }


    public void setPrivateAgreementText(){

        String text = getContext().getResources().getString(R.string.private_agreement);
        SpannableString spannableString = new SpannableString(text);

        //添加用户协议事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(getContext(), Constant.AGREEMENT_USER);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                //去掉链接的下划线
                ds.setUnderlineText(false);
                ds.setColor(getContext().getResources().getColor(R.color.auxiliary_color4));

            }
        }, text.indexOf("《"), text.indexOf("》")+1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加隐私政策事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(getContext(), Constant.AGREEMENT_USER4);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                //去掉链接的下划线
                ds.setUnderlineText(false);
                ds.setColor(getContext().getResources().getColor(R.color.auxiliary_color4));

            }
        }, text.lastIndexOf("《"), text.lastIndexOf("》")+1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        //设置点击后的颜色为透明，否则会一直出现高亮
        contentView.setHighlightColor(Color.TRANSPARENT);
        //必须设置，否则无法点击
        contentView.setMovementMethod(LinkMovementMethod.getInstance());
        contentView.setText(spannableString);
    }

    @Override
    public void onClick(View view) {
        if(view == outView){
            System.exit(0);
        }else if(view == agreeView){
            dismiss();
            mOnAgreementListener.onAgreement();
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getWindowWidth(getContext());
        layoutParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(layoutParams);
    }

    private OnAgreementListener mOnAgreementListener;

    public void setOnAgreementListener(OnAgreementListener onAgreementListener) {
        this.mOnAgreementListener = onAgreementListener;
    }

    public interface OnAgreementListener {
        void onAgreement();
    }
}
