package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.adapter.GroupRuleAdapter;
import com.dep.biguo.widget.ItemDecoration;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class GroupRuleDialog extends DialogFragment {
    public static final String DIALOG_TAG = "GroupRuleDialog";
    private RecyclerView recyclerView;
    private TextView tvClose;

    private List<String> mRules;

    private static GroupRuleDialog dialog;

    public static GroupRuleDialog getInstance(){
        if(dialog==null) {
            dialog = new GroupRuleDialog();
        }
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_group_rule, container, false);
        recyclerView = view.findViewById(R.id.recyclerView);
        tvClose = view.findViewById(R.id.tvClose);

        recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.tran).setSize(15));
        GroupRuleAdapter groupRuleAdapter = new GroupRuleAdapter(mRules);
        groupRuleAdapter.bindToRecyclerView(recyclerView);
        tvClose.setOnClickListener(v -> dismiss());
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, 0);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window dialogWindow = dialog.getWindow();
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            dialogWindow.setAttributes(lp);
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOnKeyListener((dialog1, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    getActivity().finish();
                    return true;
                }
                return false;
            });
        }
    }

    public void show(FragmentManager manager) {
        if (!isAdded()) {
            super.show(manager, DIALOG_TAG);
        }

    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }


    public void setRules(List<String> strings) {
        if (AppUtil.isEmpty(strings)) return;
        if(null == mRules) {
            mRules = new ArrayList<>();
            mRules.addAll(strings);
        } else {
            mRules.clear();
            mRules.addAll(strings);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (!AppUtil.isEmpty(mRules)) {
            mRules.clear();
            mRules = null;
        }
        dialog=null;
    }
}
