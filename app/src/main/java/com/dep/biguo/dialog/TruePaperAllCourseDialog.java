package com.dep.biguo.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.TruePaperAllBean;
import com.dep.biguo.databinding.TruePaperAllCourseDialogBinding;
import com.dep.biguo.mvp.ui.adapter.TruePaperAllCourseAdapter;
import com.dep.biguo.widget.MaxCountItemManager;

import java.util.List;

public class TruePaperAllCourseDialog extends Dialog {
    private TruePaperAllCourseDialogBinding binding;
    private TruePaperAllCourseAdapter adapter;

    private OnSelectListener onSelectListener;

    public TruePaperAllCourseDialog(@NonNull Context context) {
        super(context);

        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.true_paper_all_course_dialog, null, false);
        binding.getRoot().setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, (int) (DisplayHelper.getWindowHeight(context) * 0.8)));
        binding.closeView.setOnClickListener(v -> dismiss());
        setContentView(binding.getRoot());

        adapter = new TruePaperAllCourseAdapter();
        adapter.setOnItemClickListener((adapter, view, position) -> {
            if(onSelectListener != null){
                onSelectListener.onSelect(position);
            }
            dismiss();
        });
        binding.recyclerView.setAdapter(adapter);

        MaxCountItemManager layoutManager = new MaxCountItemManager(getContext());
        layoutManager.setSpaceHorizontal(DisplayHelper.dp2px(getContext(), 10));
        layoutManager.setSpaceVertical(DisplayHelper.dp2px(getContext(), 10));
        binding.recyclerView.setLayoutManager(layoutManager);

    }

    public TruePaperAllCourseDialog setData(List<TruePaperAllBean> list){
        adapter.setNewData(list);
        return this;
    }

    public TruePaperAllCourseDialog setSelectPosition(int position){
        adapter.setSelectPosition(position);
        return this;
    }


    public TruePaperAllCourseDialog setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
        return this;
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = DisplayHelper.getScreenWidth(getContext());
        layoutParams.height = (int) (DisplayHelper.getScreenHeight(getContext()) * 0.8);
        layoutParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(layoutParams);
    }

    public interface OnSelectListener{
        void onSelect(int position);
    }
}
