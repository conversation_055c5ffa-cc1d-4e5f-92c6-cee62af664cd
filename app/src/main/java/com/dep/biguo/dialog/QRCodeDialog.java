package com.dep.biguo.dialog;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.dep.biguo.R;
import com.dep.biguo.utils.ImgFileToPublicDirUtil;
import com.dep.biguo.utils.QRCodeUtil;
import com.dep.biguo.utils.RequestPermissions;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.hjq.toast.ToastUtils;

import java.text.SimpleDateFormat;
import java.util.Locale;

public class QRCodeDialog extends Dialog {

    public QRCodeDialog(@NonNull AppCompatActivity activity, String url) {
        super(activity);
        setContentView(R.layout.qr_code_dialog);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);

        ImageView qrCodeView = findViewById(R.id.qrCodeView);
        TextView cancelView = findViewById(R.id.cancelView);
        TextView saveView = findViewById(R.id.saveView);

        Bitmap logoBitmap = BitmapFactory.decodeResource(activity.getResources(), R.mipmap.ic_launcher);
        int size = logoBitmap.getWidth() * 6;
        Bitmap bitmap = QRCodeUtil.createQRCodeBitmap(url, size, size, "UTF-8", ErrorCorrectionLevel.L, Color.BLACK, Color.WHITE, logoBitmap, 1F);
        qrCodeView.setImageBitmap(bitmap);

        cancelView.setOnClickListener(v -> dismiss());
        saveView.setOnClickListener(v -> {
            dismiss();
            //文件名字
            checkPermissions(activity, bitmap);
        });
    }

    public static void checkPermissions(AppCompatActivity activity, Bitmap bitmap){
        new RequestPermissions.Builder(activity)
                .setPermissions(Manifest.permission.WRITE_EXTERNAL_STORAGE, true)
                .setRequestContentText("需要获取存储权限，是否允许？")
                .setOnRequestResultListener(new RequestPermissions.OnRequestResultListener() {
                    @Override
                    public void onSuccess() {
                        //文件名字
                        String fileName = new SimpleDateFormat("yyyy-MM-dd HH：mm：ss", Locale.getDefault()).format(System.currentTimeMillis())+".jpg";
                        if (ImgFileToPublicDirUtil.saveImage(activity, bitmap, fileName)) {
                            ToastUtils.show("保存成功");
                        } else {
                            ToastUtils.show("保存失败, 请通过截图方式保存");
                        }
                    }

                    public void onFailure(boolean isShowRequestContentDialog) {
                        ToastUtils.show("需要存储权限");
                    }

                    public void onNotAsk(boolean isShowRequestContentDialog) {
                        ToastUtils.show("需要存储权限");
                    }
                }).build();
    }

}
