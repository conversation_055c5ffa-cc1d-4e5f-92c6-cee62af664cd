package com.dep.biguo.dialog;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.databinding.ProfessionPopupWindowBinding;
import com.dep.biguo.mvp.ui.activity.CityActivity;
import com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.utils.ArmsUtils;

public class ProfessionPopupWindow extends PopupWindow {
    private ProfessionPopupWindowBinding binding;

    public ProfessionPopupWindow(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.profession_popup_window, null);
        binding = DataBindingUtil.bind(view);

        this.setContentView(binding.getRoot());
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(DisplayHelper.getRealScreenSize(context)[1] - DisplayHelper.getStatusBarHeight(context)-DisplayHelper.dp2px(context,44));
        this.setBackgroundDrawable(new ColorDrawable());
        this.setFocusable(true);

        if(UserCache.getSchool() != null) {
            binding.schoolNameView.setText(String.format("当前学校：%s", UserCache.getSchool().getName()));
        }

        ProfessionBean professionBean = UserCache.getProfession();
        if(professionBean != null) {
            if(professionBean.getLayer() == 76) {
                binding.professionNameView.setText(String.format("当前专业：%s(本科)", professionBean.getName()));
            }else if(professionBean.getLayer() == 71){
                binding.professionNameView.setText(String.format("当前专业：%s(专科)", professionBean.getName()));
            }else if(professionBean.getLayer() == 96){
                binding.professionNameView.setText(String.format("当前专业：%s(高升本)", professionBean.getName()));
            }
        }

        binding.changeProfessionLayout.setOnClickListener(v -> {
            if(UserCache.getCity() == null){
                ArmsUtils.startActivity(CityActivity.class);
            }else {
                CityBean.City city = UserCache.getCity();
                ProfessionSchoolActivity.Start(context, city.getProvince_name(), city.getProvince_id());
            }
            dismiss();
        });

        binding.getRoot().setOnClickListener(v -> dismiss());
    }
}