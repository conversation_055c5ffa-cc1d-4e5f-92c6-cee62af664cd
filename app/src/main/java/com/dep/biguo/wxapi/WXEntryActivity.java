package com.dep.biguo.wxapi;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.hjq.toast.ToastUtils;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.umeng.socialize.weixin.view.WXCallbackActivity;

import org.simple.eventbus.EventBus;

public class WXEntryActivity extends WXCallbackActivity {

    /**调起微信授权
     * @param context
     * @return
     */
    public static void goAuthorize(Context context){
        IWXAPI iwxapi = WXAPIFactory.createWXAPI(context, Constant.WX_KEY);
        if (iwxapi.isWXAppInstalled()) {
            SendAuth.Req req = new SendAuth.Req();
            req.scope = "snsapi_userinfo";
            req.state = "Biguo2018";
            iwxapi.sendReq(req);
        }else {
            ToastUtils.show("请先阅读协议并勾选");
        }
    }

    @Override
    public void onResp(BaseResp resp) {
        //小程序
        if (resp.getType() == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
            WXLaunchMiniProgram.Resp launchMiniProResp = (WXLaunchMiniProgram.Resp) resp;
        }
        //微信登录
        else if (resp.getType() == ConstantsAPI.COMMAND_SENDAUTH) {
            if (resp.errCode == 0) {
                EventBus.getDefault().post(((SendAuth.Resp) resp).code, EventBusTags.GET_WECHAT_AUTHORIZE_SUCCESS);
            } else {
                ToastUtils.show("微信登录失败");
            }
        } else {
            super.onResp(resp);
        }
        finish();
    }

    @Override
    public void onReq(BaseReq req) {
        super.onReq(req);
        if(req instanceof ShowMessageFromWX.Req) {
            String messageExt = ((ShowMessageFromWX.Req) req).message.messageExt;
            if(messageExt.contains("?") && (messageExt.indexOf("?") + 1 < messageExt.length())) {
                String url = messageExt.substring(messageExt.indexOf("?") + 1);
                String[] keyValueArray = url.split("&");
                Uri.Builder uri = new Uri.Builder();
                for (String keyValue : keyValueArray) {
                    String[] params = keyValue.split("=");
                    if (params.length == 1) {
                        uri.appendQueryParameter(params[0], "");
                    } else {
                        uri.appendQueryParameter(params[0], params[1]);
                    }
                }
                Intent intent = new Intent(this, MainActivity.class);
                intent.setData(uri.build());
                startActivity(intent);
                finish();
            }else {
                ToastUtils.show("参数缺失");
            }
        }
    }
}
