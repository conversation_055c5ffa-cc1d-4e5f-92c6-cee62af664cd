package com.dep.biguo.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;

import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import timber.log.Timber;

public class WXPayEntryActivity extends Activity implements IWXAPIEventHandler {

    private IWXAPI api;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = WXAPIFactory.createWXAPI(this, Constant.WX_KEY);
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq baseReq) {

    }

    @Override
    public void onResp(BaseResp resp) {
        Timber.d("onPayFinish, errCode = " + resp.errCode + ",msg:" + resp.errStr);
        finish();
        switch (resp.errCode) {
            case 0:
                //成功
                PayListenerUtils.getInstance().addSuccess();
                break;
            case -1:
                //失败
                PayListenerUtils.getInstance().addError();
                break;
            case -2:
                //用户取消
                PayListenerUtils.getInstance().addCancel();
                break;
        }
    }
}
