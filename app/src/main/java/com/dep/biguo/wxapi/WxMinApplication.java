package com.dep.biguo.wxapi;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.UrlAddParamUtil;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.integration.AppManager;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

public class WxMinApplication {
    public static int MINI_TYPE_RELEASE = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;// 可选打开 开发版、体验版和正式版
    public static int MINI_TYPE_PREVIEW = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;// 可选打开 开发版、体验版和正式版
    public static int MINI_VERSION = Api.isReleaseService() ? MINI_TYPE_RELEASE : MINI_TYPE_PREVIEW;
    //public static int MINI_VERSION = MINI_TYPE_PREVIEW;

    private static String getMessage(String message){
        if(AppUtil.isEmpty(message)){
            return "即将跳转至微信，是否继续？";
        }else {
            return message;
        }
    }

    /**启动微信小程序
     * @param context 上下文
     * @param path 路径
     */
    private static void ConfirmAgain(Context context, String path, String message){
        LogUtil.d("dddd", path+" "+path.contains("?"));
        for (int i = AppManager.getAppManager().getActivityList().size() - 1; i >= 0; i--) {
            Activity activity = AppManager.getAppManager().getActivityList().get(i);
            if (activity instanceof AppCompatActivity) {
                new MessageDialog.Builder(((BaseActivity) activity).getSupportFragmentManager())
                        .setTitle("温馨提醒")
                        .setContent(getMessage(message))
                        .setNegativeText("取消")
                        .setPositiveText("继续")
                        .setPositiveClickListener(v -> StartMinApp(context, path))
                        .builder()
                        .show();
                break;
            }
        }
    }

    //跳转移动的积分小程序，appId要使用我们自己在微信平台上的移动应用的appId
    public static void StartChainMobileMinApp(Context context){
        for(Activity activity : AppManager.getAppManager().getActivityList()){
            if(activity instanceof BaseActivity){
                new MessageDialog.Builder(((BaseActivity)activity).getSupportFragmentManager())
                        .setTitle("温馨提醒")
                        .setContent("即将跳转至微信，是否继续？")
                        .setNegativeText("取消")
                        .setPositiveText("继续")
                        .setPositiveClickListener((View.OnClickListener) v -> {
                            String appId = Constant.WX_KEY;// 填移动应用(App)的AppId，非小程序的 AppID
                            IWXAPI api = WXAPIFactory.createWXAPI(context, appId);
                            if(api.isWXAppInstalled()) {
                                WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                                req.userName = "gh_a111bf7e2c19"; // 填小程序原始id
                                req.path = "/pages/index/index?partnerId=TYZGGTFSC11";
                                req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;//正式版
                                api.sendReq(req);
                            }else {
                                ToastUtils.show("未安装微信");
                            }
                        })
                        .builder()
                        .show();
            }
        }
    }

    public static void StartMinApp(Context context, String path){
        String appId = Constant.WX_KEY;// 填移动应用(App)的AppId，非小程序的 AppID
        IWXAPI api = WXAPIFactory.createWXAPI(context, appId);
        if(api.isWXAppInstalled()) {
            WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
            req.userName = Constant.WX_LUNCH_MINI_ID; // 填小程序原始id
            req.path = path;
            req.miniprogramType = MINI_VERSION;// 可选打开 开发版，体验版和正式版
            api.sendReq(req);
        }else {
            ToastUtils.show("未安装微信");
        }
    }

    public static boolean isWXAppInstalled(Context context){
        String appId = Constant.WX_KEY;// 填移动应用(App)的AppId，非小程序的 AppID
        IWXAPI api = WXAPIFactory.createWXAPI(context, appId);
        return api.isWXAppInstalled();
    }

    /**使用Encoder编码路径，并拼接参数跳转小程序，否则在APP内部打开
     * @param context 上下文
     * @param path  路径
     * @param paramsUrl 需要拼接的参数,无参数传递空字符串
     * @return
     */
    public static void StartEncoderUrlToMinAppOrApp(Context context, String path, String paramsUrl, boolean isShowDialog){
        if(!TextUtils.isEmpty(path) && AppUtil.isInstallWechat(context)) {
            StartWechat(context, path, paramsUrl, isShowDialog);

        }else {
            HtmlActivity.start(context, paramsUrl);
        }
    }

    /**跳转到微信客服列表
     * @param context
     */
    public static void StartWechat(Context context, boolean isShowDialog){
        if(isShowDialog) {
            ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, "/pages/index/H5/service", ""), "");
        }else {
            StartMinApp(context, UrlAddParamUtil.addWechatPublicParams(context, "/pages/index/H5/service", ""));
        }
    }

    /**跳转到微信客服列表
     * @param context
     */
    public static void StartWechat(Context context){
        ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, "/pages/index/H5/service", ""), "");
    }

    /**跳转到微信小程序
     * @param context
     */
    public static void StartWechat(Context context, String url){
        ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, url, ""), "");
    }

    /**跳转到微信小程序
     * @param context
     */
    public static void StartWechat(Context context, String url, String paramsUrl){
        ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, url, paramsUrl), "");
    }

    /**跳转到微信小程序
     * @param context
     */
    public static void StartWechat(Context context, String url, String paramsUrl, boolean isShowDialog){
        if(isShowDialog) {
            ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, url, paramsUrl), "");
        }else {
            StartMinApp(context, UrlAddParamUtil.addWechatPublicParams(context, url, paramsUrl));
        }
    }

    /**跳转到微信小程序
     * @param context
     */
    public static void StartWechatAddService(Context context, String url, String paramsUrl){
        ConfirmAgain(context, UrlAddParamUtil.addWechatPublicParams(context, url, paramsUrl), getMessage("即将跳转到微信添加微信老师，是否继续?"));
    }

}





















