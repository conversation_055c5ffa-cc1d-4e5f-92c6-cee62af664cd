package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.BiguoVipBean;
import com.dep.biguo.bean.OrganizationCourseDetailBean;
import com.dep.biguo.mvp.contract.OrganizationCourseDetailContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationCourseDetailPresenter extends BasePresenter<OrganizationCourseDetailContract.Model, OrganizationCourseDetailContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationCourseDetailPresenter(OrganizationCourseDetailContract.Model model, OrganizationCourseDetailContract.View rootView) {
        super(model, rootView);
    }

    public void getOrganizationCourseDetail(int activities_id){
        mModel.getOrganizationCourseDetail(activities_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationCourseDetailBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationCourseDetailBean> response) {
                        if (response.isSuccess()) {
                            if(response.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getOrganizationCourseDetailSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(new Throwable());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void courseReservation(int activities_id) {
        //预约自习室传递1, 预约试听课传2
        mModel.courseReservation(2, activities_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.courseReservationSuccess();
                        }
                    }
                });

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
