package com.dep.biguo.mvp.ui.adapter;

import android.util.Log;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.BannerBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

/**
 * Banner 轮播图适配器
 * 用于展示商城首页的广告横幅
 */
public class BannerAdapter extends BaseQuickAdapter<BannerBean, BaseViewHolder> {

    private static final String TAG = "BannerAdapter";

    public BannerAdapter(@Nullable List<BannerBean> data) {
        super(R.layout.item_banner, data);
        Log.d(TAG, "BannerAdapter created with " + (data != null ? data.size() : 0) + " items");
    }

    @Override
    protected void convert(BaseViewHolder helper, BannerBean item) {
        if (item == null) {
            Log.w(TAG, "Banner item is null at position " + helper.getAdapterPosition());
            return;
        }

        try {
            // 设置横幅图片
            ImageView ivBannerImage = helper.getView(R.id.iv_banner_image);
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                ImageLoader.loadImage(ivBannerImage, item.getImageUrl());
            } else {
                // 使用默认渐变背景
                ivBannerImage.setImageResource(R.drawable.bg_gradient_red);
            }

            // 设置标题（如果有）
            if (item.getTitle() != null && !item.getTitle().isEmpty()) {
                helper.setText(R.id.tv_banner_title, item.getTitle());
                helper.setVisible(R.id.tv_banner_title, true);
            } else {
                helper.setVisible(R.id.tv_banner_title, false);
            }

            // 设置点击事件数据
            helper.itemView.setTag(item);

            Log.d(TAG, "Banner item converted at position " + helper.getAdapterPosition() + 
                  ", title: " + item.getTitle());

        } catch (Exception e) {
            Log.e(TAG, "Error in convert() at position " + helper.getAdapterPosition() + 
                  ": " + e.getMessage(), e);
        }
    }
}
