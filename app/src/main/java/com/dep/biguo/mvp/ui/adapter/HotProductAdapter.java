package com.dep.biguo.mvp.ui.adapter;

import android.util.Log;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

/**
 * 热卖商品适配器 - 横向滚动
 */
public class HotProductAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    private static final String TAG = "HotProductAdapter";

    public HotProductAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_product_hot, data);
        Log.d(TAG, "HotProductAdapter created with " + (data != null ? data.size() : 0) + " items");
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        Log.d(TAG, "convert() called for position: " + helper.getAdapterPosition());
        
        if (item == null) {
            Log.e(TAG, "Item is null at position: " + helper.getAdapterPosition());
            return;
        }
        
        try {
            // 商品图片
            ImageView ivProductImage = helper.getView(R.id.iv_product_image);
            if (item.getImg() != null && !item.getImg().isEmpty()) {
                ImageLoader.loadImage(ivProductImage, item.getImg());
            } else {
                ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
            }

            // 商品名称
            helper.setText(R.id.tv_product_name, item.getName());

            // 价格
            helper.setText(R.id.tv_product_price, "¥" + item.getPrice());

            // TOP标签
            int position = helper.getAdapterPosition();
            String topLabel = "月销量TOP" + (position + 1);
            helper.setText(R.id.tv_sales_rank, topLabel);

        } catch (Exception e) {
            Log.e(TAG, "Error in convert(): " + e.getMessage(), e);
        }
    }
}
