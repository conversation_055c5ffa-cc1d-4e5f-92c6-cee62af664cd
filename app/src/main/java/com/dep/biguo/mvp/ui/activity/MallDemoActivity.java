package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentTransaction;

import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.fragment.MallHomeNewFragment;

/**
 * 商城演示页面
 * 用于展示重写后的商城首页效果
 */
public class MallDemoActivity extends AppCompatActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, MallDemoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_mall_demo);

        // 设置标题
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("商城演示");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // 加载商城首页Fragment
        if (savedInstanceState == null) {
            MallHomeNewFragment fragment = MallHomeNewFragment.newInstance();
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
            transaction.replace(R.id.fragment_container, fragment);
            transaction.commit();
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
