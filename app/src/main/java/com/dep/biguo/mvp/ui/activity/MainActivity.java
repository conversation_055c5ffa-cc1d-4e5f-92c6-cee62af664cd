package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dep.biguo.bean.AppVersionBean;
import com.dep.biguo.bean.JoinWechatGroupBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProfessionInfo;
import com.dep.biguo.bean.UpdateInfoBean;
import com.dep.biguo.databinding.MainActivityBinding;
import com.dep.biguo.dialog.JoinWechatGroupDialog;
import com.dep.biguo.dialog.PrivateDialog;
import com.dep.biguo.dialog.UpdateAppDialog;
import com.dep.biguo.mvp.ui.adapter.ViewPager2Adapter;
import com.dep.biguo.mvp.ui.fragment.KefuFragment;
import com.dep.biguo.mvp.ui.fragment.LiveFragment;
import com.dep.biguo.mvp.ui.fragment.OrganizationRecommendFragment;
import com.dep.biguo.mvp.ui.fragment.LivePolyvFragment;
import com.dep.biguo.utils.StartFinal;
import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.widget.StyleTextView;
import com.dep.biguo.utils.imp.OnTabSelectedImp;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.widget.OneKeyService;
import com.google.android.material.tabs.TabLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.databinding.DataBindingUtil;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.AdvertisBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.di.component.DaggerMainComponent;
import com.dep.biguo.mvp.contract.MainContract;
import com.dep.biguo.mvp.presenter.MainPresenter;
import com.dep.biguo.mvp.ui.fragment.CircleFragment;
import com.dep.biguo.mvp.ui.fragment.HomeFragment;
import com.dep.biguo.mvp.ui.fragment.UserFragment;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.umengPush.UmengPushHelper;
import com.dep.biguo.dialog.MaxBigImageDialog;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.dialog.QuestionBankUpdateDialog;
import com.dep.biguo.wxapi.WxMinApplication;
import com.google.android.material.tabs.TabLayoutMediator;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.umeng.analytics.MobclickAgent;
import com.umeng.message.PushAgent;
import com.umeng.socialize.UMShareAPI;

import java.util.ArrayList;
import java.util.List;

import static com.jess.arms.utils.Preconditions.checkNotNull;

import org.simple.eventbus.Subscriber;
import com.dep.biguo.mvp.ui.fragment.MallHomeNewFragment;

/**
 * *
 * ......................我佛慈悲....................
 * ......................_oo0oo_.....................
 * .....................o8888888o....................
 * .....................88" . "88....................
 * .....................(| -_- |)....................
 * .....................0\  =  /0....................
 * ...................___/`---'\___..................
 * ..................' \\|     |// '.................
 * ................./ \\|||  :  |||// \..............
 * .............../ _||||| -卍-|||||- \..............
 * ..............|   | \\\  -  /// |   |.............
 * ..............| \_|  ''\---/''  |_/ |.............
 * ..............\  .-\__  '-'  ___/-. /.............
 * ............___'. .'  /--.--\  `. .'___...........
 * .........."" '<  `.___\_<|>_/___.' >' ""..........
 * ........| | :  `- \`.;`\ _ /`;.`/ - ` : | |.......
 * ........\  \ `_.   \_ __\ /__ _/   .-` /  /.......
 * ....=====`-.____`.___ \_____/___.-`___.-'=====....
 * ......................`=---='.....................
 * ..................佛祖保佑 ,永无BUG................
 * ..................南无机械工程佛...................
 * *
 */

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/21
 * @Description: 承载页
 */
public class MainActivity extends SchemeActivity<MainPresenter> implements MainContract.View {
    private MainActivityBinding binding;

    private TypedArray mMainTabIcons;

    private List<BaseFragment> mFragments;
    private TabLayoutMediator tabLayoutMediator;

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerMainComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.main_activity);
        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        super.initData(savedInstanceState);

        if(mFragments == null) {
            mFragments = new ArrayList<>();
            mFragments.add(HomeFragment.newInstance());
            mFragments.add(LiveFragment.newInstance());
            mFragments.add(MallHomeNewFragment.newInstance());
            mFragments.add(CircleFragment.newInstance());
            mFragments.add(UserFragment.newInstance());
        }

        initBottomTab();

        //清除第一次启动时间
        KVHelper.removeValue(UserCache.MAIN_AGREEMENT_TIME);

        //设置深色模式的时候，activity会重启，导致弹窗也会重新出现，因此需要判断当前的activity是否是ZkSettingActivity，
        //若是ZkSettingActivity，则认为是切换深色模式导致的启动，不需要重新请求广告
        if(!(AppManager.getAppManager().getCurrentActivity() instanceof ZkSettingActivity)) {
            MainAppUtils.checkLogin(this);

            mPresenter.createDialogTaskMap();
            //获取版本信息
            mPresenter.getAppVersion();
            //获取用户信息
            mPresenter.getUserInfo();
            //开始绑定用户信息
            mPresenter.bindPushClientId();
            //获取弹窗广告
            mPresenter.getAdvertisData();
            //获取入群弹窗
            mPresenter.getJoinWechatGroup();
            //获取题库更新
            mPresenter.popExam();
            //获取专业变化
            mPresenter.getProfessionInfo();
        }else {
            //获取版本信息，因为设置深色模式的时候，activity会重启，因此直播入口会默认隐藏，需要获取一下审核状态来设置隐藏或显示
            mPresenter.getAppVersion();
        }

        if(UserCache.getUserCache() == null) {
            UmengPushHelper.addExamInfoTag(getActivity()); //注册友盟推送标签
            UmengPushHelper.addGradeTag(getActivity(), "zk"); //注册友盟推送标签
        }

        //添加布局变化监听，当布局有变动，则认为是小窗模式或分屏模式
        addLayoutChangeListener();
    }

    public void addLayoutChangeListener(){
        binding.getRoot().addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            boolean isChange;
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                if(AppUtil.isFillScreen(getContext()) == isChange) return;
                isChange = AppUtil.isFillScreen(getContext());

                for(BaseFragment baseFragment : mFragments){
                    if(baseFragment instanceof HomeFragment){
                        ((HomeFragment)mFragments.get(0)).screenSizeChange();
                    }else if(baseFragment instanceof LiveFragment){
                        ((LiveFragment)mFragments.get(1)).screenSizeChange();
                    }else if(baseFragment instanceof MallHomeNewFragment){
                        // MallHomeNewFragment 可能不需要 screenSizeChange 方法，先注释掉
                        // ((MallHomeNewFragment)mFragments.get(2)).screenSizeChange();
                    }else if(baseFragment instanceof UserFragment){
                        ((UserFragment)mFragments.get(4)).screenSizeChange();
                    }
                }
            }
        });
    }

    /**
     * 设置底部
     */
    private void initBottomTab() {
        binding.vpMain.setAdapter(new ViewPager2Adapter(getSupportFragmentManager(), getLifecycle(), mFragments));
        //设置fragment的缓存个数
        binding.vpMain.setOffscreenPageLimit(mFragments.size() - 1);
        //禁止viewPager2滑动
        binding.vpMain.setUserInputEnabled(false);
        //初始化tabLayout
        AppUtil.clearTabClickColor(this, binding.tbMain);
        String[] mMainTabTitles = getResources().getStringArray(R.array.main_tab_text);
        binding.tbMain.addOnTabSelectedListener(new OnTabSelectedImp() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                new UmengEventUtils(getActivity())
                        .addParams("bottom_navigation", mMainTabTitles[tab.getPosition()])
                        .pushEvent(UmengEventUtils.CLICK_BOTTOM_NAVIGATION);

                if(mFragments.get(tab.getPosition()) instanceof UserFragment){
                    mPresenter.getUserInfo();
                }
            }
        });

        mMainTabIcons = getResources().obtainTypedArray(R.array.main_tab_icon);
        tabLayoutMediator = new TabLayoutMediator(binding.tbMain, binding.vpMain, false, false, (tab, position) -> {
            initTabItem(tab, mMainTabTitles[position], mMainTabIcons.getDrawable(position), position);
            /**
             * 直播模块规避审核，显示该模块的地方在{@link MainActivity#showLive(AppVersionBean)}
             * */
            if(position == 2 || position == 3){
                ((ViewGroup)tab.getCustomView().getParent()).setVisibility(View.GONE);
            }
        });
        //这句话很重要，viewPager与tabLayout绑定
        tabLayoutMediator.attach();
    }

    //初始化TabLayout按钮
    private void initTabItem(TabLayout.Tab tab, String title, Drawable imgRes, int position) {
        View tabView = LayoutInflater.from(this).inflate(R.layout.main_tab_item, null);
        ((ImageView) tabView.findViewById(R.id.ivIcon)).setImageDrawable(imgRes);
        ((TextView) tabView.findViewById(R.id.tvName)).setText(title);
        if(mFragments.get(position) instanceof LivePolyvFragment) {
            StyleTextView badgeCount = tabView.findViewById(R.id.badgeCount);
            badgeCount.setText("New");
            badgeCount.setVisibility(View.VISIBLE);
        }else if(mFragments.get(position) instanceof OrganizationRecommendFragment){
            StyleTextView badgeCount = tabView.findViewById(R.id.badgeCount);
            badgeCount.setText("Hot");
            badgeCount.setVisibility(View.VISIBLE);
        }
        tab.setCustomView(tabView);
        tabView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.dp2px(this, 44)));
        LinearLayout linearLayout = (LinearLayout) tabView.getParent();
        linearLayout.setPadding(0,0,0,0);
        linearLayout.setGravity(Gravity.CENTER);
    }

    //添加tabLayout中客服按钮的点击事件
    @Override
    @Subscriber(tag = EventBusTags.START_KEFU_ACTIVITY)
    public void customClick(String s){
        WxMinApplication.StartWechat(this);
    }

    @Override
    public void bindTeacherFail(){
        new MessageDialog.Builder(getSupportFragmentManager())
                .setContent("尚未分配专属客服，请微信联系客服反馈")
                .setNegativeText("取消")
                .setPositiveText("添加专属客服微信")
                .setPositiveClickListener(v1 -> WxMinApplication.StartWechat(this))
                .builder()
                .show();
    }

    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ToastUtils.show(message);
    }

    public void setGuideViewTopMargin(int topMargin){
        //设置手势引导图的顶部外边距
        if(binding.tbMain.getSelectedTabPosition() == 0) {
            binding.guideView.refresh(topMargin);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        UserCache.setAppType(Constant.ZK);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //解决友盟分享内存泄漏
        UMShareAPI.get(this).release();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        int currentItem = intent.getIntExtra("currentItem", -1);
        if(currentItem != -1) {
            binding.vpMain.setCurrentItem(currentItem);
        }
    }

    public void scrollViewPager(int position){
        binding.vpMain.setCurrentItem(position, false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
    }

    @Subscriber(tag = EventBusTags.SYSTEM_UNREAD_MESSAGE_COUNT)
    private void systemUnreadMessageCount(int count) {
        //设置系统消息未读数量
        String imMessageCount = "0";
        for (int i=0;i<mFragments.size();i++) {
            BaseFragment fragment = mFragments.get(i);
            TextView textView = binding.tbMain.getTabAt(i).getCustomView().findViewById(R.id.badgeCount);
            if(fragment instanceof UserFragment){
                textView.setVisibility(count > 0 ? View.VISIBLE : View.GONE);
                textView.setText(String.format("%s", count > 99 ? "99+" : count));

            }else if(fragment instanceof KefuFragment){
                imMessageCount = AppUtil.isEmpty(textView.getText().toString().replace("+", ""), "0");
            }
        }
        //如果有设置过未读数，则比较与上一次的未读数差了多少
        PushAgent.getInstance(this).setBadgeNum(Integer.parseInt(imMessageCount) + count);
    }

    @Subscriber(tag = EventBusTags.GET_USERINFO_SUCCESS)
    private void changeUserInfo(UserBean user) {
        //mPresenter.refreshProfessionInfo(user);
    }

    @Subscriber(tag = EventBusTags.LOGOUT_SUCCESS)
    private void logoutSuccess(UserBean user) {
        if (mPresenter == null) return;
        //首页的视频购买状态与账号相关，所以退出之后需要刷新一下，避免显示已购买
        mFragments.get(0).onResume();

        MobclickAgent.onProfileSignOff();
        UserCache.setEnrollCourse(null);
    }

    @Subscriber(tag = EventBusTags.ADD_PUSH_TAG)
    private void addPushTag(String...tags) {
        if (mPresenter == null || !Constant.ZK.equals(UserCache.getAppType())) return;
        mPresenter.addPushTag(tags);
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    public void showSurvey(UserBean user) {
        mPresenter.getUserInfo();
        //首页的视频购买状态与账号相关，所以登录之后需要刷新一下，避免显示未购买
        mFragments.get(0).onResume();
        //登录之后，检查当前专业与后台的专业是否一致，不一致就需要提醒
        mPresenter.getProfessionInfo();
    }

    @Override
    public void showLive(AppVersionBean bean) {
        //直播模块规避审核
        if(bean.getIs_examine() != 2){
            //考友圈显示
            ViewGroup circleGroup = ((ViewGroup)binding.tbMain.getTabAt(3).getCustomView().getParent());
            circleGroup.setVisibility(View.VISIBLE);

            //直播显示
            ViewGroup LiveGroup = ((ViewGroup)binding.tbMain.getTabAt(2).getCustomView().getParent());
            LiveGroup.setVisibility(View.VISIBLE);
            Animation animation = AnimationUtils.loadAnimation(this, R.animator.scale_show);
            LiveGroup.startAnimation(animation);
        }
    }

    @Override
    public void showPrivateDialog(){
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.PRIVATE_DIALOG);
        if(!(obj instanceof AppVersionBean)) {
            showUpdateAppDialog();
            return;
        }
        //缓存的隐私政策版本号与后台返回的不一致，说明有更新，需要先展示隐私政策弹窗，然后记录版本号
        PrivateDialog dialog = new PrivateDialog(this);
        dialog.setOnAgreementListener(() -> {
            AppVersionBean versionBean = (AppVersionBean) mPresenter.getDialogTaskMap().get(MainPresenter.PRIVATE_DIALOG);
            KVHelper.putValue(UserCache.MAIN_AGREEMENT_VERSION, versionBean.getProtocol_version());
            showUpdateAppDialog();
        });
        dialog.show();
    }

    private void showUpdateAppDialog(){
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.APP_UPDATE_DIALOG);
        if(!(obj instanceof AppVersionBean)){
            showJoinWechatGroupDialog();
            return;
        }
        AppVersionBean versionBean = (AppVersionBean) obj;
        new UpdateAppDialog.Builder(getActivity())
                .setAppDownloadLink(versionBean.getApp_download_link())
                .setIsMustUpdate(versionBean.getIs_update() == 2)
                .setUpdateContent(versionBean.getContent())
                .setNewVersion(versionBean.getVersion())
                .setOnCancelListener(dialog -> showJoinWechatGroupDialog())
                .build()
                .show();
    }

    public void showJoinWechatGroupDialog(){
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.JOIN_WECHAT_DIALOG);
        if(!(obj instanceof JoinWechatGroupBean)){
            showAdvertisementDialog();
            return;
        }

        //通知首页显示入群的悬浮按钮
        ((HomeFragment)mFragments.get(0)).setJoinWechatGroupDrag(true);

        JoinWechatGroupBean groupBean = (JoinWechatGroupBean) obj;
        //链接中有"needLogin"字符串表示必须要加上token和user_id，否则不弹出广告
        if(!TextUtils.isEmpty(groupBean.getUrl()) && groupBean.getUrl().contains("need_login") || UserCache.getUserCache() == null){
            showAdvertisementDialog();
            return;
        }
        //若显示过入群弹窗，则不再显示
        if(UserCache.isShowZkHomeJoinWechatGroupDialog()){
            showAdvertisementDialog();
            return;
        }
        JoinWechatGroupDialog dialog = new JoinWechatGroupDialog(this);
        //弹窗取消后，继续弹出广告弹窗
        dialog.setOnDismissListener(dialog12 -> showAdvertisementDialog());
        dialog.setOnClickAdListener(v -> {
                    JoinWechatGroupBean groupBean1 = (JoinWechatGroupBean) obj;
                    if(!TextUtils.isEmpty(groupBean1.getXcx_path())) {//跳转小程序
                        WxMinApplication.StartWechat(getContext(), groupBean1.getXcx_path(), groupBean1.getUrl(), false);

                    }else if(!TextUtils.isEmpty(groupBean1.getUrl())){//跳转H5页面, 我要报名、积分入户、学梦计划
                        HtmlActivity.start(getActivity(), groupBean1.getUrl());
                    }
                })
                .setImgUrlShow(groupBean.getImg());
    }

    public void showAdvertisementDialog(){
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.ADVERTISEMENT_DIALOG);
        if(!(obj instanceof List) || AppUtil.isEmpty((List<AdvertisBean>) obj)){
            showQuestionBankUpdateDialog();
            return;
        }
        List<AdvertisBean> list = (List<AdvertisBean>) obj;
        MaxBigImageDialog bigImageDialog = new MaxBigImageDialog(this);
        bigImageDialog.setUrl(list.get(0).getUrl())
                .setImgUrlShow(list.get(0).getImg())
                .setOnCancelListener(dialog -> {
                    showQuestionBankUpdateDialog();
                });
    }

    public void showQuestionBankUpdateDialog() {
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.TOPIC_UPDATE_DIALOG);
        if(!(obj instanceof UpdateInfoBean)){
            showProfessionSwitchDialog();
            return;
        }

        UpdateInfoBean infoBean = (UpdateInfoBean) obj;
        if (AppUtil.isEmpty(infoBean.getList())) {
            showProfessionSwitchDialog();
            return;
        }
        if (!TextUtils.isEmpty(infoBean.getDate()) && infoBean.getDate().equals(UserCache.getQuestionBanUpdateTime())) {
            showProfessionSwitchDialog();
            return;
        }

        QuestionBankUpdateDialog mQuestionBankUpdateDialog = QuestionBankUpdateDialog.newInstance(infoBean.getList(), infoBean.getDate());
        mQuestionBankUpdateDialog.setOnClickListener(view -> UserCache.cacheQuestionBanUpdateTime(infoBean.getDate()));
        mQuestionBankUpdateDialog.setOnCancelListener(() -> showProfessionSwitchDialog());
        mQuestionBankUpdateDialog.show(getSupportFragmentManager());
    }

    public void showProfessionSwitchDialog(){
        Object obj = mPresenter.getDialogTaskMap().remove(MainPresenter.PROFESSION_UPDATE_DIALOG);
        if(!(obj instanceof ProfessionInfo)){
            return;
        }

        ProfessionInfo professionInfo = (ProfessionInfo) obj;
        mPresenter.refreshProfessionInfo(professionInfo);
    }

    public void showProfessionChangeDialog(ProfessionInfo.Profession profession){
        if(profession.getProfession_has_new() == 0) return;
        //点击过不再提示，则不弹出弹窗
        if(UserCache.isProfessionChangeDialog()) return;

        new MessageDialog.Builder(getSupportFragmentManager())
                .setTitle("温馨提醒")
                .setContent("您所在的省份正在进行课程改革，部分专业课程已发生调整。请及时更新您的专业选择！")
                .setNegativeText("不再提示")
                .setNegativeClickListener(v -> UserCache.setProfessionChangeDialog())
                .setPositiveText("更新专业")
                .setPositiveClickListener(v -> ProfessionSchoolActivity.Start(this))
                .builder()
                .show();
    }

    @Subscriber(tag = EventBusTags.CHANGE_DAY_NIGHT)
    private void changeDayNight(boolean night) {
        if (night) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            StatusBarHelper.setStatusBarDarkMode(this);
        } else {
            StatusBarHelper.setStatusBarLightMode(this);
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        recreate();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Override
    public Context getContext() {
        return this;
    }

    private long clickTime;
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (KeyEvent.KEYCODE_BACK == keyCode) {
            if ((System.currentTimeMillis() - clickTime) > 2000) {
                showMessage("再按一次退出程序");
                clickTime = System.currentTimeMillis();
            } else {
                finish();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Subscriber(tag = EventBusTags.CHANGE_PROFESSION)
    private void notifyHome(ProfessionBean professionBean) {
        //专业有变化，需要通知home页更新数据
        mFragments.get(0).onResume();
        mPresenter.getProfessionInfo();
    }


    @Override
    public void showLoading() {
        LoadingDialog.showLoadingDialog(AppManager.getAppManager().getCurrentActivity());
    }

    @Override
    public void hideLoading() {
        LoadingDialog.hideLoadingDialog();
    }
}
