package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.os.Build;
import android.view.Window;
import android.view.WindowManager;
import android.graphics.Color;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.databinding.MallHomeFragmentBinding;
import com.dep.biguo.mvp.ui.adapter.MallProductAdapter;
import com.dep.biguo.mvp.ui.adapter.HotProductAdapter;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.TextBooksActivity;
import com.dep.biguo.mvp.ui.activity.SecondHandActivity;
import com.dep.biguo.mvp.ui.activity.StudyToolActivity;
import com.dep.biguo.mvp.ui.activity.FarmAssistActivity;
import com.dep.biguo.mvp.ui.activity.ShopCartActivity;
import com.dep.biguo.mvp.ui.activity.AddressListActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;

import java.util.ArrayList;
import java.util.List;

public class MallHomeFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "MallHomeFragment";
    
    private MallHomeFragmentBinding binding;
    private HotProductAdapter hotProductAdapter;
    private MallProductAdapter allProductAdapter;

    public static MallHomeFragment newInstance() {
        Log.d(TAG, "newInstance() called");
        return new MallHomeFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        Log.d(TAG, "setupFragmentComponent() called");
        // 不需要注入，这是一个简单的UI页面
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initView() called");
        binding = DataBindingUtil.inflate(inflater, R.layout.mall_home_fragment, container, false);
        if (binding == null) {
            Log.e(TAG, "binding is null! Layout inflation failed!");
            return null;
        }
        Log.d(TAG, "binding created successfully");
        binding.setOnClickListener(this);

        // 初始化分类标签
        initCategoryTabs();



        return binding.getRoot();
    }

    private void initCategoryTabs() {
        String[] categories = {"学习工具", "二手教材", "助农项目", "周边产品"};

        for (String category : categories) {
            binding.categoryTabs.addTab(binding.categoryTabs.newTab().setText(category));
        }

        binding.categoryTabs.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                // 处理分类选择
                String category = tab.getText().toString();
                Log.d(TAG, "Selected category: " + category);
                // TODO: 根据分类筛选商品
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });
    }



    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initData() called");
        loadMockData();
    }

    private void loadMockData() {
        Log.d(TAG, "loadMockData() called");

        try {
            // 模拟热卖商品数据
            List<ShopBean> hotProducts = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 1);
                bean.setName("笔果AI学习机");
                bean.setPrice("2500.00");
                bean.setPreferential_price("3000.00");
                hotProducts.add(bean);
            }
            Log.d(TAG, "Created " + hotProducts.size() + " hot products");

            // 设置热卖商品适配器
            hotProductAdapter = new HotProductAdapter(hotProducts);
            LinearLayoutManager hotLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
            binding.rvHotProducts.setLayoutManager(hotLayoutManager);
            binding.rvHotProducts.setAdapter(hotProductAdapter);
            Log.d(TAG, "Hot products RecyclerView setup complete");

            // 模拟全部商品数据
            List<ShopBean> allProducts = new ArrayList<>();
            for (int i = 0; i < 6; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 10);
                bean.setName("这里是商品名称\n多展示两行文字");
                bean.setPrice("128.00");
                bean.setPreferential_price("150.00");
                allProducts.add(bean);
            }
            Log.d(TAG, "Created " + allProducts.size() + " all products");

            // 设置全部商品适配器
            allProductAdapter = new MallProductAdapter(allProducts);
            GridLayoutManager allLayoutManager = new GridLayoutManager(getContext(), 2);
            binding.rvAllProducts.setLayoutManager(allLayoutManager);
            binding.rvAllProducts.setAdapter(allProductAdapter);
            Log.d(TAG, "All products RecyclerView setup complete");

            // 设置点击事件
            hotProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "Hot product clicked at position: " + position);
                ShopBean item = hotProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

            allProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "All product clicked at position: " + position);
                ShopBean item = allProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error in loadMockData(): " + e.getMessage(), e);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.cartLayout) {
            // 购物车
            if (!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(ShopCartActivity.class);
        } else if (id == R.id.searchLayout) {
            // 搜索
            // TODO: 实现搜索功能
            Log.d(TAG, "Search clicked");
        }
    }

    @Override
    public void setData(@Nullable Object data) {
        // 可以接收外部传入的数据
    }
}
