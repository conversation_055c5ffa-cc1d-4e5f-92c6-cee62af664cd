<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/theme</item>
        <item name="colorPrimaryDark">@color/theme</item>
        <item name="colorAccent">@color/theme</item>
        <item name="android:typeface">sans</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textStyle">bold</item>
        <item name="android:windowBackground">@color/bgc</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="AppTheme.MainTheme" parent="AppTheme">
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="AppTheme.Html" parent="AppTheme">
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>
    <style name="Modify_EditText" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorControlNormal">@color/guobi_div_line_color</item>
        <item name="colorControlActivated">@color/theme</item>
    </style>
    <!--启动页-->
    <style name="AppTheme.SplashStyle" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="FullWindow" parent="AppTheme">
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="AppTheme.SplashStyle.Night" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_night_bg</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="BottomDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomDialogSheetStyle</item>
        <item name="colorPrimary">@color/theme</item>
        <item name="colorPrimaryDark">@color/theme</item>
        <item name="colorAccent">@color/theme</item>
    </style>

    <style name="BottomDialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/ps_anim_up_in</item>
        <item name="android:windowExitAnimation">@anim/ps_anim_down_out</item>
    </style>

    <style name="BottomDialogSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="behavior_peekHeight">550dp</item>
    </style>

    <style name="dialogWindowScaleSAnim" parent="android:Animation" mce_bogus="1">
        <item name="android:windowEnterAnimation">@animator/dialog_enter_scale_anim</item>
        <item name="android:windowExitAnimation">@animator/dialog_out_scale_anim</item>
    </style>

    <style name="BottomInputDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="colorPrimary">@color/theme</item>
    </style>

    <style name="Transparent" parent="AppTheme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">#80505050</item>
    </style>

    <!-- 全屏 Dialog 的主题, 这里的parent必须是Theme.AppCompat.Dialog -->
    <style name="FullScreenDialogTheme" parent="Theme.AppCompat.Dialog">
        <!-- 上面说过，只要是Dialog，这两个属性必须设置 -->
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>

        <!--隐藏状态栏内容和高度，适用于SDK19（4.4）及以上版本-->
        <item name="android:windowFullscreen">true</item>
        <!-- 对于28及以上的版本，需要指定该属性，否则对于异形屏的手机，无法让布局铺满异性区域 -->
        <item name="android:windowLayoutInDisplayCutoutMode" >shortEdges</item>
        <item name="android:windowTranslucentStatus" >true</item>

        <!-- 透明导航栏 -->
        <item name="android:windowTranslucentNavigation">true</item>
    </style>

    <style name="liveRadioButtonTheme">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/tblack</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:button">@null</item>
        <item name="android:background">@null</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:visibility">gone</item>
    </style>

    <!--设置com.google.android.material.tabs.TabLayout的字体大小-->
    <style name="TabTextSize" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14dp</item>
    </style>

    <style name="MallTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textStyle">bold</item>
    </style>

    <!--答题设置开关-->
    <style name="SwitchStyle">
        <!--开启时的颜色-->
        <item name="colorControlActivated">@color/orange</item>
        <!--关闭时的颜色-->
        <item name="colorSwitchThumbNormal">@color/white</item>
        <item name="switchPadding">0dp</item>
        <!--关闭时的轨迹颜色取30%的颜色-->
        <item name="android:colorForeground">#878787</item>
    </style>
    <!--常规文字-->
    <style name="normalText">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="lightText">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
    </style>
    <style name="boldText">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!--题型 题数等标签-->
    <style name="LabelStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginRight">@dimen/dp_10</item>
        <item name="android:paddingLeft">@dimen/dp_10</item>
        <item name="android:paddingRight">@dimen/dp_10</item>
        <item name="android:paddingTop">@dimen/dp_2</item>
        <item name="android:paddingBottom">@dimen/dp_2</item>
        <item name="android:textSize">@dimen/sp_10</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="LoginEdit">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_round_5_bgc</item>
        <item name="android:drawablePadding">@dimen/app_space</item>
        <item name="android:paddingTop">@dimen/app_space</item>
        <item name="android:paddingLeft">@dimen/app_space</item>
        <item name="android:paddingRight">@dimen/app_space</item>
        <item name="android:paddingBottom">@dimen/app_space</item>
        <item name="android:textColor">@color/tblack</item>
        <item name="android:textSize">@dimen/sp_14</item>
    </style>

    <style name="IndentState_TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/dp_10</item>
        <item name="android:paddingBottom">@dimen/dp_3</item>
        <item name="android:paddingLeft">@dimen/dp_10</item>
        <item name="android:paddingRight">@dimen/dp_10</item>
        <item name="android:paddingTop">@dimen/dp_3</item>
        <item name="android:textSize">@dimen/sp_12</item>
    </style>

    <style name="EditText_V2">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/white</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColorHint">@color/tblack3</item>
        <item name="android:textColor">@color/tblack</item>
    </style>
    <style name="TextView_V2">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">@color/login_color_title</item>
        <item name="android:textSize">@dimen/sp_14</item>
    </style>

    <style name="Line_V2">
        <item name="android:layout_height">1dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_marginTop">@dimen/dp_8</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:background">@color/line_color</item>
    </style>

    <style name="Order_Secret_Content_Number">
        <item name="android:textColor">@color/twhite</item>
        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/icon_bubble</item>
        <item name="android:layout_width">@dimen/dp_20</item>
        <item name="android:layout_height">@dimen/dp_20</item>
    </style>

    <style name="Order_Secret_Content">
        <item name="android:textColor">@color/tblack</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="Order_Secret_Sub_Content">
        <item name="android:textColor">@color/tblack3</item>
        <item name="android:textSize">13dp</item>
        <item name="android:gravity">start</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:paddingStart">@dimen/dp_10</item>
        <item name="android:paddingTop">@dimen/dp_5</item>
        <item name="android:paddingEnd">@dimen/dp_5</item>
        <item name="android:lineSpacingExtra">@dimen/dp_4</item>
    </style>

    <style name="SideslipDialog" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@animator/sideslip_in</item>
        <item name="android:windowExitAnimation">@animator/sideslip_out</item>
    </style>

</resources>
