<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginEnd="8dp"
    android:background="@drawable/bg_round_10_white">

    <ImageView
        android:id="@+id/iv_banner_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_gradient_red"
        android:scaleType="centerCrop"
        tools:src="@tools:sample/backgrounds/scenic" />

    <!-- 可选：添加渐变遮罩，提升文字可读性 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:background="@drawable/gradient_black"
        android:visibility="gone" />

    <!-- 可选：添加标题文字 -->
    <TextView
        android:id="@+id/tv_banner_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="限时优惠活动"
        tools:visibility="visible" />

    <!-- 可选：添加指示器点 -->
    <LinearLayout
        android:id="@+id/ll_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="12dp"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/bg_circle_red" />

        <View
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/bg_circle_gray" />

        <View
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@drawable/bg_circle_gray" />

    </LinearLayout>

</FrameLayout>
