<?xml version="1.0" encoding="utf-8"?>
<!-- 
    使用 CardView 作为根布局，提供圆角和阴影效果。
    宽度设为 match_parent (由GridLayoutManager的列宽决定)，高度设为 wrap_content (自适应内容)。
-->
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <!-- 
        使用 ConstraintLayout 替代多层嵌套布局，实现扁平化的视图结构，提升性能。
    -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="8dp">

        <!-- 1. 商品图片 -->
        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/backgrounds/scenic"
            android:contentDescription="商品图片"/>

        <!-- 2. 商品标签 -->
        <TextView
            android:id="@+id/tv_product_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/ic_biguo_mall"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:text="笔果"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_product_image" />

        <!-- 3. 商品名称 -->
        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="这里是商品名称最多展示两行文字"
            android:textColor="#333333"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/tv_product_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_product_tag"
            app:layout_constraintTop_toTopOf="@id/tv_product_tag" />

        <!-- 4. 商品价格 -->
        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="¥128.00"
            android:textColor="#F44336"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/tv_product_tag"
            app:layout_constraintTop_toBottomOf="@id/tv_product_name" />

        <!-- 5. 已售数量 -->
        <TextView
            android:id="@+id/tv_product_sales"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已售8000+"
            android:textColor="#999999"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="@id/tv_product_name"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_product_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>