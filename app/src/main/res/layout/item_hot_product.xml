<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="150dp"
    android:layout_height="wrap_content"
    android:layout_marginEnd="12dp"
    android:background="@drawable/bg_round_8_white_stroke"
    android:orientation="vertical"
    android:padding="1dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="20dp"
            android:scaleType="centerInside"
            tools:src="@color/theme_alpha_10" />

        <TextView
            android:id="@+id/tvTopLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_top_label"
            android:paddingStart="8dp"
            android:paddingTop="3dp"
            android:paddingEnd="8dp"
            android:paddingBottom="3dp"
            android:textColor="@color/white"
            android:textSize="10sp"
            tools:text="月销量TOP1" />

    </FrameLayout>

    <TextView
        android:id="@+id/tvProductName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/tblack"
        android:textSize="14sp"
        tools:text="笔果AI学习机" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="8dp"
        android:textColor="@color/theme"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="¥2500.00" />

</LinearLayout>
