<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:background="@drawable/bg_round_8_white_stroke"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivProductImage"
        android:layout_width="match_parent"
        android:layout_height="170dp"
        android:scaleType="centerCrop"
        tools:src="@color/theme_alpha_10" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvProductTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_product_tag"
                android:paddingStart="6dp"
                android:paddingTop="2dp"
                android:paddingEnd="6dp"
                android:paddingBottom="2dp"
                android:textColor="@color/theme"
                android:textSize="10sp"
                tools:text="笔果" />

            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/tblack"
                android:textSize="14sp"
                tools:text="这里是商品名称最多展示两行文字" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="bottom"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/theme"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="¥128.00" />

            <TextView
                android:id="@+id/tvSales"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:gravity="start"
                android:textColor="@color/tblack3"
                android:textSize="12sp"
                tools:text="已售8000+" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout> 