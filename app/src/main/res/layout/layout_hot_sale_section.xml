<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!--
        1. 标题区域
        使用 ConstraintLayout 灵活地定位标题内部的元素
    -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp">

        <!-- 热卖商品标题容器，使用 bg_hot_product 背景 -->
        <LinearLayout
            android:id="@+id/ll_hot_sale_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@drawable/bg_hot_product"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="12dp"
            android:paddingTop="8dp"
            android:paddingEnd="12dp"
            android:paddingBottom="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_hot_sale_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_fire"
                android:contentDescription="热卖商品图标"/>

            <TextView
                android:id="@+id/tv_hot_sale_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="热卖商品"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_hot_sale_more"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_more_horiz_black_24dp"
            app:layout_constraintBottom_toBottomOf="@id/ll_hot_sale_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ll_hot_sale_title"
            android:contentDescription="更多"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--
        2. 热卖商品 - 横向列表
    -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_hot_sale"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="8dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="3"
        tools:listitem="@layout/item_product_hot" />

</LinearLayout>
