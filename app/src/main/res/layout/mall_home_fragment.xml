<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <!-- 定义点击事件的处理器变量 -->
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.MallHomeFragment" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="true">

        <!-- 状态栏占位视图，用于实现沉浸式状态栏效果 -->
        <com.dep.biguo.widget.StatusBarView
            android:id="@+id/statusBarView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <!--
            页面主标题 "商城"
            根据图片分析添加，居中显示在顶部。
            在您提供的参考XML中此元素缺失，现已补全。
         -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="商城"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/statusBarView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />


        <!-- 顶部操作栏容器，包含搜索和购物车 -->
        <LinearLayout
            android:id="@+id/topBarLayout"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <!--
                搜索栏布局。
                使用 LinearLayout 并设置权重 layout_weight=1，使其占据剩余空间。
                背景使用 shape drawable 实现圆角和红色描边。
            -->
            <LinearLayout
                android:id="@+id/searchLayout"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/bg_round_search_red_stroke"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="12dp"
                android:paddingEnd="12dp">

                <!-- 搜索图标 -->
                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/icon_search"
                    app:tint="@color/tblack3" />

                <!--
                    搜索输入框。
                    背景设为 null 以移除默认下划线，与父容器融为一体。
                -->
                <EditText
                    android:id="@+id/etSearch"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="搜索关键字, 查询题目"
                    android:imeOptions="actionSearch"
                    android:singleLine="true"
                    android:textColorHint="@color/tblack3"
                    android:textSize="14sp" />
            </LinearLayout>

            <!--
                购物车容器。
                使用 FrameLayout 以便将角标覆盖在图标之上。
            -->
            <FrameLayout
                android:id="@+id/cartLayout"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:onClick="@{onClickListener::onClick}">

                <!-- 购物车图标 -->
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/icon_shopping_cart"
                    app:tint="@color/black" />

                <!--
                    购物车角标。
                    使用 TextView 实现，背景为圆形红色 drawable。
                    layout_gravity="top|end" 将其定位到父容器右上角。
                -->
                <TextView
                    android:id="@+id/tvCartBadge"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/bg_circle_red"
                    android:gravity="center"
                    android:text="10"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:visibility="visible"
                    tools:text="10"/>
            </FrameLayout>

        </LinearLayout>

        <!-- 主内容区域 -->
        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/topBarLayout"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="16dp">

                    <!-- 横幅广告 -->
                    <ImageView
                        android:id="@+id/ivBanner"
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/bg_gradient_red"
                        android:scaleType="centerCrop"
                        tools:src="@color/theme_alpha_10" />

                    <!-- 分类标签 -->
                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/categoryTabs"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="16dp"
                        android:background="@color/white"
                        app:tabGravity="fill"
                        app:tabIndicatorHeight="0dp"
                        app:tabMode="fixed"
                        app:tabSelectedTextColor="@color/theme"
                        app:tabTextColor="@color/selector_tab_text_color"
                        app:tabTextAppearance="@style/MallTabTextAppearance" />

                    <!-- 热卖商品区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <!-- 火焰图标 -->
                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_fire"
                            app:tint="@color/theme" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:text="热卖商品"
                            android:textColor="@color/tblack"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <!-- 分页指示器 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <View
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:layout_marginEnd="4dp"
                                android:background="@drawable/bg_circle_red" />

                            <View
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:layout_marginEnd="4dp"
                                android:background="@drawable/bg_gradient_red" />

                            <View
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:background="@drawable/bg_gradient_red" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 热卖商品横向滚动列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvHotProducts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="false"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:orientation="horizontal" />

                    <!-- 全部商品区域 -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="32dp"
                        android:layout_marginBottom="16dp"
                        android:text="全部商品"
                        android:textColor="@color/tblack"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- 全部商品网格列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvAllProducts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="20dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </com.dep.biguo.widget.SmartRefreshLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
