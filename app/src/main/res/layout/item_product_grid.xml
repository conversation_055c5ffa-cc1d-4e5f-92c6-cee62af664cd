<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/bg_round_8_white_stroke"
    android:padding="8dp">

        <ImageView
            android:id="@+id/iv_product_image_grid"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#E0E0E0"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/backgrounds/scenic" />

        <TextView
            android:id="@+id/tv_product_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_product_tag"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="笔果"
            android:textColor="#F44336"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_product_image_grid" />

        <TextView
            android:id="@+id/tv_product_name_grid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/tv_product_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_product_tag"
            app:layout_constraintTop_toTopOf="@id/tv_product_tag"
            tools:text="这里是商品名称最多展示两行文字" />

        <TextView
            android:id="@+id/tv_product_price_grid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="#F44336"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/tv_product_tag"
            app:layout_constraintTop_toBottomOf="@id/tv_product_name_grid"
            tools:text="¥128.00" />

        <TextView
            android:id="@+id/tv_sales_count_grid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/tv_product_price_grid"
            app:layout_constraintStart_toEndOf="@id/tv_product_price_grid"
            tools:text="已售8000+" />

</androidx.constraintlayout.widget.ConstraintLayout>
